"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRole = updateRole;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = require("mssql");
function updateRole(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        const roleId = parseInt(request.params.roleId, 10);
        context.log(`Http function processed request for UpdateRole: ${roleId}`);
        let body;
        try {
            body = (yield request.json());
        }
        catch (e) {
            context.error("ERROR parsing request body:", e);
            return {
                status: 400,
                jsonBody: { message: "Invalid JSON in request body." }
            };
        }
        if (isNaN(roleId)) {
            return {
                status: 400,
                jsonBody: { error: "Invalid RoleID parameter." }
            };
        }
        if (!body || (body.RoleName === undefined && body.Description === undefined && body.IsActive === undefined)) {
            return {
                status: 400,
                jsonBody: { message: "No update data provided (RoleName, Description, or IsActive required)." }
            };
        }
        if (body.RoleName !== undefined && !body.RoleName.trim()) {
            return {
                status: 400,
                jsonBody: { message: "RoleName cannot be empty." }
            };
        }
        try {
            // 1. Validate RoleID
            if (isNaN(roleId)) {
                return { status: 400, jsonBody: { error: "Invalid RoleID parameter." } };
            }
            // Placeholder for user ID - replace with actual user ID from principal
            const modifiedByUserId = 1;
            // 2. Get current role details to check if it exists and isn't a system role
            const getCurrentRoleQuery = 'SELECT RoleID, RoleName, IsSystemRole FROM Roles WHERE RoleID = @RoleID';
            const getCurrentRoleParams = [
                { name: 'RoleID', type: sql.Int, value: roleId }
            ];
            const currentRoleResult = yield (0, db_1.executeQuery)(getCurrentRoleQuery, getCurrentRoleParams);
            if (!currentRoleResult.recordset || currentRoleResult.recordset.length === 0) {
                logger_1.logger.warn(`UpdateRole: Role with ID ${roleId} not found.`);
                return { status: 404, jsonBody: { error: "Role not found." } };
            }
            const currentRole = currentRoleResult.recordset[0];
            const currentRoleName = currentRole.RoleName;
            const isSystemRole = currentRole.IsSystemRole;
            // 3. Prevent updating system roles (maybe adjust this based on requirements)
            if (isSystemRole) {
                logger_1.logger.warn(`UpdateRole: Attempted to update system role '${currentRoleName}' (ID: ${roleId}).`);
                return { status: 403, jsonBody: { error: "System roles cannot be modified." } };
            }
            // 4. Check for name conflicts if RoleName is being updated
            if (body.RoleName && body.RoleName.trim().toLowerCase() !== currentRoleName.toLowerCase()) {
                const checkNameQuery = 'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName) AND RoleID != @RoleID';
                const checkNameParams = [
                    { name: 'RoleName', type: sql.NVarChar, value: body.RoleName.trim() },
                    { name: 'RoleID', type: sql.Int, value: roleId }
                ];
                const checkResult = yield (0, db_1.executeQuery)(checkNameQuery, checkNameParams);
                if (checkResult.recordset[0].Count > 0) {
                    logger_1.logger.warn(`UpdateRole: Role name '${body.RoleName.trim()}' already exists.`);
                    return { status: 409, jsonBody: { error: "Role name already exists." } };
                }
            }
            // 5. Build update query dynamically
            let query = 'UPDATE Roles SET ';
            const updateFields = [];
            const updateParams = []; // Use QueryParameter array
            if (body.RoleName && body.RoleName.trim().toLowerCase() !== currentRoleName.toLowerCase()) {
                updateFields.push('RoleName = @RoleName');
                updateParams.push({ name: 'RoleName', type: sql.NVarChar, value: body.RoleName.trim() });
            }
            // Use RoleDescription from schema
            if (body.Description !== undefined) {
                updateFields.push('RoleDescription = @Description');
                updateParams.push({ name: 'Description', type: sql.NVarChar, value: body.Description });
            }
            if (typeof body.IsActive === 'boolean') {
                updateFields.push('IsActive = @IsActive');
                updateParams.push({ name: 'IsActive', type: sql.Bit, value: body.IsActive });
            }
            if (updateFields.length === 0) {
                // If only IsActive was passed and it matches current state, maybe return 200 OK? No, require change.
                return { status: 400, jsonBody: { error: "No valid fields provided for update or no change detected." } };
            }
            // Always update ModifiedBy and ModifiedDate
            updateFields.push('ModifiedBy = @ModifiedBy');
            updateFields.push('ModifiedDate = GETUTCDATE()');
            updateParams.push({ name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId });
            query += updateFields.join(', ');
            query += ' OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription, INSERTED.IsActive ';
            query += ' WHERE RoleID = @RoleID';
            // Add RoleID for WHERE clause - must be last param added usually
            updateParams.push({ name: 'RoleID', type: sql.Int, value: roleId });
            // 6. Execute update
            logger_1.logger.debug(`Executing UpdateRole query: ${query} with params: ${JSON.stringify(updateParams)}`);
            const result = yield (0, db_1.executeQuery)(query, updateParams);
            if (!result.recordset || result.recordset.length === 0) {
                logger_1.logger.error(`UpdateRole: Failed to update role ${roleId}. No record returned.`);
                // This shouldn't happen if the initial check passed, but handle defensively
                return { status: 500, jsonBody: { error: "Failed to update role after execution." } };
            }
            const updatedRoleData = result.recordset[0];
            const responseRole = {
                RoleID: updatedRoleData.RoleID,
                RoleName: updatedRoleData.RoleName,
                Description: updatedRoleData.RoleDescription,
                IsActive: updatedRoleData.IsActive
            };
            logger_1.logger.info(`Successfully updated role ${roleId}: New name '${responseRole.RoleName}'`);
            return { status: 200, jsonBody: responseRole };
        }
        catch (error) {
            context.error(`Error updating role ${roleId}: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: `Error updating role ${roleId}.`,
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('UpdateRole', {
    methods: ['PUT'],
    authLevel: 'function',
    route: 'roles/{roleId:int}',
    handler: updateRole
});
//# sourceMappingURL=index.js.map