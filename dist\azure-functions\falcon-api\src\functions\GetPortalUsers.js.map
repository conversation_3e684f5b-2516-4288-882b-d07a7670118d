{"version": 3, "file": "GetPortalUsers.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/GetPortalUsers.ts"], "names": [], "mappings": ";;;;;;;;;;;AAuBA,wCA0EC;AAjGD,gDAAyF;AACzF,oFAAiF,CAAC,qBAAqB;AACvG,mDAA0E,CAAC,oBAAoB;AAC/F,mEAAoF,CAAC,oBAAoB;AACzG,mDAAgD,CAAC,gBAAgB;AAGjE,8CAA8C;AAC9C,gEAAgE;AAChE,SAAS,qBAAqB,CAAC,MAAW;IACtC,OAAO;QACH,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,uCAAuC;QAC3D,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,qBAAqB;QAChD,IAAI,EAAE,MAAM,CAAC,QAAQ;QACrB,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE,mCAAmC;QAChE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,iCAAiC;QAC9D,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,8DAA8D;QAClH,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,EAAE,2BAA2B;QAC5E,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;KAC3E,CAAC;AACN,CAAC;AAED,SAAsB,cAAc,CAAC,OAAoB,EAAE,OAA0B;;QACjF,OAAO,CAAC,GAAG,GAAG,eAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB;QAC/C,eAAM,CAAC,IAAI,CAAC,4CAA4C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAExE,0CAA0C;QAC1C,mDAAmD;QACnD,eAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;QAE3D,sEAAsE;QACtE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;YAChE,CAAC;YACD,yDAAyD;YACzD,uDAAuD;YACvD,yFAAyF;YACzF,gEAAgE;YAChE,IAAI;QACR,CAAC;aAAM,CAAC;YACJ,eAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QACvF,CAAC;QAED,sBAAsB;QACtB,+CAA+C;QAC/C,MAAM,uBAAuB,GAAG,IAAA,mCAAe,EAAC,wCAAoB,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC9H,IAAI,uBAAuB,EAAE,CAAC;YAC1B,4EAA4E;YAC5E,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC,CAAC,yCAAyC;YACrG,OAAO,uBAAuB,CAAC;QACnC,CAAC;QAED,4FAA4F;QAC5F,kEAAkE;QAClE,MAAM,WAAW,GAAG,wCAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC9E,yFAAyF;QACzF,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACvB,eAAM,CAAC,KAAK,CAAC,6FAA6F,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC/H,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;QAC7E,CAAC;QAED,kCAAkC;QAClC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC;QAEnF,IAAI,CAAC;YACD,yBAAyB;YACzB,MAAM,MAAM,GAAG,MAAM,6CAAqB,CAAC,uBAAuB,CAC9D,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EACzC,EAAE,IAAI,EAAE,QAAQ,EAAE,CACrB,CAAC;YAEF,qBAAqB;YACrB,OAAO;gBACH,MAAM,EAAE,GAAG,EAAE,4BAA4B;gBACzC,QAAQ,EAAE;oBACN,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,QAAQ;iBACrB;aACJ,CAAC;QAEN,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,eAAM,CAAC,KAAK,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,GAAY,CAAC;YAC3B,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,8BAA8B;oBACrC,OAAO,EAAE,KAAK,CAAC,OAAO;iBACzB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE;IACvB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,KAAK,EAAE,cAAc,EAAE,+EAA+E;IACtG,mFAAmF;IACnF,oFAAoF;IACpF,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,cAAc;CAC1B,CAAC,CAAC"}