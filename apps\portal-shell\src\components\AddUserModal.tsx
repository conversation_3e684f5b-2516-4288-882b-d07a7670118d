import { useState, useEffect } from 'react';
import './Modal.css';

interface Role {
  RoleID: number;
  RoleName: string;
  Description: string;
}

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddUser: (userData: { email: string; roleId: number }) => Promise<void>;
}

export const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onAddUser }) => {
  const [formData, setFormData] = useState({
    email: '',
    selectedRoleId: 0
  });
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingRoles, setLoadingRoles] = useState(false);

  // Load roles when modal opens
  useEffect(() => {
    if (isOpen) {
      loadRoles();
    }
  }, [isOpen]);

  const loadRoles = async () => {
    setLoadingRoles(true);
    try {
      const response = await fetch('/api/roles');

      if (!response.ok) {
        throw new Error(`Failed to load roles. Status: ${response.status}`);
      }

      const rolesData = await response.json();
      setRoles(rolesData);
      
      // Set default role to first available role
      if (rolesData.length > 0) {
        setFormData(prev => ({ ...prev, selectedRoleId: rolesData[0].RoleID }));
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      setError('Failed to load available roles');
    } finally {
      setLoadingRoles(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email.trim()) {
      setError('Email is required');
      return;
    }

    if (!formData.selectedRoleId) {
      setError('Please select a role');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await onAddUser({
        email: formData.email.trim(),
        roleId: formData.selectedRoleId
      });
      
      // Reset form and close modal on success
      setFormData({ email: '', selectedRoleId: 0 });
      onClose();
    } catch (error) {
      console.error('Error adding user:', error);
      setError(error instanceof Error ? error.message : 'Failed to add user');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({ email: '', selectedRoleId: 0 });
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Add New User to Portal</h2>
          <button 
            className="modal-close" 
            onClick={handleClose}
            disabled={loading}
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {error && (
            <div className="error-message" style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#fee', border: '1px solid #fcc', borderRadius: '4px', color: '#c00' }}>
              {error}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="email">Email Address *</label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              placeholder="<EMAIL>"
              required
              disabled={loading}
              style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
            />
            <small style={{ color: '#666', fontSize: '0.9em' }}>
              Enter the email address of the user you want to add to the portal
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="role">Role *</label>
            {loadingRoles ? (
              <div style={{ padding: '8px', color: '#666' }}>Loading roles...</div>
            ) : (
              <select
                id="role"
                value={formData.selectedRoleId}
                onChange={(e) => setFormData(prev => ({ ...prev, selectedRoleId: parseInt(e.target.value) }))}
                required
                disabled={loading}
                style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
              >
                <option value={0}>Select a role...</option>
                {roles.map(role => (
                  <option key={role.RoleID} value={role.RoleID}>
                    {role.RoleName} {role.Description && `- ${role.Description}`}
                  </option>
                ))}
              </select>
            )}
          </div>

          <div className="modal-footer">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              style={{ 
                padding: '8px 16px', 
                marginRight: '8px', 
                border: '1px solid #ddd', 
                borderRadius: '4px', 
                backgroundColor: '#f5f5f5',
                cursor: loading ? 'not-allowed' : 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || loadingRoles}
              style={{ 
                padding: '8px 16px', 
                border: 'none', 
                borderRadius: '4px', 
                backgroundColor: loading ? '#ccc' : '#007bff', 
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer'
              }}
            >
              {loading ? 'Adding User...' : 'Add User'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}; 