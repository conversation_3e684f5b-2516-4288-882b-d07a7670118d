"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUser = void 0;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const sql = __importStar(require("mssql")); // Add mssql import
const db_1 = require("../shared/db"); // Add QueryParameter import and use correct executeQuery import
const validationSchemas_1 = require("../shared/validationSchemas");
const authUtils_1 = require("../shared/authUtils"); // Only need getClientPrincipal
async function getUser(request, context) {
    context.log(`Http function GetUser processed request for url "${request.url}"`);
    logger_1.logger.info('GetUser function invoked.');
    // --- Authentication Check --- 
    const principal = (0, authUtils_1.getClientPrincipal)(request);
    if (!principal) {
        // Allow if provider allows anonymous and header is missing, 
        // but return 401 if header is present but invalid or not populated by provider
        // For simplicity, let's require authentication for this specific user endpoint
        if (request.headers.get('x-ms-client-principal')) {
            logger_1.logger.warn('GetUser: Invalid or missing client principal header despite presence.');
            return { status: 401, jsonBody: { message: "Unauthorized. Invalid client principal." } };
        }
        else {
            // If header truly missing, depends on provider config. Assume protected.
            logger_1.logger.warn('GetUser: Client principal header missing.');
            return { status: 401, jsonBody: { message: "Unauthorized." } };
        }
    }
    // No specific role check needed, any authenticated user can view user details?
    // --- End Auth --- 
    // --- Input Validation --- 
    const routeParams = { entraId: request.params.entraId }; // Extract for validation
    // Use the actual entraIdRouteParamSchema
    const validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.entraIdRouteParamSchema, routeParams, context, "route parameters");
    if (validationError)
        return validationError;
    // Validation passed, use validated data
    const validatedRouteParams = validationSchemas_1.entraIdRouteParamSchema.parse(routeParams); // Use parse to get typed data
    const entraId = validatedRouteParams.entraId; // Already a string
    // Manual validation REMOVED
    // --- End Validation --- 
    try {
        // Query to get user details, company name, department name, and aggregated role names
        const query = `
            SELECT 
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName,
                d.DepartmentName,
                STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) AS RoleNames
            FROM 
                Users u
            JOIN 
                Companies c ON u.CompanyID = c.CompanyID
            LEFT JOIN
                Departments d ON u.DepartmentID = d.DepartmentID AND d.IsActive = 1 -- Optional join for Department
            LEFT JOIN 
                UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN 
                Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE 
                u.EntraID = @EntraID
            GROUP BY
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName, d.DepartmentName
        `;
        logger_1.logger.debug("Executing GetUser query for Entra ID:", entraId);
        const userParams = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const result = await (0, db_1.executeQuery)(query, userParams);
        if (result.recordset.length === 0) {
            logger_1.logger.warn(`GetUser: User not found for Entra ID: ${entraId}`);
            return {
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: `User with EntraID ${entraId} not found.` }
            };
        }
        const dbUser = result.recordset[0];
        // Map the DB result to the PortalUser interface
        const portalUser = {
            id: dbUser.UserID,
            entraId: dbUser.EntraID,
            name: `${dbUser.FirstName} ${dbUser.LastName}`,
            email: dbUser.Email,
            company: dbUser.CompanyName,
            companyId: dbUser.CompanyID,
            department: dbUser.DepartmentName || undefined,
            roles: dbUser.RoleNames ? dbUser.RoleNames.split(',') : [],
            status: dbUser.IsActive ? 'Active' : 'Inactive'
        };
        logger_1.logger.info(`Successfully retrieved user details for EntraID ${entraId} (UserID: ${portalUser.id})`);
        return {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: portalUser
        };
    }
    catch (error) {
        logger_1.logger.error(`Error in GetUser function for EntraID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Error retrieving user details.",
                error: errorMessage
            }
        };
    }
}
exports.getUser = getUser;
functions_1.app.http('GetUser', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'portal-users/{entraId}',
    handler: getUser
});
//# sourceMappingURL=index.js.map