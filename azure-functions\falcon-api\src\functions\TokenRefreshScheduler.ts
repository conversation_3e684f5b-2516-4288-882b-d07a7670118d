import { app, InvocationContext, Timer } from "@azure/functions";
import { logger } from "../shared/utils/logger";
import { OAuthTokenService } from "../services/OAuthTokenService";
import { getPool } from "../shared/db";
import * as sql from 'mssql';

// Zoho Desk OAuth Configuration
interface ZohoDeskConfig {
    clientId: string;
    clientSecret: string;
    baseUrl: string;
}

interface ZohoDeskTokens {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
}

function getZohoDeskConfig(): ZohoDeskConfig {
    const region = process.env.ZOHO_DESK_REGION || 'in';
    const baseUrl = `https://accounts.zoho.${region}/oauth/v2`;
    
    return {
        clientId: process.env.ZOHO_DESK_CLIENT_ID || '',
        clientSecret: process.env.ZOHO_DESK_CLIENT_SECRET || '',
        baseUrl: baseUrl
    };
}

async function refreshZohoDeskToken(refreshToken: string): Promise<ZohoDeskTokens | null> {
    try {
        const config = getZohoDeskConfig();
        
        const refreshRequestBody = new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: refreshToken
        });

        const refreshResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: refreshRequestBody
        });

        if (!refreshResponse.ok) {
            logger.error("Failed to refresh Zoho Desk token");
            return null;
        }

        const newTokens: ZohoDeskTokens = await refreshResponse.json();
        return newTokens;
        
    } catch (error) {
        logger.error("Error refreshing Zoho Desk token:", error);
        return null;
    }
}

async function storeRefreshedTokens(userId: string, tokens: ZohoDeskTokens): Promise<void> {
    try {
        const tokensAny = tokens as any;
        const accessToken = tokens.access_token || tokensAny.accessToken || tokensAny.token;
        const refreshToken = tokens.refresh_token || tokensAny.refreshToken;
        const expiresIn = tokens.expires_in || tokensAny.expiresIn || 3600;
        const tokenType = tokens.token_type || tokensAny.tokenType || 'Bearer';
        const scope = tokens.scope;

        await OAuthTokenService.saveToken(
            userId,
            accessToken,
            refreshToken,
            expiresIn,
            scope,
            'zoho',
            'desk',
            tokenType
        );
        
        logger.info(`TokenRefreshScheduler: Successfully refreshed tokens for user ${userId}`);
    } catch (error) {
        logger.error(`TokenRefreshScheduler: Error storing refreshed tokens for user ${userId}:`, error);
        throw error;
    }
}

// Timer-triggered function that runs every 30 minutes
export async function tokenRefreshScheduler(myTimer: Timer, context: InvocationContext): Promise<void> {
    logger.info('TokenRefreshScheduler: Starting scheduled token refresh check');
    
    try {
        // Get all tokens that expire in the next 10 minutes
        const pool = await getPool();
        const result = await pool.request()
            .query(`
                SELECT DISTINCT UserId, AccessToken, RefreshToken, ExpiresAt, ServiceProvider, ServiceType
                FROM OAuthTokens 
                WHERE IsActive = 1 
                  AND ServiceProvider = 'zoho' 
                  AND ServiceType = 'desk'
                  AND ExpiresAt <= DATEADD(MINUTE, 10, GETUTCDATE())
                  AND RefreshToken IS NOT NULL
                ORDER BY ExpiresAt ASC
            `);

        if (!result.recordset || result.recordset.length === 0) {
            logger.info('TokenRefreshScheduler: No tokens need refreshing');
            return;
        }

        logger.info(`TokenRefreshScheduler: Found ${result.recordset.length} tokens that need refreshing`);

        let successCount = 0;
        let failureCount = 0;

        // Process each token that needs refreshing
        for (const tokenRecord of result.recordset) {
            try {
                logger.info(`TokenRefreshScheduler: Refreshing token for user ${tokenRecord.UserId}`);
                
                const refreshedTokens = await refreshZohoDeskToken(tokenRecord.RefreshToken);
                
                if (refreshedTokens) {
                    await storeRefreshedTokens(tokenRecord.UserId, refreshedTokens);
                    successCount++;
                } else {
                    logger.error(`TokenRefreshScheduler: Failed to refresh token for user ${tokenRecord.UserId}`);
                    failureCount++;
                    
                    // TODO: Send notification to admin about failed refresh
                    // TODO: Send email to user about need to re-authorize
                }
                
            } catch (error) {
                logger.error(`TokenRefreshScheduler: Error processing token for user ${tokenRecord.UserId}:`, error);
                failureCount++;
            }
        }

        logger.info(`TokenRefreshScheduler: Completed. Success: ${successCount}, Failures: ${failureCount}`);
        
        // TODO: Log metrics to Application Insights
        // TODO: Send summary report to admin if failures > 0
        
    } catch (error) {
        logger.error('TokenRefreshScheduler: Error during scheduled token refresh:', error);
        // TODO: Send alert to admin about scheduler failure
    }
}

// Register the timer-triggered function
// Runs every 30 minutes: "0 */30 * * * *"
app.timer('TokenRefreshScheduler', {
    schedule: '0 */30 * * * *', // Every 30 minutes
    handler: tokenRefreshScheduler
});
