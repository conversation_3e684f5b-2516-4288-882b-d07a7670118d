# Check if Azurite is installed
$azuriteInstalled = npm list -g azurite

if ($azuriteInstalled -match "azurite") {
    Write-Host "Azurite is already installed."
} else {
    Write-Host "Installing Azurite globally..."
    npm install -g azurite
}

# Create directory for Azurite data if it doesn't exist
$azuriteDataDir = ".\azurite-data"
if (-not (Test-Path $azuriteDataDir)) {
    New-Item -ItemType Directory -Path $azuriteDataDir
}

# Start Azurite
Write-Host "Starting Azurite..."
Start-Process -FilePath "azurite" -ArgumentList "--silent", "--location", $azuriteDataDir, "--debug", ".\azurite-debug.log"

Write-Host "Azurite started. You can now run 'func start' in another terminal."
Write-Host "To stop Azurite, find and close the Node.js process."
