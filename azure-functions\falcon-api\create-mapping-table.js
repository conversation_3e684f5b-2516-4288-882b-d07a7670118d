const sql = require('mssql');
const fs = require('fs');

const config = {
    server: 'fp-sql-falcon-dev-cin-001.database.windows.net',
    database: 'FalconHubDB',
    user: 'falconadmin',
    password: 'FalconIris@2024',
    options: {
        encrypt: true,
        trustServerCertificate: false,
        enableArithAbort: true
    }
};

async function createMappingTable() {
    try {
        console.log('🔄 Connecting to database...');
        console.log('📍 Server:', config.server);
        console.log('📍 Database:', config.database);
        await sql.connect(config);
        console.log('✅ Connected to database');
        
        const script = fs.readFileSync('create-entraid-mapping-table.sql', 'utf8');
        console.log('📄 SQL script loaded');
        
        // Split the script by GO statements and execute each batch
        const batches = script.split(/\nGO\n/);
        
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i].trim();
            if (batch.length > 0) {
                console.log(`🔄 Executing batch ${i + 1}/${batches.length}...`);
                try {
                    await sql.query(batch);
                    console.log(`✅ Batch ${i + 1} executed successfully`);
                } catch (batchError) {
                    console.error(`❌ Error in batch ${i + 1}:`, batchError.message);
                    // Continue with next batch if it's not critical
                    if (batchError.message.includes('already exists')) {
                        console.log('⚠️  Object already exists, continuing...');
                    } else {
                        throw batchError;
                    }
                }
            }
        }
        
        console.log('🎉 UserEntraIDMappings table, stored procedure, and view created successfully!');
        
        // Test the stored procedure
        console.log('🔄 Testing stored procedure...');
        await sql.query(`
            EXEC UpsertUserEntraIDMapping 
                @Email = '<EMAIL>',
                @EntraID = 'test-entra-id',
                @TenantID = 'test-tenant-id',
                @DisplayName = 'Test User',
                @CompanyName = 'Test Company',
                @CaptureSource = 'Test'
        `);
        
        // Clean up test data
        await sql.query(`DELETE FROM UserEntraIDMappings WHERE Email = '<EMAIL>'`);
        console.log('✅ Stored procedure test completed successfully!');
        
        // Verify table exists
        const tableCheck = await sql.query(`
            SELECT COUNT(*) as TableExists 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'UserEntraIDMappings'
        `);
        console.log('📊 UserEntraIDMappings table exists:', tableCheck.recordset[0].TableExists > 0);
        
    } catch (error) {
        console.error('❌ Error creating mapping table:', error.message);
        console.error('Full error:', error);
        process.exit(1);
    } finally {
        await sql.close();
        console.log('🔌 Database connection closed');
    }
}

createMappingTable(); 