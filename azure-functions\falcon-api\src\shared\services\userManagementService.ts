import { executeQuery, getPool, QueryParameter } from "../db";
import { logger } from "../utils/logger";
import { PortalUser } from "../interfaces";
// Import PortalUser
import { RoleDefinition } from "../interfaces"; 
// Remove GraphUser import if not used
// import { GraphUser } from "./graphService"; 
import sql from 'mssql'; // Ensure mssql is imported
// Import graphService when created
// import { graphService } from './graphService'; 

// Define interface for the structure returned by the DB for a User
// Based on falcon-db-schema.sql and portal-shell adminApi.ts PortalUser
export interface DbUser {
    UserID: number;
    EntraID: string | null;
    TenantID: string | null; // Added for multi-tenant support
    Username: string;
    Email: string;
    FirstName: string;
    LastName: string;
    CompanyID: number;
    LocationID: number | null;
    DepartmentID: number | null;
    EmployeeID: string | null;
    ContactNumber: string | null;
    ProfileImageURL: string | null;
    ManagerID: number | null;
    DateOfJoining: Date | null;
    LastLoginDate: Date | null;
    IsActive: boolean;
    CreatedBy: number;
    CreatedDate: Date;
    ModifiedBy: number | null;
    ModifiedDate: Date | null;
    // Joined fields often needed
    CompanyName?: string;
    DepartmentName?: string;
    // Role names might be fetched separately or via a more complex query
}

// Define interface for Entra User data passed to findOrCreateUser
// Based on entra-id-integration-guide.md graphService example
export interface EntraUserData {
    id: string; // maps to EntraID
    userPrincipalName: string; // maps to Username
    mail?: string; // maps to Email (or userPrincipalName if mail is null)
    givenName?: string; // maps to FirstName
    surname?: string; // maps to LastName
    companyName?: string; // Used to lookup CompanyID
    department?: string; // Used to lookup DepartmentID (optional)
    // Add other fields if needed from Graph API response
}

// Helper to map DB row to PortalUser - respects database role assignments as-is
function mapDbUserToPortalUser(dbUser: any): PortalUser {
    // Use actual FirstName and LastName from database if available, otherwise extract from email
    const firstName = dbUser.FirstName || '';
    const lastName = dbUser.LastName || '';
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName;
    
    // Parse roles from database - respect all assigned roles
    let userRoles = dbUser.Roles ? dbUser.Roles.split(',').map((role: string) => role.trim()) : [];
    
    // Fallback to email extraction if no name in database
    if (!fullName) {
        const emailPart = dbUser.Email.split('@')[0]; // Get part before @
        const nameParts = emailPart.split('.'); // Split by dots
        const extractedFirstName = nameParts[0] ? nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : '';
        const extractedLastName = nameParts[1] ? nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : '';
        const extractedFullName = extractedLastName ? `${extractedFirstName} ${extractedLastName}` : extractedFirstName;
        
        return {
            id: dbUser.EntraID, // Assuming EntraID is the primary link
            internalId: dbUser.UserID, // Internal DB UserID
            name: extractedFullName || dbUser.Username, // Use extracted name or fallback to username
            email: dbUser.Email,
            company: dbUser.CompanyName, // Assuming join brings CompanyName
            companyId: dbUser.CompanyID, // Assuming join brings CompanyID
            roles: userRoles, // Database roles as-is
            status: dbUser.IsActive ? 'Active' : 'Inactive', // Assuming IsActive column
            lastLogin: dbUser.LastLogin ? dbUser.LastLogin.toISOString() : undefined,
            tenantId: dbUser.TenantID // Multi-tenant support
        };
    }

    const mappedUser: PortalUser = {
        id: dbUser.EntraID, // Assuming EntraID is the primary link
        internalId: dbUser.UserID, // Internal DB UserID
        name: fullName, // Use actual database name
        email: dbUser.Email,
        company: dbUser.CompanyName, // Assuming join brings CompanyName
        companyId: dbUser.CompanyID, // Assuming join brings CompanyID
        roles: userRoles, // Database roles as-is
        status: dbUser.IsActive ? 'Active' : 'Inactive', // Assuming IsActive column
        lastLogin: dbUser.LastLogin ? dbUser.LastLogin.toISOString() : undefined,
        tenantId: dbUser.TenantID // Multi-tenant support
    };
    
    return mappedUser;
}

/**
 * Enhanced findOrCreateUser with security checks
 * Creates a new user in the database with proper role assignment
 */
export const findOrCreateUser = async (entraUser: EntraUserData, createdByUserId: number = 1): Promise<DbUser | null> => {
    logger.info(`findOrCreateUser called for Entra ID: ${entraUser.id}`);

    const checkUserQuery = `SELECT * FROM Users WHERE EntraID = @EntraID;`;
    const checkUserParams: QueryParameter[] = [
        { name: 'EntraID', type: sql.NVarChar, value: entraUser.id }
    ];

    try {
        const existingUserResult = await executeQuery(checkUserQuery, checkUserParams);

        if (existingUserResult.recordset && existingUserResult.recordset.length > 0) {
            const existingUser: DbUser = existingUserResult.recordset[0];
            logger.info(`Found existing user ${existingUser.UserID} for Entra ID ${entraUser.id}`);
            // TODO: Optionally update LastLoginDate or other fields on find?
            // const updateUserLoginQuery = `UPDATE Users SET LastLoginDate = GETDATE() WHERE UserID = @UserID`;
            // await executeQuery(updateUserLoginQuery, { UserID: existingUser.UserID });
            return existingUser;
        } else {
            logger.info(`User with Entra ID ${entraUser.id} not found. Creating new user.`);
            
            // --- 1. Resolve CompanyID --- 
            let companyId: number | null = null;
            if (entraUser.companyName) {
                const companyQuery = `SELECT CompanyID FROM Companies WHERE CompanyName = @CompanyName AND IsActive = 1;`;
                const companyParams: QueryParameter[] = [
                    { name: 'CompanyName', type: sql.NVarChar, value: entraUser.companyName }
                ];
                try {
                    const companyResult = await executeQuery(companyQuery, companyParams);
                    if (companyResult.recordset && companyResult.recordset.length > 0) {
                        companyId = companyResult.recordset[0].CompanyID;
                    } else {
                        // Company name provided but not found in DB - this is an error state.
                        logger.error(`Company '${entraUser.companyName}' provided by Entra ID not found or inactive in Companies table for user ${entraUser.id}.`);
                        throw new Error(`Company '${entraUser.companyName}' not found or inactive in the database. Cannot create user.`);
                    }
                } catch (companyError) {
                     logger.error(`Error looking up company '${entraUser.companyName}' for user ${entraUser.id}:`, companyError);
                     throw new Error(`Database error during company lookup for user ${entraUser.id}.`); // Rethrow as critical error
                }
            } else {
                 // No company name provided by Entra ID. What's the policy?
                 // Option A: Assign a default/placeholder company.
                 // Option B: Reject user creation.
                 // For now, let's use a placeholder (e.g., ID 1) but log a strong warning.
                 companyId = 1; // Placeholder for 'Unspecified' or first company
                 logger.warn(`Entra user ${entraUser.id} has no companyName. Assigning default CompanyID: ${companyId}. Review required.`);
                 // Consider throwing an error if a company is strictly required:
                 // throw new Error(`Entra user ${entraUser.id} is missing required company information.`);
            }

            // --- 2. Resolve DepartmentID (Optional) --- 
            let departmentId: number | null = null;
            if (entraUser.department && companyId) { // Only lookup if department and company are known
                 const deptQuery = `SELECT DepartmentID FROM Departments WHERE DepartmentName = @DepartmentName AND CompanyID = @CompanyID AND IsActive = 1;`;
                 const deptParams: QueryParameter[] = [
                     { name: 'DepartmentName', type: sql.NVarChar, value: entraUser.department },
                     { name: 'CompanyID', type: sql.Int, value: companyId }
                 ];
                 try {
                     const deptResult = await executeQuery(deptQuery, deptParams);
                     if (deptResult.recordset && deptResult.recordset.length > 0) {
                         departmentId = deptResult.recordset[0].DepartmentID;
                     } else {
                         // Department not found, but maybe that's okay? Log warning, set to NULL.
                         logger.warn(`Department '${entraUser.department}' not found or inactive in CompanyID ${companyId} for user ${entraUser.id}. Setting DepartmentID to NULL.`);
                     }
                 } catch(deptError) {
                     // Log error but don't necessarily fail user creation
                     logger.error(`Error looking up department '${entraUser.department}' for user ${entraUser.id}:`, deptError);
                 }
            }

            // --- 3. Create User --- 
            const insertUserQuery = `
                INSERT INTO Users (
                    EntraID, Username, Email, FirstName, LastName, 
                    CompanyID, DepartmentID, IsActive, CreatedBy, CreatedDate, ModifiedDate
                ) 
                OUTPUT INSERTED.* 
                VALUES (
                    @EntraID, @Username, @Email, @FirstName, @LastName, 
                    @CompanyID, @DepartmentID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE()
                );
            `;
            // Convert newUserParams object to QueryParameter array
            const newUserParamsArray: QueryParameter[] = [
                { name: 'EntraID', type: sql.NVarChar, value: entraUser.id },
                { name: 'Username', type: sql.NVarChar, value: entraUser.userPrincipalName },
                { name: 'Email', type: sql.NVarChar, value: entraUser.mail || entraUser.userPrincipalName },
                { name: 'FirstName', type: sql.NVarChar, value: entraUser.givenName || '' },
                { name: 'LastName', type: sql.NVarChar, value: entraUser.surname || 'User' },
                { name: 'CompanyID', type: sql.Int, value: companyId },
                // Handle potential null for DepartmentID - pass sql.Int, value can be null
                { name: 'DepartmentID', type: sql.Int, value: departmentId }, 
                { name: 'IsActive', type: sql.Bit, value: true }, 
                { name: 'CreatedBy', type: sql.Int, value: createdByUserId } 
            ];

            const newUserResult = await executeQuery(insertUserQuery, newUserParamsArray);

            if (!newUserResult.recordset || newUserResult.recordset.length === 0) {
                 logger.error("Failed to create user or retrieve created record.", { entraId: entraUser.id });
                 throw new Error("User creation failed in database.");
            }
            
            const createdUser: DbUser = newUserResult.recordset[0];
            logger.info(`Successfully created user ${createdUser.UserID} for Entra ID ${entraUser.id}`);

            // --- 4. Assign Default Role --- 
            const defaultRoleName = process.env.DEFAULT_USER_ROLE || 'Employee'; // Use env var or fallback
            logger.info(`Attempting to assign default role: ${defaultRoleName}`);
            let defaultRoleId: number | null = null;
            try {
                 const roleQuery = `SELECT RoleID FROM Roles WHERE RoleName = @RoleName AND IsActive = 1;`;
                 const roleParams: QueryParameter[] = [
                     { name: 'RoleName', type: sql.NVarChar, value: defaultRoleName }
                 ];
                 const roleResult = await executeQuery(roleQuery, roleParams);
                 if (roleResult.recordset && roleResult.recordset.length > 0) {
                     defaultRoleId = roleResult.recordset[0].RoleID;
                 } else {
                      logger.error(`Default role '${defaultRoleName}' not found or inactive in Roles table.`);
                 }
            } catch (roleError) {
                 logger.error(`Error looking up default role '${defaultRoleName}':`, roleError);
            }

            if (defaultRoleId) {
                const insertUserRoleQuery = `
                    INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate)
                    VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());
                `;
                const insertUserRoleParams: QueryParameter[] = [
                    { name: 'UserID', type: sql.Int, value: createdUser.UserID },
                    { name: 'RoleID', type: sql.Int, value: defaultRoleId },
                    { name: 'IsActive', type: sql.Bit, value: true },
                    { name: 'CreatedBy', type: sql.Int, value: createdByUserId }
                ];
                try {
                     await executeQuery(insertUserRoleQuery, insertUserRoleParams);
                     logger.info(`Assigned default role '${defaultRoleName}' (ID: ${defaultRoleId}) to user ${createdUser.UserID}`);
                } catch (userRoleError) {
                     // Log error but don't fail the whole process
                     logger.error(`Failed to assign default role to user ${createdUser.UserID}:`, userRoleError);
                }
            }

            return createdUser;
        }
    } catch (error) {
        // Log the specific error from company/dept lookup or user insert
        logger.error(`Error in findOrCreateUser process for Entra ID ${entraUser.id}:`, error);
        // Re-throw the error to be caught by the calling function (e.g., AddUser API)
        throw error; 
        // return null; // Returning null hides the error type
    }
};

/**
 * Assigns a specific role to a user.
 * If the assignment already exists but is inactive, it reactivates it.
 * If the assignment doesn't exist, it creates a new one.
 * 
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to assign.
 * @param assignedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role was successfully assigned or reactivated, false otherwise.
 */
export const assignRoleToUser = async (userId: number, roleId: number, assignedByUserId: number): Promise<boolean> => {
    logger.info(`assignRoleToUser called for UserID: ${userId}, RoleID: ${roleId}`);

    const checkQuery = `SELECT UserRoleID, IsActive FROM UserRoles WHERE UserID = @UserID AND RoleID = @RoleID;`;
    const checkParams: QueryParameter[] = [
        { name: 'UserID', type: sql.Int, value: userId },
        { name: 'RoleID', type: sql.Int, value: roleId }
    ];

    try {
        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset && checkResult.recordset.length > 0) {
            // Assignment exists
            const existingAssignment = checkResult.recordset[0];
            if (!existingAssignment.IsActive) {
                // Reactivate existing inactive assignment
                logger.info(`Reactivating existing role assignment (UserRoleID: ${existingAssignment.UserRoleID}) for UserID: ${userId}, RoleID: ${roleId}`);
                const updateQuery = `UPDATE UserRoles SET IsActive = 1, ModifiedBy = @ModifiedBy, ModifiedDate = GETDATE() WHERE UserRoleID = @UserRoleID;`;
                const updateParams: QueryParameter[] = [
                    { name: 'UserRoleID', type: sql.Int, value: existingAssignment.UserRoleID },
                    { name: 'ModifiedBy', type: sql.Int, value: assignedByUserId }
                ];
                await executeQuery(updateQuery, updateParams);
                return true;
            } else {
                // Role already active
                logger.info(`Role ${roleId} is already actively assigned to user ${userId}. No action needed.`);
                return true; // Consider already assigned as success
            }
        } else {
            // Assignment does not exist, create new one
            logger.info(`Creating new role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            const insertQuery = `INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate) VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());`;
            const insertParams: QueryParameter[] = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'RoleID', type: sql.Int, value: roleId },
                { name: 'IsActive', type: sql.Bit, value: true },
                { name: 'CreatedBy', type: sql.Int, value: assignedByUserId }
            ];
            await executeQuery(insertQuery, insertParams);
            return true;
        }
    } catch (error) {
        logger.error(`Error assigning role ${roleId} to user ${userId}:`, error);
        return false; // Indicate failure
    }
};

/**
 * Removes a specific role from a user (by marking the assignment as inactive).
 * 
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to remove.
 * @param removedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role assignment was successfully marked as inactive, false otherwise.
 */
export const removeRoleFromUser = async (userId: number, roleId: number, removedByUserId: number): Promise<boolean> => {
    logger.info(`removeRoleFromUser called for UserID: ${userId}, RoleID: ${roleId}`);

    const updateQuery = `UPDATE UserRoles SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID AND RoleID = @RoleID AND IsActive = 1;`;
    const updateParams: QueryParameter[] = [
        { name: 'UserID', type: sql.Int, value: userId },
        { name: 'RoleID', type: sql.Int, value: roleId },
        { name: 'ModifiedBy', type: sql.Int, value: removedByUserId }
    ];

    try {
        const result = await executeQuery(updateQuery, updateParams);

        // rowsAffected will be 1 if an active record was found and updated, 0 otherwise.
        if (result.rowsAffected && result.rowsAffected[0] === 1) {
            logger.info(`Successfully deactivated role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            return true;
        } else {
            logger.warn(`No active role assignment found for UserID: ${userId}, RoleID: ${roleId} to deactivate.`);
            // Consider it success if the role wasn't active anyway?
            // Returning true might be less confusing for the caller.
            return true; // Indicate success even if no change was needed
        }
    } catch (error) {
        logger.error(`Error removing role ${roleId} from user ${userId}:`, error);
        return false; // Indicate failure
    }
};

/**
 * Updates the LastLoginDate for a given user in the local database.
 * 
 * @param userId - The internal UserID of the user.
 * @returns True if the update was successful, false otherwise.
 */
export const updateUserLastLogin = async (userId: number): Promise<boolean> => {
    logger.info(`Updating LastLoginDate for UserID: ${userId}`);
    const query = `UPDATE Users SET LastLoginDate = GETUTCDATE() WHERE UserID = @UserID;`;
    const params: QueryParameter[] = [
        { name: 'UserID', type: sql.Int, value: userId }
    ];
    try {
        await executeQuery(query, params);
        return true;
    } catch (error) {
        logger.error(`Error updating LastLoginDate for UserID ${userId}:`, error);
        return false;
    }
};

/**
 * Updates a user's EntraID to match the actual Entra Object ID.
 * This is used when a user is found by email but has an incorrect EntraID stored.
 * @param userId - The internal UserID of the user to update.
 * @param correctEntraId - The correct Entra Object ID from authentication.
 * @returns True if the update was successful, false otherwise.
 */
export const updateUserEntraId = async (userId: number, correctEntraId: string): Promise<boolean> => {
    logger.info(`updateUserEntraId: Updating UserID ${userId} with correct EntraID: ${correctEntraId}`);
    const query = `
        UPDATE Users 
        SET EntraID = @EntraID, ModifiedDate = GETUTCDATE() 
        WHERE UserID = @UserID AND IsActive = 1;
    `;
    const params: QueryParameter[] = [
        { name: 'EntraID', type: sql.NVarChar, value: correctEntraId },
        { name: 'UserID', type: sql.Int, value: userId }
    ];

    try {
        const result = await executeQuery(query, params);
        if (result.rowsAffected && result.rowsAffected[0] > 0) {
            logger.info(`Successfully updated EntraID for UserID: ${userId}`);
            return true;
        }
        logger.warn(`No active user found to update with UserID: ${userId}`);
        return false;
    } catch (error) {
        logger.error(`Error updating EntraID for UserID ${userId}:`, error);
        throw new Error(`Database error while updating EntraID for UserID ${userId}.`);
    }
}

/**
 * Gets a portal user by email address.
 * Used as fallback when EntraID lookup fails due to data inconsistency.
 * @param email - The email address of the user.
 * @returns The PortalUser or null if not found.
 */
export const getPortalUserByEmail = async (email: string): Promise<PortalUser | null> => {
    logger.info(`getPortalUserByEmail: Starting lookup for email: ${email}`);
    const query = `
        SELECT
            pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
            c.CompanyID, c.CompanyName,
            (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
             FROM UserRoles ur
             JOIN Roles r ON ur.RoleID = r.RoleID
             WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
        FROM Users pu
        LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
        WHERE pu.Email = @Email AND pu.IsActive = 1;
    `;
    const params: QueryParameter[] = [
        { name: 'Email', type: sql.NVarChar, value: email }
    ];

    try {
        logger.info(`getPortalUserByEmail: Executing SQL query...`);
        logger.info(`getPortalUserByEmail: SQL: ${query.replace(/\s+/g, ' ').trim()}`);
        logger.info(`getPortalUserByEmail: Params: Email = '${email}'`);
        
        const result = await executeQuery(query, params);
        
        logger.info(`getPortalUserByEmail: Query executed successfully`);
        logger.info(`getPortalUserByEmail: Result count: ${result.recordset?.length || 0}`);
        
        if (result.recordset && result.recordset.length > 0) {
            const dbUser = result.recordset[0];
            logger.info(`getPortalUserByEmail: ✅ USER FOUND IN DATABASE`);
            logger.info(`getPortalUserByEmail: UserID: ${dbUser.UserID}`);
            logger.info(`getPortalUserByEmail: Email: ${dbUser.Email}`);
            logger.info(`getPortalUserByEmail: Username: ${dbUser.Username}`);
            logger.info(`getPortalUserByEmail: FirstName: ${dbUser.FirstName}`);
            logger.info(`getPortalUserByEmail: LastName: ${dbUser.LastName}`);
            logger.info(`getPortalUserByEmail: IsActive: ${dbUser.IsActive}`);
            logger.info(`getPortalUserByEmail: CompanyName: ${dbUser.CompanyName}`);
            logger.info(`getPortalUserByEmail: Roles from DB: '${dbUser.Roles}'`);
            
            const mappedUser = mapDbUserToPortalUser(dbUser);
            logger.info(`getPortalUserByEmail: Mapped user roles: [${mappedUser.roles.join(', ')}]`);
            return mappedUser;
        }
        
        logger.warn(`getPortalUserByEmail: ❌ NO USER FOUND - Email '${email}' not in database or user is inactive`);
        return null;
    } catch (error) {
        logger.error(`getPortalUserByEmail: 💥 DATABASE ERROR for email ${email}:`, error);
        throw new Error(`Database error while fetching user by email ${email}.`);
    }
}



/**
 * Updates a user's EntraID and TenantID.
 * Used when migrating legacy users to multi-tenant identification.
 * @param userId - The internal UserID from the Users table.
 * @param entraId - The new EntraID to set.
 * @param tenantId - The new TenantID to set.
 * @returns Promise that resolves when the update is complete.
 */
export const updateUserEntraIdAndTenant = async (userId: number, entraId: string, tenantId: string): Promise<void> => {
    logger.info(`updateUserEntraIdAndTenant called for UserID: ${userId}, EntraID: ${entraId}, TenantID: ${tenantId}`);
    const query = `
        UPDATE Users 
        SET EntraID = @EntraID, TenantID = @TenantID, ModifiedDate = GETDATE()
        WHERE UserID = @UserID;
    `;
    const params: QueryParameter[] = [
        { name: 'UserID', type: sql.Int, value: userId },
        { name: 'EntraID', type: sql.NVarChar, value: entraId },
        { name: 'TenantID', type: sql.NVarChar, value: tenantId }
    ];

    try {
        await executeQuery(query, params);
        logger.info(`Successfully updated user ${userId} with EntraID ${entraId} and TenantID ${tenantId}`);
    } catch (error) {
        logger.error(`Error updating user ${userId} with EntraID ${entraId} and TenantID ${tenantId}:`, error);
        throw new Error(`Database error while updating user ${userId} with EntraID and TenantID.`);
    }
};

// TODO: Implement other user management functions from the guide:
// - (Potentially) updateUser(userId: number, updateData: Partial<DbUser>, modifiedBy: number): Promise<DbUser | null>
// - (Potentially) getUserById(userId: number): Promise<DbUser | null> - might need joins
// - (Potentially) getUsersWithRoles(filters, pagination): Promise<{users: DbUser[], totalCount: number}> - complex query needed

export class UserManagementService {
    // ... existing methods ...

    async updateUserLastLogin(userId: number): Promise<void> {
        // ... existing implementation ...
    }

    /**
     * Retrieves a paginated and filtered list of portal users.
     * @param filters - Filtering criteria (searchTerm, companyFilter, roleFilter, status).
     * @param pagination - Pagination options (page, pageSize).
     * @returns A promise resolving to an object containing the list of users and the total count.
     */
    async getPortalUsersPaginated(
        filters: { searchTerm?: string; companyFilter?: string; roleFilter?: string; status?: 'Active' | 'Inactive' },
        pagination: { page: number; pageSize: number }
    ): Promise<{ users: PortalUser[]; totalCount: number }> {
        logger.info(`[DEBUG] Executing getPortalUsersPaginated with filters: ${JSON.stringify(filters)}, pagination: ${JSON.stringify(pagination)}`);

        // Build WHERE conditions and parameters
        const whereClauses: string[] = [];
        const parameters: QueryParameter[] = [];
        
        // Search filter
        if (filters.searchTerm) {
            whereClauses.push(`(pu.Username LIKE @searchTerm OR pu.Email LIKE @searchTerm OR pu.FirstName LIKE @searchTerm OR pu.LastName LIKE @searchTerm)`);
            parameters.push({ name: 'searchTerm', type: sql.NVarChar, value: `%${filters.searchTerm}%` });
        }

        // Company filter - filter by company name
        if (filters.companyFilter) {
            whereClauses.push(`c.CompanyName = @companyFilter`);
            parameters.push({ name: 'companyFilter', type: sql.NVarChar, value: filters.companyFilter });
        }

        // Status filter - THIS IS THE KEY FIX
        if (filters.status === 'Active') {
            whereClauses.push(`pu.IsActive = 1`);
        } else if (filters.status === 'Inactive') {
            whereClauses.push(`pu.IsActive = 0`);
        }
        // If status is undefined (All), don't add any filter

        // Role filter - filter by role name using EXISTS subquery
        if (filters.roleFilter) {
            whereClauses.push(`EXISTS (SELECT 1 FROM UserRoles ur JOIN Roles r ON ur.RoleID = r.RoleID WHERE ur.UserID = pu.UserID AND ur.IsActive = 1 AND r.RoleName = @roleFilter)`);
            parameters.push({ name: 'roleFilter', type: sql.NVarChar, value: filters.roleFilter });
        }

        const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        
        logger.info(`[DEBUG] WHERE clause: ${whereClause}`);

        // Simple count query
        const countQuery = `
            SELECT COUNT(*) as totalCount
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            ${whereClause}
        `;

        // Simple data query with pagination
        const dataQuery = `
            SELECT 
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') 
                 FROM UserRoles ur 
                 JOIN Roles r ON ur.RoleID = r.RoleID 
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            ${whereClause}
            ORDER BY pu.UserID
            OFFSET @offset ROWS
            FETCH NEXT @pageSize ROWS ONLY
        `;

        // Add pagination parameters
        const offset = (pagination.page - 1) * pagination.pageSize;
        parameters.push({ name: 'offset', type: sql.Int, value: offset });
        parameters.push({ name: 'pageSize', type: sql.Int, value: pagination.pageSize });

        try {
            // Execute count query (without pagination parameters)
            const countParams = parameters.filter(p => p.name !== 'offset' && p.name !== 'pageSize');
            const countResult = await executeQuery(countQuery, countParams);
            const totalCount = countResult.recordset[0].totalCount;

            // Execute data query (with pagination parameters)
            const dataResult = await executeQuery(dataQuery, parameters);
            
            logger.info(`[DEBUG] Raw query returned ${dataResult.recordset.length} users`);
            logger.info(`[DEBUG] Total count: ${totalCount}`);

            // Map results
            const users = dataResult.recordset.map(mapDbUserToPortalUser);

            return {
                users,
                totalCount
            };

        } catch (error) {
            logger.error(`Error in getPortalUsersPaginated: ${error}`);
            throw error;
        }
    }

    async getPortalUserByEntraId(entraId: string): Promise<PortalUser | null> {
        logger.info(`getPortalUserByEntraId called for Entra ID: ${entraId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.EntraID = @EntraID;
        `;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger.debug("Raw DB result for user:", JSON.stringify(result.recordset[0], null, 2));
                // Ensure mapDbUserToPortalUser is accessible, if it's not exported or part of the class, you might need to define it locally or make it accessible.
                // For now, assuming mapDbUserToPortalUser is accessible in this scope.
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger.warn(`No user found with EntraID: ${entraId}`);
            return null;
        } catch (error) {
            logger.error(`Error fetching user by EntraID ${entraId}:`, error);
            // It's better to throw the error to let the caller (Azure function) handle the HTTP response code
            throw new Error(`Database error while fetching user by EntraID ${entraId}.`);
        }
    }

    /**
     * Gets a portal user by their Entra ID and Tenant ID (multi-tenant support).
     * @param entraId - The Entra ID of the user.
     * @param tenantId - The Tenant ID of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByEntraIdAndTenant(entraId: string, tenantId: string): Promise<PortalUser | null> {
        logger.info(`getPortalUserByEntraIdAndTenant called for Entra ID: ${entraId}, Tenant ID: ${tenantId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.EntraID = @EntraID AND pu.TenantID = @TenantID;
        `;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId },
            { name: 'TenantID', type: sql.NVarChar, value: tenantId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger.debug("Raw DB result for user by EntraID and TenantID:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger.warn(`No user found with EntraID: ${entraId} and TenantID: ${tenantId}`);
            return null;
        } catch (error) {
            logger.error(`Error fetching user by EntraID ${entraId} and TenantID ${tenantId}:`, error);
            throw new Error(`Database error while fetching user by EntraID ${entraId} and TenantID ${tenantId}.`);
        }
    }

    /**
     * Gets a portal user by their internal UserID.
     * @param userId - The internal UserID of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByUserId(userId: number): Promise<PortalUser | null> {
        logger.info(`getPortalUserByUserId called for UserID: ${userId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.UserID = @UserID;
        `;
        const params: QueryParameter[] = [
            { name: 'UserID', type: sql.Int, value: userId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger.debug("Raw DB result for user by UserID:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger.warn(`No user found with UserID: ${userId}`);
            return null;
        } catch (error) {
            logger.error(`Error fetching user by UserID ${userId}:`, error);
            throw new Error(`Database error while fetching user by UserID ${userId}.`);
        }
    }

    /**
     * Gets the internal UserID for a user by their Entra ID.
     * @param entraId - The Entra ID of the user.
     * @returns The internal UserID or null if not found.
     */
    async getUserIdByEntraId(entraId: string): Promise<number | null> {
        logger.info(`getUserIdByEntraId called for Entra ID: ${entraId}`);
        const query = `SELECT UserID FROM Users WHERE EntraID = @EntraID AND IsActive = 1;`;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.recordset && result.recordset.length > 0) {
                return result.recordset[0].UserID;
            }
            logger.warn(`No active user found with EntraID: ${entraId}`);
            return null;
        } catch (error) {
            logger.error(`Error fetching UserID by EntraID ${entraId}:`, error);
            throw new Error(`Database error while fetching UserID by EntraID ${entraId}.`);
        }
    }

    /**
     * Deactivates a portal user by their Entra ID.
     * @param entraId - The Entra ID of the user to deactivate.
     * @param modifiedByUserId - The UserID of the user performing the operation.
     * @returns True if the user was successfully deactivated, false otherwise.
     */
    async deactivatePortalUserByEntraId(entraId: string, modifiedByUserId: number): Promise<boolean> {
        logger.info(`deactivatePortalUserByEntraId called for Entra ID: ${entraId} by UserID: ${modifiedByUserId}`);
        const query = `
            UPDATE Users 
            SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() 
            WHERE EntraID = @EntraID AND IsActive = 1;
        `;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId },
            { name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.rowsAffected && result.rowsAffected[0] > 0) {
                logger.info(`Successfully deactivated user with EntraID: ${entraId}`);
                return true;
            }
            logger.warn(`No active user found to deactivate with EntraID: ${entraId}`);
            return false;
        } catch (error) {
            logger.error(`Error deactivating user by EntraID ${entraId}:`, error);
            throw new Error(`Database error while deactivating user by EntraID ${entraId}.`);
        }
    }

    /**
     * Gets a portal user by email address.
     * Used as fallback when EntraID lookup fails due to data inconsistency.
     * @param email - The email address of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByEmail(email: string): Promise<PortalUser | null> {
        logger.info(`getPortalUserByEmail called for email: ${email}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.Email = @Email AND pu.IsActive = 1;
        `;
        const params: QueryParameter[] = [
            { name: 'Email', type: sql.NVarChar, value: email }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger.debug("Raw DB result for user by email:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger.warn(`No user found with email: ${email}`);
            return null;
        } catch (error) {
            logger.error(`Error fetching user by email ${email}:`, error);
            throw new Error(`Database error while fetching user by email ${email}.`);
        }
    }

    /**
     * Updates a user's EntraID to match the actual Entra Object ID.
     * This is used when a user is found by email but has an incorrect EntraID stored.
     * @param userId - The internal UserID of the user to update.
     * @param correctEntraId - The correct Entra Object ID from authentication.
     * @returns True if the update was successful, false otherwise.
     */
    async updateUserEntraId(userId: number, correctEntraId: string): Promise<boolean> {
        logger.info(`updateUserEntraId: Updating UserID ${userId} with correct EntraID: ${correctEntraId}`);
        const query = `
            UPDATE Users 
            SET EntraID = @EntraID, ModifiedDate = GETUTCDATE() 
            WHERE UserID = @UserID AND IsActive = 1;
        `;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: correctEntraId },
            { name: 'UserID', type: sql.Int, value: userId }
        ];

        try {
            const result = await executeQuery(query, params);
            if (result.rowsAffected && result.rowsAffected[0] > 0) {
                logger.info(`Successfully updated EntraID for UserID: ${userId}`);
                return true;
            }
            logger.warn(`No active user found to update with UserID: ${userId}`);
            return false;
        } catch (error) {
            logger.error(`Error updating EntraID for UserID ${userId}:`, error);
            throw new Error(`Database error while updating EntraID for UserID ${userId}.`);
        }
    }

    /**
     * Updates a user's EntraID and TenantID for multi-tenant support.
     * Used when migrating legacy users to multi-tenant identification.
     * @param userId - The internal UserID from the Users table.
     * @param entraId - The new EntraID to set.
     * @param tenantId - The new TenantID to set.
     * @returns Promise that resolves when the update is complete.
     */
    async updateUserEntraIdAndTenant(userId: number, entraId: string, tenantId: string): Promise<void> {
        logger.info(`updateUserEntraIdAndTenant called for UserID: ${userId}, EntraID: ${entraId}, TenantID: ${tenantId}`);
        const query = `
            UPDATE Users 
            SET EntraID = @EntraID, TenantID = @TenantID, ModifiedDate = GETUTCDATE()
            WHERE UserID = @UserID;
        `;
        const params: QueryParameter[] = [
            { name: 'UserID', type: sql.Int, value: userId },
            { name: 'EntraID', type: sql.NVarChar, value: entraId },
            { name: 'TenantID', type: sql.NVarChar, value: tenantId }
        ];

        try {
            await executeQuery(query, params);
            logger.info(`Successfully updated user ${userId} with EntraID ${entraId} and TenantID ${tenantId}`);
        } catch (error) {
            logger.error(`Error updating user ${userId} with EntraID ${entraId} and TenantID ${tenantId}:`, error);
            throw new Error(`Database error while updating user ${userId} with EntraID and TenantID.`);
        }
    }

    // ======= PHASE 2: MAPPING TABLE METHODS =======

    /**
     * Upserts a user's EntraID mapping when they login
     * This is the core of Phase 2 - automatic EntraID capture
     */
    async upsertUserEntraIDMapping(
        email: string, 
        entraId: string, 
        tenantId: string, 
        displayName?: string, 
        companyName?: string,
        captureSource: string = 'AutoLogin'
    ): Promise<void> {
        logger.info(`Upserting EntraID mapping for ${email} with EntraID ${entraId}`);
        
        const query = `EXEC UpsertUserEntraIDMapping @Email, @EntraID, @TenantID, @DisplayName, @CompanyName, @CaptureSource`;
        const parameters: QueryParameter[] = [
            { name: 'Email', type: sql.NVarChar, value: email },
            { name: 'EntraID', type: sql.NVarChar, value: entraId },
            { name: 'TenantID', type: sql.NVarChar, value: tenantId },
            { name: 'DisplayName', type: sql.NVarChar, value: displayName || null },
            { name: 'CompanyName', type: sql.NVarChar, value: companyName || null },
            { name: 'CaptureSource', type: sql.NVarChar, value: captureSource }
        ];

        await executeQuery(query, parameters);
        logger.info(`Successfully upserted EntraID mapping for ${email}`);
    }

    /**
     * Gets user data using the new mapping table view
     * This provides the most complete user information with automatic mapping
     */
    async getPortalUserWithMapping(email: string): Promise<PortalUser | null> {
        logger.info(`Getting user with mapping for email: ${email}`);
        
        const query = `
            SELECT 
                u.UserID,
                COALESCE(m.MappedEntraID, u.EntraID) as EntraID,
                COALESCE(m.MappedTenantID, u.TenantID) as TenantID,
                u.Username,
                u.Email,
                u.FirstName,
                u.LastName,
                u.CompanyID,
                c.CompanyName,
                u.IsActive,
                u.LastLoginDate,
                STRING_AGG(r.RoleName, ',') as Roles,
                m.EntraIDStatus,
                m.LastSeen
            FROM vw_UsersWithEntraIDMappings u
            LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            LEFT JOIN UserEntraIDMappings m ON u.Email = m.Email
            WHERE u.Email = @email AND u.IsActive = 1
            GROUP BY 
                u.UserID, u.EntraID, u.TenantID, u.Username, u.Email, u.FirstName, u.LastName, 
                u.CompanyID, c.CompanyName, u.IsActive, u.LastLoginDate,
                m.MappedEntraID, m.MappedTenantID, m.EntraIDStatus, m.LastSeen
        `;
        
        const parameters: QueryParameter[] = [
            { name: 'email', type: sql.NVarChar, value: email }
        ];

        try {
            const result = await executeQuery(query, parameters);
            
            if (result.recordset && result.recordset.length > 0) {
                const dbUser = result.recordset[0];
                const user = mapDbUserToPortalUser(dbUser);
                logger.info(`Found user with mapping: ${email} (Status: ${dbUser.EntraIDStatus})`);
                return user;
            }
            
            logger.info(`No user found with mapping for email: ${email}`);
            return null;
        } catch (error) {
            logger.error(`Error getting user with mapping for ${email}:`, error);
            throw error;
        }
    }

    /**
     * Gets user by EntraID and TenantID using the mapping table system
     * This is the preferred method for production authentication
     */
    async getPortalUserByEntraIdAndTenantWithMapping(entraId: string, tenantId: string): Promise<PortalUser | null> {
        logger.info(`Getting user by EntraID ${entraId} and TenantID ${tenantId} with mapping`);
        
        // First try direct mapping table lookup
        const mappingQuery = `
            SELECT Email FROM UserEntraIDMappings 
            WHERE EntraID = @entraId AND TenantID = @tenantId AND IsActive = 1
        `;
        const mappingParams: QueryParameter[] = [
            { name: 'entraId', type: sql.NVarChar, value: entraId },
            { name: 'tenantId', type: sql.NVarChar, value: tenantId }
        ];

        try {
            const mappingResult = await executeQuery(mappingQuery, mappingParams);
            
            if (mappingResult.recordset && mappingResult.recordset.length > 0) {
                const email = mappingResult.recordset[0].Email;
                logger.info(`Found mapping for EntraID ${entraId}: ${email}`);
                return await this.getPortalUserWithMapping(email);
            }
            
            // Fallback to original method if no mapping found
            logger.info(`No mapping found for EntraID ${entraId}, falling back to direct lookup`);
            return await this.getPortalUserByEntraIdAndTenant(entraId, tenantId);
        } catch (error) {
            logger.error(`Error getting user by EntraID and Tenant with mapping:`, error);
            throw error;
        }
    }

    /**
     * Captures and stores EntraID mapping automatically during login
     * This is called every time a user successfully authenticates
     */
    async captureEntraIDOnLogin(
        email: string,
        entraId: string,
        tenantId: string,
        displayName?: string,
        companyName?: string
    ): Promise<PortalUser | null> {
        logger.info(`Capturing EntraID on login for ${email}`);
        
        try {
            // 1. Upsert the mapping data
            await this.upsertUserEntraIDMapping(email, entraId, tenantId, displayName, companyName, 'AutoLogin');
            
            // 2. Update the main Users table if we have a user record
            const existingUser = await this.getPortalUserByEmail(email);
            if (existingUser && existingUser.internalId) {
                // Update Users table with captured EntraID and TenantID
                await this.updateUserEntraIdAndTenant(existingUser.internalId, entraId, tenantId);
                logger.info(`Updated Users table for ${email} with captured EntraID`);
            }
            
            // 3. Return the complete user data with mapping
            return await this.getPortalUserWithMapping(email);
        } catch (error) {
            logger.error(`Error capturing EntraID on login for ${email}:`, error);
            throw error;
        }
    }

    /**
     * Gets users with incomplete mappings for administrative review
     * Useful for identifying users who need manual attention
     */
    async getUsersWithIncompleteMapping(): Promise<any[]> {
        const query = `
            SELECT 
                u.Email,
                u.FirstName,
                u.LastName,
                c.CompanyName,
                u.EntraID as CurrentEntraID,
                m.EntraID as MappedEntraID,
                m.EntraIDStatus,
                m.LastSeen,
                CASE 
                    WHEN m.EntraID IS NULL THEN 'No Mapping'
                    WHEN u.EntraID LIKE '%placeholder%' OR u.EntraID LIKE '%simulated%' THEN 'Placeholder'
                    WHEN u.EntraID != m.EntraID THEN 'Mismatch'
                    ELSE 'Complete'
                END as MappingStatus
            FROM Users u
            LEFT JOIN UserEntraIDMappings m ON u.Email = m.Email
            LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE u.IsActive = 1
            AND (
                m.EntraID IS NULL 
                OR u.EntraID LIKE '%placeholder%' 
                OR u.EntraID LIKE '%simulated%'
                OR u.EntraID LIKE '00000000-%'
                OR u.EntraID != m.EntraID
            )
            ORDER BY c.CompanyName, u.LastName, u.FirstName
        `;
        
        try {
            const result = await executeQuery(query, []);
            logger.info(`Found ${result.recordset.length} users with incomplete mappings`);
            return result.recordset;
        } catch (error) {
            logger.error('Error getting users with incomplete mapping:', error);
            throw error;
        }
    }
}

export const userManagementService = new UserManagementService();

