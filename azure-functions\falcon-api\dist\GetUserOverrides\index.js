"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserOverrides = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = __importStar(require("mssql"));
async function getUserOverrides(request, context) {
    const entraId = request.params.entraId;
    context.log(`Http function processed request for GetUserOverrides: ${entraId}`);
    if (!entraId) {
        return {
            status: 400,
            jsonBody: { message: "EntraID parameter is required." }
        };
    }
    try {
        // 1. Get UserID from EntraID
        const userQuery = `SELECT UserID, IsActive FROM Users WHERE EntraID = @EntraID`;
        const userParams = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const userResult = await (0, db_1.executeQuery)(userQuery, userParams);
        if (!userResult.recordset || userResult.recordset.length === 0) {
            logger_1.logger.warn(`GetUserOverrides: User not found for EntraID: ${entraId}`);
            return {
                status: 404,
                jsonBody: {
                    message: "User not found in the portal database."
                }
            };
        }
        const userId = userResult.recordset[0].UserID;
        const isActive = userResult.recordset[0].IsActive;
        // 2. Get user's active roles
        const rolesQuery = `
            SELECT r.RoleID, r.RoleName
            FROM Roles r
            JOIN UserRoles ur ON r.RoleID = ur.RoleID 
            WHERE ur.UserID = @UserID AND ur.IsActive = 1 AND r.IsActive = 1
        `;
        const rolesParams = [
            { name: 'UserID', type: sql.Int, value: userId }
        ];
        const rolesResult = await (0, db_1.executeQuery)(rolesQuery, rolesParams);
        const assignedRoles = rolesResult.recordset.map(row => ({ id: row.RoleID, name: row.RoleName }));
        logger_1.logger.info(`Retrieved status and roles for EntraID ${entraId} (UserID: ${userId})`);
        return {
            status: 200,
            jsonBody: {
                isActive: isActive,
                roles: assignedRoles
            }
        };
    }
    catch (error) {
        context.error(`Error fetching user overrides: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: "Error fetching user overrides.",
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}
exports.getUserOverrides = getUserOverrides;
functions_1.app.http('GetUserOverrides', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'users/overrides/{entraId}',
    handler: getUserOverrides
});
//# sourceMappingURL=index.js.map