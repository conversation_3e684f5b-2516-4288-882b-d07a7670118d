"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testUpdateDate = void 0;
const functions_1 = require("@azure/functions");
async function testUpdateDate(request, context) {
    context.log('TestUpdateDate function invoked.');
    return {
        status: 200,
        jsonBody: {
            message: 'Test endpoint is working!',
            timestamp: new Date().toISOString()
        }
    };
}
exports.testUpdateDate = testUpdateDate;
functions_1.app.http('TestUpdateDate', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'test/update-date',
    handler: testUpdateDate
});
//# sourceMappingURL=TestUpdateDate.js.map