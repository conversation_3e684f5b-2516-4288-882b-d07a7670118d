{"root": ["../createrole/index.ts", "../getroles/index.ts", "../apps/portal-shell/vite.config.ts", "../apps/portal-shell/src/app.tsx", "../apps/portal-shell/src/authconfig.ts", "../apps/portal-shell/src/main.tsx", "../apps/portal-shell/src/vite-env.d.ts", "../apps/portal-shell/src/components/addusermodal.tsx", "../apps/portal-shell/src/components/dashboardsection.tsx", "../apps/portal-shell/src/components/editusermodal.tsx", "../apps/portal-shell/src/components/header.tsx", "../apps/portal-shell/src/components/layout.tsx", "../apps/portal-shell/src/components/maincontent.tsx", "../apps/portal-shell/src/components/sidebar.tsx", "../apps/portal-shell/src/components/common/modal.tsx", "../apps/portal-shell/src/components/forms/roleform.tsx", "../apps/portal-shell/src/components/layout/maincontent.tsx", "../apps/portal-shell/src/components/layout/shelllayout.tsx", "../apps/portal-shell/src/components/layout/sidebar.tsx", "../apps/portal-shell/src/components/layout/topbar.tsx", "../apps/portal-shell/src/components/modals/quicklinkcustomizemodal.tsx", "../apps/portal-shell/src/data/healthtips.ts", "../apps/portal-shell/src/pages/actionspage.tsx", "../apps/portal-shell/src/pages/announcementspage.tsx", "../apps/portal-shell/src/pages/comingsoonpage.tsx", "../apps/portal-shell/src/pages/documentspage.tsx", "../apps/portal-shell/src/pages/eventspage.tsx", "../apps/portal-shell/src/pages/itpage.tsx", "../apps/portal-shell/src/pages/usereditpage.tsx", "../apps/portal-shell/src/pages/usermanagementpage.tsx", "../apps/portal-shell/src/pages/adminhub/rolemanagementpage.tsx", "../apps/portal-shell/src/pages/adminhub/useraddpage.tsx", "../apps/portal-shell/src/pages/adminhub/usereditpage.tsx", "../apps/portal-shell/src/pages/adminhub/usermanagementpage.tsx", "../apps/portal-shell/src/services/adminapi.ts", "../apps/portal-shell/src/services/dashboardapi.ts", "../apps/portal-shell/src/services/ithubapi.ts", "../azure-functions/falcon-api/jest.setup.ts", "../azure-functions/falcon-api/createrole/index.ts", "../azure-functions/falcon-api/getroles/index.ts", "../azure-functions/falcon-api/getuseroverrides/index.ts", "../azure-functions/falcon-api/updaterole/index.ts", "../azure-functions/falcon-api/shared/db.ts", "../azure-functions/falcon-api/shared/interfaces.ts", "../azure-functions/falcon-api/src/basic.test.ts", "../azure-functions/falcon-api/src/functions.ts", "../azure-functions/falcon-api/src/index.ts", "../azure-functions/falcon-api/src/main.ts", "../azure-functions/falcon-api/src/adduser/index.ts", "../azure-functions/falcon-api/src/assignrole/index.ts", "../azure-functions/falcon-api/src/createrole/index.ts", "../azure-functions/falcon-api/src/createuser/index.ts", "../azure-functions/falcon-api/src/getroles/index.ts", "../azure-functions/falcon-api/src/getuser/index.ts", "../azure-functions/falcon-api/src/getuseroverrides/index.ts", "../azure-functions/falcon-api/src/getusers/index.ts", "../azure-functions/falcon-api/src/removerole/index.ts", "../azure-functions/falcon-api/src/testfunc/index.ts", "../azure-functions/falcon-api/src/updateportaluser/index.ts", "../azure-functions/falcon-api/src/updaterole/index.ts", "../azure-functions/falcon-api/src/updateuser/index.ts", "../azure-functions/falcon-api/src/functions/createportaluser.ts", "../azure-functions/falcon-api/src/functions/createrole.ts", "../azure-functions/falcon-api/src/functions/deleteportaluser.ts", "../azure-functions/falcon-api/src/functions/deleterole.test.ts", "../azure-functions/falcon-api/src/functions/deleterole.ts", "../azure-functions/falcon-api/src/functions/getportaluserbyid.ts", "../azure-functions/falcon-api/src/functions/getportalusers.test.ts", "../azure-functions/falcon-api/src/functions/getportalusers.ts", "../azure-functions/falcon-api/src/functions/getroles.ts", "../azure-functions/falcon-api/src/functions/getuseroverrides.ts", "../azure-functions/falcon-api/src/functions/syncuserfromentra.ts", "../azure-functions/falcon-api/src/functions/updateportaluser.ts", "../azure-functions/falcon-api/src/functions/updaterole.ts", "../azure-functions/falcon-api/src/shared/authutils.test.ts", "../azure-functions/falcon-api/src/shared/authutils.ts", "../azure-functions/falcon-api/src/shared/db.ts", "../azure-functions/falcon-api/src/shared/interfaces.ts", "../azure-functions/falcon-api/src/shared/validationschemas.test.ts", "../azure-functions/falcon-api/src/shared/validationschemas.ts", "../azure-functions/falcon-api/src/shared/__mocks__/db.ts", "../azure-functions/falcon-api/src/shared/services/graphservice.test.ts", "../azure-functions/falcon-api/src/shared/services/graphservice.ts", "../azure-functions/falcon-api/src/shared/services/usermanagementservice.test.ts", "../azure-functions/falcon-api/src/shared/services/usermanagementservice.ts", "../azure-functions/falcon-api/src/shared/services/__mocks__/usermanagementservice.ts", "../azure-functions/falcon-api/src/shared/utils/logger.ts", "../azure-functions/falcon-api/src/shared/utils/__mocks__/logger.ts", "../portal-shell/src/pages/admin/usermanagementpage.tsx", "../portal-shell/src/services/adminapi.ts", "../temp-functions/falcon-api-v2/src/functions/samplehttpfunction.ts"], "errors": true, "version": "5.8.3"}