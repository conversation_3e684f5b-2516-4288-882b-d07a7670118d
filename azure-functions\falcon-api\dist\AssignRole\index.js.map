{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/AssignRole/index.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,oFAA4E;AAC5E,mDAAgD;AAChD,mDAAkG,CAAC,oBAAoB;AACvH,mEAA4G;AAE5G,0BAA0B;AAC1B,mHAAmH;AACnH,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,yCAAyC;AAEzE,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,OAAO,CAAC,GAAG,CAAC,uDAAuD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;IACnF,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAE5C,0CAA0C;IAC1C,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;KAC5F;IAED,8EAA8E;IAC9E,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE;QAC7C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,6CAA6C,aAAa,IAAI,CAAC,CAAC;QAClI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yDAAyD,EAAE,EAAE,CAAC;KAC7G;IAED,MAAM,mBAAmB,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7E,IAAI,CAAC,mBAAmB,EAAE;QACtB,kGAAkG;QAClG,eAAM,CAAC,KAAK,CAAC,6EAA6E,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,6EAA6E,EAAE,EAAE,CAAC;KAChI;IACD,gFAAgF;IAChF,6EAA6E;IAC7E,6EAA6E;IAC7E,eAAM,CAAC,IAAI,CAAC,iCAAiC,mBAAmB,EAAE,CAAC,CAAC;IACpE,oBAAoB;IAEpB,4BAA4B;IAC5B,4BAA4B;IAC5B,MAAM,WAAW,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,yBAAyB;IAChF,IAAI,eAAe,GAAG,IAAA,mCAAe,EAAC,0CAAsB,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;IACxG,IAAI,eAAe;QAAE,OAAO,eAAe,CAAC;IAC5C,wCAAwC;IACxC,MAAM,oBAAoB,GAAG,0CAAsB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8BAA8B;IACtG,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,mBAAmB;IAE/D,wBAAwB;IACxB,IAAI,UAAe,CAAC;IACpB,IAAI;QACA,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;KACrC;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;KAClF;IAED,eAAe,GAAG,IAAA,mCAAe,EAAC,wCAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;IAC7F,IAAI,eAAe;QAAE,OAAO,eAAe,CAAC;IAE5C,wCAAwC;IACxC,MAAM,aAAa,GAAG,wCAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC7D,MAAM,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,mBAAmB;IACrD,0BAA0B;IAE1B,IAAI;QACA,uDAAuD;QACvD,MAAM,OAAO,GAAG,MAAM,IAAA,wCAAgB,EAAC,MAAM,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAE5E,IAAI,OAAO,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,0CAA0C,MAAM,aAAa,MAAM,cAAc,mBAAmB,GAAG,CAAC,CAAC;YACrH,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE;aACvD,CAAC;SACL;aAAM;YACH,sDAAsD;YACtD,6CAA6C;YAC7C,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;aAClD,CAAC;SACL;KAEJ;IAAC,OAAO,KAAK,EAAE;QACZ,sEAAsE;QACtE,eAAM,CAAC,KAAK,CAAC,oDAAoD,MAAM,UAAU,MAAM,cAAc,mBAAmB,GAAG,EAAE,KAAK,CAAC,CAAC;QACpI,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACN,OAAO,EAAE,wDAAwD;gBACjE,KAAK,EAAE,YAAY;aACtB;SACJ,CAAC;KACL;AACL,CAAC;AAxFD,gCAwFC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,sBAAsB;IAC7B,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}