{"version": 3, "file": "CreateRole.js", "sourceRoot": "", "sources": ["../../src/functions/CreateRole.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,2CAA6B;AAStB,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,OAAO,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;IAExE,IAAI,IAA2B,CAAC;IAChC,IAAI;QACA,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAC;KACxD;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;QAChD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;SACzD,CAAC;KACL;IAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;QAC1C,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE;SACpD,CAAC;KACL;IAED,IAAI;QACA,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC;QACrD,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,cAAc;QAEnC,qDAAqD;QACrD,MAAM,UAAU,GAAG,8EAA8E,CAAC;QAClG,wBAAwB;QACxB,MAAM,WAAW,GAAqB;YAClC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;SAC5D,CAAC;QACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAEhE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;YACpC,eAAM,CAAC,IAAI,CAAC,uCAAuC,QAAQ,mBAAmB,CAAC,CAAC;YAChF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,QAAQ,mBAAmB,EAAE;aACxE,CAAC;SACL;QAED,qBAAqB;QACrB,MAAM,KAAK,GAAG;;;;SAIb,CAAC;QACF,wBAAwB;QACxB,MAAM,YAAY,GAAqB;YACnC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;YAC9D,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;YACpE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;SAC9D,CAAC;QACF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,eAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;YACvE,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,kCAAkC;iBAC5C;aACJ,CAAC;SACL;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,OAAO,GAAmB;YAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,WAAW,EAAE,SAAS,CAAC,eAAe;SACzC,CAAC;QAEF,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,OAAO;SACpB,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,sBAAsB;gBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAChE;SACJ,CAAC;KACL;AACL,CAAC;AAzFD,gCAyFC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,cAAc;IACrB,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}