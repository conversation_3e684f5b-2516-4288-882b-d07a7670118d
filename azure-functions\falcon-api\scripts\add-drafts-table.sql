-- Add ChangeRequestDrafts table if it doesn't exist
-- This script can be run independently

USE falcon_portal_dev;
GO

-- Create ChangeRequestDrafts table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestDrafts')
BEGIN
    CREATE TABLE ChangeRequestDrafts (
        DraftID NVARCHAR(50) PRIMARY KEY,
        Title NVARCHAR(255) NOT NULL,
        Description NVARCHAR(MAX),
        TypeID INT NOT NULL,
        Priority NVARCHAR(20) NOT NULL,
        BusinessJustification NVARCHAR(MAX),
        ExpectedBenefit NVARCHAR(MAX),
        RequestedCompletionDate DATE,
        RichContent NVARCHAR(MAX), -- JSON string containing rich content blocks
        RequestedBy INT NOT NULL,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    -- Indexes for better performance
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_RequestedBy 
        ON ChangeRequestDrafts(RequestedBy);
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_LastModified 
        ON ChangeRequestDrafts(LastModified DESC);
    
    PRINT 'Created table: ChangeRequestDrafts';
END
ELSE
BEGIN
    PRINT 'Table ChangeRequestDrafts already exists';
END
GO

-- Verify the table was created
SELECT COUNT(*) as TableCount FROM sys.tables WHERE name='ChangeRequestDrafts';
GO 