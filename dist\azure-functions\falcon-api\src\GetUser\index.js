"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUser = getUser;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const sql = require("mssql"); // Add mssql import
const db_1 = require("../shared/db"); // Add QueryParameter import and use correct executeQuery import
const validationSchemas_1 = require("../shared/validationSchemas");
const authUtils_1 = require("../shared/authUtils"); // Only need getClientPrincipal
function getUser(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function GetUser processed request for url "${request.url}"`);
        logger_1.logger.info('GetUser function invoked.');
        // --- Authentication Check --- 
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            // Allow if provider allows anonymous and header is missing, 
            // but return 401 if header is present but invalid or not populated by provider
            // For simplicity, let's require authentication for this specific user endpoint
            if (request.headers.get('x-ms-client-principal')) {
                logger_1.logger.warn('GetUser: Invalid or missing client principal header despite presence.');
                return { status: 401, jsonBody: { message: "Unauthorized. Invalid client principal." } };
            }
            else {
                // If header truly missing, depends on provider config. Assume protected.
                logger_1.logger.warn('GetUser: Client principal header missing.');
                return { status: 401, jsonBody: { message: "Unauthorized." } };
            }
        }
        // No specific role check needed, any authenticated user can view user details?
        // --- End Auth --- 
        // --- Input Validation --- 
        const routeParams = { entraId: request.params.entraId }; // Extract for validation
        // Use the actual entraIdRouteParamSchema
        const validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.entraIdRouteParamSchema, routeParams, context, "route parameters");
        if (validationError)
            return validationError;
        // Validation passed, use validated data
        const validatedRouteParams = validationSchemas_1.entraIdRouteParamSchema.parse(routeParams); // Use parse to get typed data
        const entraId = validatedRouteParams.entraId; // Already a string
        // Manual validation REMOVED
        // --- End Validation --- 
        try {
            // Query to get user details, company name, department name, and aggregated role names
            const query = `
            SELECT 
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName,
                d.DepartmentName,
                STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) AS RoleNames
            FROM 
                Users u
            JOIN 
                Companies c ON u.CompanyID = c.CompanyID
            LEFT JOIN
                Departments d ON u.DepartmentID = d.DepartmentID AND d.IsActive = 1 -- Optional join for Department
            LEFT JOIN 
                UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN 
                Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE 
                u.EntraID = @EntraID
            GROUP BY
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName, d.DepartmentName
        `;
            logger_1.logger.debug("Executing GetUser query for Entra ID:", entraId);
            const userParams = [
                { name: 'EntraID', type: sql.NVarChar, value: entraId }
            ];
            const result = yield (0, db_1.executeQuery)(query, userParams);
            if (result.recordset.length === 0) {
                logger_1.logger.warn(`GetUser: User not found for Entra ID: ${entraId}`);
                return {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { message: `User with EntraID ${entraId} not found.` }
                };
            }
            const dbUser = result.recordset[0];
            // Map the DB result to the PortalUser interface
            const portalUser = {
                id: dbUser.UserID,
                entraId: dbUser.EntraID,
                name: `${dbUser.FirstName} ${dbUser.LastName}`,
                email: dbUser.Email,
                company: dbUser.CompanyName,
                companyId: dbUser.CompanyID,
                department: dbUser.DepartmentName || undefined,
                roles: dbUser.RoleNames ? dbUser.RoleNames.split(',') : [],
                status: dbUser.IsActive ? 'Active' : 'Inactive'
            };
            logger_1.logger.info(`Successfully retrieved user details for EntraID ${entraId} (UserID: ${portalUser.id})`);
            return {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: portalUser
            };
        }
        catch (error) {
            logger_1.logger.error(`Error in GetUser function for EntraID ${entraId}:`, error);
            const errorMessage = (error instanceof Error) ? error.message : String(error);
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "Error retrieving user details.",
                    error: errorMessage
                }
            };
        }
    });
}
functions_1.app.http('GetUser', {
    methods: ['GET'],
    authLevel: 'anonymous', // Handled by provider + code checks
    route: 'portal-users/{entraId}', // Match function.json
    handler: getUser
});
//# sourceMappingURL=index.js.map