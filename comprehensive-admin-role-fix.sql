-- ====================================================================
-- COMPREHENSIVE ADMIN ROLE SECURITY FIX
-- This script identifies and removes unauthorized Administrator role assignments
-- ====================================================================

PRINT '=== FALCON PORTAL - ADMIN ROLE SECURITY AUDIT & FIX ===';
PRINT 'Started at: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';

-- ====================================================================
-- STEP 1: CURRENT STATE ANALYSIS
-- ====================================================================

PRINT '1. ANALYZING CURRENT ADMIN ROLE ASSIGNMENTS...';
PRINT '';

-- Check all users with Administrator role
SELECT 
    'CURRENT ADMIN USERS' as [Section],
    u.UserID,
    u.Email,
    u.FirstName + ' ' + u.LastName as FullName,
    u.CompanyID,
    c.CompanyName,
    ur.UserRoleID,
    ur.CreatedDate as RoleAssignedDate,
    ur.CreatedBy as AssignedBy,
    ur.IsActive
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID
JOIN Roles r ON ur.RoleID = r.RoleID
LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE r.RoleName = 'Administrator'
ORDER BY ur.IsActive DESC, u.Email;

PRINT '';

-- ====================================================================
-- STEP 2: IDENTIFY UNAUTHORIZED ADMIN USERS
-- ====================================================================

PRINT '2. IDENTIFYING UNAUTHORIZED ADMIN USERS...';
PRINT '';

-- Show users who have admin but shouldn't
SELECT 
    'UNAUTHORIZED ADMINS TO REMOVE' as [Section],
    u.UserID,
    u.Email,
    u.FirstName + ' ' + u.LastName as FullName,
    c.CompanyName,
    ur.UserRoleID,
    ur.CreatedDate as RoleAssignedDate,
    'WILL BE REMOVED' as Action
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
JOIN Roles r ON ur.RoleID = r.RoleID
LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE r.RoleName = 'Administrator'
AND u.Email NOT IN (
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY u.Email;

-- Check if grievance user specifically has admin role
IF EXISTS (
    SELECT 1 FROM Users u
    JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
    JOIN Roles r ON ur.RoleID = r.RoleID
    WHERE r.RoleName = 'Administrator'
    AND u.Email = '<EMAIL>'
)
BEGIN
    PRINT '⚠️  SECURITY ALERT: <EMAIL> has Administrator role - WILL BE REMOVED';
END
ELSE
BEGIN
    PRINT '✅ SECURITY OK: <EMAIL> does not have Administrator role in database';
END

PRINT '';

-- ====================================================================
-- STEP 3: BACKUP CURRENT STATE
-- ====================================================================

PRINT '3. CREATING BACKUP OF CURRENT ADMIN ROLE ASSIGNMENTS...';

-- Create backup table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UserRoles_AdminBackup]') AND type in (N'U'))
BEGIN
    CREATE TABLE UserRoles_AdminBackup (
        BackupDate DATETIME DEFAULT GETDATE(),
        UserRoleID INT,
        UserID INT,
        RoleID INT,
        UserEmail NVARCHAR(255),
        RoleName NVARCHAR(100),
        IsActive BIT,
        CreatedBy INT,
        CreatedDate DATETIME,
        ModifiedBy INT,
        ModifiedDate DATETIME
    );
    PRINT '✅ Created backup table: UserRoles_AdminBackup';
END

-- Insert current admin role assignments into backup
INSERT INTO UserRoles_AdminBackup (
    UserRoleID, UserID, RoleID, UserEmail, RoleName, IsActive, 
    CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
)
SELECT 
    ur.UserRoleID, ur.UserID, ur.RoleID, u.Email, r.RoleName, ur.IsActive,
    ur.CreatedBy, ur.CreatedDate, ur.ModifiedBy, ur.ModifiedDate
FROM UserRoles ur
JOIN Users u ON ur.UserID = u.UserID
JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator';

PRINT '✅ Backup completed - ' + CAST(@@ROWCOUNT AS VARCHAR) + ' admin role assignments backed up';
PRINT '';

-- ====================================================================
-- STEP 4: REMOVE UNAUTHORIZED ADMIN ROLES
-- ====================================================================

PRINT '4. REMOVING UNAUTHORIZED ADMINISTRATOR ROLES...';

DECLARE @RemovedCount INT = 0;

-- Remove Administrator role from unauthorized users
UPDATE ur
SET IsActive = 0, 
    ModifiedBy = 1, 
    ModifiedDate = GETUTCDATE()
FROM UserRoles ur
JOIN Users u ON ur.UserID = u.UserID
JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
AND ur.IsActive = 1
AND u.Email NOT IN (
    '<EMAIL>',
    '<EMAIL>'
);

SET @RemovedCount = @@ROWCOUNT;
PRINT '✅ Removed Administrator role from ' + CAST(@RemovedCount AS VARCHAR) + ' unauthorized users';

-- Specifically check grievance user
IF EXISTS (
    SELECT 1 FROM Users u
    JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
    JOIN Roles r ON ur.RoleID = r.RoleID
    WHERE r.RoleName = 'Administrator'
    AND u.Email = '<EMAIL>'
)
BEGIN
    PRINT '⚠️  WARNING: <EMAIL> still has admin role after cleanup!';
END
ELSE
BEGIN
    PRINT '✅ CONFIRMED: <EMAIL> no longer has Administrator role';
END

PRINT '';

-- ====================================================================
-- STEP 5: ENSURE PROPER EMPLOYEE ROLES
-- ====================================================================

PRINT '5. ENSURING ALL USERS HAVE EMPLOYEE ROLE...';

-- Get Employee role ID
DECLARE @EmployeeRoleID INT;
SELECT @EmployeeRoleID = RoleID FROM Roles WHERE RoleName = 'Employee' AND IsActive = 1;

IF @EmployeeRoleID IS NULL
BEGIN
    PRINT '❌ ERROR: Employee role not found in database!';
    RETURN;
END

-- Ensure grievance user has Employee role
IF NOT EXISTS (
    SELECT 1 FROM Users u
    JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
    WHERE u.Email = '<EMAIL>'
    AND ur.RoleID = @EmployeeRoleID
)
BEGIN
    DECLARE @GrievanceUserID INT;
    SELECT @GrievanceUserID = UserID FROM Users WHERE Email = '<EMAIL>';
    
    IF @GrievanceUserID IS NOT NULL
    BEGIN
        INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate)
        VALUES (@GrievanceUserID, @EmployeeRoleID, 1, 1, GETUTCDATE(), GETUTCDATE());
        PRINT '✅ Added Employee <NAME_EMAIL>';
    END
    ELSE
    BEGIN
        PRINT '⚠️  WARNING: <EMAIL> user not found in database';
    END
END
ELSE
BEGIN
    PRINT '✅ CONFIRMED: <EMAIL> already has Employee role';
END

PRINT '';

-- ====================================================================
-- STEP 6: FINAL VERIFICATION
-- ====================================================================

PRINT '6. FINAL VERIFICATION - CURRENT ADMIN USERS AFTER FIX...';
PRINT '';

SELECT 
    'FINAL ADMIN USERS' as [Section],
    u.Email,
    u.FirstName + ' ' + u.LastName as FullName,
    c.CompanyName,
    'Administrator' as RoleName,
    ur.IsActive,
    CASE 
        WHEN u.Email IN ('<EMAIL>', '<EMAIL>') 
        THEN '✅ AUTHORIZED' 
        ELSE '❌ UNAUTHORIZED' 
    END as AuthorizationStatus
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
JOIN Roles r ON ur.RoleID = r.RoleID
LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE r.RoleName = 'Administrator'
ORDER BY u.Email;

-- Check grievance user final state
PRINT '';
PRINT 'GRIEVANCE USER FINAL STATE:';
SELECT 
    u.Email,
    u.FirstName + ' ' + u.LastName as FullName,
    r.RoleName,
    ur.IsActive,
    ur.CreatedDate
FROM Users u
LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
LEFT JOIN Roles r ON ur.RoleID = r.RoleID
WHERE u.Email = '<EMAIL>'
ORDER BY r.RoleName;

-- ====================================================================
-- STEP 7: SUMMARY
-- ====================================================================

PRINT '';
PRINT '=== SECURITY FIX SUMMARY ===';
PRINT 'Unauthorized admin roles removed: ' + CAST(@RemovedCount AS VARCHAR);
PRINT 'Authorized admin users: <EMAIL>, <EMAIL>';
PRINT 'Backup table created: UserRoles_AdminBackup';
PRINT 'Completed at: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';
PRINT '✅ ADMIN ROLE SECURITY FIX COMPLETED';
PRINT 'Users should clear browser cache and re-login to see updated roles.';
PRINT ''; 