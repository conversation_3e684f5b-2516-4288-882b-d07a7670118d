-- Targeted Tenant ID Fix Script
-- Based on observed CompanyID mappings from actual data

-- Step 1: Add TenantID column to Companies table (if it doesn't exist)
BEGIN TRY
    ALTER TABLE Companies ADD TenantID NVARCHAR(50) NULL;
END TRY
BEGIN CATCH
    -- Column might already exist, continue
END CATCH

-- Step 2: Add TenantID column to Users table (if it doesn't exist)  
BEGIN TRY
    ALTER TABLE Users ADD TenantID NVARCHAR(50) NULL;
END TRY
BEGIN CATCH
    -- Column might already exist, continue
END CATCH

-- Step 3: Update Companies table based on observed CompanyID mappings
-- Note: Based on your data, sasmos.com users have CompanyID 1, Avirata users have CompanyID 4

-- First, let's update companies by CompanyID (safer approach)
UPDATE Companies SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyID = 1;  -- SASMOS (observed from @sasmos.com users)
UPDATE Companies SET TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' WHERE CompanyID = 4;  -- Avirata (<NAME_EMAIL>)

-- Update other companies by name (as fallback)
UPDATE Companies SET TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' WHERE CompanyName LIKE '%FE-SIL%' OR CompanyName LIKE '%FESIL%';
UPDATE Companies SET TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' WHERE CompanyName LIKE '%Glode%';
UPDATE Companies SET TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' WHERE CompanyName LIKE '%Hanuka%';
UPDATE Companies SET TenantID = 'PLACEHOLDER_WWH_TENANT_ID' WHERE CompanyName LIKE '%West%Wire%' OR CompanyName LIKE '%WWH%';

-- Step 4: Update Users table based on their CompanyID
UPDATE Users SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyID = 1;  -- SASMOS users
UPDATE Users SET TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' WHERE CompanyID = 4;  -- Avirata users

-- Update other users by CompanyID (if they exist)
UPDATE Users SET TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' WHERE CompanyID IN (SELECT CompanyID FROM Companies WHERE TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1');
UPDATE Users SET TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' WHERE CompanyID IN (SELECT CompanyID FROM Companies WHERE TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0');
UPDATE Users SET TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' WHERE CompanyID IN (SELECT CompanyID FROM Companies WHERE TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775');
UPDATE Users SET TenantID = 'PLACEHOLDER_WWH_TENANT_ID' WHERE CompanyID IN (SELECT CompanyID FROM Companies WHERE TenantID = 'PLACEHOLDER_WWH_TENANT_ID');

-- Step 5: Show results
SELECT 'UPDATED USERS' as ResultType, UserID, Email, CompanyID, TenantID FROM Users ORDER BY Email; 