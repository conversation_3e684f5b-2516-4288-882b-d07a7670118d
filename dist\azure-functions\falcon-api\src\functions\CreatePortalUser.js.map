{"version": 3, "file": "CreatePortalUser.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/CreatePortalUser.ts"], "names": [], "mappings": ";;;;;;;;;;;AAeA,sDAiLC;AAhMD,gDAAyF;AACzF,6BAAwB;AACxB,qCAAuC;AACvC,iCAAwB;AAExB,qDAAqD;AACrD,MAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAChD,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,sCAAsC,CAAC;IACzE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;CACtE,CAAC,CAAC;AAKH,SAAsB,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;;QACxF,OAAO,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QAEzF,2CAA2C;QAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,+FAA+F,CAAC,CAAC;QAClH,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;YAC/F,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,gDAAgD,EAAE,CAAC;QACnF,CAAC;QAED,IAAI,WAAwC,CAAC;QAE7C,kCAAkC;QAClC,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YACrC,WAAW,GAAG,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,sDAAsD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACtG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;gBAC7B,OAAO,CAAC,KAAK,CAAC,oDAAoD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAClG,OAAO;oBACJ,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,OAAO,EAAE,gFAAgF;wBACzF,MAAM,EAAE,KAAK,CAAC,MAAM;qBACvB;iBACJ,CAAC;YACN,CAAC;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAChC,OAAO,CAAC,KAAK,CAAC,8DAA8D,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC5F,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3G,CAAC;iBAAM,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,yDAAyD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACzF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;YACnF,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,oFAAoF;QACpF,iGAAiG;QACjG,MAAM,kBAAkB,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,8BAA8B;QACjI,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,oBAAoB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,uCAAuC;QAC9G,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,4CAA4C;QAC7G,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,gCAAgC;QAC/E,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC,yCAAyC;QACzE,MAAM,sBAAsB,GAAG,CAAC,CAAC,CAAC,mDAAmD;QACrF,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,4BAA4B;QAC9E,OAAO,CAAC,IAAI,CAAC,uEAAuE,kBAAkB,gBAAgB,mBAAmB,iBAAiB,oBAAoB,gBAAgB,mBAAmB,iBAAiB,oBAAoB,mBAAmB,sBAAsB,wBAAwB,CAAC,CAAC;QACzT,gCAAgC;QAEhC,IAAI,WAAW,GAA2B,IAAI,CAAC;QAC/C,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAE7B,kDAAkD;YAClD,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;YACpC,MAAM,SAAS,GAAG,yDAAyD,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC/I,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACnC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;gBAC9B,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,eAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAEtD,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,OAAO,CAAC,KAAK,CAAC,4DAA4D,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,oDAAoD,EAAE,EAAE,CAAC;YACxG,CAAC;YAED,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;gBACnD,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAC7D,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC1E,OAAO,CAAC,KAAK,CAAC,wDAAwD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,uBAAuB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;YACpG,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,iDAAiD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAEvH,+CAA+C;YAC/C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBACnC,KAAK,CAAC,OAAO,EAAE,eAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;iBAC/C,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAE5D,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,IAAI,CAAC,iDAAiD,WAAW,CAAC,KAAK,kBAAkB,CAAC,CAAC;gBACnG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,EAAE,CAAC;YAC1F,CAAC;YAED,oBAAoB;YACpB,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAEnE,2DAA2D;YAC3D,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE;iBAC/C,KAAK,CAAC,SAAS,EAAE,eAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC;iBACjD,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAG,0BAA0B;iBACjF,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,kBAAkB;iBACzE,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAG,iBAAiB;iBACxE,KAAK,CAAC,OAAO,EAAE,eAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;iBAC/C,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,oBAAoB,CAAC;iBACjD,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;iBACpC,KAAK,CAAC,eAAe,EAAE,eAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;iBAC1C,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,kBAAkB;iBACtE,KAAK,CAAC;;;;aAIN,CAAC,CAAC;YAEP,IAAI,gBAAgB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBACnF,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACrE,CAAC;YACD,MAAM,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACvD,OAAO,CAAC,IAAI,CAAC,4DAA4D,SAAS,EAAE,CAAC,CAAC;YAEtF,4CAA4C;YAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,MAAM,WAAW,CAAC,OAAO,EAAE;qBACtB,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,GAAG,EAAE,SAAS,CAAC;qBACnC,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,GAAG,EAAE,MAAM,CAAC;qBAChC,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC;qBACnD,KAAK,CAAC,0FAA0F,CAAC,CAAC;YAC3G,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,0CAA0C,OAAO,CAAC,MAAM,oBAAoB,CAAC,CAAC;YAE3F,qBAAqB;YACrB,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YAElF,+BAA+B;YAC/B,MAAM,YAAY,GAAG;gBACjB,OAAO,EAAE,4BAA4B;gBACrC,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,WAAW,CAAC,MAAM;aAC7B,CAAC;YAEF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,uBAAuB;YACvB,IAAI,WAAW,EAAE,CAAC;gBACd,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;gBACxF,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC;oBACpB,MAAM,UAAU,GAAG,CAAC,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;oBACrG,OAAO,CAAC,KAAK,CAAC,kEAAkE,UAAU,EAAE,CAAC,CAAC;gBAClG,CAAC;YACL,CAAC;YAED,wBAAwB;YACxB,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO,CAAC,KAAK,CAAC,8DAA8D,YAAY,EAAE,CAAC,CAAC;YAE5F,IAAI,KAAK,YAAY,eAAG,CAAC,YAAY,IAAI,KAAK,YAAY,eAAG,CAAC,gBAAgB,EAAE,CAAC;gBAC5E,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBAC5I,OAAO,CAAC,KAAK,CAAC,iEAAiE,YAAY,EAAE,CAAC,CAAC;oBAC/F,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC;wBAC9C,CAAC,CAAC,sCAAsC;wBACxC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC;4BAC7C,CAAC,CAAC,2CAA2C;4BAC7C,CAAC,CAAC,gCAAgC,CAAC;oBACtD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;gBACvF,CAAC;gBACF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+CAA+C,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;YACzH,CAAC;iBAAM,CAAC;gBACJ,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,uDAAuD,EAAE,MAAM,EAAE,YAAY,EAAE;iBACvG,CAAC;YACN,CAAC;QACL,CAAC;IACL,CAAC;CAAA;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE;IAC9B,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,KAAK,EAAE,cAAc;IACrB,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,qBAAqB;CACjC,CAAC,CAAC"}