{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../azure-functions/falcon-api/GetUserOverrides/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAGA,4CA+DC;AAlED,gDAAyF;AACzF,qCAA4C;AAE5C,SAAsB,gBAAgB,CAAC,OAAoB,EAAE,OAA0B;;QACnF,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,yDAAyD,OAAO,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,gCAAgC,EAAE;aAC1D,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,iDAAiD;YACjD,MAAM,SAAS,GAAG;;;;SAIjB,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,IAAA,iBAAY,EAAC,SAAS,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAEvE,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,iCAAiC;gBACjC,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,OAAO,EAAE,wCAAwC;qBACpD;iBACJ,CAAC;YACN,CAAC;YAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC9C,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAElD,mBAAmB;YACnB,MAAM,UAAU,GAAG;;;;;SAKlB,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAEvE,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE/D,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,QAAQ,EAAE,QAAQ,KAAK,CAAC;oBACxB,KAAK,EAAE,KAAK;iBACf;aACJ,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAClG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,gCAAgC;oBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;iBAC/E;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,2BAA2B;IAClC,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC"}