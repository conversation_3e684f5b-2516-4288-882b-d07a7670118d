import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';
import { EmailService, EmailNotificationData } from "../shared/services/emailService";

export async function rejectChangeRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('RejectChangeRequest function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json() as any;
        const { reason, userId } = body;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        if (!reason || !reason.trim()) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Rejection reason is required'
                    }
                }
            };
        }

        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required for rejection'
                    }
                }
            };
        }

        // First, check if the request exists and can be rejected
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const currentRequest = checkResult.recordset[0];
        
        // Check if request can be rejected
        if (!['Under Review'].includes(currentRequest.Status)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be rejected in current status: ${currentRequest.Status}`
                    }
                }
            };
        }

        // Update the request to rejected status
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Rejected',
                RejectionReason = @reason,
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'reason', type: sql.NVarChar, value: reason.trim() },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Rejected', @userId, GETDATE(), @comments
            )
        `;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: `Request rejected: ${reason.trim()}` }
        ];

        await executeQuery(historyQuery, historyParams);

        // Add rejection comment
        const commentQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
            )
            VALUES (
                @requestId, @commentText, 'ApprovalNote', 0, @userId, GETDATE()
            )
        `;

        const commentParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: `[REJECTION] ${reason.trim()}` },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(commentQuery, commentParams);

        // Get comprehensive request details for email notification
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Status as status,
                cr.Priority as priority,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                cr.RejectionReason as rejectionReason,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                CONCAT(rejecter.FirstName, ' ', rejecter.LastName) as approverName
            FROM ChangeRequests cr
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON requester.CompanyID = c.CompanyID
                LEFT JOIN Users rejecter ON cr.ModifiedBy = rejecter.UserID
            WHERE cr.RequestID = @requestId
        `;

        const detailsResult = await executeQuery(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];

        // Send email notification asynchronously
        try {
            const emailData: EmailNotificationData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                comments: reason.trim(),
                approverName: requestDetails.approverName,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.dueDate ? new Date(requestDetails.dueDate) : undefined
            };

            // Send rejection notification (don't await to avoid blocking the response)
            EmailService.getInstance().sendChangeRequestRejected(emailData).catch((error: any) => {
                context.error('Failed to send rejection email notification:', error);
            });

            context.log(`Email notification queued for rejected change request ${requestId}`);
        } catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the rejection if email fails
        }

        context.log(`Successfully rejected change request ${requestId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request rejected successfully',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    rejectionReason: requestDetails.rejectionReason,
                    requesterName: requestDetails.requesterName,
                    requesterEmail: requestDetails.requesterEmail
                }
            }
        };

    } catch (error: any) {
        context.error('Error in RejectChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while rejecting the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('RejectChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/reject',
    handler: rejectChangeRequest
}); 