{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/AddChangeRequestComment/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,kEAAsF;AACtF,2CAA6B;AAEtB,KAAK,UAAU,uBAAuB,CAAC,OAAoB,EAAE,OAA0B;IAC1F,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,IAAI;QACA,8CAA8C;QAC9C,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;QAC3C,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAS,CAAC;QACzC,MAAM,EAAE,OAAO,EAAE,WAAW,GAAG,SAAS,EAAE,UAAU,GAAG,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAE/F,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;gBA<PERSON>,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE;wBACH,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,wBAAwB;qBACpC;iBACJ;aACJ,CAAC;SACL;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;YAC7B,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE;wBACH,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,0BAA0B;qBACtC;iBACJ;aACJ,CAAC;SACL;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE;wBACH,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,qBAAqB;qBACjC;iBACJ;aACJ,CAAC;SACL;QAED,wBAAwB;QACxB,MAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACzF,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YAC1C,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE;wBACH,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,yCAAyC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;qBACnF;iBACJ;aACJ,CAAC;SACL;QAED,0CAA0C;QAC1C,MAAM,UAAU,GAAG;;;;SAIlB,CAAC;QAEF,MAAM,WAAW,GAAqB;YAClC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;SACnE,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAEhE,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE;wBACH,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,0BAA0B;qBACtC;iBACJ;aACJ,CAAC;SACL;QAED,mDAAmD;QACnD,IAAI,eAAe,EAAE;YACjB,MAAM,gBAAgB,GAAG;;;;aAIxB,CAAC;YAEF,MAAM,iBAAiB,GAAqB;gBACxC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAC5E,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;aACnE,CAAC;YAEF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAElF,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC1C,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,KAAK,EAAE;4BACH,IAAI,EAAE,kBAAkB;4BACxB,OAAO,EAAE,0BAA0B;yBACtC;qBACJ;iBACJ,CAAC;aACL;SACJ;QAED,kBAAkB;QAClB,MAAM,WAAW,GAAG;;;;;;;;SAQnB,CAAC;QAEF,MAAM,YAAY,GAAqB;YACnC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;YAChE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE;YAClE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;YAC/D,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE;YACxD,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACrG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;SAC7D,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACnE,MAAM,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAEzD,kDAAkD;QAClD,MAAM,eAAe,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAkCvB,CAAC;QAEF,MAAM,gBAAgB,GAAqB;YACvC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE;SAC5D,CAAC;QAEF,MAAM,aAAa,GAAG,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;QAC5E,MAAM,cAAc,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAElD,gFAAgF;QAChF,IAAI,CAAC,UAAU,EAAE;YACb,IAAI;gBACA,MAAM,SAAS,GAA0B;oBACrC,SAAS,EAAE,cAAc,CAAC,SAAS;oBACnC,aAAa,EAAE,cAAc,CAAC,aAAa;oBAC3C,KAAK,EAAE,cAAc,CAAC,KAAK;oBAC3B,WAAW,EAAE,cAAc,CAAC,WAAW;oBACvC,QAAQ,EAAE,cAAc,CAAC,QAAQ;oBACjC,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,aAAa,EAAE,cAAc,CAAC,aAAa;oBAC3C,cAAc,EAAE,cAAc,CAAC,cAAc;oBAC7C,YAAY,EAAE,cAAc,CAAC,YAAY;oBACzC,aAAa,EAAE,cAAc,CAAC,aAAa;oBAC3C,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,cAAc;oBACzD,QAAQ,EAAE,cAAc,CAAC,WAAW;oBACpC,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,qCAAqC,sBAAsB,cAAc,CAAC,SAAS,EAAE;oBACpI,WAAW,EAAE,cAAc,CAAC,kBAAkB;oBAC9C,OAAO,EAAE,cAAc,CAAC,OAAO;iBAClC,CAAC;gBAEF,2BAAY,CAAC,WAAW,EAAE,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;oBACrF,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,yCAAyC,YAAY,sBAAsB,SAAS,EAAE,CAAC,CAAC;aACvG;YAAC,OAAO,UAAU,EAAE;gBACjB,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,UAAU,CAAC,CAAC;gBACjE,iDAAiD;aACpD;SACJ;QAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,sBAAsB,SAAS,EAAE,CAAC,CAAC;QAEzF,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE,cAAc;aACvB;SACJ,CAAC;KAEL;IAAC,OAAO,KAAU,EAAE;QACjB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE;oBACH,IAAI,EAAE,uBAAuB;oBAC7B,OAAO,EAAE,4CAA4C;oBACrD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;iBACpG;aACJ;SACJ,CAAC;KACL;AACL,CAAC;AA3OD,0DA2OC;AAED,eAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE;IAChC,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,sCAAsC;IAC7C,OAAO,EAAE,uBAAuB;CACnC,CAAC,CAAC"}