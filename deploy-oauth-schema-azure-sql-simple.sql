-- OAuth Token Management Schema for Azure SQL Database
-- Database: fp-sqldb-falcon-dev-cin-001
-- Server: fp-sql-falcon-dev-cin-001.database.windows.net

-- Note: Connect directly to the target database before running this script

PRINT 'Starting OAuth Token Management Schema Deployment...';

-- Drop existing objects if they exist (safe approach)
DROP FUNCTION IF EXISTS [dbo].[NeedsTokenRefresh];
DROP PROCEDURE IF EXISTS [dbo].[SaveOAuthToken];
DROP PROCEDURE IF EXISTS [dbo].[GetActiveOAuthToken];

-- Drop tables if they exist (be careful - this will lose data!)
-- Comment out these lines if you want to preserve existing data
-- DROP TABLE IF EXISTS [dbo].[OAuthTokenUsage];
-- DROP TABLE IF EXISTS [dbo].[OAuthConfigurations];
-- DROP TABLE IF EXISTS [dbo].[OAuthTokens];

PRINT 'Creating OAuthTokens table...';
GO

CREATE TABLE [dbo].[OAuthTokens] (
    [TokenId] INT IDENTITY(1,1) PRIMARY KEY,
    [UserId] NVARCHAR(255) NOT NULL,
    [ServiceProvider] NVARCHAR(100) NOT NULL, -- 'zoho', 'microsoft', etc.
    [ServiceType] NVARCHAR(100) NOT NULL, -- 'desk', 'crm', 'people', etc.
    [AccessToken] NVARCHAR(MAX) NOT NULL,
    [RefreshToken] NVARCHAR(MAX) NULL,
    [ExpiresAt] DATETIME2 NOT NULL,
    [Scope] NVARCHAR(500) NULL,
    [TokenType] NVARCHAR(50) NOT NULL DEFAULT 'Bearer',
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

PRINT 'Creating indexes for OAuthTokens...';
GO

CREATE NONCLUSTERED INDEX [IX_OAuthTokens_UserService] 
ON [dbo].[OAuthTokens] ([UserId], [ServiceProvider], [ServiceType], [IsActive], [ExpiresAt]);

CREATE NONCLUSTERED INDEX [IX_OAuthTokens_Expiry] 
ON [dbo].[OAuthTokens] ([ExpiresAt], [IsActive]) 
INCLUDE ([UserId], [ServiceProvider], [ServiceType]);

PRINT 'Creating OAuthConfigurations table...';
GO

CREATE TABLE [dbo].[OAuthConfigurations] (
    [ConfigId] INT IDENTITY(1,1) PRIMARY KEY,
    [ServiceProvider] NVARCHAR(100) NOT NULL,
    [ServiceType] NVARCHAR(100) NOT NULL,
    [ConfigKey] NVARCHAR(255) NOT NULL,
    [ConfigValue] NVARCHAR(MAX) NOT NULL,
    [IsEncrypted] BIT NOT NULL DEFAULT 0,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT [UQ_OAuthConfigurations_Service_Key] UNIQUE ([ServiceProvider], [ServiceType], [ConfigKey])
);

PRINT 'Creating indexes for OAuthConfigurations...';
GO

CREATE NONCLUSTERED INDEX [IX_OAuthConfigurations_Service] 
ON [dbo].[OAuthConfigurations] ([ServiceProvider], [ServiceType], [IsActive]);

PRINT 'Creating OAuthTokenUsage table...';
GO

CREATE TABLE [dbo].[OAuthTokenUsage] (
    [UsageId] BIGINT IDENTITY(1,1) PRIMARY KEY,
    [TokenId] INT NOT NULL,
    [RequestEndpoint] NVARCHAR(500) NULL,
    [RequestTime] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [ResponseStatus] INT NULL,
    [ErrorMessage] NVARCHAR(MAX) NULL,
    
    CONSTRAINT [FK_OAuthTokenUsage_TokenId] FOREIGN KEY ([TokenId]) REFERENCES [dbo].[OAuthTokens]([TokenId])
);

PRINT 'Creating indexes for OAuthTokenUsage...';
GO

CREATE NONCLUSTERED INDEX [IX_OAuthTokenUsage_Token] 
ON [dbo].[OAuthTokenUsage] ([TokenId], [RequestTime]);

CREATE NONCLUSTERED INDEX [IX_OAuthTokenUsage_Time] 
ON [dbo].[OAuthTokenUsage] ([RequestTime]);

PRINT 'Creating GetActiveOAuthToken procedure...';
GO

CREATE PROCEDURE [dbo].[GetActiveOAuthToken]
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1
        [TokenId],
        [AccessToken],
        [RefreshToken],
        [ExpiresAt],
        [Scope],
        [TokenType],
        [CreatedAt],
        [UpdatedAt]
    FROM [dbo].[OAuthTokens]
    WHERE [UserId] = @UserId
        AND [ServiceProvider] = @ServiceProvider
        AND [ServiceType] = @ServiceType
        AND [IsActive] = 1
        AND [ExpiresAt] > GETUTCDATE()
    ORDER BY [CreatedAt] DESC;
END
GO

PRINT 'Creating SaveOAuthToken procedure...';
GO

CREATE PROCEDURE [dbo].[SaveOAuthToken]
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100),
    @AccessToken NVARCHAR(MAX),
    @RefreshToken NVARCHAR(MAX) = NULL,
    @ExpiresIn INT = 3600,
    @Scope NVARCHAR(500) = NULL,
    @TokenType NVARCHAR(50) = 'Bearer'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ExpiresAt DATETIME2 = DATEADD(SECOND, @ExpiresIn, GETUTCDATE());
    DECLARE @TokenId INT;
    
    -- Deactivate existing tokens for this user/service combination
    UPDATE [dbo].[OAuthTokens] 
    SET [IsActive] = 0, [UpdatedAt] = GETUTCDATE()
    WHERE [UserId] = @UserId 
        AND [ServiceProvider] = @ServiceProvider 
        AND [ServiceType] = @ServiceType 
        AND [IsActive] = 1;
    
    -- Insert new token
    INSERT INTO [dbo].[OAuthTokens] (
        [UserId], [ServiceProvider], [ServiceType], [AccessToken], [RefreshToken],
        [ExpiresAt], [Scope], [TokenType], [IsActive]
    )
    VALUES (
        @UserId, @ServiceProvider, @ServiceType, @AccessToken, @RefreshToken,
        @ExpiresAt, @Scope, @TokenType, 1
    );
    
    SET @TokenId = SCOPE_IDENTITY();
    
    SELECT @TokenId as TokenId;
END
GO

PRINT 'Creating NeedsTokenRefresh function...';
GO

CREATE FUNCTION [dbo].[NeedsTokenRefresh](
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
)
RETURNS BIT
AS
BEGIN
    DECLARE @Result BIT = 1; -- Default to needs refresh
    DECLARE @ExpiresAt DATETIME2;
    
    SELECT @ExpiresAt = [ExpiresAt]
    FROM [dbo].[OAuthTokens]
    WHERE [UserId] = @UserId
        AND [ServiceProvider] = @ServiceProvider
        AND [ServiceType] = @ServiceType
        AND [IsActive] = 1;
    
    IF @ExpiresAt IS NOT NULL AND @ExpiresAt > DATEADD(MINUTE, 5, GETUTCDATE())
    BEGIN
        SET @Result = 0; -- Token is valid for more than 5 minutes
    END
    
    RETURN @Result;
END
GO

PRINT 'Inserting sample ZohoDesk OAuth configuration...';
GO

INSERT INTO [dbo].[OAuthConfigurations] ([ServiceProvider], [ServiceType], [ConfigKey], [ConfigValue], [IsEncrypted])
VALUES 
    ('zoho', 'desk', 'client_id', '1000.03IZH9ZAA9U4H8OTTE0J2LNKB3U9VO', 0),
    ('zoho', 'desk', 'client_secret', '1acd0b7dfbbbb332a1b226a97fae998cfe4a28a3f3', 1),
    ('zoho', 'desk', 'redirect_uri', 'http://localhost:7071/api/auth/zoho-desk/callback', 0),
    ('zoho', 'desk', 'base_url', 'https://accounts.zoho.in/oauth/v2', 0),
    ('zoho', 'desk', 'scopes', 'Desk.tickets.ALL,Desk.contacts.ALL,Desk.basic.READ,Desk.settings.READ,Desk.agents.READ,Desk.departments.READ,Desk.categories.READ,Desk.threads.ALL,Desk.attachments.READ', 0);

PRINT '';
PRINT '=== OAuth Token Management Schema Deployment Complete ===';
PRINT 'Database: fp-sqldb-falcon-dev-cin-001';
PRINT 'Server: fp-sql-falcon-dev-cin-001.database.windows.net';
PRINT '';
PRINT 'Status:';
PRINT '✅ Tables: OAuthTokens, OAuthConfigurations, OAuthTokenUsage';
PRINT '✅ Procedures: GetActiveOAuthToken, SaveOAuthToken';  
PRINT '✅ Function: NeedsTokenRefresh';
PRINT '✅ Sample Configuration: ZohoDesk OAuth settings';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Deploy the updated Azure Functions code';
PRINT '2. Visit: http://localhost:7071/api/auth/zoho-desk/authorize';
PRINT '3. Complete OAuth flow to store tokens in database';
PRINT '4. Test that tokens survive Azure Functions restart'; 