// jest.setup.ts
// This file runs before any tests execute
// Set default environment variables needed by services like graphService
process.env.ENTRA_TENANT_ID = 'test-tenant-id';
process.env.ENTRA_CLIENT_ID = 'test-client-id';
process.env.ENTRA_CLIENT_SECRET = 'test-client-secret';
// Set other potentially required env vars with defaults if needed
process.env.DEFAULT_USER_ROLE = 'Employee';
process.env.DATABASE_URL = 'mock-db-url'; // Or other DB vars if needed by db.ts
console.log('Jest setup: Default environment variables set.');
//# sourceMappingURL=jest.setup.js.map