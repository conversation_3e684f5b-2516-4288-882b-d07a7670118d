{"version": 3, "file": "FixUnauthorizedAdminAccess.js", "sourceRoot": "", "sources": ["../../src/functions/FixUnauthorizedAdminAccess.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAyF;AACzF,qCAA4C;AAC5C,mDAAgD;AAChD,kDAAwB;AAExB,kFAAkF;AAClF,MAAM,sBAAsB,GAAG;IAC3B,8BAA8B;IAC9B,sBAAsB;IACtB,wCAAwC;CAC3C,CAAC;AAEF;;;GAGG;AACI,KAAK,UAAU,0BAA0B,CAAC,OAAoB,EAAE,OAA0B;IAC7F,IAAI;QACA,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAE7D,2CAA2C;QAC3C,MAAM,eAAe,GAAG;;;;;;;;;;;;;;SAcvB,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QAE5D,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9D,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,wCAAwC;oBACjD,YAAY,EAAE,CAAC;iBAClB;aACJ,CAAC;SACL;QAED,eAAM,CAAC,IAAI,CAAC,SAAS,WAAW,CAAC,SAAS,CAAC,MAAM,gCAAgC,CAAC,CAAC;QAEnF,uCAAuC;QACvC,MAAM,kBAAkB,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAC3D,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAC7D,CAAC;QAEF,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACjD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,mCAAmC;oBAC5C,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;oBACzD,YAAY,EAAE,CAAC;iBAClB;aACJ,CAAC;SACL;QAED,eAAM,CAAC,IAAI,CAAC,SAAS,kBAAkB,CAAC,MAAM,uCAAuC,CAAC,CAAC;QAEvF,uDAAuD;QACvD,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;YACnC,IAAI;gBACA,MAAM,eAAe,GAAG;;;;;;iBAMvB,CAAC;gBAEF,MAAM,MAAM,GAAG;oBACX,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,eAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE;iBAChE,CAAC;gBAEF,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAE5C,YAAY,CAAC,IAAI,CAAC;oBACd,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAC1C,oBAAoB,EAAE,IAAI,CAAC,SAAS;oBACpC,sBAAsB,EAAE,IAAI,CAAC,WAAW;iBAC3C,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;aAEhE;YAAC,OAAO,KAAK,EAAE;gBACZ,eAAM,CAAC,KAAK,CAAC,4CAA4C,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;aAClF;SACJ;QAED,oBAAoB;QACpB,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,gDAAgD,YAAY,CAAC,MAAM,qBAAqB;gBACjG,YAAY,EAAE,YAAY;gBAC1B,gBAAgB,EAAE,WAAW,CAAC,SAAS;qBAClC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;qBACnE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;gBACtB,YAAY,EAAE,YAAY,CAAC,MAAM;aACpC;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC5D,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,yCAAyC;gBAChD,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aACpE;SACJ,CAAC;KACL;AACL,CAAC;AA/GD,gEA+GC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,4BAA4B,EAAE;IACnC,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,0BAA0B;CACtC,CAAC,CAAC;AAEH,kBAAe,0BAA0B,CAAC"}