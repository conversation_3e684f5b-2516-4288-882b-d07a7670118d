// shared/interfaces.ts

/**
 * Database representation of a role
 */
export interface RoleDefinition {
    RoleID: number;
    RoleName: string;
    Description?: string; // This maps to RoleDescription in the database
    IsSystemRole?: boolean;
    IsActive?: boolean;
}

/**
 * Request body for creating a role - supports both naming conventions
 */
export interface CreateRoleRequestBody {
    // Backend naming convention
    RoleName?: string;
    Description?: string;

    // Frontend naming convention
    name?: string;
    description?: string;
}

/**
 * Request body for updating a role - supports both naming conventions
 */
export interface UpdateRoleRequestBody {
    // Backend naming convention
    RoleName?: string;
    Description?: string;

    // Frontend naming convention
    name?: string;
    description?: string;
}

/**
 * Represents a User managed within the portal, combining data from 
 * the database (PortalUsers, Companies, UserRoles) and potentially Entra ID.
 */
export interface PortalUser {
    id: string; // User ID (e.g., Entra ID Object ID)
    internalId?: number; // Internal DB UserID from PortalUsers table
    name: string;
    email: string;
    company: string; // Company Name from Companies table
    companyId?: number; // CompanyID from Companies table
    roles: string[]; // List of RoleNames assigned via UserRoles/Roles tables
    status: 'Active' | 'Inactive'; // Derived from IsActive column in PortalUsers table
    lastLogin?: string; // ISO string format from LastLogin column
    tenantId?: string; // Azure Entra ID Tenant ID for multi-tenant support
}
