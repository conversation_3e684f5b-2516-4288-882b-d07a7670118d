"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
async function getUsersWithIncompleteMapping(req, context) {
    logger_1.logger.info("GetUsersWithIncompleteMapping: Function invoked");
    try {
        // Get client principal from Azure Easy Auth
        const principal = (0, authUtils_1.getClientPrincipal)(req);
        if (!principal) {
            return { status: 401, jsonBody: { error: "Unauthorized - Authentication required" } };
        }
        // Extract user information from claims
        const entraId = principal.claims?.find((claim) => claim.typ === 'http://schemas.microsoft.com/identity/claims/objectidentifier' ||
            claim.typ === 'oid')?.val || principal.userId;
        const email = principal.claims?.find((claim) => claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress' ||
            claim.typ === 'email' ||
            claim.typ === 'preferred_username')?.val || principal.userDetails;
        const tenantId = principal.claims?.find((claim) => claim.typ === 'http://schemas.microsoft.com/identity/claims/tenantid' ||
            claim.typ === 'tid')?.val;
        if (!entraId || !tenantId) {
            return { status: 400, jsonBody: { error: "Invalid authentication token - missing required identity information" } };
        }
        // Get current user from database
        let currentUser = await userManagementService_1.userManagementService.getPortalUserByEntraIdAndTenantWithMapping(entraId, tenantId);
        // If not found by EntraID, try email as fallback
        if (!currentUser && email) {
            currentUser = await userManagementService_1.userManagementService.getPortalUserWithMapping(email);
        }
        if (!currentUser) {
            return { status: 403, jsonBody: { error: "User not found in portal database" } };
        }
        // Check if user has admin privileges
        if (!currentUser.roles.includes('Administrator') && !currentUser.roles.includes('Admin')) {
            logger_1.logger.warn(`GetUsersWithIncompleteMapping: Access denied for user ${currentUser.email} - insufficient privileges`);
            return { status: 403, jsonBody: { error: "Access denied. Administrator privileges required." } };
        }
        logger_1.logger.info(`GetUsersWithIncompleteMapping: Admin ${currentUser.email} requesting incomplete mapping report`);
        // Get users with incomplete mappings
        const incompleteUsers = await userManagementService_1.userManagementService.getUsersWithIncompleteMapping();
        // Group by mapping status for better organization
        const groupedResults = {
            noMapping: incompleteUsers.filter(u => u.MappingStatus === 'No Mapping'),
            placeholder: incompleteUsers.filter(u => u.MappingStatus === 'Placeholder'),
            mismatch: incompleteUsers.filter(u => u.MappingStatus === 'Mismatch'),
            summary: {
                totalIncomplete: incompleteUsers.length,
                noMappingCount: incompleteUsers.filter(u => u.MappingStatus === 'No Mapping').length,
                placeholderCount: incompleteUsers.filter(u => u.MappingStatus === 'Placeholder').length,
                mismatchCount: incompleteUsers.filter(u => u.MappingStatus === 'Mismatch').length
            }
        };
        logger_1.logger.info(`GetUsersWithIncompleteMapping: Found ${incompleteUsers.length} users with incomplete mappings`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                data: groupedResults,
                timestamp: new Date().toISOString(),
                requestedBy: currentUser.email
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetUsersWithIncompleteMapping: Error:", error);
        return {
            status: 500,
            jsonBody: {
                error: "Failed to retrieve users with incomplete mapping",
                details: error instanceof Error ? error.message : "Unknown error"
            }
        };
    }
}
// Register the Azure Function
functions_1.app.http('GetUsersWithIncompleteMapping', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'admin/users/incomplete-mapping',
    handler: getUsersWithIncompleteMapping
});
//# sourceMappingURL=GetUsersWithIncompleteMapping.js.map