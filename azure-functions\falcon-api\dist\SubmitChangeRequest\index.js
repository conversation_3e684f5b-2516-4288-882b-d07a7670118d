"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.submitChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
const emailService_1 = require("../shared/services/emailService");
async function submitChangeRequest(request, context) {
    context.log('SubmitChangeRequest function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        // First, check if the request exists and can be submitted
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        const currentRequest = checkResult.recordset[0];
        // Check if request can be submitted
        if (currentRequest.Status !== 'Draft') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be submitted in current status: ${currentRequest.Status}`
                    }
                }
            };
        }
        // Update the request to submitted status
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Under Review',
                ModifiedBy = @requestedBy,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'requestedBy', type: sql.Int, value: currentRequest.RequestedBy }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Under Review', @requestedBy, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'requestedBy', type: sql.Int, value: currentRequest.RequestedBy },
            { name: 'comments', type: sql.NVarChar, value: 'Change request submitted for review' }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Get comprehensive request details for email notification
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.ExpectedBenefit as expectedBenefit,
                cr.RequestedCompletionDate as requestedCompletionDate,
                ISNULL(cr.DevelopmentProgress, 0) as developmentProgress,
                cr.CreatedDate as createdDate,
                crt.TypeName as typeName,
                crt.Description as typeDescription,
                ISNULL(crt.EstimatedDays, 0) as estimatedDays,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
            WHERE cr.RequestID = @requestId
        `;
        const detailsResult = await (0, db_1.executeQuery)(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];
        // Send email notification to Change Managers asynchronously
        try {
            const emailData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.requestedCompletionDate ? new Date(requestDetails.requestedCompletionDate) : undefined
            };
            // Send submission notification to Change Managers (don't await to avoid blocking the response)
            emailService_1.EmailService.getInstance().sendChangeRequestSubmitted(emailData).catch((error) => {
                context.error('Failed to send submission email notification:', error);
            });
            context.log(`Email notification queued for submitted change request ${requestId}`);
        }
        catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the submission if email fails
        }
        context.log(`Successfully submitted change request ${requestId} for approval`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request submitted for approval successfully. Change Managers have been notified.',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    description: requestDetails.description,
                    priority: requestDetails.priority,
                    status: requestDetails.status,
                    businessJustification: requestDetails.businessJustification,
                    expectedBenefit: requestDetails.expectedBenefit,
                    requestedCompletionDate: requestDetails.requestedCompletionDate,
                    developmentProgress: requestDetails.developmentProgress,
                    createdDate: requestDetails.createdDate,
                    typeName: requestDetails.typeName,
                    typeDescription: requestDetails.typeDescription,
                    estimatedDays: requestDetails.estimatedDays,
                    requesterName: requestDetails.requesterName,
                    requesterEmail: requestDetails.requesterEmail,
                    companyName: requestDetails.companyName
                }
            }
        };
    }
    catch (error) {
        context.error('Error in SubmitChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while submitting the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.submitChangeRequest = submitChangeRequest;
functions_1.app.http('SubmitChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/submit',
    handler: submitChangeRequest
});
//# sourceMappingURL=index.js.map