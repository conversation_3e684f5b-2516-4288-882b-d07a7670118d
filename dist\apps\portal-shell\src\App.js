"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
require("./App.css");
const react_router_dom_1 = require("react-router-dom");
// MSAL Imports
const msal_react_1 = require("@azure/msal-react");
const authConfig_1 = require("./authConfig");
// Import Layouts and Pages/Components
const ShellLayout_1 = require("./components/layout/ShellLayout");
const DashboardSection_1 = require("./components/DashboardSection");
// Hub/Page components
const ComingSoonPage_1 = require("./pages/ComingSoonPage"); // Import ComingSoon
const ITPage_1 = require("./pages/ITPage"); // Import IT Page
// Import new user management pages
const UserManagementPage_1 = require("./pages/AdminHub/UserManagementPage");
const UserEditPage_1 = require("./pages/AdminHub/UserEditPage");
const UserAddPage_1 = require("./pages/AdminHub/UserAddPage");
// Import Role Management page
const RoleManagementPage_1 = require("./pages/AdminHub/RoleManagementPage");
// TODO: Add RoleManagementPage import when created
// import RoleManagementPage from './pages/AdminHub/RoleManagementPage';
// Remove placeholder
// const PlaceholderHub: React.FC<{ name: string }> = ({ name }) => <div className="p-4">Welcome to {name} Hub (Base - Build Required)</div>; 
// Remove imports for pages we'll replace with ComingSoon
// import AnnouncementsPage from './pages/AnnouncementsPage';
// import ActionsPage from './pages/ActionsPage';
// import DocumentsPage from './pages/DocumentsPage';
// import EventsPage from './pages/EventsPage';
function LoginPrompt() {
    const { instance } = (0, msal_react_1.useMsal)();
    const handleLogin = () => {
        instance.loginRedirect(authConfig_1.loginRequest).catch((e) => {
            if (e instanceof Error) {
                console.error("Login Failed:", e.message);
            }
            else {
                console.error("An unknown login error occurred:", e);
            }
        });
    };
    // Basic centered login prompt
    return (<div className="flex items-center justify-center h-screen bg-gray-100">
      <div className="text-center p-10 bg-white rounded shadow-md">
          <h1 className="text-2xl font-bold mb-4">Falcon Portal</h1>
          <p className="mb-6">Please sign in to continue.</p>
          <button onClick={handleLogin} className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Login with Microsoft
          </button>
      </div>
    </div>);
}
function App() {
    return (<react_router_dom_1.BrowserRouter>
      <msal_react_1.AuthenticatedTemplate>
        {/* Use Routes within the ShellLayout */}
        <react_router_dom_1.Routes>
          <react_router_dom_1.Route path="/" element={<ShellLayout_1.default />}>
            <react_router_dom_1.Route index element={<react_router_dom_1.Navigate to="/dashboard" replace/>}/> 
            <react_router_dom_1.Route path="dashboard" element={<DashboardSection_1.default />}/>
            
            {/* Hubs using ComingSoon or Placeholder */}
            <react_router_dom_1.Route path="knowledge" element={<ComingSoonPage_1.default pageName="Knowledge Hub"/>}/>
            <react_router_dom_1.Route path="it" element={<ITPage_1.default />}/>
            <react_router_dom_1.Route path="hr" element={<ComingSoonPage_1.default pageName="HR Hub"/>}/>
            <react_router_dom_1.Route path="admin-hub" element={<ComingSoonPage_1.default pageName="Admin Hub (Travel, etc.)"/>}/> {/* New Functional Admin Hub */}
            {/* <Route path="admin" element={<PlaceholderHub name="Admin" />} /> Removed Placeholder */}
            
            {/* Portal Administration Routes */}
            <react_router_dom_1.Route path="portal-admin/user-management" element={<UserManagementPage_1.default />}/> 
            <react_router_dom_1.Route path="portal-admin/add-user" element={<UserAddPage_1.default />}/>
            <react_router_dom_1.Route path="portal-admin/manage-user/:userId" element={<UserEditPage_1.default />}/> 
            {/* Add Role Management route */}
            <react_router_dom_1.Route path="portal-admin/role-management" element={<RoleManagementPage_1.default />}/>

            <react_router_dom_1.Route path="communication" element={<ComingSoonPage_1.default pageName="Communication Hub"/>}/>
            
            {/* Specific pages replaced with ComingSoon */}
            <react_router_dom_1.Route path="knowledge/documents" element={<ComingSoonPage_1.default pageName="Documents"/>}/> 
            <react_router_dom_1.Route path="communication/announcements" element={<ComingSoonPage_1.default pageName="Announcements"/>}/>
            <react_router_dom_1.Route path="communication/events" element={<ComingSoonPage_1.default pageName="Events"/>}/>
            <react_router_dom_1.Route path="actions" element={<ComingSoonPage_1.default pageName="Pending Actions"/>}/> 
            
            {/* TODO: Add other routes like profile, settings, specific item views */}
            <react_router_dom_1.Route path="*" element={<ComingSoonPage_1.default pageName="Page Not Found"/>}/> {/* Use ComingSoon for 404 too? */}
          </react_router_dom_1.Route>
        </react_router_dom_1.Routes>
      </msal_react_1.AuthenticatedTemplate>

      <msal_react_1.UnauthenticatedTemplate>
        {/* Show a login prompt when not authenticated - outside main router */}
        <LoginPrompt />
      </msal_react_1.UnauthenticatedTemplate>
    </react_router_dom_1.BrowserRouter>);
}
exports.default = App;
//# sourceMappingURL=App.js.map