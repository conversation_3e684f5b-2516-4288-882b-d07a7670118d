"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRole = createRole;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db"); // Assuming shared DB utility exists one level up
function createRole(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c;
        context.log(`Http function processed request for url "${request.url}" to create a role.`);
        let body;
        try {
            // Use request.json() for parsing
            body = (yield request.json());
        }
        catch (e) {
            context.error("ERROR parsing request body:", e);
            return {
                status: 400,
                jsonBody: { message: "Invalid JSON in request body." }
            };
        }
        // Handle both roleName (frontend) and name (generic) fields
        const roleName = ((_a = body === null || body === void 0 ? void 0 : body.roleName) === null || _a === void 0 ? void 0 : _a.trim()) || ((_b = body === null || body === void 0 ? void 0 : body.name) === null || _b === void 0 ? void 0 : _b.trim());
        const description = ((_c = body === null || body === void 0 ? void 0 : body.description) === null || _c === void 0 ? void 0 : _c.trim()) || null; // Use null for empty description
        if (!roleName) {
            return {
                status: 400,
                jsonBody: { message: "Role name is required and cannot be empty." }
            };
        }
        try {
            // 1. Check if role already exists (case-insensitive)
            const checkQuery = 'SELECT COUNT(*) as Count FROM dbo.Roles WHERE LOWER(RoleName) = LOWER(@RoleName)';
            const checkParams = { RoleName: roleName };
            const checkResult = yield (0, db_1.executeQuery)(checkQuery, checkParams);
            if (checkResult.recordset[0].Count > 0) {
                context.log(`Conflict: Role with name '${roleName}' already exists.`);
                return {
                    status: 409, // Conflict
                    jsonBody: { message: `Role with name '${roleName}' already exists.` }
                };
            }
            // 2. Insert the new role
            // Adapt table/column names and add CreatedBy/CreatedDate if needed based on schema
            const insertQuery = `
            INSERT INTO dbo.Roles (RoleName, Description, IsActive, CreatedDate) 
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.Description
            VALUES (@RoleNameParam, @DescriptionParam, 1, GETUTCDATE()) 
        `;
            const insertParams = {
                RoleNameParam: roleName,
                DescriptionParam: description,
                // Add CreatedByParam: userId // If tracking user who created it
            };
            const result = yield (0, db_1.executeQuery)(insertQuery, insertParams);
            if (result.recordset && result.recordset.length > 0) {
                const outputRow = result.recordset[0];
                const newRole = {
                    id: outputRow.RoleID.toString(),
                    name: outputRow.RoleName,
                    description: outputRow.Description
                };
                context.log(`Successfully created role '${roleName}' with ID: ${newRole.id}`);
                return {
                    status: 201, // Created
                    jsonBody: newRole
                };
            }
            else {
                context.error("Role creation query did not return the expected output.");
                return {
                    status: 500,
                    jsonBody: { message: "Failed to create role due to unexpected database response." }
                };
            }
        }
        catch (err) {
            context.error(`Error creating role '${roleName}':`, err instanceof Error ? err.message : err);
            // Specific error handling (like unique constraint) is now likely handled within executeQuery or needs adjustment
            return {
                status: 500,
                jsonBody: {
                    message: "Failed to create role due to a server error.",
                    error: err instanceof Error ? err.message : "Unknown error" // Provide error details cautiously
                }
            };
        }
    });
}
// Register the function (ensure function name matches export and is unique)
functions_1.app.http('CreateRoleFunction', {
    methods: ['POST'],
    authLevel: 'function', // Or 'anonymous' / 'admin' depending on requirements
    route: 'roles', // Matches the API path /api/roles
    handler: createRole
});
//# sourceMappingURL=index.js.map