{"version": 3, "file": "GetKnowledgeArticles.js", "sourceRoot": "", "sources": ["../../src/functions/GetKnowledgeArticles.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAiF;AACjF,2CAA6B;AAwBtB,KAAK,UAAU,oBAAoB,CAAC,OAAoB,EAAE,OAA0B;IACvF,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE;YAC3B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,CAAC;SACjE;QAED,yBAAyB;QACzB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpD,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAElC,8BAA8B;QAC9B,IAAI,eAAe,GAAG,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC;QAChE,MAAM,WAAW,GAAqB;YAClC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;YAChD,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;SACjD,CAAC;QAEF,oBAAoB;QACpB,IAAI,MAAM,EAAE;YACR,eAAe,CAAC,IAAI,CAAC;;;;eAIlB,CAAC,CAAC;YACL,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;SAClF;QAED,sBAAsB;QACtB,IAAI,UAAU,IAAI,UAAU,KAAK,KAAK,EAAE;YACpC,eAAe,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACpD,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;SACxF;QAED,qBAAqB;QACrB,IAAI,SAAS,IAAI,SAAS,KAAK,KAAK,EAAE;YAClC,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAClD,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SACtF;QAED,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/F,MAAM,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;kBA2BJ,WAAW;;;;;;;;;;;;;;;;;cAiBf,WAAW;SAChB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAEtD,6BAA6B;QAC7B,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;QAChG,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEjF,MAAM,QAAQ,GAAuB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YACjE,EAAE,EAAE,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC5B,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;YAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,QAAQ,EAAE;gBACN,EAAE,EAAE,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACpC,IAAI,EAAE,GAAG,CAAC,YAAY,IAAI,eAAe;aAC5C;YACD,OAAO,EAAE,GAAG,CAAC,WAAW,IAAI,SAAS;YACrC,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE;YACtD,SAAS,EAAE;gBACP,IAAI,EAAE,GAAG,CAAC,aAAa,IAAI,SAAS;gBACpC,KAAK,EAAE,GAAG,CAAC,cAAc,IAAI,EAAE;aAClC;YACD,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,YAAY,EAAE,GAAG,CAAC,YAAY;YAC9B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;SAC5C,CAAC,CAAC,CAAC;QAEJ,MAAM,UAAU,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;QAEjD,eAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,CAAC,MAAM,sBAAsB,MAAM,IAAI,UAAU,EAAE,CAAC,CAAC;QAE5G,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,QAAQ;gBACR,UAAU,EAAE;oBACR,IAAI;oBACJ,KAAK;oBACL,UAAU;oBACV,UAAU;oBACV,OAAO,EAAE,IAAI,GAAG,UAAU;oBAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;iBACpB;aACJ;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACxE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,sCAAsC;gBAC/C,KAAK,EAAE,YAAY;aACtB;SACJ,CAAC;KACL;AACL,CAAC;AAnKD,oDAmKC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE;IAC7B,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,oBAAoB;CAChC,CAAC,CAAC"}