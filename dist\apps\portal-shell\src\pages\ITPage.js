"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const itHubApi_1 = require("../services/itHubApi");
const date_fns_1 = require("date-fns");
const FeatherIcons = require("feather-icons-react");
// Specify props more explicitly
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DynamicFeatherIcon = (_a) => {
    var { name, size, className } = _a, props = __rest(_a, ["name", "size", "className"]);
    // Type assertion needed for dynamic lookup, suppress warning
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const IconComponent = FeatherIcons[name] || FeatherIcons.HelpCircle;
    return <IconComponent size={size} className={className} {...props}/>;
};
const formatRelativeTime = (isoDate) => {
    try {
        return (0, date_fns_1.formatDistanceToNow)(new Date(isoDate), { addSuffix: true });
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    catch (_e) {
        return 'Invalid date';
    }
};
const formatDate = (isoDate) => {
    try {
        return (0, date_fns_1.format)(new Date(isoDate), 'MMM d, yyyy');
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    catch (_e) {
        return 'Invalid date';
    }
};
const getStatusColor = (status) => {
    switch (status) {
        case 'Open': return 'text-blue-600 bg-blue-100';
        case 'Pending': return 'text-yellow-600 bg-yellow-100';
        case 'Resolved': return 'text-green-600 bg-green-100';
        case 'Closed': return 'text-gray-600 bg-gray-100';
        default: return 'text-gray-600 bg-gray-100';
    }
};
const ITPage = () => {
    const [tickets, setTickets] = (0, react_1.useState)([]);
    const [actions, setActions] = (0, react_1.useState)([]);
    const [assets, setAssets] = (0, react_1.useState)([]);
    const [policies, setPolicies] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(true);
    const navigate = (0, react_router_dom_1.useNavigate)();
    (0, react_1.useEffect)(() => {
        const loadData = () => __awaiter(void 0, void 0, void 0, function* () {
            setLoading(true);
            try {
                const [ticketData, actionData, assetData, policyData] = yield Promise.all([
                    (0, itHubApi_1.fetchMyOpenTickets)(5), // Limit initial display
                    (0, itHubApi_1.fetchITQuickActions)(),
                    (0, itHubApi_1.fetchMyAssets)(),
                    (0, itHubApi_1.fetchITPolicies)()
                ]);
                setTickets(ticketData);
                setActions(actionData);
                setAssets(assetData);
                setPolicies(policyData);
            }
            catch (error) {
                console.error("Failed to load IT Hub data:", error);
                // TODO: Set error state for display
            }
            setLoading(false);
        });
        loadData();
    }, []);
    const handleActionClick = (link) => {
        if (link.startsWith('/')) {
            navigate(link);
        }
        else {
            window.open(link, '_blank', 'noopener,noreferrer');
        }
    };
    const handleReleaseRequest = (assetId, assetName) => {
        console.log(`TODO: Initiate release request workflow for asset ${assetName} (${assetId}) - Requires Admin Approval`);
        alert(`Release request submitted for ${assetName}. It requires admin approval.`);
    };
    return (<div>
      <h1 className="text-2xl font-bold mb-6">IT Hub</h1>
      
      {/* Main content area - Single column flow */}
      {loading ? (<div className="text-center text-gray-500 py-10">Loading IT Hub information...</div>) : (<div className="flex flex-col space-y-6">
          
          {/* 1. Quick Actions (Horizontal Scroll) */}
          <section>
            <h2 className="text-lg font-semibold mb-3 px-1">Quick Actions</h2>
            <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 px-4">
              {actions.map(action => (<button key={action.id} onClick={() => handleActionClick(action.link)} className="flex-shrink-0 w-64 bg-white p-4 rounded-lg shadow text-left flex flex-col justify-between hover:shadow-md transition-shadow border border-gray-200">
                  <div className="flex items-start mb-2">
                    <DynamicFeatherIcon name={action.icon} size={24} className="text-blue-600 mr-3 mt-1 flex-shrink-0"/>
                    <p className="text-md font-semibold">{action.title}</p>
                  </div>
                  <p className="text-xs text-gray-500 line-clamp-2">{action.description}</p> 
                </button>))}
              {actions.length === 0 && <p className="text-sm text-gray-500 pl-1">No quick actions available.</p>}
            </div>
          </section>

          {/* 2. My Open Tickets Section */}
          <section className="bg-white p-4 rounded-lg shadow border border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <h2 className="text-lg font-semibold">My Open Tickets</h2>
              <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => navigate('/it/tickets')}>View All</span>
            </div>
            <div className="space-y-3">
              {tickets.length === 0 && <p className="text-sm text-gray-500">No open tickets found.</p>}
              {tickets.map(ticket => (<a href={ticket.link} target="_blank" rel="noopener noreferrer" key={ticket.id} className="block p-3 hover:bg-gray-50 border rounded">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-blue-700">{ticket.id}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(ticket.status)}`}>{ticket.status}</span>
                  </div>
                  <p className="text-sm font-semibold mb-1 truncate" title={ticket.subject}>{ticket.subject}</p>
                  <p className="text-xs text-gray-500">Last updated: {formatRelativeTime(ticket.lastUpdated)}</p>
                </a>))}
            </div>
          </section>

          {/* 3. IT Policies (Horizontal Scroll) */}
          <section>
             <div className="flex justify-between items-center mb-3 px-1">
                <h2 className="text-lg font-semibold">IT Policies</h2>
                <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => navigate('/knowledge/policies')}>View All</span>
             </div>
             <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {policies.length === 0 && <p className="text-sm text-gray-500 pl-1">No policies found.</p>}
                {policies.map(policy => (<button key={policy.id} onClick={() => handleActionClick(policy.link)} className="flex-shrink-0 w-72 bg-white p-4 rounded-lg shadow text-left flex items-center hover:shadow-md transition-shadow border border-gray-200">
                    <FeatherIcons.FileText size={24} className="text-gray-500 mr-4 flex-shrink-0"/>
                    <div>
                        <p className="text-sm font-medium mb-1">{policy.title}</p>
                        <p className="text-xs text-gray-500 line-clamp-2">{policy.summary}</p>
                    </div>
                  </button>))}
              </div>
          </section>

          {/* 4. My Assets Section */}
          <section className="bg-white p-4 rounded-lg shadow border border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <h2 className="text-lg font-semibold">My Assets</h2>
              <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => navigate('/it/assets')}>View All</span>
            </div>
             <div className="space-y-3">
              {assets.length === 0 && <p className="text-sm text-gray-500">No assets assigned.</p>}
              {assets.map(asset => (<div key={asset.id} className="p-3 border rounded bg-gray-50 flex flex-col sm:flex-row justify-between items-start sm:items-center">
                  <div className="mb-2 sm:mb-0">
                    <p className="text-sm font-semibold mb-1">{asset.name}</p>
                    <div className="text-xs text-gray-600">
                      <span>{asset.type} {asset.serialNumber ? `(SN: ${asset.serialNumber})` : ''}</span>
                      <span className="ml-2 pl-2 border-l border-gray-300">Assigned: {formatDate(asset.assignedDate)}</span>
                    </div>
                  </div>
                  <button onClick={() => handleReleaseRequest(asset.id, asset.name)} className="mt-2 sm:mt-0 px-3 py-1 text-xs font-medium text-orange-700 bg-orange-100 rounded hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 flex-shrink-0">
                      Request Release
                  </button>
                </div>))}
            </div>
          </section>

        </div>)}
    </div>);
};
exports.default = ITPage;
//# sourceMappingURL=ITPage.js.map