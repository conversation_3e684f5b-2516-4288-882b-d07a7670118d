"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServeImage = void 0;
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const cors_1 = require("../shared/cors");
// Configuration
const STORAGE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'falconhubstorage';
const STORAGE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const CONTAINER_NAME = 'change-request-images';
async function ServeImage(request, context) {
    context.log('ServeImage function processed a request.');
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: ''
        });
    }
    try {
        // Get the blob path from the route parameters
        const blobPath = request.params.path;
        if (!blobPath) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'Image path is required'
                }
            });
        }
        context.log(`Attempting to serve image: ${blobPath}`);
        // Check if Azure Storage is configured
        if (!STORAGE_ACCOUNT_KEY) {
            return (0, cors_1.addCorsHeaders)({
                status: 503,
                jsonBody: {
                    success: false,
                    message: 'Azure Storage not configured'
                }
            });
        }
        // Create storage shared key credential
        const sharedKeyCredential = new storage_blob_1.StorageSharedKeyCredential(STORAGE_ACCOUNT_NAME, STORAGE_ACCOUNT_KEY);
        // Create blob service client
        const blobServiceClient = new storage_blob_1.BlobServiceClient(`https://${STORAGE_ACCOUNT_NAME}.blob.core.windows.net`, sharedKeyCredential);
        // Get container client
        const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
        // Get blob client
        const blobClient = containerClient.getBlobClient(blobPath);
        // Check if blob exists
        const exists = await blobClient.exists();
        if (!exists) {
            context.log(`Blob not found: ${blobPath}`);
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                jsonBody: {
                    success: false,
                    message: 'Image not found'
                }
            });
        }
        // Get blob properties to determine content type
        const properties = await blobClient.getProperties();
        const contentType = properties.contentType || 'application/octet-stream';
        // Download the blob as a buffer
        const downloadResponse = await blobClient.downloadToBuffer();
        if (!downloadResponse || downloadResponse.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                jsonBody: {
                    success: false,
                    message: 'Failed to read image data'
                }
            });
        }
        context.log(`Successfully serving image: ${blobPath}, size: ${downloadResponse.length} bytes`);
        // Return the image with appropriate headers
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: downloadResponse,
            headers: {
                'Content-Type': contentType,
                'Content-Length': downloadResponse.length.toString(),
                'Cache-Control': 'public, max-age=3600',
                'Content-Disposition': 'inline'
            }
        });
    }
    catch (error) {
        context.log('Error serving image:', error);
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            jsonBody: {
                success: false,
                message: 'Internal server error while serving image',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
}
exports.ServeImage = ServeImage;
functions_1.app.http('ServeImage', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'images/{*path}',
    handler: ServeImage
});
//# sourceMappingURL=ServeImage.js.map