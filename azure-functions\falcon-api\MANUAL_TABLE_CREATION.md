# Manual UserEntraIDMappings Table Creation Guide

## Issue
Having authentication issues connecting to the database programmatically to create the Phase 2 mapping table.

## Solution Options

### Option 1: Azure Portal (Recommended)
1. Go to Azure Portal
2. Navigate to SQL Database: `fp-sql-falcon-dev-cin-001` > `FalconHubDB`
3. Open "Query editor"
4. Copy and paste the contents of `create-entraid-mapping-table.sql`
5. Execute the script

### Option 2: SQL Server Management Studio (SSMS)
1. Connect to: `fp-sql-falcon-dev-cin-001.database.windows.net`
2. Database: `FalconHubDB`
3. Use Azure Active Directory authentication
4. Execute the `create-entraid-mapping-table.sql` script

### Option 3: Azure CLI (If you have correct permissions)
```bash
# First, ensure you're logged in
az login

# Execute the SQL script
az sql query --server fp-sql-falcon-dev-cin-001 --database FalconHubDB --file create-entraid-mapping-table.sql
```

### Option 4: PowerShell (If you have Az module)
```powershell
# Ensure Azure PowerShell is installed and logged in
Connect-AzAccount

# Execute the script
$sqlScript = Get-Content 'create-entraid-mapping-table.sql' -Raw
Invoke-AzSqlDatabaseQuery -ServerName 'fp-sql-falcon-dev-cin-001' -DatabaseName 'FalconHubDB' -ResourceGroupName 'FalconHub' -Query $sqlScript
```

## What This Creates

1. **UserEntraIDMappings Table**: Core mapping table for automatic EntraID capture
2. **UpsertUserEntraIDMapping Stored Procedure**: Handles insert/update operations
3. **vw_UsersWithEntraIDMappings View**: Unified view for user lookups

## Verification

After creating the table, verify with:
```sql
SELECT COUNT(*) FROM UserEntraIDMappings;
SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UserEntraIDMappings';
```

## Next Steps
Once the table is created, the Phase 2 system will automatically:
- Capture EntraIDs when users login
- Store mapping data with audit trail
- Provide administrative review capabilities

The Azure Functions are already deployed with Phase 2 code and ready to use the table once it exists. 