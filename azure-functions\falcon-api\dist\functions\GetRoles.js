"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoles = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
async function getRoles(request, context) {
    logger_1.logger.info(`GetRoles: Http function processed request for url "${request.url}"`);
    // Check if we're in development mode
    const isDevelopment = !process.env.WEBSITE_SITE_NAME;
    logger_1.logger.info(`GetRoles: Development mode check - isDevelopment: ${isDevelopment}`);
    try {
        // Query the correct description column
        const result = await (0, db_1.executeQuery)('SELECT RoleID, RoleName, RoleDescription, IsActive FROM Roles WHERE IsActive = 1 ORDER BY RoleName');
        logger_1.logger.info("GetRoles: Raw recordset from DB:", JSON.stringify(result.recordset, null, 2));
        // Map the database result to the RoleDefinition interface using correct property names
        const roles = result.recordset.map(dbRole => ({
            RoleID: dbRole.RoleID,
            RoleName: dbRole.RoleName,
            Description: dbRole.RoleDescription // Map Description, assuming it exists
            // Include IsSystemRole and IsActive if they are selected and needed
            // IsSystemRole: dbRole.IsSystemRole,
            // IsActive: dbRole.IsActive
        }));
        return {
            status: 200,
            jsonBody: roles
        };
    }
    catch (error) {
        logger_1.logger.error(`GetRoles: Error fetching roles: ${error instanceof Error ? error.message : error}`);
        // In development mode, return mock data if database is unavailable or login fails
        if (isDevelopment && error instanceof Error &&
            (error.message.includes('Connection') ||
                error.message.includes('Login failed') ||
                error.message.includes('ELOGIN') ||
                error.message.includes('Database Connection Failed'))) {
            logger_1.logger.warn("GetRoles: Database connection/login failed in development mode, returning mock data");
            const mockRoles = [
                {
                    RoleID: 1,
                    RoleName: "Administrator",
                    Description: "Full system access and administration privileges"
                },
                {
                    RoleID: 2,
                    RoleName: "Employee",
                    Description: "Standard employee access to portal features"
                },
                {
                    RoleID: 3,
                    RoleName: "HR Manager",
                    Description: "Human resources management access"
                },
                {
                    RoleID: 4,
                    RoleName: "IT Support",
                    Description: "IT support and ticketing system access"
                }
            ];
            return {
                status: 200,
                jsonBody: mockRoles
            };
        }
        return {
            status: 500,
            jsonBody: {
                message: "Error fetching roles.",
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}
exports.getRoles = getRoles;
functions_1.app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'roles',
    handler: getRoles
});
//# sourceMappingURL=GetRoles.js.map