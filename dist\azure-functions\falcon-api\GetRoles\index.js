"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoles = getRoles;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
function getRoles(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function processed request for url "${request.url}"`);
        try {
            // Use RoleDescription column name and alias it as Description for consistency
            const result = yield (0, db_1.executeQuery)('SELECT RoleID, RoleName, RoleDescription as Description, IsSystemRole, IsActive FROM Roles WHERE IsActive = 1 ORDER BY RoleName');
            // Map the database result to the format expected by the frontend
            const roles = result.recordset.map(role => ({
                id: role.RoleID.toString(),
                name: role.RoleName,
                description: role.Description || null // Ensure description is never undefined
            }));
            context.log("Mapped roles:", JSON.stringify(roles));
            // Log the raw database result for debugging
            context.log("Raw database result:", JSON.stringify(result.recordset));
            context.log("Returning roles:", JSON.stringify(roles));
            // The roles are already in the format expected by the frontend
            context.log("Roles ready for frontend:", JSON.stringify(roles));
            return {
                status: 200,
                jsonBody: roles
            };
        }
        catch (error) {
            context.error(`Error fetching roles: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error fetching roles.",
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'roles',
    handler: getRoles
});
//# sourceMappingURL=index.js.map