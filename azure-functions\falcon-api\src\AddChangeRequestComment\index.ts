import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { EmailService, EmailNotificationData } from "../shared/services/emailService";
import * as sql from 'mssql';

export async function addChangeRequestComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('AddChangeRequestComment function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json() as any;
        const { comment, commentType = 'General', isInternal = false, parentCommentId, userId } = body;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        if (!comment || !comment.trim()) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Comment text is required'
                    }
                }
            };
        }

        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required'
                    }
                }
            };
        }

        // Validate comment type
        const validCommentTypes = ['General', 'ApprovalNote', 'DevUpdate', 'Question', 'Answer'];
        if (!validCommentTypes.includes(commentType)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: `Invalid comment type. Must be one of: ${validCommentTypes.join(', ')}`
                    }
                }
            };
        }

        // First, verify the change request exists
        const checkQuery = `
            SELECT RequestID, Status 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        // If parentCommentId is provided, verify it exists
        if (parentCommentId) {
            const parentCheckQuery = `
                SELECT CommentID 
                FROM ChangeRequestComments 
                WHERE CommentID = @parentCommentId AND RequestID = @requestId
            `;

            const parentCheckParams: QueryParameter[] = [
                { name: 'parentCommentId', type: sql.Int, value: parseInt(parentCommentId) },
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
            ];

            const parentCheckResult = await executeQuery(parentCheckQuery, parentCheckParams);

            if (parentCheckResult.recordset.length === 0) {
                return {
                    status: 400,
                    jsonBody: {
                        error: {
                            code: 'VALIDATION_ERROR',
                            message: 'Parent comment not found'
                        }
                    }
                };
            }
        }

        // Add the comment
        const insertQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, ParentCommentID, CreatedBy, CreatedDate
            )
            OUTPUT INSERTED.CommentID
            VALUES (
                @requestId, @commentText, @commentType, @isInternal, @parentCommentId, @userId, GETDATE()
            )
        `;

        const insertParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: comment.trim() },
            { name: 'commentType', type: sql.NVarChar, value: commentType },
            { name: 'isInternal', type: sql.Bit, value: isInternal },
            { name: 'parentCommentId', type: sql.Int, value: parentCommentId ? parseInt(parentCommentId) : null },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        const insertResult = await executeQuery(insertQuery, insertParams);
        const newCommentId = insertResult.recordset[0].CommentID;

        // Get the newly created comment with user details
        const getCommentQuery = `
            SELECT 
                c.CommentID as commentId,
                c.RequestID as requestId,
                c.CommentText as commentText,
                c.CommentType as commentType,
                c.IsInternal as isInternal,
                c.ParentCommentID as parentCommentId,
                c.CreatedBy as createdBy,
                CONCAT(u.FirstName, ' ', u.LastName) as createdByName,
                u.Email as createdByEmail,
                c.CreatedDate as createdDate,
                c.ModifiedBy as modifiedBy,
                c.ModifiedDate as modifiedDate,
                c.IsEdited as isEdited,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.CreatedDate as requestCreatedDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                CONCAT(assignee.FirstName, ' ', assignee.LastName) as assigneeName,
                assignee.Email as assigneeEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequestComments c
                LEFT JOIN Users u ON c.CreatedBy = u.UserID
                LEFT JOIN ChangeRequests cr ON c.RequestID = cr.RequestID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Users assignee ON cr.AssignedToDevID = assignee.UserID
                LEFT JOIN Companies companies ON requester.CompanyID = companies.CompanyID
            WHERE c.CommentID = @commentId
        `;

        const getCommentParams: QueryParameter[] = [
            { name: 'commentId', type: sql.Int, value: newCommentId }
        ];

        const commentResult = await executeQuery(getCommentQuery, getCommentParams);
        const commentDetails = commentResult.recordset[0];

        // Send email notification for comment addition (only for non-internal comments)
        if (!isInternal) {
            try {
                const emailData: EmailNotificationData = {
                    requestId: commentDetails.requestId,
                    requestNumber: commentDetails.requestNumber,
                    title: commentDetails.title,
                    description: commentDetails.description,
                    priority: commentDetails.priority,
                    status: commentDetails.status,
                    requesterName: commentDetails.requesterName,
                    requesterEmail: commentDetails.requesterEmail,
                    assigneeName: commentDetails.assigneeName,
                    assigneeEmail: commentDetails.assigneeEmail,
                    companyName: commentDetails.companyName || 'SASMOS Group',
                    comments: commentDetails.commentText,
                    actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${commentDetails.requestId}`,
                    createdDate: commentDetails.requestCreatedDate,
                    dueDate: commentDetails.dueDate
                };

                EmailService.getInstance().sendChangeRequestCommentAdded(emailData).catch((error: any) => {
                    context.error('Failed to send comment notification email:', error);
                });

                context.log(`Email notification queued for comment ${newCommentId} on change request ${requestId}`);
            } catch (emailError) {
                context.error('Error preparing email notification:', emailError);
                // Don't fail the comment addition if email fails
            }
        }

        context.log(`Successfully added comment ${newCommentId} to change request ${requestId}`);

        return {
            status: 201,
            jsonBody: {
                success: true,
                message: 'Comment added successfully',
                data: commentDetails
            }
        };

    } catch (error: any) {
        context.error('Error in AddChangeRequestComment:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while adding the comment',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('AddChangeRequestComment', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/comments',
    handler: addChangeRequestComment
}); 