<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Falcon Portal - SASMOS Group</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.28.0/feather.min.js"></script>
  <style>
    .section {
      display: none;
    }
    .section.active {
      display: block;
    }
    .dropdown {
      display: none;
    }
    .dropdown.show {
      display: block;
    }
  </style>
</head>
<body class="bg-gray-100">
  <div class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <div id="sidebar" class="bg-gray-800 text-white w-64 transition-all duration-300 flex flex-col">
      <div class="p-4 flex items-center justify-between border-b border-gray-700">
        <div id="brand-title" class="font-bold text-xl">Falcon Portal</div>
        <button id="toggle-sidebar" class="text-gray-300 hover:text-white">
          <i data-feather="menu"></i>
        </button>
      </div>

      <div class="p-4">
        <select id="company-select" class="bg-gray-700 text-white p-2 rounded w-full">
          <option>SASMOS HET</option>
          <option>FESIL</option>
          <option>WestWire Harnesses</option>
          <option>Avirata Defence Systems</option>
          <option>LiDER Technologies</option>
          <option>GloDesi Technologies</option>
          <option>Hanuka</option>
        </select>
      </div>
      
      <nav class="flex-1">
        <button data-section="dashboard" class="nav-link flex items-center p-4 w-full hover:bg-gray-700 bg-blue-600">
          <i data-feather="home"></i>
          <span class="sidebar-text ml-4">Dashboard</span>
        </button>
        <button data-section="knowledge" class="nav-link flex items-center p-4 w-full hover:bg-gray-700">
          <i data-feather="book"></i>
          <span class="sidebar-text ml-4">Knowledge Hub</span>
        </button>
        <button data-section="it" class="nav-link flex items-center p-4 w-full hover:bg-gray-700">
          <i data-feather="monitor"></i>
          <span class="sidebar-text ml-4">IT Hub</span>
        </button>
        <button data-section="hr" class="nav-link flex items-center p-4 w-full hover:bg-gray-700">
          <i data-feather="users"></i>
          <span class="sidebar-text ml-4">HR Hub</span>
        </button>
        <button data-section="admin" class="nav-link flex items-center p-4 w-full hover:bg-gray-700">
          <i data-feather="briefcase"></i>
          <span class="sidebar-text ml-4">Admin Hub</span>
        </button>
        <button data-section="communication" class="nav-link flex items-center p-4 w-full hover:bg-gray-700">
          <i data-feather="message-square"></i>
          <span class="sidebar-text ml-4">Communication Hub</span>
        </button>
      </nav>
      
      <div class="p-4 border-t border-gray-700">
        <div class="flex items-center">
          <div class="bg-gray-500 rounded-full h-8 w-8 flex items-center justify-center mr-2">
            <i data-feather="user" class="text-white" style="width: 16px; height: 16px;"></i>
          </div>
          <div class="sidebar-text">
            <div class="text-sm font-medium">Rohit Kumar</div>
            <div class="text-xs text-gray-400">Project Manager</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Navigation -->
      <header class="bg-white shadow-sm">
        <div class="flex items-center justify-between p-4">
          <div class="flex-1 max-w-xl">
            <div class="relative">
              <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                <i data-feather="search" class="text-gray-400" style="width: 16px; height: 16px;"></i>
              </span>
              <input 
                type="text" 
                placeholder="Search Falcon Portal..." 
                class="w-full py-2 pl-10 pr-4 border rounded-lg"
              >
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
              <button id="notifications-btn" class="text-gray-600 hover:text-gray-800 relative">
                <i data-feather="bell"></i>
                <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              
              <div id="notifications-dropdown" class="dropdown absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-10 border">
                <div class="px-4 py-2 border-b">
                  <div class="text-sm font-medium">Notifications</div>
                </div>
                <div class="max-h-64 overflow-y-auto">
                  <div class="px-4 py-2 hover:bg-gray-50 cursor-pointer">
                    <div class="text-sm">Your travel request has been approved</div>
                    <div class="text-xs text-gray-500">2 hours ago</div>
                  </div>
                  <div class="px-4 py-2 hover:bg-gray-50 cursor-pointer">
                    <div class="text-sm">New policy document requires acknowledgment</div>
                    <div class="text-xs text-gray-500">Yesterday</div>
                  </div>
                  <div class="px-4 py-2 hover:bg-gray-50 cursor-pointer">
                    <div class="text-sm">IT maintenance scheduled for tomorrow</div>
                    <div class="text-xs text-gray-500">2 days ago</div>
                  </div>
                </div>
                <div class="px-4 py-2 border-t text-center">
                  <button class="text-blue-600 text-sm hover:text-blue-800">
                    View All Notifications
                  </button>
                </div>
              </div>
            </div>
            
            <!-- User menu -->
            <div class="relative">
              <button id="user-dropdown-btn" class="flex items-center space-x-2 text-gray-600 hover:text-gray-800">
                <div class="bg-gray-200 rounded-full h-8 w-8 flex items-center justify-center">
                  <i data-feather="user" style="width: 16px; height: 16px;"></i>
                </div>
                <i data-feather="chevron-down" style="width: 16px; height: 16px;"></i>
              </button>
              
              <div id="user-dropdown" class="dropdown absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-10 border">
                <button class="px-4 py-2 hover:bg-gray-50 w-full text-left flex items-center">
                  <i data-feather="user" class="mr-2 text-gray-500" style="width: 16px; height: 16px;"></i>
                  <span>My Profile</span>
                </button>
                <button class="px-4 py-2 hover:bg-gray-50 w-full text-left flex items-center">
                  <i data-feather="settings" class="mr-2 text-gray-500" style="width: 16px; height: 16px;"></i>
                  <span>Settings</span>
                </button>
                <div class="border-t my-1"></div>
                <button class="px-4 py-2 hover:bg-gray-50 w-full text-left flex items-center text-red-600">
                  <i data-feather="log-out" class="mr-2" style="width: 16px; height: 16px;"></i>
                  <span>Sign Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      <!-- Main Content Area -->
      <main class="flex-1 overflow-y-auto">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="section active p-6">
          <h1 class="text-2xl font-bold mb-6">Welcome to Falcon Portal</h1>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Critical Announcements -->
            <div class="bg-white p-4 rounded shadow col-span-3">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">Critical Announcements</h2>
                <span class="text-sm text-blue-600 cursor-pointer">View All</span>
              </div>
              <div class="space-y-3">
                <div class="border-l-4 border-red-500 pl-3 py-2">
                  <div class="font-medium">System Maintenance Notice</div>
                  <div class="text-sm text-gray-600">Scheduled maintenance on May 5th from 10 PM to 2 AM</div>
                  <div class="text-xs text-gray-500 mt-1">Group-wide • 2 hours ago</div>
                </div>
                <div class="border-l-4 border-yellow-500 pl-3 py-2">
                  <div class="font-medium">New Travel Policy Update</div>
                  <div class="text-sm text-gray-600">Important changes to domestic travel approval workflow</div>
                  <div class="text-xs text-gray-500 mt-1"><span class="company-name">SASMOS HET</span> • 1 day ago</div>
                </div>
              </div>
            </div>
            
            <!-- Pending Actions -->
            <div class="bg-white p-4 rounded shadow">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">Pending Actions</h2>
                <span class="text-sm text-blue-600 cursor-pointer">View All</span>
              </div>
              <div class="space-y-3">
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="check-square" class="text-orange-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">Travel Request Approval</div>
                    <div class="text-xs text-gray-500">From: Rahul S. • 3 hours ago</div>
                  </div>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="check-square" class="text-orange-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">Document Review</div>
                    <div class="text-xs text-gray-500">Project: Falcon • Due today</div>
                  </div>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="clock" class="text-blue-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">HR Policy Acknowledgment</div>
                    <div class="text-xs text-gray-500">Code of Conduct 2025 • Due in 2 days</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Quick Links -->
            <div class="bg-white p-4 rounded shadow">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">Quick Links</h2>
                <span class="text-sm text-blue-600 cursor-pointer">Customize</span>
              </div>
              <div class="grid grid-cols-2 gap-2">
                <div class="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer" data-goto="it">
                  <i data-feather="file-text" class="text-blue-500 mb-1" style="width: 24px; height: 24px;"></i>
                  <span class="text-xs text-center">Submit IT Ticket</span>
                </div>
                <div class="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer" data-goto="admin">
                  <i data-feather="calendar" class="text-blue-500 mb-1" style="width: 24px; height: 24px;"></i>
                  <span class="text-xs text-center">Book Meeting Room</span>
                </div>
                <div class="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer" data-goto="admin">
                  <i data-feather="home" class="text-blue-500 mb-1" style="width: 24px; height: 24px;"></i>
                  <span class="text-xs text-center">Request Travel</span>
                </div>
                <div class="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer" data-goto="admin">
                  <i data-feather="shopping-cart" class="text-blue-500 mb-1" style="width: 24px; height: 24px;"></i>
                  <span class="text-xs text-center">Procurement</span>
                </div>
              </div>
            </div>
            
            <!-- Recent Documents -->
            <div class="bg-white p-4 rounded shadow">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">Recent Documents</h2>
                <span class="text-sm text-blue-600 cursor-pointer">View All</span>
              </div>
              <div class="space-y-3">
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="file-text" class="text-gray-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">Q1 Project Report.docx</div>
                    <div class="text-xs text-gray-500">Opened yesterday</div>
                  </div>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="file-text" class="text-gray-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">Travel Expense Policy.pdf</div>
                    <div class="text-xs text-gray-500">Opened 3 days ago</div>
                  </div>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <i data-feather="file-text" class="text-gray-500 mr-2" style="width: 16px; height: 16px;"></i>
                  <div>
                    <div class="text-sm font-medium">Meeting Minutes.docx</div>
                    <div class="text-xs text-gray-500">Opened last week</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Upcoming Events Section -->
          <div class="mt-6 bg-white p-4 rounded shadow">
            <div class="flex justify-between items-center mb-3">
              <h2 class="text-lg font-semibold">Upcoming Events</h2>
              <span class="text-sm text-blue-600 cursor-pointer">View All</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="border rounded p-3 hover:shadow cursor-pointer">
                <div class="font-medium">Company Town Hall</div>
                <div class="text-sm text-gray-600">Apr 25, 2025 • 10:00 AM - 11:30 AM</div>
                <div class="text-xs text-gray-500 mt-1">Virtual • <span class="company-name">SASMOS HET</span></div>
              </div>
              <div class="border rounded p-3 hover:shadow cursor-pointer">
                <div class="font-medium">IT Security Training</div>
                <div class="text-sm text-gray-600">Apr 28, 2025 • 2:00 PM - 4:00 PM</div>
                <div class="text-xs text-gray-500 mt-1">Training Room B • Group-wide</div>
              </div>
              <div class="border rounded p-3 hover:shadow cursor-pointer">
                <div class="font-medium">New Product Launch</div>
                <div class="text-sm text-gray-600">May 5, 2025 • 9:00 AM - 12:00 PM</div>
                <div class="text-xs text-gray-500 mt-1">Main Conference Room • <span class="company-name">SASMOS HET</span></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Knowledge Hub Section -->
        <div id="knowledge-section" class="section p-6">
          <h1 class="text-2xl font-bold mb-6">Knowledge Hub</h1>
          <div class="bg-white p-8 rounded shadow">
            <div class="text-center">
              <i data-feather="book" class="text-blue-500 mb-4" style="width: 48px; height: 48px; margin: 0 auto;"></i>
              <h2 class="text-xl mb-2">Knowledge Hub Content</h2>
              <p class="text-gray-600 mb-4">Document management, knowledge base, and search functionality</p>
              <button class="back-to-dashboard bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
        
        <!-- IT Hub Section -->
        <div id="it-section" class="section p-6">
          <h1 class="text-2xl font-bold mb-6">IT Hub</h1>
          <div class="bg-white p-8 rounded shadow">
            <div class="text-center">
              <i data-feather="monitor" class="text-blue-500 mb-4" style="width: 48px; height: 48px; margin: 0 auto;"></i>
              <h2 class="text-xl mb-2">IT Hub Content</h2>
              <p class="text-gray-600 mb-4">IT service desk, software access, and announcements</p>
              <button class="back-to-dashboard bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
        
        <!-- HR Hub Section -->
        <div id="hr-section" class="section p-6">
          <h1 class="text-2xl font-bold mb-6">HR Hub</h1>
          <div class="bg-white p-8 rounded shadow">
            <div class="text-center">
              <i data-feather="users" class="text-blue-500 mb-4" style="width: 48px; height: 48px; margin: 0 auto;"></i>
              <h2 class="text-xl mb-2">HR Hub Content</h2>
              <p class="text-gray-600 mb-4">Policy repository, PeopleStrong integration, and HR self-service</p>
              <button class="back-to-dashboard bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
        
        <!-- Admin Hub Section -->
        <div id="admin-section" class="section p-6">
          <h1 class="text-2xl font-bold mb-6">Admin Hub</h1>
          <div class="bg-white p-8 rounded shadow">
            <div class="text-center">
              <i data-feather="briefcase" class="text-blue-500 mb-4" style="width: 48px; height: 48px; margin: 0 auto;"></i>
              <h2 class="text-xl mb-2">Admin Hub Content</h2>
              <p class="text-gray-600 mb-4">Travel management, hospitality services, transportation, facility management, and procurement</p>
              <button class="back-to-dashboard bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
        
        <!-- Communication Hub Section -->
        <div id="communication-section" class="section p-6">
          <h1 class="text-2xl font-bold mb-6">Communication Hub</h1>
          <div class="bg-white p-8 rounded shadow">
            <div class="text-center">
              <i data-feather="message-square" class="text-blue-500 mb-4" style="width: 48px; height: 48px; margin: 0 auto;"></i>
              <h2 class="text-xl mb-2">Communication Hub Content</h2>
              <p class="text-gray-600 mb-4">Announcement system, events calendar, and customer interaction showcase</p>
              <button class="back-to-dashboard bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    // Initialize Feather icons
    document.addEventListener('DOMContentLoaded', function() {
      feather.replace();
      
      // DOM Elements
      const sidebar = document.getElementById('sidebar');
      const brandTitle = document.getElementById('brand-title');
      const sidebarTexts = document.querySelectorAll('.sidebar-text');
      const toggleSidebarBtn = document.getElementById('toggle-sidebar');
      const notificationsBtn = document.getElementById('notifications-btn');
      const notificationsDropdown = document.getElementById('notifications-dropdown');
      const userDropdownBtn = document.getElementById('user-dropdown-btn');
      const userDropdown = document.getElementById('user-dropdown');
      const navLinks = document.querySelectorAll('.nav-link');
      const sections = document.querySelectorAll('.section');
      const companySelect = document.getElementById('company-select');
      const companyNames = document.querySelectorAll('.company-name');
      const quickLinks = document.querySelectorAll('[data-goto]');
      const backToDashboardBtns = document.querySelectorAll('.back-to-dashboard');
      
      // Toggle sidebar
      toggleSidebarBtn.addEventListener('click', function() {
        if (sidebar.classList.contains('w-64')) {
          sidebar.classList.remove('w-64');
          sidebar.classList.add('w-20');
          brandTitle.style.display = 'none';
          sidebarTexts.forEach(text => text.style.display = 'none');
        } else {
          sidebar.classList.remove('w-20');
          sidebar.classList.add('w-64');
          brandTitle.style.display = 'block';
          sidebarTexts.forEach(text => text.style.display = 'block');
        }
      });
      
      // Toggle notifications dropdown
      notificationsBtn.addEventListener('click', function() {
        notificationsDropdown.classList.toggle('show');
        userDropdown.classList.remove('show');
      });
      
      // Toggle user dropdown
      userDropdownBtn.addEventListener('click', function() {
        userDropdown.classList.toggle('show');
        notificationsDropdown.classList.remove('show');
      });
      
      // Close dropdowns when clicking elsewhere
      document.addEventListener('click', function(event) {
        if (!event.target.closest('#notifications-btn') && !event.target.closest('#notifications-dropdown')) {
          notificationsDropdown.classList.remove('show');
        }
        
        if (!event.target.closest('#user-dropdown-btn') && !event.target.closest('#user-dropdown')) {
          userDropdown.classList.remove('show');
        }
      });
      
      // Navigation
      navLinks.forEach(navLink => {
        navLink.addEventListener('click', function() {
          const targetSection = this.getAttribute('data-section');
          
          // Update active state for nav links
          navLinks.forEach(link => {
            link.classList.remove('bg-blue-600');
          });
          this.classList.add('bg-blue-600');
          
          // Show appropriate section
          sections.forEach(section => {
            section.classList.remove('active');
            if (section.id === targetSection + '-section') {
              section.classList.add('active');
            }
          });
        });
      });
      
      // Company selection
      companySelect.addEventListener('change', function() {
        const selectedCompany = this.value;
        companyNames.forEach(name => {
          name.textContent = selectedCompany;
        });
      });
      
      // Quick links navigation
      quickLinks.forEach(link => {
        link.addEventListener('click', function() {
          const targetSection = this.getAttribute('data-goto');
          
          // Update active state for nav links
          navLinks.forEach(navLink => {
            navLink.classList.remove('bg-blue-600');
            if (navLink.getAttribute('data-section') === targetSection) {
              navLink.classList.add('bg-blue-600');
            }
          });
          
          // Show appropriate section
          sections.forEach(section => {
            section.classList.remove('active');
            if (section.id === targetSection + '-section') {
              section.classList.add('active');
            }
          });
        });
      });
      
      // Back to dashboard buttons
      backToDashboardBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          // Update active state for nav links
          navLinks.forEach(navLink => {
            navLink.classList.remove('bg-blue-600');
            if (navLink.getAttribute('data-section') === 'dashboard') {
              navLink.classList.add('bg-blue-600');
            }
          });
          
          // Show dashboard section
          sections.forEach(section => {
            section.classList.remove('active');
            if (section.id === 'dashboard-section') {
              section.classList.add('active');
            }
          });
        });
      });
    });
  </script>
</body>
</html>