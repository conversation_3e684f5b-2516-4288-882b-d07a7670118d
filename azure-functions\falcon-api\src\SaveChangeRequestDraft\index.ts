import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';
import { addCorsHeaders } from '../shared/cors';

interface ContentBlock {
  id: string;
  type: 'paragraph' | 'heading1' | 'heading2' | 'heading3' | 'list' | 'code' | 'image';
  content: string;
  imageUrl?: string;
  imageCaption?: string;
  imageAlt?: string;
  listItems?: string[];
}

interface SaveDraftData {
  draftId?: string; // If updating existing draft
  title: string;
  description: string;
  typeId: number;
  priority: string;
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  requestedBy: number;
  richContent: ContentBlock[];
}

export async function SaveChangeRequestDraft(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  context.log('SaveChangeRequestDraft function processed a request.');

  // <PERSON>le preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      body: ''
    });
  }

  try {
    // Parse request body
    const requestBody = await request.text();
    if (!requestBody) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { 
          success: false, 
          message: 'Request body is required' 
        }
      });
    }

    const draftData: SaveDraftData = JSON.parse(requestBody);

    // Validate required fields for draft
    if (!draftData.title?.trim()) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { 
          success: false, 
          message: 'Title is required for draft' 
        }
      });
    }

    // Get database connection
    const pool: ConnectionPool = await getPool();

    let draftId: string;
    const now = new Date();

    if (draftData.draftId) {
      // Update existing draft
      const updateDraftQuery = `
        UPDATE ChangeRequestDrafts 
        SET 
          Title = @title,
          Description = @description,
          TypeID = @typeId,
          Priority = @priority,
          BusinessJustification = @businessJustification,
          ExpectedBenefit = @expectedBenefit,
          RequestedCompletionDate = @requestedCompletionDate,
          RichContent = @richContent,
          LastModified = @lastModified
        WHERE DraftID = @draftId AND RequestedBy = @requestedBy
      `;

      await pool.request()
        .input('draftId', draftData.draftId)
        .input('title', draftData.title)
        .input('description', draftData.description)
        .input('typeId', draftData.typeId)
        .input('priority', draftData.priority)
        .input('businessJustification', draftData.businessJustification || null)
        .input('expectedBenefit', draftData.expectedBenefit || null)
        .input('requestedCompletionDate', draftData.requestedCompletionDate || null)
        .input('richContent', JSON.stringify(draftData.richContent))
        .input('requestedBy', draftData.requestedBy)
        .input('lastModified', now)
        .query(updateDraftQuery);

      draftId = draftData.draftId;
    } else {
      // Create new draft
      draftId = generateDraftId();
      
      const insertDraftQuery = `
        INSERT INTO ChangeRequestDrafts (
          DraftID, Title, Description, TypeID, Priority, 
          BusinessJustification, ExpectedBenefit, RequestedCompletionDate,
          RichContent, RequestedBy, CreatedDate, LastModified
        ) VALUES (
          @draftId, @title, @description, @typeId, @priority,
          @businessJustification, @expectedBenefit, @requestedCompletionDate,
          @richContent, @requestedBy, @createdDate, @lastModified
        )
      `;

      await pool.request()
        .input('draftId', draftId)
        .input('title', draftData.title)
        .input('description', draftData.description)
        .input('typeId', draftData.typeId)
        .input('priority', draftData.priority)
        .input('businessJustification', draftData.businessJustification || null)
        .input('expectedBenefit', draftData.expectedBenefit || null)
        .input('requestedCompletionDate', draftData.requestedCompletionDate || null)
        .input('richContent', JSON.stringify(draftData.richContent))
        .input('requestedBy', draftData.requestedBy)
        .input('createdDate', now)
        .input('lastModified', now)
        .query(insertDraftQuery);
    }

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        message: 'Draft saved successfully',
        draftId: draftId,
        lastModified: now.toISOString()
      }
    });

  } catch (error) {
    context.log('Error saving draft:', error);
    
    return addCorsHeaders({
      status: 500,
      jsonBody: {
        success: false,
        message: 'Internal server error while saving draft',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });
  }
}

function generateDraftId(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substr(2, 5);
  return `DRAFT-${timestamp}-${randomStr}`.toUpperCase();
}

app.http('SaveChangeRequestDraft', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  handler: SaveChangeRequestDraft
}); 