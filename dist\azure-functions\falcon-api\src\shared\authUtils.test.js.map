{"version": 3, "file": "authUtils.test.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/shared/authUtils.test.ts"], "names": [], "mappings": ";;;;;;;;;;;AACA,2CAA2G;AAC3G,qCAA4C;AAC5C,oFAA+E;AAC/E,mDAAgD;AAEhD,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC1B,IAAI,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;AACtD,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;AAEpC,oDAAoD;AACpD,MAAM,WAAW,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAkC,CAAC;AAEvE,sCAAsC;AACtC,MAAM,iBAAiB,GAAG,CAAC,oBAAmC,EAAe,EAAE;IAC3E,MAAM,OAAO,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC1C,IAAI,oBAAoB,KAAK,IAAI,EAAE,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,oBAAoB,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO;QACH,OAAO,EAAE;YACL,GAAG,EAAE,CAAC,GAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI;SACjD;QACD,yEAAyE;KAClD,CAAC;AAChC,CAAC,CAAC;AAEF,4EAA4E;AAC5E,MAAM,eAAe,GAAG,CAAC,SAA0B,EAAU,EAAE;IAC3D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACrE,CAAC,CAAC;AAEF,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAExB,+BAA+B;IAC/B,UAAU,CAAC,GAAG,EAAE;QACZ,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,uCAAuC;IACvC,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YAC/C,MAAM,GAAG,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,2DAA2D,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACpD,MAAM,GAAG,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,CAAC,eAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,6CAA6C,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACzI,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACrD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,CAAC,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,sFAAsF,CAAC,CAAC;QACtI,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC7D,MAAM,aAAa,GAAoB;gBACnC,gBAAgB,EAAE,KAAK;gBACvB,MAAM,EAAE,cAAc;gBACtB,WAAW,EAAE,WAAW;gBACxB,SAAS,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC;gBACrC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;aAC5C,CAAC;YACF,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;YACrD,MAAM,GAAG,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC7B,MAAM,iBAAiB,GAAG,CAAC,cAAc,CAAC,CAAC;QAC3C,MAAM,gBAAgB,GAAG,CAAC,UAAU,CAAC,CAAC;QAEtC,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,IAAA,2BAAe,EAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC5D,MAAM,SAAS,GAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC,EAAE,CAAC;YAClJ,MAAM,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gEAAgE,EAAE,GAAG,EAAE;YACtE,MAAM,SAAS,GAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;YACnL,MAAM,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEF,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACxE,MAAM,SAAS,GAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;YACpL,MAAM,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACnE,MAAM,SAAS,GAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,CAAC;YAC9I,MAAM,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACrE,MAAM,SAAS,GAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE,CAAC;YAC9I,MAAM,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IAEH,2CAA2C;IAC3C,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACpC,MAAM,aAAa,GAAoB;YACnC,gBAAgB,EAAE,KAAK;YACvB,MAAM,EAAE,cAAc;YACtB,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;YACxC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,+DAA+D,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC;SAC3G,CAAC;QAEF,EAAE,CAAC,yCAAyC,EAAE,GAAS,EAAE;YACrD,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAChF,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAS,EAAE;YACxD,MAAM,cAAc,mCAAyB,aAAa,KAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,GAAE,CAAC;YAC1G,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,cAAc,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtF,MAAM,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,mDAAmD,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/I,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAS,EAAE;YAChE,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC;YACrC,iBAA0B,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrF,MAAM,CAAC,eAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,EAAE,OAAO,CAAC,CAAC;QACzG,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAS,EAAE;YAC1D,iBAA0B,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;YACjE,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrF,MAAM,CAAC,eAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,CAAC,CAAC,CAAC;QAChH,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAS,EAAE;YAChF,MAAM,UAAU,GAAG,GAAG,CAAC;YACtB,iBAA0B,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;YACtF,2CAAiC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,iCAAiC;YAE7F,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3F,MAAM,CAAC,iBAAY,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YAC5F,qFAAqF;YACrF,8FAA8F;YAC9F,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,mDAAmD;YACxF,MAAM,CAAC,2CAAmB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACjE,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,qEAAqE,EAAE,GAAS,EAAE;YACjF,MAAM,UAAU,GAAG,GAAG,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACnD,iBAA0B,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;YACtF,2CAAiC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,iCAAiC;YAEnG,MAAM,MAAM,CAAC,IAAA,kCAAsB,EAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3F,MAAM,CAAC,iBAAY,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YAE5F,0CAA0C;YAC1C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpC,MAAM,CAAC,2CAAmB,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,eAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,EAAE,UAAU,CAAC,CAAC;QACrH,CAAC,CAAA,CAAC,CAAC;IAEP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}