import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, User, Clock, CornerDownLeft } from 'feather-icons-react';
import { changeManagementApi, type ChangeRequestComment } from '../../services/changeManagementApi';

interface ChangeRequestCommentsProps {
  requestId: number;
}

const ChangeRequestComments: React.FC<ChangeRequestCommentsProps> = ({ requestId }) => {
  const [comments, setComments] = useState<ChangeRequestComment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState<'General' | 'Question' | 'DevUpdate'>('General');
  const [isInternal, setIsInternal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState('');

  // TODO: Get user ID from auth context when available

  useEffect(() => {
    loadComments();
  }, [requestId]);

  const loadComments = async () => {
    try {
      setLoading(true);
      setError(null);
      const commentsData = await changeManagementApi.getComments(requestId);
      // Ensure commentsData is always an array
      setComments(Array.isArray(commentsData) ? commentsData : []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load comments');
      console.error('Error loading comments:', err);
      // Set empty array on error to prevent reduce issues
      setComments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newComment.trim()) return;

    try {
      setSubmitting(true);
      const addedComment = await changeManagementApi.addComment(
        requestId,
        newComment.trim(),
        commentType,
        isInternal
      );
      
      setComments(prev => [...prev, addedComment]);
      setNewComment('');
      setCommentType('General');
      setIsInternal(false);
    } catch (err) {
      console.error('Error adding comment:', err);
      setError(err instanceof Error ? err.message : 'Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentCommentId: number) => {
    if (!replyText.trim()) return;

    try {
      setSubmitting(true);
      const addedComment = await changeManagementApi.addComment(
        requestId,
        replyText.trim(),
        'Answer',
        false,
        parentCommentId
      );
      
      setComments(prev => [...prev, addedComment]);
      setReplyText('');
      setReplyingTo(null);
    } catch (err) {
      console.error('Error adding reply:', err);
      setError(err instanceof Error ? err.message : 'Failed to add reply');
    } finally {
      setSubmitting(false);
    }
  };

  const getCommentTypeColor = (type: string, commentText: string) => {
    // Check if this is an information request based on comment text prefix
    if (type === 'ApprovalNote' && commentText.startsWith('[INFO_REQUEST]')) {
      return 'bg-red-100 text-red-800';
    }
    
    // Check if this is a rejection based on comment text prefix
    if (type === 'ApprovalNote' && commentText.startsWith('[REJECTION]')) {
      return 'bg-red-100 text-red-800';
    }
    
    switch (type) {
      case 'ApprovalNote': return 'bg-green-100 text-green-800';
      case 'InfoRequest': return 'bg-red-100 text-red-800';
      case 'DevUpdate': return 'bg-blue-100 text-blue-800';
      case 'Question': return 'bg-yellow-100 text-yellow-800';
      case 'Answer': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCommentTypeLabel = (type: string, commentText: string) => {
    // Check if this is an information request based on comment text prefix
    if (type === 'ApprovalNote' && commentText.startsWith('[INFO_REQUEST]')) {
      return 'Information Requested';
    }
    
    // Check if this is a rejection based on comment text prefix
    if (type === 'ApprovalNote' && commentText.startsWith('[REJECTION]')) {
      return 'Rejected';
    }
    
    switch (type) {
      case 'ApprovalNote': return 'Approval Note';
      case 'InfoRequest': return 'Information Requested';
      case 'DevUpdate': return 'Development Update';
      case 'Question': return 'Question';
      case 'Answer': return 'Answer';
      default: return 'General';
    }
  };

  const getDisplayText = (commentText: string) => {
    // Remove the [INFO_REQUEST] prefix for display
    if (commentText.startsWith('[INFO_REQUEST]')) {
      return commentText.substring('[INFO_REQUEST] '.length);
    }
    
    // Remove the [REJECTION] prefix for display
    if (commentText.startsWith('[REJECTION]')) {
      return commentText.substring('[REJECTION] '.length);
    }
    
    return commentText;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  // Group comments by thread (parent comments and their replies)
  const groupedComments = comments.reduce((acc, comment) => {
    if (!comment.parentCommentId) {
      // This is a parent comment
      acc.push({
        parent: comment,
        replies: comments.filter(c => c.parentCommentId === comment.commentId)
      });
    }
    return acc;
  }, [] as Array<{ parent: ChangeRequestComment; replies: ChangeRequestComment[] }>);

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <MessageCircle size={20} className="text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Comments & Communication</h2>
        </div>
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading comments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center space-x-3 mb-6">
        <MessageCircle size={20} className="text-gray-600" />
        <h2 className="text-lg font-semibold text-gray-900">Comments & Communication</h2>
        <span className="text-sm text-gray-500">({comments.length})</span>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Add Comment Form */}
      <form onSubmit={handleSubmitComment} className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="space-y-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Comment Type
              </label>
              <select
                value={commentType}
                onChange={(e) => setCommentType(e.target.value as 'General' | 'Question' | 'DevUpdate')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              >
                <option value="General">General Comment</option>
                <option value="Question">Question</option>
                <option value="DevUpdate">Development Update</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2 pt-6">
              <input
                type="checkbox"
                id="internal-comment"
                checked={isInternal}
                onChange={(e) => setIsInternal(e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="internal-comment" className="text-sm text-gray-700">
                Internal only
              </label>
            </div>
          </div>

          <div>
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Add your comment..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              required
            />
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={submitting || !newComment.trim()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send size={16} className="mr-2" />
              {submitting ? 'Posting...' : 'Post Comment'}
            </button>
          </div>
        </div>
      </form>

      {/* Comments List */}
      <div className="space-y-4">
        {groupedComments.length === 0 ? (
          <div className="text-center py-8">
            <MessageCircle size={48} className="mx-auto text-gray-300 mb-4" />
            <p className="text-gray-500">No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          groupedComments.map(({ parent, replies }) => (
            <div key={parent.commentId} className="space-y-3">
              {/* Parent Comment */}
              <div className="flex space-x-3 p-4 bg-white border border-gray-200 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <User size={16} className="text-gray-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <p className="text-sm font-medium text-gray-900">
                      {parent.createdByName}
                    </p>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCommentTypeColor(parent.commentType, parent.commentText)}`}>
                      {getCommentTypeLabel(parent.commentType, parent.commentText)}
                    </span>
                    {parent.isInternal && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Internal
                      </span>
                    )}
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Clock size={12} />
                      <span>{formatDate(parent.createdDate)}</span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700 whitespace-pre-wrap mb-3">
                    {getDisplayText(parent.commentText)}
                  </p>
                  
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => setReplyingTo(replyingTo === parent.commentId ? null : parent.commentId)}
                      className="text-xs text-indigo-600 hover:text-indigo-800 flex items-center space-x-1"
                    >
                      <CornerDownLeft size={12} />
                      <span>Reply</span>
                    </button>
                  </div>
                  
                  {/* Reply Form */}
                  {replyingTo === parent.commentId && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <textarea
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                        placeholder="Write your reply..."
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                      />
                      <div className="flex justify-end space-x-2 mt-2">
                        <button
                          type="button"
                          onClick={() => {
                            setReplyingTo(null);
                            setReplyText('');
                          }}
                          className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={() => handleSubmitReply(parent.commentId)}
                          disabled={submitting || !replyText.trim()}
                          className="px-3 py-1 text-xs text-white bg-indigo-600 hover:bg-indigo-700 rounded disabled:opacity-50"
                        >
                          {submitting ? 'Posting...' : 'Reply'}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Replies */}
              {replies.length > 0 && (
                <div className="ml-8 space-y-3">
                  {replies.map((reply) => (
                    <div key={reply.commentId} className="flex space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                          <User size={12} className="text-gray-600" />
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <p className="text-xs font-medium text-gray-900">
                            {reply.createdByName}
                          </p>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock size={10} />
                            <span>{formatDate(reply.createdDate)}</span>
                          </div>
                        </div>
                        
                        <p className="text-xs text-gray-700 whitespace-pre-wrap">
                          {reply.commentText}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ChangeRequestComments; 