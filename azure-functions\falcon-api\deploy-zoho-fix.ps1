# Deploy ZohoDesk Critical Bug Fix
# This script builds and deploys the fixed ZohoDesk integration

Write-Host "🔧 Deploying ZohoDesk Critical Bug Fix..." -ForegroundColor Yellow

# Step 1: Build the project
Write-Host "📦 Building Azure Functions..." -ForegroundColor Cyan
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build successful!" -ForegroundColor Green

# Step 2: Deploy to Azure
Write-Host "🚀 Deploying to Azure Functions..." -ForegroundColor Cyan

# Check if Azure CLI is logged in
$account = az account show 2>$null
if (-not $account) {
    Write-Host "❌ Not logged into Azure CLI. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Deploy using Azure Functions Core Tools
func azure functionapp publish fp-func-falcon-dev-cin-001

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Deployment failed!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Deployment successful!" -ForegroundColor Green

# Step 3: Test the fix
Write-Host "🧪 Testing the deployment..." -ForegroundColor Cyan

# Wait a moment for deployment to propagate
Start-Sleep -Seconds 10

# Test the simple endpoint first
Write-Host "Testing simple endpoint..."
$simpleTest = curl -s "https://fp-func-falcon-dev-cin-001.azurewebsites.net/api/simple-test"
if ($simpleTest -match "Hello from SimpleTest") {
    Write-Host "✅ Simple test passed" -ForegroundColor Green
} else {
    Write-Host "❌ Simple test failed" -ForegroundColor Red
}

# Test the new health check endpoint
Write-Host "Testing token health check endpoint..."
$healthCheck = curl -s "https://fp-func-falcon-dev-cin-001.azurewebsites.net/api/admin/token-health"
if ($healthCheck -match "overallStatus") {
    Write-Host "✅ Token health check endpoint is working" -ForegroundColor Green
} else {
    Write-Host "⚠️  Token health check endpoint not yet available (may need a few minutes)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 ZohoDesk Bug Fix Deployment Complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was fixed:" -ForegroundColor Cyan
Write-Host "   • Critical timestamp calculation bug in token refresh logic" -ForegroundColor White
Write-Host "   • Token expiry detection now uses database timestamps directly" -ForegroundColor White
Write-Host "   • Added proactive token refresh scheduler (runs every 30 minutes)" -ForegroundColor White
Write-Host "   • Added token health monitoring endpoint" -ForegroundColor White
Write-Host ""
Write-Host "🔍 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Test ZohoDesk integration in the frontend" -ForegroundColor White
Write-Host "   2. Monitor token health at: /api/admin/token-health" -ForegroundColor White
Write-Host "   3. Check Azure Functions logs for scheduler activity" -ForegroundColor White
Write-Host ""
Write-Host "⏰ The token refresh scheduler will run automatically every 30 minutes" -ForegroundColor Green
Write-Host "   to prevent future connection losses." -ForegroundColor Green
