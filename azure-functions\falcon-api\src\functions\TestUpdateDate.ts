import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";

export async function testUpdateDate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('TestUpdateDate function invoked.');
    
    return {
        status: 200,
        jsonBody: {
            message: 'Test endpoint is working!',
            timestamp: new Date().toISOString()
        }
    };
}

app.http('TestUpdateDate', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'test/update-date',
    handler: testUpdateDate
}); 