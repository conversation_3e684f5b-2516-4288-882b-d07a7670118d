import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { fetchPortalUser, updatePortalUser, fetchPortalRoleNames, PortalUser, PORTAL_STATUSES } from '../services/adminApi';
import { ArrowLeft, Save } from 'feather-icons-react';
import toast from 'react-hot-toast';

const UserEditPage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();

  // State for user data, form data (roles/status), loading/saving, errors, roles
  const [user, setUser] = useState<PortalUser | null>(null);
  const [formData, setFormData] = useState<Pick<PortalUser, 'roles' | 'status'>>({ roles: [], status: 'Inactive' });
  const [availableRoles, setAvailableRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch user details and available roles
  useEffect(() => {
    const loadData = async () => {
      if (!userId) {
        setError('User ID is missing.');
        toast.error('User ID is missing from the URL.');
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        // Fetch both user details and available roles concurrently
        const [fetchedUser, fetchedRoles] = await Promise.all([
          fetchPortalUser(userId),
          fetchPortalRoleNames()
        ]);

        if (fetchedUser) {
          setUser(fetchedUser);
          // Initialize form only with editable fields (roles, status)
          setFormData({ roles: fetchedUser.roles, status: fetchedUser.status });
        } else {
          const notFoundMsg = 'User not found in portal management.';
          setError(notFoundMsg);
          toast.error(notFoundMsg);
          // Consider navigating back if user not found
          // setTimeout(() => navigate('/admin/users'), 2000);
        }
        setAvailableRoles(fetchedRoles);
      } catch (err) {
        console.error("Error loading user data or roles:", err);
        const loadErrorMsg = 'Failed to load user details or roles.';
        setError(loadErrorMsg);
        toast.error(loadErrorMsg);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [userId, navigate]);

  // Handle status change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData(prev => ({ ...prev, status: e.target.value as PortalUser['status'] }));
  };

  // Handle role changes (multi-select)
  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedRoles = Array.from(e.target.selectedOptions, option => option.value);
    setFormData(prev => ({ ...prev, roles: selectedRoles.length > 0 ? selectedRoles : ['User'] }));
  };

  // Handle form submission
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId) return;

    setIsSaving(true);
    const toastId = toast.loading('Saving changes...');

    try {
      const updatedUser = await updatePortalUser(userId, formData);
      if (updatedUser) {
        setUser(updatedUser); // Update local display
        setFormData({ roles: updatedUser.roles, status: updatedUser.status }); // Resync form
        toast.success('User roles/status updated successfully!', { id: toastId });
      } else {
        toast.error('Failed to save: User not found.', { id: toastId });
        setError('Failed to save user. User might not exist anymore.');
      }
    } catch (err) {
      console.error("Error saving user:", err);
      let message = 'An error occurred while saving.';
      if (err instanceof Error) message = err.message;
      toast.error(message, { id: toastId });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center mb-4">
        <button
          onClick={() => navigate('/admin/user-management')} // Navigate back to the main list
          className="mr-3 p-1 rounded-full hover:bg-gray-200 transition-colors"
          aria-label="Back to user management list"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-2xl font-semibold text-gray-800">Manage User Roles & Status</h1>
      </div>

      {loading && <p className="text-center text-gray-500">Loading user details...</p>}
      {error && !loading && <p className="text-center text-red-600 bg-red-100 p-3 rounded-md border border-red-300 mb-4">Error: {error}</p>}

      {!loading && user && (
        <form onSubmit={handleSave} className="bg-white shadow rounded-lg p-6 space-y-6">
          {/* Display User Info (Readonly) */}
          <div className="mb-6 pb-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-2">{user.name}</h2>
            <p className="text-sm text-gray-500">{user.email}</p>
            <p className="text-sm text-gray-500">Company: {user.company}</p>
          </div>

          {/* Status Select */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Portal Status</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleStatusChange}
              required
              className="w-full md:w-1/2 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white"
            >
              {PORTAL_STATUSES.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
             <p className="text-xs text-gray-500 mt-1">Controls if the user is active or inactive within the portal.</p>
          </div>

          {/* Roles Multi-Select */}
          <div>
            <label htmlFor="roles" className="block text-sm font-medium text-gray-700 mb-1">Assign Portal Roles</label>
            <select
              id="roles"
              name="roles"
              multiple
              value={formData.roles}
              onChange={handleRoleChange}
              required // Must assign at least one role
              className="w-full md:w-1/2 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 h-40 bg-white"
            >
              {availableRoles.map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">Hold Ctrl (or Cmd on Mac) to select multiple roles. Determines user permissions within the portal.</p>
          </div>

          {/* Save Button */} 
          <div className="flex justify-start pt-4 border-t border-gray-200">
            <button
              type="submit"
              disabled={isSaving || loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${isSaving ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'}`}
            >
              <Save size={16} className={`mr-2 ${isSaving ? 'animate-spin' : ''}`} />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      )}

      {!loading && !user && !error && (
           <p className="text-center text-gray-500">User could not be found or loaded.</p>
      )}
    </div>
  );
};

export default UserEditPage; 