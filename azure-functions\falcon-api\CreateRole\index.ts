import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { CreateRoleRequestBody, RoleDefinition } from "../shared/interfaces";

export async function createRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}"`);

    let body: any;
    try {
        body = await request.json();
        context.log("Received request body:", JSON.stringify(body));
    } catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }

    // Handle multiple naming conventions: roleName (frontend), RoleName (backend), name (generic)
    const roleName = body.roleName || body.RoleName || body.name;
    const description = body.description || body.Description;

    if (!roleName || !roleName.trim()) {
        return {
            status: 400,
            jsonBody: { message: "Role name is required." }
        };
    }

    try {
        // Use the variables we defined above
        const roleNameTrimmed = roleName.trim();
        const descriptionTrimmed = description?.trim() || null;
        const createdBy = 1; // Placeholder

        const checkResult = await executeQuery('SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName)', { RoleName: roleNameTrimmed });
        if (checkResult.recordset[0].Count > 0) {
            return {
                status: 409,
                jsonBody: { message: `Role with name '${roleNameTrimmed}' already exists.` }
            };
        }

        // The column in the database is RoleDescription, not Description
        const query = `
            INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate)
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription as Description, INSERTED.IsSystemRole, INSERTED.IsActive
            VALUES (@RoleName, @RoleDescription, 0, 1, @CreatedBy, GETUTCDATE())
        `;
        const params = { RoleName: roleNameTrimmed, RoleDescription: descriptionTrimmed, CreatedBy: createdBy };

        const result = await executeQuery(query, params);

        // Map the database result to the format expected by the frontend
        const newRole = {
            id: result.recordset[0].RoleID.toString(),
            name: result.recordset[0].RoleName,
            description: result.recordset[0].Description
        };

        context.log("Created new role:", JSON.stringify(newRole));

        return {
            status: 201,
            jsonBody: newRole
        };

    } catch (error) {
        context.error(`Error creating role: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: "Error creating role.",
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}

app.http('CreateRole', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'roles',
    handler: createRole
});