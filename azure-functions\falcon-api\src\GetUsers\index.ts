import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import * as sql from 'mssql'; // Import mssql for types
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal } from "../shared/authUtils"; // Only need getClientPrincipal
// Import validation function and schema
import { validateRequest, listUsersQuerySchema } from "../shared/validationSchemas";
// import { listUsersQuerySchema } from "../shared/validationSchemas";

// Interface matching frontend adminApi.ts PortalUser
interface PortalUser {
    id: number; // UserID
    entraId: string; // EntraID
    name: string; // Combined First + Last Name
    email: string;
    company: string; // CompanyName
    companyId: number; // CompanyID
    department?: string; // DepartmentName
    roles: string[]; // Role Names
    status: 'Active' | 'Inactive'; // Based on IsActive
    // lastLogin?: string; // TODO: Decide if needed/source
}

// Interface matching frontend adminApi.ts PaginatedPortalUsersResponse
interface PaginatedPortalUsersResponse {
    users: PortalUser[];
    totalCount: number;
    pageSize: number;
    totalPages: number;
}

export async function getUsers(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function GetUsers processed request for url "${request.url}"`);
    logger.info('GetUsers function invoked.');

    // --- Authentication Check --- 
    const principal = getClientPrincipal(request);
    if (!principal) {
        // Require authentication to view the user list
         if (request.headers.get('x-ms-client-principal')) {
             logger.warn('GetUsers: Invalid or missing client principal header despite presence.');
             return { status: 401, jsonBody: { message: "Unauthorized. Invalid client principal." } };
        } else {
             logger.warn('GetUsers: Client principal header missing.');
            return { status: 401, jsonBody: { message: "Unauthorized." } };
        }
    }
     // No specific role check needed, any authenticated user can view the list?
    // TODO: Implement company filtering based on principal claims if needed
    // --- End Auth --- 

    // --- Input Validation --- 
    const queryParams = {
        search: request.query.get('search'),
        company: request.query.get('company'),
        role: request.query.get('role'),
        status: request.query.get('status'),
        page: request.query.get('page'),
        pageSize: request.query.get('pageSize'),
    };
    
    const validationError = validateRequest(listUsersQuerySchema, queryParams, context, "query parameters");
    if (validationError) return validationError;
    
    // Validation passed, use validated data (Zod handles defaults/preprocessing)
    const validatedQueryParams = listUsersQuerySchema.parse(queryParams); // Use parse to get typed data
    const searchTerm = validatedQueryParams.search || '';
    const companyFilter = validatedQueryParams.company ?? 'All';
    const roleFilter = validatedQueryParams.role ?? 'All';
    const statusFilter = validatedQueryParams.status ?? 'All';
    const page = validatedQueryParams.page ?? 1;
    const pageSize = validatedQueryParams.pageSize ?? 10;

    logger.info(`GetPortalUsers: Received parameters - page: ${page}, pageSize: ${pageSize}, searchTerm: ${searchTerm}, companyFilter: ${companyFilter}, roleFilter: ${roleFilter}, statusFilter: ${statusFilter}`);

    // Manual validation REMOVED
    // --- End Validation --- 

    // --- Build Dynamic Query --- 
    const offset = (page - 1) * pageSize;

    // Initialize parameter array and clause arrays *before* adding filters
    const params: QueryParameter[] = []; 
    const whereClauses: string[] = [];
    const havingClauses: string[] = []; 

    const baseQuery = `
        FROM Users u
        LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
        LEFT JOIN Departments d ON u.DepartmentID = d.DepartmentID AND d.IsActive = 1
        -- Subquery to get role names for filtering/aggregation
        LEFT JOIN (
            SELECT ur_agg.UserID, STRING_AGG(r_agg.RoleName, ',') WITHIN GROUP (ORDER BY r_agg.RoleName) AS RoleNames
            FROM UserRoles ur_agg
            JOIN Roles r_agg ON ur_agg.RoleID = r_agg.RoleID AND r_agg.IsActive = 1
            WHERE ur_agg.IsActive = 1
            GROUP BY ur_agg.UserID
        ) UserRoleInfo ON u.UserID = UserRoleInfo.UserID
    `;

    if (searchTerm) {
        whereClauses.push(`(u.FirstName LIKE @searchTerm OR u.LastName LIKE @searchTerm OR u.Email LIKE @searchTerm OR u.Username LIKE @searchTerm)`);
        params.push({ name: 'searchTerm', type: sql.NVarChar, value: `%${searchTerm}%` });
    }
    if (companyFilter && companyFilter !== 'All') {
        whereClauses.push(`c.CompanyName = @companyFilter`);
        params.push({ name: 'companyFilter', type: sql.NVarChar, value: companyFilter });
    }
    if (statusFilter === 'Active') {
        whereClauses.push(`u.IsActive = 1`);
    } else if (statusFilter === 'Inactive') {
        whereClauses.push(`u.IsActive = 0`);
    }

    // Role Filter (needs subquery or HAVING clause after grouping)
    // Using HAVING for simplicity here
    let havingClause = '';
    if (roleFilter && roleFilter !== 'All') {
        // This checks if the aggregated list of roles contains the specific role
        havingClauses.push(`UserRoleInfo.RoleNames LIKE @roleNameFilter`); 
        // We need the Role Name, not the ID, for this filter
        // Let's assume we fetch the role name based on roleId earlier or pass it in
        // For now, placeholder - needs adjustment based on actual role filtering requirements
        // If filtering by RoleID is desired, the query structure needs change
        // For now, commenting out role filtering to avoid complexity/errors
        // params.push({ name: 'roleNameFilter', type: sql.NVarChar, value: `%${roleName}%` }); // Placeholder roleName needed
        logger.warn("Role filtering in GetUsers/index.ts (legacy) is currently disabled due to query complexity.");
    }

    const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    // Count Query
    const simplerCountQuery = `SELECT COUNT(DISTINCT u.UserID) as TotalCount ${baseQuery} ${whereSql}`;

    // Data Query
    const dataQuery = `
        SELECT 
            u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
            c.CompanyID, c.CompanyName,
            d.DepartmentName,
            UserRoleInfo.RoleNames
        ${baseQuery}
        ${whereSql}
        GROUP BY
            u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
            c.CompanyID, c.CompanyName, d.DepartmentName, UserRoleInfo.RoleNames
        ${havingClause}
        ORDER BY 
            u.LastName, u.FirstName -- Default sort order
        OFFSET @offset ROWS
        FETCH NEXT @pageSize ROWS ONLY;
    `;

    try {
        logger.debug("Executing Count Query:", simplerCountQuery, "Params:", params);
        // Use the params array directly for count (it doesn't include offset/pageSize yet)
        const countResult = await executeQuery(simplerCountQuery, params); 
        const totalCount = countResult.recordset[0]?.TotalCount || 0;
        logger.info(`Total users found matching criteria: ${totalCount}`);

        let users: PortalUser[] = [];
        if (totalCount > 0 && offset < totalCount) {
            // Add pagination params ONLY for the data query
            const dataParams: QueryParameter[] = [
                ...params, // Spread the existing filter parameters
                { name: 'offset', type: sql.Int, value: offset },
                { name: 'pageSize', type: sql.Int, value: pageSize }
            ];
            logger.debug("Executing Data Query:", dataQuery, "Params:", dataParams);
            const dataResult = await executeQuery(dataQuery, dataParams); // Use dataParams
            
            // Map DB result to frontend PortalUser interface
            users = dataResult.recordset.map(dbUser => ({
                id: dbUser.UserID,
                entraId: dbUser.EntraID,
                name: `${dbUser.FirstName} ${dbUser.LastName}`,
                email: dbUser.Email,
                company: dbUser.CompanyName,
                companyId: dbUser.CompanyID,
                department: dbUser.DepartmentName || undefined,
                roles: dbUser.RoleNames ? dbUser.RoleNames.split(',') : [],
                status: dbUser.IsActive ? 'Active' : 'Inactive'
            }));
            logger.info(`Retrieved page ${page} (${users.length} users)`);
        }

        const responseBody: PaginatedPortalUsersResponse = {
            users: users,
            totalCount: totalCount,
            pageSize: pageSize,
            totalPages: Math.ceil(totalCount / pageSize)
        };

        return {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: responseBody
        };

    } catch (error) {
        logger.error('Error in GetUsers function:', error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Failed to retrieve users.",
                error: errorMessage
            }
        };
    }
    // Ensure function returns if try/catch is exited unexpectedly (shouldn't happen ideally)
    return { status: 500, jsonBody: { message: 'Unexpected error in GetUsers function.' } }; 
}

// app.http('GetUsers', {
//     methods: ['GET'],
//     authLevel: 'anonymous', // Handled by provider + code checks
//     route: 'portal-users', // Match function.json
//     handler: getUsers
// }); 