"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDevelopers = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
async function getDevelopers(request, context) {
    context.log('GetDevelopers function invoked.');
    try {
        // Get developers who can be assigned to change requests
        // This could be based on roles, departments, or specific user flags
        const developersQuery = `
            SELECT 
                u.UserID as userId,
                u.FirstName as firstName,
                u.LastName as lastName,
                CONCAT(u.FirstName, ' ', u.LastName) as fullName,
                u.Email as email,
                d.Department<PERSON> as departmentName,
                c.CompanyName as companyName,
                -- Count current assignments
                (SELECT COUNT(*) 
                 FROM ChangeRequests cr 
                 WHERE cr.AssignedToDevID = u.UserID 
                 AND cr.Status IN ('Assigned', 'In Development')) as currentAssignments
            FROM Users u
                LEFT JOIN Departments d ON u.DepartmentID = d.DepartmentID
                LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE u.IsActive = 1
                AND (u.UserID IN (
                    -- Users with Developer or Admin roles (adjust based on your role system)
                    SELECT ur.UserID 
                    FROM UserRoles ur 
                    INNER JOIN Roles r ON ur.RoleID = r.RoleID 
                    WHERE r.RoleName IN ('Developer', 'Application Developer', 'IT Admin', 'Administrator')
                ) OR u.UserID = 1) -- Include admin user for testing
            ORDER BY u.FirstName, u.LastName
        `;
        const developersResult = await (0, db_1.executeQuery)(developersQuery, []);
        context.log(`Retrieved ${developersResult.recordset.length} available developers`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                data: developersResult.recordset
            }
        };
    }
    catch (error) {
        context.error('Error in GetDevelopers:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while retrieving developers',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.getDevelopers = getDevelopers;
functions_1.app.http('GetDevelopers', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'developers',
    handler: getDevelopers
});
//# sourceMappingURL=index.js.map