import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

export async function downloadDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`DownloadDocument function invoked.`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = await getUserIdFromPrincipal(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }

        // Get document ID from route parameters
        const documentId = request.params.documentId;
        if (!documentId) {
            return { status: 400, jsonBody: { error: "Document ID is required" } };
        }

        // Get document information from database
        const documentQuery = `
            SELECT 
                d.DocumentID,
                d.Title,
                d.FileName,
                d.FileExtension,
                d.BlobUrl,
                d.CompanyID,
                d.IsPublished,
                d.IsActive,
                c.CompanyName
            FROM Documents d
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            WHERE d.DocumentID = @documentId AND d.IsActive = 1
        `;

        const documentResult = await executeQuery(documentQuery, [
            { name: 'documentId', type: sql.Int, value: parseInt(documentId) }
        ]);

        if (!documentResult.recordset || documentResult.recordset.length === 0) {
            return { status: 404, jsonBody: { error: "Document not found" } };
        }

        const document = documentResult.recordset[0];

        // Check if document is published
        if (!document.IsPublished) {
            return { status: 403, jsonBody: { error: "Document is not published" } };
        }

        // TODO: Add company-based access control if needed
        // For now, allow access to published documents

        // Increment download count
        const updateQuery = `
            UPDATE Documents 
            SET DownloadCount = DownloadCount + 1 
            WHERE DocumentID = @documentId
        `;

        await executeQuery(updateQuery, [
            { name: 'documentId', type: sql.Int, value: parseInt(documentId) }
        ]);

        // In development mode or if blob URL is a mock URL, return a redirect to a placeholder
        if (isDevelopment || document.BlobUrl.startsWith('/api/files/')) {
            logger.info(`DownloadDocument: Development mode - returning mock download for ${document.FileName}`);
            return {
                status: 200,
                jsonBody: {
                    message: "Development mode - file download not implemented",
                    documentId: document.DocumentID,
                    fileName: document.FileName,
                    downloadUrl: document.BlobUrl,
                    note: "In production, this would redirect to Azure Blob Storage"
                }
            };
        }

        // In production, redirect to the blob URL
        return {
            status: 302,
            headers: {
                'Location': document.BlobUrl,
                'Content-Disposition': `attachment; filename="${document.FileName}"`
            }
        };

    } catch (error) {
        logger.error("DownloadDocument: Error downloading document:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error downloading document.",
                error: errorMessage
            }
        };
    }
}

// Register the function
app.http('DownloadDocument', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents/{documentId}/download',
    handler: downloadDocument
}); 