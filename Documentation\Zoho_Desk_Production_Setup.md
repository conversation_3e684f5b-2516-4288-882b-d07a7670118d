# Zoho Desk Production Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the Zoho Desk integration in production. The technical implementation is complete, and this document covers the configuration and deployment steps.

## ✅ Implementation Status

### Completed Components
- **Backend OAuth Functions**: `ZohoDeskAuth.ts` with authorization, callback, and token management
- **API Proxy Service**: `ZohoDeskAPI.ts` with comprehensive endpoint coverage
- **Frontend Integration**: Updated `ITTicketsPage.tsx` to use backend services
- **Configuration Interface**: `ZohoDeskSetup.tsx` for setup and monitoring
- **Route Integration**: Protected admin routes for configuration

### Production-Ready Features
- OAuth 2.0 authorization flow with PKCE security
- Automatic token refresh and expiration handling
- Comprehensive error handling and user feedback
- Multi-company department routing
- Contact auto-creation and management
- Real-time status monitoring and testing

## 🚀 Production Setup Steps

### Step 1: Zoho Desk OAuth App Registration

#### 1.1 Access Zoho Desk Developer Console
1. Log in to your Zoho Desk organization at `https://desk.zoho.in`
2. Navigate to **Setup** → **Developer Space** → **OAuth**
3. Click **"Add OAuth App"**

#### 1.2 Configure OAuth Application
```
App Name: Falcon Portal IT Hub
Client Type: Web Application
Redirect URLs:
  - https://your-production-domain.com/api/auth/zoho-desk/callback
  - http://localhost:7075/api/auth/zoho-desk/callback (for development)

Scopes (Required):
✅ Desk.tickets.ALL
✅ Desk.contacts.ALL  
✅ Desk.agents.READ
✅ Desk.departments.READ
✅ Desk.categories.READ
✅ Desk.threads.ALL
✅ Desk.attachments.READ
```

#### 1.3 Note OAuth Credentials
After registration, save these values:
- **Client ID**: `your_client_id_here`
- **Client Secret**: `your_client_secret_here`
- **Organization ID**: Found in **Setup** → **General** → **Company Details**

### Step 2: Environment Configuration

#### 2.1 Azure Functions Environment Variables
Set these in your Azure Functions App Settings:

```env
# Zoho Desk OAuth Configuration
ZOHO_DESK_CLIENT_ID=your_client_id_from_step_1
ZOHO_DESK_CLIENT_SECRET=your_client_secret_from_step_1
ZOHO_DESK_ORG_ID=your_organization_id
ZOHO_DESK_REDIRECT_URI=https://your-domain.com/api/auth/zoho-desk/callback

# Production Domain
WEBSITE_SITE_NAME=your-azure-app-service-name
```

#### 2.2 Local Development Environment
Create `azure-functions/falcon-api/.env`:

```env
# Development Configuration
ZOHO_DESK_CLIENT_ID=your_client_id_from_step_1
ZOHO_DESK_CLIENT_SECRET=your_client_secret_from_step_1
ZOHO_DESK_ORG_ID=your_organization_id
ZOHO_DESK_REDIRECT_URI=http://localhost:7075/api/auth/zoho-desk/callback
```

### Step 3: Zoho Desk Organization Setup

#### 3.1 Configure Departments
Create departments matching your company structure:

```
1. IT Support
   - Description: "Information Technology support for all SASMOS companies"
   - Enabled: Yes
   
2. HR Support  
   - Description: "Human Resources support and assistance"
   - Enabled: Yes
   
3. Administrative Support
   - Description: "General administrative and operational support"
   - Enabled: Yes
```

#### 3.2 Configure Service Categories
Set up categories for IT Support department:

```
Hardware Issues:
  - Laptop/Desktop Problems
  - Printer and Scanner Issues
  - Network Equipment Problems
  - Mobile Device Support

Software Support:
  - Microsoft Office Applications
  - Email and Communication Tools
  - Business Application Support
  - License and Access Management

Access & Security:
  - Password Reset Requests
  - File and Folder Access
  - VPN and Remote Access
  - Security Incident Reports

Service Requests:
  - New Equipment Requests
  - Software Installation
  - Account Setup and Changes
  - General IT Consultations
```

#### 3.3 Import Contacts (Optional)
Use Zoho Desk's import feature to bulk import employee contacts:

```csv
First Name,Last Name,Email,Phone,Account Name
John,Doe,<EMAIL>,+91-**********,SASMOS HET
Jane,Smith,<EMAIL>,+91-**********,Avirata Defence Systems
```

### Step 4: Testing and Verification

#### 4.1 Backend Function Testing
1. Deploy Azure Functions to production
2. Test OAuth endpoints:
   - `GET /api/auth/zoho-desk/authorize` - Should return authorization URL
   - `GET /api/auth/zoho-desk/callback?code=test` - Should handle callback
   - `POST /api/auth/zoho-desk/token` - Should return access token

#### 4.2 Frontend Integration Testing
1. Navigate to `/it/setup` (requires Administrator role)
2. Check integration status - should show "Not Configured" initially
3. Click "Authorize with Zoho Desk" - should open OAuth flow
4. Complete authorization in popup window
5. Verify status changes to "Connected"
6. Click "Test Connection" - should successfully fetch departments

#### 4.3 API Functionality Testing
Test key endpoints through the setup interface:
- **Departments**: Should list your configured departments
- **Categories**: Should show service categories by department
- **Tickets**: Create a test ticket to verify full workflow

### Step 5: Production Deployment

#### 5.1 Azure Functions Deployment
```bash
# Build and deploy functions
cd azure-functions/falcon-api
npm run build
func azure functionapp publish your-function-app-name
```

#### 5.2 Frontend Deployment
```bash
# Build and deploy React application
cd apps/portal-shell
npm run build
# Deploy build folder to Azure Static Web Apps or App Service
```

#### 5.3 DNS and SSL Configuration
- Configure custom domain for production
- Ensure SSL certificate is valid
- Update Zoho Desk OAuth redirect URLs to use HTTPS

## 🔧 Troubleshooting

### Common Issues

#### OAuth Authorization Fails
```
Error: "invalid_client" or "unauthorized_client"
```
**Solution**: Verify Client ID and Secret in environment variables

#### Token Refresh Fails
```
Error: "invalid_grant" or "token_expired"
```
**Solution**: User needs to re-authorize; clear stored tokens

#### API Calls Return 401
```
Error: "Authentication failed"
```
**Solution**: Check Organization ID and token validity

#### CORS Issues in Development
```
Error: "CORS policy blocked"
```
**Solution**: Configure CORS in Azure Functions for localhost:5174

### Debug Endpoints

#### Check Configuration Status
```bash
curl -X GET "https://your-api.azurewebsites.net/api/auth/zoho-desk/authorize" \
  -H "Authorization: Bearer your-user-token"
```

#### Test API Proxy
```bash
curl -X GET "https://your-api.azurewebsites.net/api/zoho-desk/departments" \
  -H "Authorization: Bearer your-user-token"
```

## 📊 Monitoring and Maintenance

### Key Metrics to Monitor
- OAuth token expiration and refresh rates
- API call success/failure rates
- Ticket creation and update performance
- User authentication success rates

### Regular Maintenance Tasks
1. **Weekly**: Review OAuth token health and refresh patterns
2. **Monthly**: Audit API usage and performance metrics
3. **Quarterly**: Review and update service categories
4. **Annually**: Refresh OAuth application credentials

### Log Monitoring
Monitor Azure Functions logs for:
- `ZohoDeskAuth: OAuth authorization flow`
- `ZohoDeskAPI: Processing API requests`
- `Error:` prefix for any integration issues

## 🎯 Success Criteria

### Technical Validation
- [ ] OAuth authorization completes successfully
- [ ] API proxy returns valid responses
- [ ] Frontend shows "Connected" status
- [ ] Test ticket creation works end-to-end

### User Experience Validation
- [ ] IT staff can create tickets without errors
- [ ] Department routing works correctly
- [ ] Contact auto-creation functions properly
- [ ] Setup interface provides clear status feedback

### Performance Validation
- [ ] API response times under 2 seconds
- [ ] OAuth flow completes within 30 seconds
- [ ] Token refresh happens seamlessly
- [ ] Error handling provides helpful feedback

## 📞 Support and Documentation

### Additional Resources
- **Zoho Desk API Documentation**: https://desk.zoho.in/support/APIDocument.do
- **OAuth 2.0 Flow Documentation**: https://desk.zoho.in/support/APIDocument.do#OauthTokens
- **Azure Functions Documentation**: https://docs.microsoft.com/en-us/azure/azure-functions/

### Contact Information
For technical support during setup:
- Implementation team: [Your technical team contact]
- Zoho Desk support: [Your Zoho support contact]
- Azure support: [Your Microsoft support contact]

---

**Setup Guide Version**: 1.0  
**Last Updated**: January 29, 2025  
**Compatible with**: Falcon Portal v1.0+ 