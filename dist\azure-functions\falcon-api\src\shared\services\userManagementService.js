"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userManagementService = exports.UserManagementService = exports.updateUserLastLogin = exports.removeRoleFromUser = exports.assignRoleToUser = exports.findOrCreateUser = void 0;
const db_1 = require("../db");
const logger_1 = require("../utils/logger");
// Remove GraphUser import if not used
// import { GraphUser } from "./graphService"; 
const mssql_1 = require("mssql"); // Ensure mssql is imported
// Helper to map DB row to PortalUser
function mapDbUserToPortalUser(dbUser) {
    // Ensure lastLogin is handled correctly (might be null, convert Date to ISO string)
    const lastLogin = dbUser.LastLogin ? new Date(dbUser.LastLogin).toISOString() : undefined;
    return {
        id: dbUser.EntraID,
        internalId: dbUser.UserID,
        name: dbUser.Username,
        email: dbUser.Email,
        company: dbUser.CompanyName,
        companyId: dbUser.CompanyID,
        // Roles might be null if user has no roles or due to LEFT JOIN; handle null/empty string
        roles: dbUser.Roles ? dbUser.Roles.split(',').filter((r) => r) : [],
        status: dbUser.IsActive ? 'Active' : 'Inactive',
        lastLogin: lastLogin
    };
}
/**
 * Finds an existing user in the local DB based on Entra ID, or creates a new one
 * if not found. Also assigns a default role upon creation.
 *
 * @param entraUser - The user data obtained from Microsoft Graph API.
 * @param createdByUserId - The UserID of the user performing the operation (for audit columns). Defaults to system user (e.g., 1).
 * @returns The DbUser object for the found or created user.
 */
const findOrCreateUser = (entraUser_1, ...args_1) => __awaiter(void 0, [entraUser_1, ...args_1], void 0, function* (entraUser, createdByUserId = 1) {
    logger_1.logger.info(`findOrCreateUser called for Entra ID: ${entraUser.id}`);
    const checkUserQuery = `SELECT * FROM Users WHERE EntraID = @EntraID;`;
    const checkUserParams = [
        { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraUser.id }
    ];
    try {
        const existingUserResult = yield (0, db_1.executeQuery)(checkUserQuery, checkUserParams);
        if (existingUserResult.recordset && existingUserResult.recordset.length > 0) {
            const existingUser = existingUserResult.recordset[0];
            logger_1.logger.info(`Found existing user ${existingUser.UserID} for Entra ID ${entraUser.id}`);
            // TODO: Optionally update LastLoginDate or other fields on find?
            // const updateUserLoginQuery = `UPDATE Users SET LastLoginDate = GETDATE() WHERE UserID = @UserID`;
            // await executeQuery(updateUserLoginQuery, { UserID: existingUser.UserID });
            return existingUser;
        }
        else {
            logger_1.logger.info(`User with Entra ID ${entraUser.id} not found. Creating new user.`);
            // --- 1. Resolve CompanyID --- 
            let companyId = null;
            if (entraUser.companyName) {
                const companyQuery = `SELECT CompanyID FROM Companies WHERE CompanyName = @CompanyName AND IsActive = 1;`;
                const companyParams = [
                    { name: 'CompanyName', type: mssql_1.default.NVarChar, value: entraUser.companyName }
                ];
                try {
                    const companyResult = yield (0, db_1.executeQuery)(companyQuery, companyParams);
                    if (companyResult.recordset && companyResult.recordset.length > 0) {
                        companyId = companyResult.recordset[0].CompanyID;
                    }
                    else {
                        // Company name provided but not found in DB - this is an error state.
                        logger_1.logger.error(`Company '${entraUser.companyName}' provided by Entra ID not found or inactive in Companies table for user ${entraUser.id}.`);
                        throw new Error(`Company '${entraUser.companyName}' not found or inactive in the database. Cannot create user.`);
                    }
                }
                catch (companyError) {
                    logger_1.logger.error(`Error looking up company '${entraUser.companyName}' for user ${entraUser.id}:`, companyError);
                    throw new Error(`Database error during company lookup for user ${entraUser.id}.`); // Rethrow as critical error
                }
            }
            else {
                // No company name provided by Entra ID. What's the policy?
                // Option A: Assign a default/placeholder company.
                // Option B: Reject user creation.
                // For now, let's use a placeholder (e.g., ID 1) but log a strong warning.
                companyId = 1; // Placeholder for 'Unspecified' or first company
                logger_1.logger.warn(`Entra user ${entraUser.id} has no companyName. Assigning default CompanyID: ${companyId}. Review required.`);
                // Consider throwing an error if a company is strictly required:
                // throw new Error(`Entra user ${entraUser.id} is missing required company information.`);
            }
            // --- 2. Resolve DepartmentID (Optional) --- 
            let departmentId = null;
            if (entraUser.department && companyId) { // Only lookup if department and company are known
                const deptQuery = `SELECT DepartmentID FROM Departments WHERE DepartmentName = @DepartmentName AND CompanyID = @CompanyID AND IsActive = 1;`;
                const deptParams = [
                    { name: 'DepartmentName', type: mssql_1.default.NVarChar, value: entraUser.department },
                    { name: 'CompanyID', type: mssql_1.default.Int, value: companyId }
                ];
                try {
                    const deptResult = yield (0, db_1.executeQuery)(deptQuery, deptParams);
                    if (deptResult.recordset && deptResult.recordset.length > 0) {
                        departmentId = deptResult.recordset[0].DepartmentID;
                    }
                    else {
                        // Department not found, but maybe that's okay? Log warning, set to NULL.
                        logger_1.logger.warn(`Department '${entraUser.department}' not found or inactive in CompanyID ${companyId} for user ${entraUser.id}. Setting DepartmentID to NULL.`);
                    }
                }
                catch (deptError) {
                    // Log error but don't necessarily fail user creation
                    logger_1.logger.error(`Error looking up department '${entraUser.department}' for user ${entraUser.id}:`, deptError);
                }
            }
            // --- 3. Create User --- 
            const insertUserQuery = `
                INSERT INTO Users (
                    EntraID, Username, Email, FirstName, LastName, 
                    CompanyID, DepartmentID, IsActive, CreatedBy, CreatedDate, ModifiedDate
                ) 
                OUTPUT INSERTED.* 
                VALUES (
                    @EntraID, @Username, @Email, @FirstName, @LastName, 
                    @CompanyID, @DepartmentID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE()
                );
            `;
            // Convert newUserParams object to QueryParameter array
            const newUserParamsArray = [
                { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraUser.id },
                { name: 'Username', type: mssql_1.default.NVarChar, value: entraUser.userPrincipalName },
                { name: 'Email', type: mssql_1.default.NVarChar, value: entraUser.mail || entraUser.userPrincipalName },
                { name: 'FirstName', type: mssql_1.default.NVarChar, value: entraUser.givenName || '' },
                { name: 'LastName', type: mssql_1.default.NVarChar, value: entraUser.surname || 'User' },
                { name: 'CompanyID', type: mssql_1.default.Int, value: companyId },
                // Handle potential null for DepartmentID - pass sql.Int, value can be null
                { name: 'DepartmentID', type: mssql_1.default.Int, value: departmentId },
                { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                { name: 'CreatedBy', type: mssql_1.default.Int, value: createdByUserId }
            ];
            const newUserResult = yield (0, db_1.executeQuery)(insertUserQuery, newUserParamsArray);
            if (!newUserResult.recordset || newUserResult.recordset.length === 0) {
                logger_1.logger.error("Failed to create user or retrieve created record.", { entraId: entraUser.id });
                throw new Error("User creation failed in database.");
            }
            const createdUser = newUserResult.recordset[0];
            logger_1.logger.info(`Successfully created user ${createdUser.UserID} for Entra ID ${entraUser.id}`);
            // --- 4. Assign Default Role --- 
            const defaultRoleName = process.env.DEFAULT_USER_ROLE || 'Employee'; // Use env var or fallback
            logger_1.logger.info(`Attempting to assign default role: ${defaultRoleName}`);
            let defaultRoleId = null;
            try {
                const roleQuery = `SELECT RoleID FROM Roles WHERE RoleName = @RoleName AND IsActive = 1;`;
                const roleParams = [
                    { name: 'RoleName', type: mssql_1.default.NVarChar, value: defaultRoleName }
                ];
                const roleResult = yield (0, db_1.executeQuery)(roleQuery, roleParams);
                if (roleResult.recordset && roleResult.recordset.length > 0) {
                    defaultRoleId = roleResult.recordset[0].RoleID;
                }
                else {
                    logger_1.logger.error(`Default role '${defaultRoleName}' not found or inactive in Roles table.`);
                }
            }
            catch (roleError) {
                logger_1.logger.error(`Error looking up default role '${defaultRoleName}':`, roleError);
            }
            if (defaultRoleId) {
                const insertUserRoleQuery = `
                    INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate)
                    VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());
                `;
                const insertUserRoleParams = [
                    { name: 'UserID', type: mssql_1.default.Int, value: createdUser.UserID },
                    { name: 'RoleID', type: mssql_1.default.Int, value: defaultRoleId },
                    { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                    { name: 'CreatedBy', type: mssql_1.default.Int, value: createdByUserId }
                ];
                try {
                    yield (0, db_1.executeQuery)(insertUserRoleQuery, insertUserRoleParams);
                    logger_1.logger.info(`Assigned default role '${defaultRoleName}' (ID: ${defaultRoleId}) to user ${createdUser.UserID}`);
                }
                catch (userRoleError) {
                    // Log error but don't fail the whole process
                    logger_1.logger.error(`Failed to assign default role to user ${createdUser.UserID}:`, userRoleError);
                }
            }
            return createdUser;
        }
    }
    catch (error) {
        // Log the specific error from company/dept lookup or user insert
        logger_1.logger.error(`Error in findOrCreateUser process for Entra ID ${entraUser.id}:`, error);
        // Re-throw the error to be caught by the calling function (e.g., AddUser API)
        throw error;
        // return null; // Returning null hides the error type
    }
});
exports.findOrCreateUser = findOrCreateUser;
/**
 * Assigns a specific role to a user.
 * If the assignment already exists but is inactive, it reactivates it.
 * If the assignment doesn't exist, it creates a new one.
 *
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to assign.
 * @param assignedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role was successfully assigned or reactivated, false otherwise.
 */
const assignRoleToUser = (userId, roleId, assignedByUserId) => __awaiter(void 0, void 0, void 0, function* () {
    logger_1.logger.info(`assignRoleToUser called for UserID: ${userId}, RoleID: ${roleId}`);
    const checkQuery = `SELECT UserRoleID, IsActive FROM UserRoles WHERE UserID = @UserID AND RoleID = @RoleID;`;
    const checkParams = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId },
        { name: 'RoleID', type: mssql_1.default.Int, value: roleId }
    ];
    try {
        const checkResult = yield (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset && checkResult.recordset.length > 0) {
            // Assignment exists
            const existingAssignment = checkResult.recordset[0];
            if (!existingAssignment.IsActive) {
                // Reactivate existing inactive assignment
                logger_1.logger.info(`Reactivating existing role assignment (UserRoleID: ${existingAssignment.UserRoleID}) for UserID: ${userId}, RoleID: ${roleId}`);
                const updateQuery = `UPDATE UserRoles SET IsActive = 1, ModifiedBy = @ModifiedBy, ModifiedDate = GETDATE() WHERE UserRoleID = @UserRoleID;`;
                const updateParams = [
                    { name: 'UserRoleID', type: mssql_1.default.Int, value: existingAssignment.UserRoleID },
                    { name: 'ModifiedBy', type: mssql_1.default.Int, value: assignedByUserId }
                ];
                yield (0, db_1.executeQuery)(updateQuery, updateParams);
                return true;
            }
            else {
                // Role already active
                logger_1.logger.info(`Role ${roleId} is already actively assigned to user ${userId}. No action needed.`);
                return true; // Consider already assigned as success
            }
        }
        else {
            // Assignment does not exist, create new one
            logger_1.logger.info(`Creating new role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            const insertQuery = `INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate) VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());`;
            const insertParams = [
                { name: 'UserID', type: mssql_1.default.Int, value: userId },
                { name: 'RoleID', type: mssql_1.default.Int, value: roleId },
                { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                { name: 'CreatedBy', type: mssql_1.default.Int, value: assignedByUserId }
            ];
            yield (0, db_1.executeQuery)(insertQuery, insertParams);
            return true;
        }
    }
    catch (error) {
        logger_1.logger.error(`Error assigning role ${roleId} to user ${userId}:`, error);
        return false; // Indicate failure
    }
});
exports.assignRoleToUser = assignRoleToUser;
/**
 * Removes a specific role from a user (by marking the assignment as inactive).
 *
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to remove.
 * @param removedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role assignment was successfully marked as inactive, false otherwise.
 */
const removeRoleFromUser = (userId, roleId, removedByUserId) => __awaiter(void 0, void 0, void 0, function* () {
    logger_1.logger.info(`removeRoleFromUser called for UserID: ${userId}, RoleID: ${roleId}`);
    const updateQuery = `UPDATE UserRoles SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID AND RoleID = @RoleID AND IsActive = 1;`;
    const updateParams = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId },
        { name: 'RoleID', type: mssql_1.default.Int, value: roleId },
        { name: 'ModifiedBy', type: mssql_1.default.Int, value: removedByUserId }
    ];
    try {
        const result = yield (0, db_1.executeQuery)(updateQuery, updateParams);
        // rowsAffected will be 1 if an active record was found and updated, 0 otherwise.
        if (result.rowsAffected && result.rowsAffected[0] === 1) {
            logger_1.logger.info(`Successfully deactivated role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            return true;
        }
        else {
            logger_1.logger.warn(`No active role assignment found for UserID: ${userId}, RoleID: ${roleId} to deactivate.`);
            // Consider it success if the role wasn't active anyway?
            // Returning true might be less confusing for the caller.
            return true; // Indicate success even if no change was needed
        }
    }
    catch (error) {
        logger_1.logger.error(`Error removing role ${roleId} from user ${userId}:`, error);
        return false; // Indicate failure
    }
});
exports.removeRoleFromUser = removeRoleFromUser;
/**
 * Updates the LastLoginDate for a given user in the local database.
 *
 * @param userId - The internal UserID of the user.
 * @returns True if the update was successful, false otherwise.
 */
const updateUserLastLogin = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    logger_1.logger.info(`Updating LastLoginDate for UserID: ${userId}`);
    const query = `UPDATE Users SET LastLoginDate = GETUTCDATE() WHERE UserID = @UserID;`;
    const params = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId }
    ];
    try {
        yield (0, db_1.executeQuery)(query, params);
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Error updating LastLoginDate for UserID ${userId}:`, error);
        return false;
    }
});
exports.updateUserLastLogin = updateUserLastLogin;
// TODO: Implement other user management functions from the guide:
// - (Potentially) updateUser(userId: number, updateData: Partial<DbUser>, modifiedBy: number): Promise<DbUser | null>
// - (Potentially) getUserById(userId: number): Promise<DbUser | null> - might need joins
// - (Potentially) getUsersWithRoles(filters, pagination): Promise<{users: DbUser[], totalCount: number}> - complex query needed
class UserManagementService {
    // ... existing methods ...
    updateUserLastLogin(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            // ... existing implementation ...
        });
    }
    /**
     * Retrieves a paginated and filtered list of portal users.
     * @param filters - Filtering criteria (searchTerm, companyId, roleId, status).
     * @param pagination - Pagination options (page, pageSize).
     * @returns A promise resolving to an object containing the list of users and the total count.
     */
    getPortalUsersPaginated(filters, pagination) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const { searchTerm, companyId, roleId, status } = filters;
            const { page, pageSize } = pagination;
            const offset = (page - 1) * pageSize;
            const baseQuery = `
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            LEFT JOIN UserRoles ur ON pu.UserID = ur.UserID AND ur.IsActive = 1 -- Only join active roles for filtering/display
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID
        `;
            const whereClauses = [];
            const parameters = [];
            // Add search term filter (search name and email)
            if (searchTerm) {
                whereClauses.push(`(pu.Username LIKE @searchTerm OR pu.Email LIKE @searchTerm)`);
                parameters.push({ name: 'searchTerm', type: mssql_1.default.NVarChar, value: `%${searchTerm}%` });
            }
            // Add company filter
            if (companyId) {
                whereClauses.push(`pu.CompanyID = @companyId`);
                parameters.push({ name: 'companyId', type: mssql_1.default.Int, value: companyId });
            }
            // Add role filter (check if user has the role)
            // Note: This requires the user to have AT LEAST this role, might need adjustment
            // if the goal is to show ONLY users with this role.
            if (roleId) {
                // We need a subquery/aggregation check here, as a simple WHERE ur.RoleID = @roleId
                // would exclude users without roles or with different roles in the main result.
                // Let's adjust the base query slightly for easier role filtering.
                // This approach filters users based on whether they possess the specified active role.
                whereClauses.push(`EXISTS (
                SELECT 1
                FROM UserRoles ur_filter
                WHERE ur_filter.UserID = pu.UserID AND ur_filter.RoleID = @roleId AND ur_filter.IsActive = 1
            )`);
                parameters.push({ name: 'roleId', type: mssql_1.default.Int, value: roleId });
            }
            // Add status filter
            if (status) {
                whereClauses.push(`pu.IsActive = @isActive`);
                parameters.push({ name: 'isActive', type: mssql_1.default.Bit, value: status === 'Active' ? 1 : 0 });
            }
            const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
            // Main query for fetching paginated users
            // Use a subquery for distinct users before aggregation to avoid duplicate role issues in pagination
            const dataQuery = `
            WITH UserCTE AS (
                SELECT DISTINCT pu.UserID
                /* Add columns needed for WHERE filters */
                , pu.CompanyID, pu.IsActive
            FROM Users pu -- Changed PortalUsers to Users
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            LEFT JOIN UserRoles ur ON pu.UserID = ur.UserID AND ur.IsActive = 1 -- Only join active roles for filtering/display
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID
                ${whereString}
            )
            SELECT
                pu.UserID, pu.EntraID, pu.Username, pu.Email, pu.IsActive, pu.LastLoginDate AS LastLogin, -- Changed UserName to Username, LastLoginDate to LastLogin
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r_agg.RoleName, ',') WITHIN GROUP (ORDER BY r_agg.RoleName)
                 FROM UserRoles ur_agg
                 JOIN Roles r_agg ON ur_agg.RoleID = r_agg.RoleID
                 WHERE ur_agg.UserID = pu.UserID AND ur_agg.IsActive = 1) AS Roles
            FROM Users pu -- Changed PortalUsers to Users
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.UserID IN (SELECT UserID FROM UserCTE)
            ORDER BY pu.Username -- Changed UserName to Username
            OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY;
        `;
            // Count query (needs the same filters)
            // Count distinct users matching the criteria
            const countQuery = `
            SELECT COUNT(DISTINCT pu.UserID) as totalCount
            ${baseQuery}
            ${whereString};
        `;
            // Add pagination parameters
            parameters.push({ name: 'offset', type: mssql_1.default.Int, value: offset });
            parameters.push({ name: 'pageSize', type: mssql_1.default.Int, value: pageSize });
            try {
                logger_1.logger.debug(`Executing getPortalUsersPaginated data query with filters: ${JSON.stringify(filters)}, pagination: ${JSON.stringify(pagination)}`);
                const dataResult = yield (0, db_1.executeQuery)(dataQuery, parameters);
                // Log the raw data before mapping
                logger_1.logger.debug("Raw dataResult.recordset:", JSON.stringify(dataResult.recordset, null, 2));
                logger_1.logger.debug(`Executing getPortalUsersPaginated count query with filters: ${JSON.stringify(filters)}`);
                // Remove offset/pageSize for count query before executing
                const countParams = parameters.filter(p => p.name !== 'offset' && p.name !== 'pageSize');
                const countResult = yield (0, db_1.executeQuery)(countQuery, countParams);
                const users = dataResult.recordset.map(mapDbUserToPortalUser);
                const totalCount = ((_a = countResult.recordset[0]) === null || _a === void 0 ? void 0 : _a.totalCount) || 0;
                logger_1.logger.info(`Retrieved ${users.length} users (page ${page}/${pageSize}) with total count ${totalCount}`);
                return { users, totalCount };
            }
            catch (error) {
                logger_1.logger.error('Error in getPortalUsersPaginated:', error);
                throw new Error(`Failed to retrieve portal users: ${error instanceof Error ? error.message : String(error)}`);
            }
        });
    }
    getPortalUserByEntraId(entraId) {
        return __awaiter(this, void 0, void 0, function* () {
            logger_1.logger.info(`getPortalUserByEntraId called for Entra ID: ${entraId}`);
            const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.Username, pu.Email, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.EntraID = @EntraID;
        `;
            const params = [
                { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId }
            ];
            try {
                const result = yield (0, db_1.executeQuery)(query, params);
                if (result.recordset && result.recordset.length > 0) {
                    logger_1.logger.debug("Raw DB result for user:", JSON.stringify(result.recordset[0], null, 2));
                    // Ensure mapDbUserToPortalUser is accessible, if it's not exported or part of the class, you might need to define it locally or make it accessible.
                    // For now, assuming mapDbUserToPortalUser is accessible in this scope.
                    return mapDbUserToPortalUser(result.recordset[0]);
                }
                logger_1.logger.warn(`No user found with EntraID: ${entraId}`);
                return null;
            }
            catch (error) {
                logger_1.logger.error(`Error fetching user by EntraID ${entraId}:`, error);
                // It's better to throw the error to let the caller (Azure function) handle the HTTP response code
                throw new Error(`Database error while fetching user by EntraID ${entraId}.`);
            }
        });
    }
    /**
     * Gets the internal UserID for a user by their Entra ID.
     * @param entraId - The Entra ID of the user.
     * @returns The internal UserID or null if not found.
     */
    getUserIdByEntraId(entraId) {
        return __awaiter(this, void 0, void 0, function* () {
            logger_1.logger.info(`getUserIdByEntraId called for Entra ID: ${entraId}`);
            const query = `SELECT UserID FROM Users WHERE EntraID = @EntraID AND IsActive = 1;`;
            const params = [
                { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId }
            ];
            try {
                const result = yield (0, db_1.executeQuery)(query, params);
                if (result.recordset && result.recordset.length > 0) {
                    return result.recordset[0].UserID;
                }
                logger_1.logger.warn(`No active user found with EntraID: ${entraId}`);
                return null;
            }
            catch (error) {
                logger_1.logger.error(`Error fetching UserID by EntraID ${entraId}:`, error);
                throw new Error(`Database error while fetching UserID by EntraID ${entraId}.`);
            }
        });
    }
    /**
     * Deactivates a portal user by their Entra ID.
     * @param entraId - The Entra ID of the user to deactivate.
     * @param modifiedByUserId - The UserID of the user performing the operation.
     * @returns True if the user was successfully deactivated, false otherwise.
     */
    deactivatePortalUserByEntraId(entraId, modifiedByUserId) {
        return __awaiter(this, void 0, void 0, function* () {
            logger_1.logger.info(`deactivatePortalUserByEntraId called for Entra ID: ${entraId} by UserID: ${modifiedByUserId}`);
            const query = `
            UPDATE Users 
            SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() 
            WHERE EntraID = @EntraID AND IsActive = 1;
        `;
            const params = [
                { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
                { name: 'ModifiedBy', type: mssql_1.default.Int, value: modifiedByUserId }
            ];
            try {
                const result = yield (0, db_1.executeQuery)(query, params);
                if (result.rowsAffected && result.rowsAffected[0] > 0) {
                    logger_1.logger.info(`Successfully deactivated user with EntraID: ${entraId}`);
                    return true;
                }
                logger_1.logger.warn(`No active user found to deactivate with EntraID: ${entraId}`);
                return false;
            }
            catch (error) {
                logger_1.logger.error(`Error deactivating user by EntraID ${entraId}:`, error);
                throw new Error(`Database error while deactivating user by EntraID ${entraId}.`);
            }
        });
    }
}
exports.UserManagementService = UserManagementService;
exports.userManagementService = new UserManagementService();
//# sourceMappingURL=userManagementService.js.map