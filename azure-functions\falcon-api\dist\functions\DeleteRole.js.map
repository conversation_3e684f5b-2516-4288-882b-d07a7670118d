{"version": 3, "file": "DeleteRole.js", "sourceRoot": "", "sources": ["../../src/functions/DeleteRole.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,2CAA6B,CAAC,2BAA2B;AACzD,qCAAuC,CAAC,0BAA0B;AAG3D,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;IAEzE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACf,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,0BAA0B;aACtC;SACJ,CAAC;KACL;IAED,mCAAmC;IACnC,IAAI,WAAW,GAA2B,IAAI,CAAC;IAC/C,IAAI;QACA,0BAA0B;QAC1B,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAC7B,sBAAsB;QACtB,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;QAE1B,4EAA4E;QAC5E,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/C,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,eAAe,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;QAC1G,IAAI,eAAe,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;YACzD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,gBAAgB,MAAM,aAAa;iBAC/C;aACJ,CAAC;SACL;QACD,MAAM,QAAQ,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACvD,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAClD,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;YACzD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,sCAAsC,QAAQ,EAAE;iBAC5D;aACJ,CAAC;SACL;QAED,2DAA2D;QAC3D,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC/H,MAAM,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1G,IAAI,CAAC,iBAAiB,EAAE;YACpB,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;YACzD,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;SACjF;QAED,uEAAuE;QACvE,MAAM,4BAA4B,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QAC3D,4BAA4B,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,0BAA0B,GAAG;;;;;;;;;;SAUlC,CAAC;QACF,MAAM,mBAAmB,GAAG,MAAM,4BAA4B,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACjG,MAAM,aAAa,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3E,iEAAiE;QACjE,MAAM,wBAAwB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QACvD,wBAAwB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC1D,MAAM,wBAAwB,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAErF,4FAA4F;QAC5F,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,eAAe,aAAa,CAAC,MAAM,oCAAoC,CAAC,CAAC;YACrF,oEAAoE;YACpE,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;gBAChC,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC9C,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,8CAA8C;gBACrG,eAAe,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;gBACnE,yFAAyF;gBACzF,MAAM,eAAe,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;aACtG;SACJ;QAEL,8CAA8C;QAC9C,MAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QAChD,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACnD,MAAM,iBAAiB,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAE1E,yBAAyB;QACzB,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;QAE3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;QACnD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa;KAExC;IAAC,OAAO,KAAU,EAAE;QACjB,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEhE,8CAA8C;QAC9C,0EAA0E;QAC1E,IAAI,WAAW,EAAE;YACb,IAAI;gBACA,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;aACxD;YAAC,OAAO,aAAkB,EAAE;gBACzB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;aAC5E;SACJ;QAED,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;gBACzC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,sCAAsC;aAC/D;SACJ,CAAC;KACL;AACL,CAAC;AA3HD,gCA2HC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnB,KAAK,EAAE,oBAAoB;IAC3B,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}