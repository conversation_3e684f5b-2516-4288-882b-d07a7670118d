"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteRole = void 0;
const functions_1 = require("@azure/functions");
const sql = __importStar(require("mssql")); // Import the mssql library
const db_1 = require("../shared/db"); // Corrected relative path
async function deleteRole(request, context) {
    const roleId = parseInt(request.params.roleId, 10);
    context.log(`Http function processed request for DeleteRole: ${roleId}`);
    if (isNaN(roleId)) {
        return {
            status: 400,
            jsonBody: {
                message: "Invalid RoleID provided."
            }
        };
    }
    // --- Transaction Handling ---    
    let transaction = null;
    try {
        // Get the connection pool
        const pool = await (0, db_1.getPool)();
        // Start a transaction
        transaction = pool.transaction();
        await transaction.begin();
        // Check if the role is one of the protected base roles (within transaction)
        const roleCheckRequest = transaction.request();
        roleCheckRequest.input('roleId', sql.Int, roleId);
        const roleCheckResult = await roleCheckRequest.query('SELECT RoleName FROM Roles WHERE RoleID = @roleId');
        if (roleCheckResult.recordset.length === 0) {
            await transaction.rollback(); // Rollback before throwing
            return {
                status: 404,
                jsonBody: {
                    message: `Role with ID ${roleId} not found.`
                }
            };
        }
        const roleName = roleCheckResult.recordset[0].RoleName;
        if (['Administrator', 'Employee'].includes(roleName)) {
            await transaction.rollback(); // Rollback before throwing
            return {
                status: 400,
                jsonBody: {
                    message: `Cannot delete protected base role: ${roleName}`
                }
            };
        }
        // Find the default 'Employee' role ID (within transaction)
        const defaultRoleRequest = transaction.request();
        const userRoleResult = await defaultRoleRequest.query("SELECT RoleID FROM Roles WHERE RoleName = 'Employee' AND IsActive = 1");
        const defaultUserRoleId = userRoleResult.recordset.length > 0 ? userRoleResult.recordset[0].RoleID : null;
        if (!defaultUserRoleId) {
            await transaction.rollback(); // Rollback before throwing
            throw new Error("Default 'Employee' role not found or inactive in database!");
        }
        // Find users who ONLY have the role being deleted (within transaction)
        const usersOnlyWithThisRoleRequest = transaction.request();
        usersOnlyWithThisRoleRequest.input('roleId', sql.Int, roleId);
        const usersOnlyWithThisRoleQuery = `
            SELECT UserID
            FROM UserRoles ur
            WHERE ur.RoleID = @roleId
            AND NOT EXISTS (
                SELECT 1
                FROM UserRoles ur2
                WHERE ur2.UserID = ur.UserID
                AND ur2.RoleID != @roleId
            );
        `;
        const usersToUpdateResult = await usersOnlyWithThisRoleRequest.query(usersOnlyWithThisRoleQuery);
        const usersToUpdate = usersToUpdateResult.recordset.map(row => row.UserID);
        // Delete all assignments of the target role (within transaction)
        const deleteAssignmentsRequest = transaction.request();
        deleteAssignmentsRequest.input('roleId', sql.Int, roleId);
        await deleteAssignmentsRequest.query('DELETE FROM UserRoles WHERE RoleID = @roleId');
        // Reassign users who only had this role to the default 'Employee' role (within transaction)
        if (usersToUpdate.length > 0) {
            context.log(`Reassigning ${usersToUpdate.length} users to default 'Employee' role.`);
            // Batch insert might be more efficient, but loop is clearer for now
            for (const userId of usersToUpdate) {
                const reassignRequest = transaction.request();
                reassignRequest.input('userId', sql.NVarChar, userId); // Assuming UserID is NVarChar based on schema
                reassignRequest.input('defaultRoleId', sql.Int, defaultUserRoleId);
                // Use MERGE or INSERT IGNORE to prevent errors if user somehow already has Employee role
                await reassignRequest.query('INSERT INTO UserRoles (UserID, RoleID) VALUES (@userId, @defaultRoleId)');
            }
        }
        // Delete the role itself (within transaction)
        const deleteRoleRequest = transaction.request();
        deleteRoleRequest.input('roleId', sql.Int, roleId);
        await deleteRoleRequest.query('DELETE FROM Roles WHERE RoleID = @roleId');
        // Commit the transaction
        await transaction.commit();
        context.log(`Successfully deleted role ${roleId}`);
        return { status: 204 }; // No content
    }
    catch (error) {
        context.error(`Error deleting role ${roleId}:`, error?.message);
        // Rollback the transaction if it was started 
        // Attempt rollback - mssql should handle if already rolled back/committed
        if (transaction) {
            try {
                await transaction.rollback();
                context.log("Transaction rolled back due to error.");
            }
            catch (rollbackError) {
                context.error("Failed to rollback transaction:", rollbackError?.message);
            }
        }
        return {
            status: 500,
            jsonBody: {
                message: `Error deleting role ${roleId}.`,
                error: error?.message // Include actual error message safely
            }
        };
    }
}
exports.deleteRole = deleteRole;
functions_1.app.http('DeleteRole', {
    methods: ['DELETE'],
    route: 'roles/{roleId:int}',
    authLevel: 'function',
    handler: deleteRole
});
//# sourceMappingURL=DeleteRole.js.map