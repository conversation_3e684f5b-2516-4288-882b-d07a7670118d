{"version": 3, "file": "OAuthTokenService.js", "sourceRoot": "", "sources": ["../../src/services/OAuthTokenService.ts"], "names": [], "mappings": ";;;;;;AAAA,mDAAgD;AAChD,qCAAuC;AACvC,kDAAwB;AA2BxB,MAAa,iBAAiB;IAE1B;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACvB,MAAc,EACd,kBAA0B,MAAM,EAChC,cAAsB,MAAM;QAE5B,IAAI;YACA,eAAM,CAAC,IAAI,CAAC,oDAAoD,MAAM,aAAa,eAAe,IAAI,WAAW,EAAE,CAAC,CAAC;YAErH,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBAC9B,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;iBAC1C,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC;iBAC5D,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACpD,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAEpC,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,eAAM,CAAC,IAAI,CAAC,kDAAkD,MAAM,gBAAgB,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;gBAEvG,OAAO;oBACH,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,eAAe;oBAChC,WAAW,EAAE,WAAW;oBACxB,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,SAAS,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;oBACpC,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,QAAQ;oBACtC,QAAQ,EAAE,IAAI;iBACjB,CAAC;aACL;YAED,eAAM,CAAC,IAAI,CAAC,qDAAqD,MAAM,EAAE,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,SAAS,CAClB,MAAc,EACd,WAAmB,EACnB,YAAqB,EACrB,YAAoB,IAAI,EACxB,KAAc,EACd,kBAA0B,MAAM,EAChC,cAAsB,MAAM,EAC5B,YAAoB,QAAQ;QAE5B,IAAI;YACA,eAAM,CAAC,IAAI,CAAC,4CAA4C,MAAM,gBAAgB,SAAS,UAAU,CAAC,CAAC;YAEnG,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBAC9B,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;iBAC1C,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC;iBAC5D,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACpD,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,eAAG,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACxD,KAAK,CAAC,cAAc,EAAE,eAAG,CAAC,QAAQ,CAAC,eAAG,CAAC,GAAG,CAAC,EAAE,YAAY,IAAI,IAAI,CAAC;iBAClE,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,SAAS,CAAC;iBACtC,KAAK,CAAC,OAAO,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC;iBAChD,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;iBAC/C,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE/B,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC5C,eAAM,CAAC,IAAI,CAAC,uDAAuD,OAAO,EAAE,CAAC,CAAC;gBAC9E,OAAO,OAAO,CAAC;aAClB;YAED,eAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAC7E,OAAO,IAAI,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CACrB,MAAc,EACd,kBAA0B,MAAM,EAChC,cAAsB,MAAM;QAE5B,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBAC9B,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;iBAC1C,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC;iBAC5D,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACpD,KAAK,CAAC,uFAAuF,CAAC,CAAC;YAEpG,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC;aACjD;YAED,OAAO,KAAK,CAAC;SAEhB;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,2DAA2D,EAAE,KAAK,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC,CAAC,2CAA2C;SAC3D;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,SAAS,CAClB,SAAiB,EACjB,kBAA0B,MAAM,EAChC,cAAsB,MAAM;QAE5B,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBAC9B,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC;iBAC5D,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACpD,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC;iBAChD,KAAK,CAAC;;;;;;;iBAON,CAAC,CAAC;YAEP,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEnC,gEAAgE;gBAChE,IAAI,MAAM,CAAC,WAAW,EAAE;oBACpB,eAAM,CAAC,IAAI,CAAC,6BAA6B,SAAS,sEAAsE,CAAC,CAAC;iBAC7H;gBAED,OAAO,MAAM,CAAC,WAAW,CAAC;aAC7B;YAED,eAAM,CAAC,IAAI,CAAC,wCAAwC,SAAS,EAAE,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACpB,MAAc,EACd,kBAA0B,MAAM,EAChC,cAAsB,MAAM;QAE5B,IAAI;YACA,eAAM,CAAC,IAAI,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;YAEpE,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,OAAO,EAAE;iBACf,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC;iBAC1C,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,CAAC;iBAC5D,KAAK,CAAC,aAAa,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;iBACpD,KAAK,CAAC;;;;;;;iBAON,CAAC,CAAC;YAEP,eAAM,CAAC,IAAI,CAAC,6CAA6C,MAAM,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;SAEf;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CACjB,OAAe,EACf,eAAwB,EACxB,cAAuB,EACvB,YAAqB;QAErB,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,OAAO,EAAE;iBACf,KAAK,CAAC,SAAS,EAAE,eAAG,CAAC,GAAG,EAAE,OAAO,CAAC;iBAClC,KAAK,CAAC,iBAAiB,EAAE,eAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,eAAe,IAAI,IAAI,CAAC;iBACpE,KAAK,CAAC,gBAAgB,EAAE,eAAG,CAAC,GAAG,EAAE,cAAc,IAAI,IAAI,CAAC;iBACxD,KAAK,CAAC,cAAc,EAAE,eAAG,CAAC,QAAQ,CAAC,eAAG,CAAC,GAAG,CAAC,EAAE,YAAY,IAAI,IAAI,CAAC;iBAClE,KAAK,CAAC;;;iBAGN,CAAC,CAAC;SAEV;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE;gBAC1D,OAAO;gBACP,eAAe;gBACf,YAAY,EAAG,KAAe,CAAC,OAAO;aACzC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC3B,IAAI;YACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;iBAC9B,KAAK,CAAC;;;;;;iBAMN,CAAC,CAAC;YAEP,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAChC,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,eAAe,EAAE,GAAG,CAAC,eAAe;gBACpC,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,WAAW,EAAE,UAAU;gBACvB,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;gBAClC,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,SAAS,EAAE,GAAG,CAAC,SAAS;aAC3B,CAAC,CAAC,CAAC;SAEP;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,EAAE,CAAC;SACb;IACL,CAAC;CACJ;AAzQD,8CAyQC"}