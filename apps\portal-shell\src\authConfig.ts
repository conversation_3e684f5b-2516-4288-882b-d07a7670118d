import { Configuration, LogLevel } from "@azure/msal-browser";

// MSAL configuration for multi-tenant support
export const msalConfig: Configuration = {
    auth: {
        clientId: "8e0a4158-ee89-4a4f-ac1c-3c08f3b3a972", // Client ID from FalconIris SPA App Registration
        authority: "https://login.microsoftonline.com/common", // Multi-tenant endpoint
        redirectUri: window.location.origin + "/", // Dynamic redirect URI based on current origin
        postLogoutRedirectUri: window.location.origin + "/", // Dynamic post-logout redirect
        navigateToLoginRequestUrl: false, // Prevent navigation to login request URL after login
    },
    cache: {
        cacheLocation: "sessionStorage", // "localStorage" or "sessionStorage"
        storeAuthStateInCookie: false, // Set to true for IE11 or Edge
    },
    system: {
        loggerOptions: {
            loggerCallback: (level: LogLevel, message: string, containsPii: boolean) => {
                if (containsPii) {
                    return;
                }
                switch (level) {
                    case LogLevel.Error:
                        console.error(message);
                        return;
                    case LogLevel.Info:
                        console.info(message);
                        return;
                    case LogLevel.Verbose:
                        console.debug(message);
                        return;
                    case LogLevel.Warning:
                        console.warn(message);
                        return;
                    default:
                        return;
                }
            },
            logLevel: LogLevel.Info, // More verbose logging for debugging
        },
    },
};

// Add scopes here for ID token to be used at Microsoft identity platform endpoints.
export const loginRequest = {
    scopes: [
        "openid", 
        "profile", 
        "User.Read"  // Basic user profile - no admin consent required
    ],
    prompt: "select_account" // Always show account picker for multi-tenant scenarios
};

// Add scopes here for access token to be used at Microsoft Graph endpoints.
export const graphConfig = {
    graphMeEndpoint: "https://graph.microsoft.com/v1.0/me",
    graphUsersEndpoint: "https://graph.microsoft.com/v1.0/users",
    graphOrganizationEndpoint: "https://graph.microsoft.com/v1.0/organization"
};

// Scopes for Microsoft Graph API calls - admin permissions available when needed
export const graphScopes = {
    userRead: ["User.Read"],
    userReadAll: ["User.Read.All"],           // Admin consent required - for admin functions only
    directoryReadAll: ["Directory.Read.All"], // Admin consent required - for admin functions only
    // Basic user permissions for regular operations
    basic: ["openid", "profile", "User.Read"]
}; 