"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateChangeRequestCompletionDate = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const mssql_1 = __importDefault(require("mssql"));
async function updateChangeRequestCompletionDate(request, context) {
    context.log('UpdateChangeRequestCompletionDate function invoked.');
    try {
        // Set CORS headers
        const headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Content-Type': 'application/json'
        };
        // Handle preflight request
        if (request.method === 'OPTIONS') {
            return {
                status: 200,
                headers
            };
        }
        const requestId = request.params.requestId;
        if (!requestId) {
            return {
                status: 400,
                headers,
                jsonBody: {
                    error: {
                        code: 'MISSING_REQUEST_ID',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        // Parse request body
        const body = await request.json();
        if (!body.requestedCompletionDate) {
            return {
                status: 400,
                headers,
                jsonBody: {
                    error: {
                        code: 'MISSING_COMPLETION_DATE',
                        message: 'Requested completion date is required'
                    }
                }
            };
        }
        // Update the requested completion date and deployment date
        const updateQuery = `
            UPDATE ChangeRequests 
            SET RequestedCompletionDate = @requestedCompletionDate,
                DeploymentDate = @requestedCompletionDate
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: mssql_1.default.Int, value: parseInt(requestId) },
            { name: 'requestedCompletionDate', type: mssql_1.default.Date, value: new Date(body.requestedCompletionDate) }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add a comment if reason was provided
        if (body.reason && body.reason.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
                )
            `;
            const commentParams = [
                { name: 'requestId', type: mssql_1.default.Int, value: parseInt(requestId) },
                { name: 'commentText', type: mssql_1.default.NVarChar, value: `[DATE_OVERRIDE] ${body.reason.trim()}` },
                { name: 'commentType', type: mssql_1.default.NVarChar, value: 'General' },
                { name: 'isInternal', type: mssql_1.default.Bit, value: false },
                { name: 'userId', type: mssql_1.default.Int, value: body.userId || 1 }
            ];
            await (0, db_1.executeQuery)(commentQuery, commentParams);
        }
        context.log(`Successfully updated completion date for change request ${requestId}`);
        return {
            status: 200,
            headers,
            jsonBody: {
                success: true,
                message: 'Completion date updated successfully',
                data: {
                    requestId: parseInt(requestId),
                    newCompletionDate: body.requestedCompletionDate
                }
            }
        };
    }
    catch (error) {
        context.error('Error in UpdateChangeRequestCompletionDate:', error);
        return {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'application/json'
            },
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while updating the completion date'
                }
            }
        };
    }
}
exports.updateChangeRequestCompletionDate = updateChangeRequestCompletionDate;
functions_1.app.http('UpdateChangeRequestCompletionDate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/update-completion-date',
    handler: updateChangeRequestCompletionDate
});
//# sourceMappingURL=UpdateChangeRequestCompletionDate.js.map