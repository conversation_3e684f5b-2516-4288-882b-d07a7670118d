{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/UpdateRole/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAeA,gCAkJC;AAjKD,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,6BAA6B;AAW7B,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;QAEzE,IAAI,IAA2B,CAAC;QAChC,IAAI,CAAC;YACD,IAAI,IAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAA,CAAC;QACzD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;gBACH,<PERSON>AM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACzD,CAAC;QACN,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAChB,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;aACnD,CAAC;QACN,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE,CAAC;YAC1G,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wEAAwE,EAAE;aAClG,CAAC;QACN,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;YACvD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;aACrD,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,qBAAqB;YACrB,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;YAC7E,CAAC;YAED,uEAAuE;YACvE,MAAM,gBAAgB,GAAG,CAAC,CAAC;YAE3B,4EAA4E;YAC5E,MAAM,mBAAmB,GAAG,yEAAyE,CAAC;YACtG,MAAM,oBAAoB,GAAqB;gBAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;aACnD,CAAC;YACF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;YAExF,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3E,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,aAAa,CAAC,CAAC;gBAC7D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,EAAE,CAAC;YACnE,CAAC;YACD,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,WAAW,CAAC,QAAQ,CAAC;YAC7C,MAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;YAE9C,6EAA6E;YAC7E,IAAI,YAAY,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,gDAAgD,eAAe,UAAU,MAAM,IAAI,CAAC,CAAC;gBACjG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;YACpF,CAAC;YAED,2DAA2D;YAC3D,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxF,MAAM,cAAc,GAAG,oGAAoG,CAAC;gBAC5H,MAAM,eAAe,GAAqB;oBACtC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;oBACrE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;iBACnD,CAAC;gBACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;gBACxE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;oBACrC,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;oBAC/E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;gBAC7E,CAAC;YACL,CAAC;YAED,oCAAoC;YACpC,IAAI,KAAK,GAAG,mBAAmB,CAAC;YAChC,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,YAAY,GAAqB,EAAE,CAAC,CAAC,2BAA2B;YAEtE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxF,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC7F,CAAC;YACD,kCAAkC;YAClC,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACjC,YAAY,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACpD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YAC5F,CAAC;YACD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACrC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,qGAAqG;gBACpG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,4DAA4D,EAAE,EAAE,CAAC;YAC/G,CAAC;YAED,4CAA4C;YAC5C,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC9C,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACjD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAElF,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,KAAK,IAAI,0FAA0F,CAAC;YACpG,KAAK,IAAI,yBAAyB,CAAC;YACnC,iEAAiE;YACjE,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAEpE,oBAAoB;YACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,iBAAiB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAClG,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrD,eAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,uBAAuB,CAAC,CAAC;gBACjF,4EAA4E;gBAC5E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;YAC1F,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,YAAY,GAAmB;gBACjC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,WAAW,EAAE,eAAe,CAAC,eAAe;gBAC5C,QAAQ,EAAE,eAAe,CAAC,QAAQ;aACrC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,eAAe,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;YACxF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAClG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;oBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;iBAC/E;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}