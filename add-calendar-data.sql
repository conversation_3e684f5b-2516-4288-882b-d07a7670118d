-- Add deployment date columns if they don't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ChangeRequests' AND COLUMN_NAME = 'DeploymentDate')
BEGIN
    ALTER TABLE ChangeRequests 
    ADD 
        DeploymentDate DATETIME2 NULL,
        DeploymentDuration INT NULL,
        DeploymentLocation NVARCHAR(100) NULL,
        DeploymentType NVARCHAR(50) NULL,
        CalendarColor NVARCHAR(7) NULL,
        RequiresSystemDowntime BIT DEFAULT 0;
END

-- Add sample deployment dates to existing change requests
UPDATE TOP(10) ChangeRequests 
SET 
    DeploymentDate = DATEADD(DAY, 
        CASE 
            WHEN RequestID % 7 = 0 THEN 1
            WHEN RequestID % 7 = 1 THEN 3
            WHEN RequestID % 7 = 2 THEN 7
            WHEN RequestID % 7 = 3 THEN 10
            WHEN RequestID % 7 = 4 THEN 14
            WHEN RequestID % 7 = 5 THEN 21
            ELSE 30
        END, 
        GETDATE()
    ),
    DeploymentDuration = 
        CASE Priority
            WHEN 'Critical' THEN 30
            WHEN 'High' THEN 60
            WHEN 'Medium' THEN 120
            WHEN 'Low' THEN 240
            ELSE 60
        END,
    DeploymentLocation = 
        CASE RequestID % 3
            WHEN 0 THEN 'Production'
            WHEN 1 THEN 'Staging'
            ELSE 'Development'
        END,
    DeploymentType = 'Manual',
    RequiresSystemDowntime = 
        CASE 
            WHEN Priority IN ('Critical', 'High') THEN 1
            ELSE 0
        END,
    CalendarColor = 
        CASE Priority
            WHEN 'Critical' THEN '#FF4444'
            WHEN 'High' THEN '#FF8800'
            WHEN 'Medium' THEN '#4488FF'
            WHEN 'Low' THEN '#44AA44'
            ELSE '#666666'
        END
WHERE DeploymentDate IS NULL;

PRINT 'Calendar deployment dates added successfully'; 