{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/UpdateRole/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,2CAA6B;AAWtB,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;IAEzE,IAAI,IAA2B,CAAC;IAChC,IAAI;QACA,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAC;KACxD;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;QAChD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;SACzD,CAAC;KACL;IAED,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACf,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;SACnD,CAAC;KACL;IAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE;QACzG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wEAAwE,EAAE;SAClG,CAAC;KACL;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;QACtD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;SACrD,CAAC;KACL;IAED,IAAI;QACA,qBAAqB;QACrB,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACf,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;SAC5E;QAED,uEAAuE;QACvE,MAAM,gBAAgB,GAAG,CAAC,CAAC;QAE3B,4EAA4E;QAC5E,MAAM,mBAAmB,GAAG,yEAAyE,CAAC;QACtG,MAAM,oBAAoB,GAAqB;YAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;SACnD,CAAC;QACF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;QAExF,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1E,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,aAAa,CAAC,CAAC;YAC7D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,EAAE,CAAC;SAClE;QACD,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,eAAe,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC7C,MAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAE9C,6EAA6E;QAC7E,IAAI,YAAY,EAAE;YACd,eAAM,CAAC,IAAI,CAAC,gDAAgD,eAAe,UAAU,MAAM,IAAI,CAAC,CAAC;YACjG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;SACnF;QAED,2DAA2D;QAC3D,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,EAAE;YACvF,MAAM,cAAc,GAAG,oGAAoG,CAAC;YAC5H,MAAM,eAAe,GAAqB;gBACtC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;gBACrE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;aACnD,CAAC;YACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YACxE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;gBACpC,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBAC/E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;aAC5E;SACJ;QAED,oCAAoC;QACpC,IAAI,KAAK,GAAG,mBAAmB,CAAC;QAChC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,YAAY,GAAqB,EAAE,CAAC,CAAC,2BAA2B;QAEtE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,EAAE;YACvF,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SAC5F;QACD,kCAAkC;QAClC,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YAChC,YAAY,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACpD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SAC3F;QACD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YACpC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChF;QAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,qGAAqG;YACpG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,4DAA4D,EAAE,EAAE,CAAC;SAC9G;QAED,4CAA4C;QAC5C,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC9C,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACjD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAElF,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,KAAK,IAAI,0FAA0F,CAAC;QACpG,KAAK,IAAI,yBAAyB,CAAC;QACnC,iEAAiE;QACjE,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAEpE,oBAAoB;QACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,iBAAiB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClG,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEvD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,eAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,uBAAuB,CAAC,CAAC;YACjF,4EAA4E;YAC5E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;SACzF;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAmB;YACjC,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,WAAW,EAAE,eAAe,CAAC,eAAe;YAC5C,QAAQ,EAAE,eAAe,CAAC,QAAQ;SACrC,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,eAAe,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;QACxF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;KAElD;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;QAClG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;gBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;aAC/E;SACJ,CAAC;KACL;AACL,CAAC;AAlJD,gCAkJC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}