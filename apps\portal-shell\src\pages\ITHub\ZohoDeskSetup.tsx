import React, { useState, useEffect } from 'react';
import { ExternalLink, CheckCircle, AlertCircle, Settings, Key } from 'feather-icons-react';

interface ZohoDeskStatus {
  isConfigured: boolean;
  isAuthenticated: boolean;
  organizationId?: string;
  lastSync?: string;
  error?: string;
}

const ZohoDeskSetup: React.FC = () => {
  const [status, setStatus] = useState<ZohoDeskStatus>({
    isConfigured: false,
    isAuthenticated: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check current Zoho Desk integration status
  useEffect(() => {
    checkZohoDeskStatus();
  }, []);

  const checkZohoDeskStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if OAuth token exists and is valid
      const response = await fetch('/api/auth/zoho-desk/token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setStatus({
          isConfigured: true,
          isAuthenticated: true,
          lastSync: new Date().toISOString()
        });
      } else if (response.status === 404) {
        setStatus({
          isConfigured: true,
          isAuthenticated: false,
          error: 'Not authenticated with Zoho Desk'
        });
      } else {
        setStatus({
          isConfigured: false,
          isAuthenticated: false,
          error: 'Zoho Desk not configured'
        });
      }
    } catch (err) {
      console.error('Failed to check Zoho Desk status:', err);
      setError('Failed to check Zoho Desk status');
      setStatus({
        isConfigured: false,
        isAuthenticated: false,
        error: 'Connection error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOAuthAuthorization = async () => {
    setIsAuthenticating(true);
    setError(null);

    try {
      // Get authorization URL from backend
      const response = await fetch('/api/auth/zoho-desk/authorize', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get authorization URL');
      }

      const { authUrl } = await response.json();
      
      // Open authorization URL in new window
      const authWindow = window.open(authUrl, 'zoho-auth', 'width=600,height=700,scrollbars=yes,resizable=yes');
      
      // Poll for completion
      const checkCompletion = setInterval(() => {
        try {
          if (authWindow?.closed) {
            clearInterval(checkCompletion);
            // Check status after a brief delay
            setTimeout(() => {
              checkZohoDeskStatus();
            }, 1000);
            setIsAuthenticating(false);
          }
        } catch (err) {
          // Cross-origin error means the window is still open
          console.error('Auth window polling error:', err);
        }
      }, 1000);

    } catch (err) {
      console.error('Failed to start OAuth authorization:', err);
      setError('Failed to start OAuth authorization');
      setIsAuthenticating(false);
    }
  };

  const testConnection = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Test connection by fetching departments
      const response = await fetch('/api/zoho-desk/departments', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setError(null);
        alert(`Connection successful! Found ${data.data?.length || 0} departments.`);
        await checkZohoDeskStatus();
      } else {
        throw new Error(`Connection test failed: ${response.status}`);
      }
    } catch (err) {
      console.error('Connection test failed:', err);
      setError('Connection test failed. Please check your configuration.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Zoho Desk Integration Setup</h1>
        <p className="text-gray-600">Configure and manage the connection to Zoho Desk for IT support tickets.</p>
      </div>

      {/* Status Card */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Integration Status</h2>
          <button
            onClick={checkZohoDeskStatus}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
          >
            {isLoading ? 'Checking...' : 'Refresh Status'}
          </button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Configuration Status */}
            <div className="flex items-center">
              {status.isConfigured ? (
                <CheckCircle size={20} className="text-green-500 mr-3" />
              ) : (
                <AlertCircle size={20} className="text-red-500 mr-3" />
              )}
              <div>
                <p className="font-medium text-gray-900">
                  Backend Configuration: {status.isConfigured ? 'Configured' : 'Not Configured'}
                </p>
                <p className="text-sm text-gray-500">
                  {status.isConfigured 
                    ? 'OAuth credentials and endpoints are set up'
                    : 'Missing OAuth client ID, secret, or organization ID'
                  }
                </p>
              </div>
            </div>

            {/* Authentication Status */}
            <div className="flex items-center">
              {status.isAuthenticated ? (
                <CheckCircle size={20} className="text-green-500 mr-3" />
              ) : (
                <AlertCircle size={20} className="text-amber-500 mr-3" />
              )}
              <div>
                <p className="font-medium text-gray-900">
                  Authentication: {status.isAuthenticated ? 'Connected' : 'Not Connected'}
                </p>
                <p className="text-sm text-gray-500">
                  {status.isAuthenticated 
                    ? 'Valid OAuth token available for API calls'
                    : 'OAuth authorization required to access Zoho Desk'
                  }
                </p>
              </div>
            </div>

            {/* Last Sync */}
            {status.lastSync && (
              <div className="flex items-center">
                <Settings size={20} className="text-gray-400 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Last Sync</p>
                  <p className="text-sm text-gray-500">{formatDate(status.lastSync)}</p>
                </div>
              </div>
            )}

            {/* Organization ID */}
            {status.organizationId && (
              <div className="flex items-center">
                <Key size={20} className="text-gray-400 mr-3" />
                <div>
                  <p className="font-medium text-gray-900">Organization ID</p>
                  <p className="text-sm text-gray-500">{status.organizationId}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Setup Instructions */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Setup Instructions</h2>
        
        <div className="space-y-4">
          <div className="border-l-4 border-blue-400 pl-4">
            <h3 className="font-medium text-gray-900">1. Backend Configuration</h3>
            <p className="text-sm text-gray-600 mt-1">
              Ensure the following environment variables are set in your Azure Functions:
            </p>
            <ul className="text-sm text-gray-600 mt-2 space-y-1">
              <li><code className="bg-gray-100 px-1 rounded">ZOHO_DESK_CLIENT_ID</code> - OAuth Client ID from Zoho Desk</li>
              <li><code className="bg-gray-100 px-1 rounded">ZOHO_DESK_CLIENT_SECRET</code> - OAuth Client Secret</li>
              <li><code className="bg-gray-100 px-1 rounded">ZOHO_DESK_ORG_ID</code> - Your organization ID</li>
              <li><code className="bg-gray-100 px-1 rounded">ZOHO_DESK_REDIRECT_URI</code> - OAuth callback URL</li>
            </ul>
          </div>

          <div className="border-l-4 border-green-400 pl-4">
            <h3 className="font-medium text-gray-900">2. Zoho Desk OAuth App</h3>
            <p className="text-sm text-gray-600 mt-1">
              Create an OAuth application in Zoho Desk Developer Space with these settings:
            </p>
            <ul className="text-sm text-gray-600 mt-2 space-y-1">
              <li>• Client Type: Web Application</li>
              <li>• Redirect URL: <code className="bg-gray-100 px-1 rounded">http://localhost:7075/api/auth/zoho-desk/callback</code></li>
              <li>• Scopes: Desk.tickets.ALL, Desk.contacts.ALL, Desk.agents.READ, etc.</li>
            </ul>
          </div>

          <div className="border-l-4 border-purple-400 pl-4">
            <h3 className="font-medium text-gray-900">3. OAuth Authorization</h3>
            <p className="text-sm text-gray-600 mt-1">
              After backend configuration, authorize the application to access your Zoho Desk.
            </p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
        
        <div className="flex flex-wrap gap-4">
          {/* OAuth Authorization Button */}
          <button
            onClick={handleOAuthAuthorization}
            disabled={isAuthenticating || !status.isConfigured}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <ExternalLink size={16} className="mr-2" />
            {isAuthenticating ? 'Authorizing...' : 'Authorize with Zoho Desk'}
          </button>

          {/* Test Connection Button */}
          <button
            onClick={testConnection}
            disabled={isLoading || !status.isAuthenticated}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <Settings size={16} className="mr-2" />
            Test Connection
          </button>

          {/* Zoho Desk Console Link */}
          <a
            href="https://desk.zoho.in"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <ExternalLink size={16} className="mr-2" />
            Open Zoho Desk Console
          </a>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle size={20} className="text-red-400 mr-3" />
              <div className="text-sm text-red-700">{error}</div>
            </div>
          </div>
        )}

        {/* Success Message */}
        {status.isAuthenticated && !error && (
          <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircle size={20} className="text-green-400 mr-3" />
              <div className="text-sm text-green-700">
                Zoho Desk integration is active and ready to use!
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ZohoDeskSetup; 