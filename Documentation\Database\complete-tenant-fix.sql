-- Complete Tenant ID Fix Script
-- This script handles the entire tenant ID setup from scratch

-- Step 1: Add TenantID column to Companies table if it doesn't exist
PRINT 'Step 1: Checking and adding TenantID column to Companies table...';
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'TenantID')
BEGIN
    ALTER TABLE Companies ADD TenantID NVARCHAR(50) NULL;
    PRINT 'Added TenantID column to Companies table';
END
ELSE
BEGIN
    PRINT 'TenantID column already exists in Companies table';
END

-- Step 2: Update Companies table with correct tenant IDs
PRINT '';
PRINT 'Step 2: Updating Companies table with correct tenant IDs...';

-- Update each company with its correct tenant ID
UPDATE Companies SET TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' WHERE CompanyName = 'Avirata Defence Systems';
UPDATE Companies SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyName = 'SASMOS HET';
UPDATE Companies SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyName = 'SASMOS Group';
UPDATE Companies SET TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' WHERE CompanyName = 'FE-SIL';
UPDATE Companies SET TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' WHERE CompanyName = 'Glodesi';
UPDATE Companies SET TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' WHERE CompanyName = 'Hanuka';
UPDATE Companies SET TenantID = 'PLACEHOLDER_WWH_TENANT_ID' WHERE CompanyName = 'West Wire Harnessing';

PRINT 'Companies table updated with tenant IDs';

-- Step 3: Show current Companies table state
PRINT '';
PRINT 'Step 3: Current Companies table with tenant IDs:';
SELECT CompanyID, CompanyName, CompanyCode, TenantID FROM Companies ORDER BY CompanyName;

-- Step 4: Show current Users table state BEFORE fix
PRINT '';
PRINT 'Step 4: Current Users table state (BEFORE FIX):';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    u.TenantID as CurrentUserTenantID
FROM Users u
ORDER BY u.Email;

-- Step 5: Update Users table with correct tenant IDs based on CompanyID
PRINT '';
PRINT 'Step 5: Updating Users table with correct tenant IDs...';

-- Direct update based on CompanyID
UPDATE Users 
SET TenantID = 
    CASE 
        WHEN CompanyID = 1 THEN 'ecb4a448-4a99-443b-aaff-063150b6c9ea'  -- Avirata Defence Systems
        WHEN CompanyID = 2 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS HET
        WHEN CompanyID = 3 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS Group (same as HET)
        WHEN CompanyID = 4 THEN 'd6a5d909-b6c5-4724-a46d-2641d73acff1'  -- FE-SIL
        WHEN CompanyID = 5 THEN '7732add2-c45c-472b-8da8-4e2b4699bbb0'  -- Glodesi
        WHEN CompanyID = 6 THEN 'a8dcc1ff-5cc0-4432-827b-9da18737a775'  -- Hanuka
        WHEN CompanyID = 7 THEN 'PLACEHOLDER_WWH_TENANT_ID'              -- West Wire Harnessing (placeholder)
        ELSE TenantID -- Keep current if unknown company
    END,
    ModifiedDate = GETUTCDATE(),
    ModifiedBy = 1  -- System update
WHERE CompanyID IN (1, 2, 3, 4, 5, 6, 7);

PRINT 'Users table updated with correct tenant IDs';

-- Step 6: Final verification - show Users table state AFTER fix
PRINT '';
PRINT 'Step 6: Users table state (AFTER FIX):';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    u.TenantID as UpdatedUserTenantID
FROM Users u
ORDER BY u.Email;

-- Step 7: Show complete mapping verification
PRINT '';
PRINT 'Step 7: Complete User-Company-Tenant Mapping Verification:';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    c.CompanyName,
    u.TenantID as UserTenantID,
    c.TenantID as CompanyTenantID,
    CASE 
        WHEN u.TenantID = c.TenantID THEN 'CORRECT ✓'
        ELSE 'MISMATCH ✗'
    END as Status
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
ORDER BY u.Email;

-- Step 8: Show tenant distribution
PRINT '';
PRINT 'Step 8: User Distribution by Tenant:';
SELECT 
    CASE 
        WHEN u.TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' THEN 'Avirata Defence Systems'
        WHEN u.TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' THEN 'SASMOS HET'
        WHEN u.TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' THEN 'FE-SIL'
        WHEN u.TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' THEN 'Glodesi'
        WHEN u.TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' THEN 'Hanuka'
        WHEN u.TenantID = 'PLACEHOLDER_WWH_TENANT_ID' THEN 'West Wire Harnessing (TBD)'
        ELSE 'Unknown Tenant'
    END as TenantName,
    u.TenantID,
    COUNT(*) as UserCount,
    STRING_AGG(u.Email, ', ') as UserEmails
FROM Users u
GROUP BY u.TenantID
ORDER BY TenantName;

-- Step 9: Check for any remaining issues
PRINT '';
PRINT 'Step 9: Checking for remaining issues:';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    c.CompanyName,
    u.TenantID as UserTenantID,
    c.TenantID as CompanyTenantID
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE u.TenantID != c.TenantID OR u.TenantID IS NULL OR c.TenantID IS NULL;

PRINT '';
PRINT '==========================================';
PRINT 'TENANT ID FIX COMPLETED!';
PRINT '==========================================';
PRINT '';
PRINT 'Summary:';
PRINT '- Added TenantID column to Companies table (if needed)';
PRINT '- Updated all companies with correct tenant IDs';
PRINT '- Updated all users with correct tenant IDs based on company';
PRINT '- Verified mapping consistency';
PRINT '';
PRINT 'IMPORTANT NOTES:';
PRINT '- WestWire Harnessing users have placeholder TenantID';
PRINT '- Update WWH TenantID once you have the actual tenant ID';
PRINT '- All other users should now authenticate with their company tenant';
PRINT '- Multi-tenant authentication should now work correctly'; 