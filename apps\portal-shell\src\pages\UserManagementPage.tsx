import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { fetchPortalUsers, PortalUser, fetchPortalRoleNames, fetchCompanyNames, PORTAL_STATUSES, PaginatedPortalUsersResponse } from '../services/adminApi';
import { ChevronLeft, ChevronRight } from 'feather-icons-react';

// Constants
const PAGE_SIZE = 10;

// Pagination helper (same as before)
const getPaginationRange = (currentPage: number, totalPages: number, siblingCount: number = 1): (number | string)[] => {
    const totalPageNumbers = siblingCount + 5;
    if (totalPageNumbers >= totalPages) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1;
    const firstPageIndex = 1;
    const lastPageIndex = totalPages;
    const DOTS = '...';
    if (!shouldShowLeftDots && shouldShowRightDots) {
        const leftItemCount = 3 + 2 * siblingCount;
        const leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
        return [...leftRange, DOTS, lastPageIndex];
    }
    if (shouldShowLeftDots && !shouldShowRightDots) {
        const rightItemCount = 3 + 2 * siblingCount;
        const rightRange = Array.from({ length: rightItemCount }, (_, i) => totalPages - i).reverse();
        return [firstPageIndex, DOTS, ...rightRange];
    }
    if (shouldShowLeftDots && shouldShowRightDots) {
        const middleRange = Array.from({ length: rightSiblingIndex - leftSiblingIndex + 1 }, (_, i) => leftSiblingIndex + i);
        return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }
    return [];
};

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<PortalUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Filters for the list of managed users
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [companyFilter, setCompanyFilter] = useState<string>('All');
  const [roleFilter, setRoleFilter] = useState<string>('All');
  const [statusFilter, setStatusFilter] = useState<PortalUser['status'] | 'All'>('All');
  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const totalPages = Math.ceil(totalUsers / PAGE_SIZE);
  const paginationRange = getPaginationRange(currentPage, totalPages);

  // --- State for Dynamic Roles and Companies ---
  const [availableRoles, setAvailableRoles] = useState<string[]>([]);
  const [availableCompanies, setAvailableCompanies] = useState<string[]>([]);
  const ALL_ROLES = ['All', ...availableRoles];
  const ALL_COMPANIES = ['All', ...availableCompanies];
  const ALL_STATUSES = ['All', ...PORTAL_STATUSES];

  // --- Fetch Available Roles and Companies ---
  useEffect(() => {
    const fetchRolesAndCompanies = async () => {
      try {
        const [roles, companies] = await Promise.all([
          fetchPortalRoleNames(),
          fetchCompanyNames()
        ]);
        setAvailableRoles(roles);
        setAvailableCompanies(companies);
      } catch (err) {
        console.error("Error fetching roles or companies:", err);
        // Fallback to basic roles if API fails
        console.error("Failed to load roles from database");
        setAvailableRoles([]);
        setAvailableCompanies(['Avirata Defence Systems', 'SASMOS HET']);
      }
    };
    fetchRolesAndCompanies();
  }, []);

  // Fetch managed portal users
  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true);
      setError(null);
      try {
        const response: PaginatedPortalUsersResponse = await fetchPortalUsers(
          searchTerm, companyFilter, roleFilter, statusFilter, currentPage, PAGE_SIZE
        );
        setUsers(response.users);
        setTotalUsers(response.totalCount);
      } catch (err) {
        console.error("Error fetching portal users:", err);
        setError('Failed to load managed users. Please try again later.');
        // TODO: Add toast notification for error
      } finally {
        setLoading(false);
      }
    };
    loadUsers();
  }, [searchTerm, companyFilter, roleFilter, statusFilter, currentPage]);

  // Reset page on filter change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, companyFilter, roleFilter, statusFilter]);

  // --- Handlers ---
  const handleNextPage = () => { if (currentPage < totalPages) setCurrentPage(currentPage + 1); };
  const handlePreviousPage = () => { if (currentPage > 1) setCurrentPage(currentPage - 1); };
  const handlePageChange = (pageNumber: number | string) => { if (typeof pageNumber === 'number') setCurrentPage(pageNumber); };

  // TODO: Implement user search functionality (to find users *not* yet managed)
  // const handleSearchForUserToAdd = () => { ... }

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">User Role Management</h1>
        {/* TODO: Add a button/search mechanism to find and add *new* users to manage */}
        {/* <button 
            onClick={handleSearchForUserToAdd}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
            <Search size={18} className="mr-2 -ml-1" />
            Find User to Add
        </button> */}
      </div>

      {/* Filters Section (for managed users list) */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 items-end">
        <div>
          <label htmlFor="search-term" className="block text-sm font-medium text-gray-700 mb-1">Filter Managed Users</label>
          <input 
            id="search-term" 
            type="text" 
            placeholder="By name or email..." 
            value={searchTerm} 
            onChange={(e) => setSearchTerm(e.target.value)} 
            className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
          />
        </div>
        <div>
          <label htmlFor="company-filter" className="block text-sm font-medium text-gray-700 mb-1">Company</label>
          <select id="company-filter" value={companyFilter} onChange={(e) => setCompanyFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {ALL_COMPANIES.map(company => (<option key={company} value={company}>{company}</option>))}
          </select>
        </div>
        <div>
          <label htmlFor="role-filter" className="block text-sm font-medium text-gray-700 mb-1">Role</label>
          <select id="role-filter" value={roleFilter} onChange={(e) => setRoleFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {ALL_ROLES.map(role => (<option key={role} value={role}>{role}</option>))}
          </select>
        </div>
        <div>
          <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select id="status-filter" value={statusFilter} onChange={(e) => setStatusFilter(e.target.value as PortalUser['status'] | 'All')} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {ALL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
          </select>
        </div>
      </div>

      {/* Managed Users Table and Pagination */} 
      <div className="bg-white shadow rounded-lg overflow-hidden"> 
          {loading && <p className="text-center text-gray-500 p-4">Loading users...</p>}
          {error && <p className="text-center text-red-500 p-4">{error}</p>} 
          
          {!loading && !error && (
            <>
              <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                          <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Roles</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Status</th>
                              <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                          </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                          {users.length > 0 ? users.map((user) => (
                              <tr key={user.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.company}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.roles.join(', ')}</td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{user.status}</span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                      {/* Link to the new edit page */} 
                                      <Link to={`/admin/manage-user/${user.id}`} className="text-indigo-600 hover:text-indigo-900">
                                          Manage
                                      </Link>
                                  </td>
                              </tr>
                          )) : (
                            <tr><td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">No users found matching your criteria.</td></tr>
                          )}
                      </tbody>
                  </table>
              </div>
              {/* Pagination Controls */} 
              {totalUsers > 0 && totalPages > 1 && (
                <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    {/* Mobile Pagination */} 
                    <div className="flex-1 flex justify-between sm:hidden">
                        <button onClick={handlePreviousPage} disabled={currentPage === 1} className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">Previous</button>
                        <button onClick={handleNextPage} disabled={currentPage === totalPages} className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">Next</button>
                    </div>
                    {/* Desktop Pagination */} 
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700">Showing <span className="font-medium">{(currentPage - 1) * PAGE_SIZE + 1}</span> to <span className="font-medium">{Math.min(currentPage * PAGE_SIZE, totalUsers)}</span> of <span className="font-medium">{totalUsers}</span> results</p>
                        </div>
                        <div>
                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button onClick={handlePreviousPage} disabled={currentPage === 1} className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span className="sr-only">Previous</span><ChevronLeft className="h-5 w-5" aria-hidden="true" />
                                </button>
                                {paginationRange.map((pageNumber, index) => {
                                    if (pageNumber === '...') {
                                        return <span key={`${pageNumber}-${index}`} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>;
                                    }
                                    return (
                                        <button key={pageNumber} onClick={() => handlePageChange(pageNumber)} aria-current={pageNumber === currentPage ? 'page' : undefined} className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNumber === currentPage ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`}>
                                            {pageNumber}
                                        </button>
                                    );
                                })}
                                <button onClick={handleNextPage} disabled={currentPage === totalPages} className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span className="sr-only">Next</span><ChevronRight className="h-5 w-5" aria-hidden="true" />
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
               )}
            </>
          )} 
      </div>
    </div>
  );
};

export default UserManagementPage; 