import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar, User, Home, Tag, Clock, CheckCircle, XCircle, AlertCircle, Check, X, Send, Edit3 } from 'feather-icons-react';
import { changeManagementApi, type ChangeRequestDetails } from '../../services/changeManagementApi';
import { RichContentViewer } from '../../components/RichContentViewer/RichContentViewer';
import ChangeRequestComments from '../../components/ChangeRequestComments/ChangeRequestComments';
import { useCurrentUser, hasAnyRole } from '../../services/userContext';

const ChangeRequestDetailsPage: React.FC = () => {
  const { requestId } = useParams<{ requestId: string }>();
  const navigate = useNavigate();
  const { user } = useCurrentUser();
  const [changeRequest, setChangeRequest] = useState<ChangeRequestDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Approval action states
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | 'request-info' | null>(null);
  const [reviewComments, setReviewComments] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  // Date override states
  const [showDateOverrideModal, setShowDateOverrideModal] = useState(false);
  const [newRequestedDate, setNewRequestedDate] = useState('');
  const [dateOverrideReason, setDateOverrideReason] = useState('');

  useEffect(() => {
    if (requestId) {
      loadChangeRequest(parseInt(requestId));
    }
  }, [requestId]);

  const loadChangeRequest = async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      const request = await changeManagementApi.getChangeRequestById(id);
      console.log('🔍 Change Request Data:', request);
      console.log('🔍 Content Array:', request.content);
      console.log('🔍 Content Length:', request.content?.length || 0);
      if (request.content && request.content.length > 0) {
        request.content.forEach((block, index) => {
          console.log(`🔍 Content Block ${index}:`, block);
        });
      }
      setChangeRequest(request);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load change request');
      console.error('Error loading change request:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Submitted': return 'bg-orange-100 text-orange-800'; // Orange for "needs action" from submitter
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'In Development': return 'bg-purple-100 text-purple-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'Submitted': return 'Needs Clarification';
      default: return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircle size={20} className="text-green-600" />;
      case 'Rejected': return <XCircle size={20} className="text-red-600" />;
      case 'Under Review': return <AlertCircle size={20} className="text-yellow-600" />;
      default: return <Clock size={20} className="text-blue-600" />;
    }
  };

  // Role-based access control
  const canReview = (request: ChangeRequestDetails) => {
    // Check actual user roles from authentication context
    const isAdminOrChangeManager = hasAnyRole(user, ['Administrator', 'IT Admin', 'Manager']);
    return isAdminOrChangeManager && ['Submitted', 'Under Review'].includes(request.status);
  };

  // Check if user can override completion date
  const canOverrideDate = (request: ChangeRequestDetails) => {
    console.log('🔍 Checking canOverrideDate:', {
      user: user,
      userRoles: user?.roles,
      requestStatus: request.status,
      allowedStatuses: !['Completed', 'In Development'].includes(request.status)
    });
    
    const isAdminOrITHead = hasAnyRole(user, ['Administrator', 'IT Admin']);
    console.log('🔒 Permission check result:', {
      isAdminOrITHead,
      requiredRoles: ['Administrator', 'IT Admin'],
      statusCheck: !['Completed', 'In Development'].includes(request.status),
      finalResult: isAdminOrITHead && !['Completed', 'In Development'].includes(request.status)
    });
    
    return isAdminOrITHead && !['Completed', 'In Development'].includes(request.status);
  };

  // Approval action handlers
  const openReviewModal = (action: 'approve' | 'reject' | 'request-info') => {
    setReviewAction(action);
    setReviewComments('');
    setShowReviewModal(true);
  };

  const handleReviewAction = async () => {
    if (!changeRequest || !reviewAction) return;

    try {
      setActionLoading(true);
      
      switch (reviewAction) {
        case 'approve':
          await changeManagementApi.approveChangeRequest(changeRequest.requestId, reviewComments);
          break;
        case 'reject':
          if (!reviewComments.trim()) {
            alert('Rejection reason is required');
            return;
          }
          await changeManagementApi.rejectChangeRequest(changeRequest.requestId, reviewComments);
          break;
        case 'request-info':
          await changeManagementApi.requestMoreInfoChangeRequest(changeRequest.requestId, reviewComments);
          break;
      }

      // Reload the change request to get updated data
      await loadChangeRequest(changeRequest.requestId);
      
      // Close modal and reset state
      setShowReviewModal(false);
      setReviewAction(null);
      setReviewComments('');
      
    } catch (error) {
      console.error('Error performing review action:', error);
      alert('Failed to perform action. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle submitting a draft change request
  const handleSubmitDraft = async () => {
    if (!changeRequest) return;

    try {
      setActionLoading(true);
      await changeManagementApi.submitChangeRequest(changeRequest.requestId);
      
      // Reload the change request to get updated status
      await loadChangeRequest(changeRequest.requestId);
      
    } catch (error) {
      console.error('Error submitting draft:', error);
      alert('Failed to submit change request. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle date override
  const handleDateOverride = async () => {
    if (!changeRequest || !newRequestedDate) return;

    try {
      setActionLoading(true);
      await changeManagementApi.updateRequestedCompletionDate(
        changeRequest.requestId, 
        newRequestedDate, 
        dateOverrideReason
      );
      
      // Reload the change request to get updated data
      await loadChangeRequest(changeRequest.requestId);
      
      // Close modal and reset state
      setShowDateOverrideModal(false);
      setNewRequestedDate('');
      setDateOverrideReason('');
      
    } catch (error) {
      console.error('Error updating completion date:', error);
      alert('Failed to update completion date. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const openDateOverrideModal = () => {
    if (changeRequest?.requestedCompletionDate) {
      setNewRequestedDate(changeRequest.requestedCompletionDate.split('T')[0]);
    } else {
      setNewRequestedDate('');
    }
    setDateOverrideReason('');
    setShowDateOverrideModal(true);
  };

  if (loading) {
    return (
      <div className="p-6 bg-white min-h-screen">
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading change request...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-white min-h-screen">
        <div className="mb-6">
          <button
            onClick={() => navigate('/it/change-management')}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Change Management
          </button>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <XCircle size={24} className="text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-red-900">Error Loading Request</h3>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!changeRequest) {
    return (
      <div className="p-6 bg-white min-h-screen">
        <div className="mb-6">
          <button
            onClick={() => navigate('/it/change-management')}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Change Management
          </button>
        </div>
        
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Change Request Not Found</h3>
          <p className="text-gray-500">The requested change request could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white min-h-screen">
      {/* Header with Back Button */}
      <div className="mb-6">
        <button
          onClick={() => navigate('/it/change-management')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Change Management
        </button>
        
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{changeRequest.title}</h1>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span className="font-medium text-blue-600">{changeRequest.requestNumber}</span>
              <span>•</span>
              <span>Created {new Date(changeRequest.createdDate).toLocaleDateString()}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(changeRequest.priority)}`}>
              {changeRequest.priority}
            </span>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(changeRequest.status)}`}>
              {getStatusIcon(changeRequest.status)}
              <span className="ml-2">{getStatusDisplayText(changeRequest.status)}</span>
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Description</h2>
            {changeRequest.content && changeRequest.content.length > 0 ? (
              <RichContentViewer content={changeRequest.content} />
            ) : (
              <p className="text-gray-700 whitespace-pre-wrap">{changeRequest.description}</p>
            )}
          </div>

          {/* Business Justification */}
          {changeRequest.businessJustification && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Business Justification</h2>
              <p className="text-gray-700 whitespace-pre-wrap">{changeRequest.businessJustification}</p>
            </div>
          )}

          {/* Expected Benefit */}
          {changeRequest.expectedBenefit && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Expected Benefits</h2>
              <p className="text-gray-700 whitespace-pre-wrap">{changeRequest.expectedBenefit}</p>
            </div>
          )}

          {/* Development Progress */}
          {changeRequest.status === 'In Development' || changeRequest.status === 'Completed' ? (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Development Progress</h2>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium">{changeRequest.developmentProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${changeRequest.developmentProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ) : null}

          {/* Comments Section */}
          <ChangeRequestComments requestId={changeRequest.requestId} />

        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Request Details */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Request Details</h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Tag size={16} className="text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{changeRequest.typeName}</p>
                  {changeRequest.typeDescription && (
                    <p className="text-xs text-gray-500">{changeRequest.typeDescription}</p>
                  )}
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <User size={16} className="text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{changeRequest.requesterName}</p>
                  <p className="text-xs text-gray-500">{changeRequest.requesterEmail}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Home size={16} className="text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{changeRequest.companyName}</p>
                  {changeRequest.departmentName && (
                    <p className="text-xs text-gray-500">{changeRequest.departmentName}</p>
                  )}
                </div>
              </div>

              {changeRequest.requestedCompletionDate && (
                <div className="flex items-start space-x-3">
                  <Calendar size={16} className="text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Requested Completion</p>
                    <p className="text-xs text-gray-500">
                      {new Date(changeRequest.requestedCompletionDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-start space-x-3">
                <Clock size={16} className="text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Estimated Duration</p>
                  <p className="text-xs text-gray-500">{changeRequest.estimatedDays} days</p>
                </div>
              </div>
            </div>
          </div>

          {/* Assignment Information */}
          {(changeRequest.approverName || changeRequest.developerName) && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Assignment</h2>
              <div className="space-y-4">
                {changeRequest.approverName && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Approver</p>
                    <p className="text-sm text-gray-600">{changeRequest.approverName}</p>
                  </div>
                )}
                
                {changeRequest.developerName && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Developer</p>
                    <p className="text-sm text-gray-600">{changeRequest.developerName}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Timeline */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Created</span>
                <span className="text-gray-900">{new Date(changeRequest.createdDate).toLocaleDateString()}</span>
              </div>

              {/* Requested Completion Date - Prominently displayed */}
              <div className="flex justify-between items-center text-sm bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex items-center">
                  <Calendar size={16} className="text-blue-600 mr-2" />
                  <span className="font-medium text-blue-900">Requested Completion</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-blue-900 font-medium">
                    {changeRequest.requestedCompletionDate 
                      ? new Date(changeRequest.requestedCompletionDate).toLocaleDateString()
                      : 'Not specified'
                    }
                  </span>
                  {canOverrideDate(changeRequest) && (
                    <button
                      onClick={openDateOverrideModal}
                      className="inline-flex items-center p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors duration-150"
                      title="Override completion date"
                    >
                      <Edit3 size={14} />
                    </button>
                  )}
                </div>
              </div>
              
              {changeRequest.approvedDate && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Approved</span>
                  <span className="text-gray-900">{new Date(changeRequest.approvedDate).toLocaleDateString()}</span>
                </div>
              )}
              
              {changeRequest.actualStartDate && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Started</span>
                  <span className="text-gray-900">{new Date(changeRequest.actualStartDate).toLocaleDateString()}</span>
                </div>
              )}
              
              {changeRequest.actualCompletionDate && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Completed</span>
                  <span className="text-gray-900">{new Date(changeRequest.actualCompletionDate).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Submit Draft Section - Only for Draft Status */}
      {changeRequest && changeRequest.status === 'Draft' && (
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-4">Submit for Review</h2>
          <p className="text-sm text-blue-700 mb-6">
            This change request is currently saved as a draft. Submit it for review to make it visible in the approval queue.
          </p>
          
          <button
            onClick={handleSubmitDraft}
            disabled={actionLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150 disabled:opacity-50"
          >
            {actionLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Submitting...
              </>
            ) : (
              <>
                <Send size={16} className="mr-2" />
                Submit for Review
              </>
            )}
          </button>
        </div>
      )}

      {/* Approval Actions Section - Only for Admins/Change Managers */}
      {changeRequest && canReview(changeRequest) && (
        <div className="mt-8 bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Review & Approval</h2>
          <p className="text-sm text-gray-600 mb-6">
            Review all details above and select the appropriate action for this change request.
          </p>
          
          <div className="flex space-x-4">
            <button
              onClick={() => openReviewModal('approve')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-150"
            >
              <Check size={16} className="mr-2" />
              Approve
            </button>
            
            <button
              onClick={() => openReviewModal('request-info')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150"
            >
              <AlertCircle size={16} className="mr-2" />
              Request More Information
            </button>
            
            <button
              onClick={() => openReviewModal('reject')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
            >
              <X size={16} className="mr-2" />
              Reject (Final)
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Review Modal */}
      {showReviewModal && changeRequest && reviewAction && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center mb-4">
                {reviewAction === 'approve' && <Check size={24} className="text-green-600 mr-3" />}
                {reviewAction === 'request-info' && <AlertCircle size={24} className="text-blue-600 mr-3" />}
                {reviewAction === 'reject' && <X size={24} className="text-red-600 mr-3" />}
                <h3 className="text-lg font-medium text-gray-900">
                  {reviewAction === 'approve' && 'Approve Change Request'}
                  {reviewAction === 'request-info' && 'Request More Information'}
                  {reviewAction === 'reject' && 'Reject Change Request'}
                </h3>
              </div>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>{changeRequest.requestNumber}:</strong> {changeRequest.title}
                </p>
                <p className="text-xs text-gray-500">
                  Submitted by {changeRequest.requesterName}
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {reviewAction === 'approve' && 'Approval Comments (Optional)'}
                  {reviewAction === 'request-info' && 'Information Needed (Optional)'}
                  {reviewAction === 'reject' && 'Rejection Reason'}
                  {reviewAction === 'reject' && <span className="text-red-500 ml-1">*</span>}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  rows={3}
                  placeholder={
                    reviewAction === 'approve' ? 'Add any comments about this approval...' :
                    reviewAction === 'request-info' ? 'Explain what additional information is needed...' :
                    'Please provide a clear reason for rejection...'
                  }
                  value={reviewComments}
                  onChange={(e) => setReviewComments(e.target.value)}
                  required={reviewAction === 'reject'}
                />
              </div>

              {reviewAction === 'request-info' && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>Note:</strong> The submitter will be able to edit and resubmit this request after providing the requested information.
                  </p>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowReviewModal(false);
                    setReviewComments('');
                    setReviewAction(null);
                  }}
                  disabled={actionLoading}
                  className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReviewAction}
                  disabled={actionLoading || (reviewAction === 'reject' && !reviewComments.trim())}
                  className={`flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                    reviewAction === 'approve' ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500' :
                    reviewAction === 'request-info' ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' :
                    'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                  }`}
                >
                  {actionLoading ? 
                    (reviewAction === 'approve' ? 'Approving...' : 
                     reviewAction === 'request-info' ? 'Requesting...' : 'Rejecting...') :
                    (reviewAction === 'approve' ? 'Approve' : 
                     reviewAction === 'request-info' ? 'Request More Info' : 'Reject')
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Date Override Modal */}
      {showDateOverrideModal && changeRequest && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center mb-4">
                <Calendar size={24} className="text-blue-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">
                  Override Completion Date
                </h3>
              </div>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  <strong>{changeRequest.requestNumber}:</strong> {changeRequest.title}
                </p>
                <p className="text-xs text-gray-500">
                  Current requested date: {changeRequest.requestedCompletionDate 
                    ? new Date(changeRequest.requestedCompletionDate).toLocaleDateString()
                    : 'Not specified'
                  }
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Completion Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={newRequestedDate}
                  onChange={(e) => setNewRequestedDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for Override (Optional)
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Explain why the completion date is being changed..."
                  value={dateOverrideReason}
                  onChange={(e) => setDateOverrideReason(e.target.value)}
                />
              </div>

              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> This action will be logged and visible to all stakeholders. 
                  The submitter will be notified of the date change.
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowDateOverrideModal(false);
                    setNewRequestedDate('');
                    setDateOverrideReason('');
                  }}
                  disabled={actionLoading}
                  className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDateOverride}
                  disabled={actionLoading || !newRequestedDate}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {actionLoading ? 'Updating...' : 'Update Date'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChangeRequestDetailsPage; 