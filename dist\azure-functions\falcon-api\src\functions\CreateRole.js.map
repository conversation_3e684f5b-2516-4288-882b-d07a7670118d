{"version": 3, "file": "CreateRole.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/CreateRole.ts"], "names": [], "mappings": ";;;;;;;;;;;AAaA,gCAyFC;AAtGD,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,6BAA6B;AAS7B,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;;QAC7E,OAAO,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAExE,IAAI,IAA2B,CAAC;QAChC,IAAI,CAAC;YACD,IAAI,IAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAA,CAAC;QACzD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACzD,CAAC;QACN,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;YAC3C,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE;aACpD,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAClC,MAAM,WAAW,GAAG,CAAA,MAAA,IAAI,CAAC,WAAW,0CAAE,IAAI,EAAE,KAAI,IAAI,CAAC;YACrD,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,cAAc;YAEnC,qDAAqD;YACrD,MAAM,UAAU,GAAG,8EAA8E,CAAC;YAClG,wBAAwB;YACxB,MAAM,WAAW,GAAqB;gBAClC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;aAC5D,CAAC;YACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEhE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrC,eAAM,CAAC,IAAI,CAAC,uCAAuC,QAAQ,mBAAmB,CAAC,CAAC;gBAChF,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,QAAQ,mBAAmB,EAAE;iBACxE,CAAC;YACN,CAAC;YAED,qBAAqB;YACrB,MAAM,KAAK,GAAG;;;;SAIb,CAAC;YACF,wBAAwB;YACxB,MAAM,YAAY,GAAqB;gBACnC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;gBAC9D,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;gBACpE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;aAC9D,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrD,eAAM,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;gBACvE,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,OAAO,EAAE,sBAAsB;wBAC/B,KAAK,EAAE,kCAAkC;qBAC5C;iBACJ,CAAC;YACN,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,OAAO,GAAmB;gBAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,WAAW,EAAE,SAAS,CAAC,eAAe;aACzC,CAAC;YAEF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,OAAO;aACpB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAChG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}