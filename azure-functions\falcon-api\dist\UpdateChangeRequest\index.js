"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const emailService_1 = require("../shared/services/emailService");
const sql = __importStar(require("mssql"));
async function updateChangeRequest(request, context) {
    context.log('UpdateChangeRequest function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json();
        const { title, description, requestType, priority, businessJustification, risksAndMitigation, rollbackPlan, affectedSystems, requiredResources, proposedSchedule, userId } = body;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required'
                    }
                }
            };
        }
        if (!title || !description || !requestType || !priority) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Title, description, request type, and priority are required'
                    }
                }
            };
        }
        // First, verify the change request exists and can be updated
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        const changeRequest = checkResult.recordset[0];
        // Validate that the request can be updated (only if status is "Submitted" after info request)
        if (changeRequest.Status !== 'Submitted') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Cannot update request with status: ${changeRequest.Status}. Only submitted requests waiting for clarification can be updated.`
                    }
                }
            };
        }
        // Update the change request and resubmit it
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Title = @title,
                Description = @description,
                RequestType = @requestType,
                Priority = @priority,
                BusinessJustification = @businessJustification,
                RisksAndMitigation = @risksAndMitigation,
                RollbackPlan = @rollbackPlan,
                AffectedSystems = @affectedSystems,
                RequiredResources = @requiredResources,
                ProposedSchedule = @proposedSchedule,
                Status = 'Under Review',
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'title', type: sql.NVarChar, value: title.trim() },
            { name: 'description', type: sql.NVarChar, value: description.trim() },
            { name: 'requestType', type: sql.NVarChar, value: requestType },
            { name: 'priority', type: sql.NVarChar, value: priority },
            { name: 'businessJustification', type: sql.NVarChar, value: businessJustification?.trim() || null },
            { name: 'risksAndMitigation', type: sql.NVarChar, value: risksAndMitigation?.trim() || null },
            { name: 'rollbackPlan', type: sql.NVarChar, value: rollbackPlan?.trim() || null },
            { name: 'affectedSystems', type: sql.NVarChar, value: affectedSystems?.trim() || null },
            { name: 'requiredResources', type: sql.NVarChar, value: requiredResources?.trim() || null },
            { name: 'proposedSchedule', type: sql.NVarChar, value: proposedSchedule?.trim() || null },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add history entry for the update and resubmission
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, @statusTo, @userId, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: 'Submitted' },
            { name: 'statusTo', type: sql.NVarChar, value: 'Under Review' },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: 'Request updated with clarifications and resubmitted for review' }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Add a comment indicating the update
        const commentQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
            )
            VALUES (
                @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
            )
        `;
        const commentParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: 'Request has been updated with clarifications and resubmitted for review.' },
            { name: 'commentType', type: sql.NVarChar, value: 'General' },
            { name: 'isInternal', type: sql.Bit, value: false },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(commentQuery, commentParams);
        // Get the updated change request details
        const getUpdatedQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.RequestType as requestType,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.RisksAndMitigation as risksAndMitigation,
                cr.RollbackPlan as rollbackPlan,
                cr.AffectedSystems as affectedSystems,
                cr.RequiredResources as requiredResources,
                cr.ProposedSchedule as proposedSchedule,
                cr.CreatedDate as createdDate,
                cr.ModifiedDate as modifiedDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(u.FirstName, ' ', u.LastName) as requesterName,
                u.Email as requesterEmail,
                CONCAT(m.FirstName, ' ', m.LastName) as modifiedByName,
                CONCAT(assignee.FirstName, ' ', assignee.LastName) as assigneeName,
                assignee.Email as assigneeEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequests cr
                LEFT JOIN Users u ON cr.RequestedBy = u.UserID
                LEFT JOIN Users m ON cr.ModifiedBy = m.UserID
                LEFT JOIN Users assignee ON cr.AssignedToDevID = assignee.UserID
                LEFT JOIN Companies companies ON u.CompanyID = companies.CompanyID
            WHERE cr.RequestID = @requestId
        `;
        const getUpdatedResult = await (0, db_1.executeQuery)(getUpdatedQuery, checkParams);
        const requestDetails = getUpdatedResult.recordset[0];
        // Send email notification for request update to Change Managers asynchronously
        try {
            const emailData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description,
                priority: requestDetails.priority,
                status: 'Under Review',
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                assigneeName: requestDetails.assigneeName,
                assigneeEmail: requestDetails.assigneeEmail,
                companyName: requestDetails.companyName || 'SASMOS Group',
                comments: 'Request has been updated with clarifications and resubmitted for review.',
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${requestDetails.requestId}`,
                createdDate: requestDetails.createdDate,
                dueDate: requestDetails.dueDate
            };
            emailService_1.EmailService.getInstance().sendChangeRequestStatusUpdated(emailData).catch((error) => {
                context.error('Failed to send update notification email:', error);
            });
            context.log(`Email notification queued for updated change request ${requestId}`);
        }
        catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the update if email fails
        }
        context.log(`Successfully updated change request ${requestId}`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request updated and resubmitted for review',
                data: requestDetails
            }
        };
    }
    catch (error) {
        context.error('Error in UpdateChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while updating the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.updateChangeRequest = updateChangeRequest;
functions_1.app.http('UpdateChangeRequest', {
    methods: ['PUT'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}',
    handler: updateChangeRequest
});
//# sourceMappingURL=index.js.map