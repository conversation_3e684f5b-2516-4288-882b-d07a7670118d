"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixCurrentChangeRequests = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
async function FixCurrentChangeRequests(request, context) {
    try {
        context.log('FixCurrentChangeRequests function triggered');
        // Get database connection
        const pool = await (0, db_1.getPool)();
        // First, fix missing CompanyID for recent change requests
        const fixCompanyIdQuery = `
            UPDATE ChangeRequests 
            SET CompanyID = 4
            WHERE CompanyID IS NULL 
            AND RequestedBy = 1  -- <PERSON><PERSON><PERSON>'s user ID
            AND CreatedDate >= '2024-01-01';
        `;
        const companyResult = await pool.request().query(fixCompanyIdQuery);
        context.log(`Fixed CompanyID for ${companyResult.rowsAffected[0]} change requests`);
        // Second, add deployment dates based on RequestedCompletionDate for records that don't have them
        const fixDeploymentDateQuery = `
            UPDATE ChangeRequests 
            SET DeploymentDate = RequestedCompletionDate
            WHERE DeploymentDate IS NULL 
            AND RequestedCompletionDate IS NOT NULL
            AND CompanyID = 4;
        `;
        const deploymentResult = await pool.request().query(fixDeploymentDateQuery);
        context.log(`Fixed DeploymentDate for ${deploymentResult.rowsAffected[0]} change requests`);
        // Get updated results to show what was fixed
        const selectQuery = `
            SELECT 
                RequestID, 
                Title, 
                Status, 
                Priority, 
                CompanyID,
                DeploymentDate,
                RequestedCompletionDate,
                CreatedDate
            FROM ChangeRequests 
            WHERE CompanyID = 4
            ORDER BY CreatedDate DESC;
        `;
        const selectResult = await pool.request().query(selectQuery);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: `Fixed ${companyResult.rowsAffected[0]} CompanyID issues and ${deploymentResult.rowsAffected[0]} DeploymentDate issues`,
                companyIdFixed: companyResult.rowsAffected[0],
                deploymentDateFixed: deploymentResult.rowsAffected[0],
                changeRequests: selectResult.recordset
            }
        };
    }
    catch (error) {
        context.log('Error in FixCurrentChangeRequests:', error);
        return {
            status: 500,
            jsonBody: {
                error: 'Failed to fix change requests',
                details: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}
exports.FixCurrentChangeRequests = FixCurrentChangeRequests;
// Register the function
functions_1.app.http('FixCurrentChangeRequests', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'fix-change-requests',
    handler: FixCurrentChangeRequests
});
//# sourceMappingURL=FixCurrentChangeRequests.js.map