{"version": 3, "file": "DeletePortalUser.js", "sourceRoot": "", "sources": ["../../src/functions/DeletePortalUser.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,oFAAiF;AACjF,mDAAkE;AAClE,mDAAgD;AAEzC,KAAK,UAAU,gBAAgB,CAAC,OAAoB,EAAE,OAA0B;IACnF,OAAO,CAAC,GAAG,GAAG,eAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B;IACxD,eAAM,CAAC,IAAI,CAAC,4CAA4C,OAAO,CAAC,GAAG,mBAAmB,CAAC,CAAC;IAExF,6CAA6C;IAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE;QACxC,eAAM,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;KAClF;SAAM;QACH,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE;YACZ,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,CAAC;SAC1F;QAED,sCAAsC;QACtC,IAAI,CAAC,IAAA,mBAAO,EAAC,SAAS,CAAC,EAAE;YACrB,eAAM,CAAC,IAAI,CAAC,qCAAqC,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,oBAAoB,CAAC,CAAC;YACrH,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,CAAC;SAC1F;KACJ;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;IACvC,IAAI,CAAC,OAAO,EAAE;QACV,eAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAC3E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;KACzF;IAED,IAAI;QACA,qFAAqF;QACrF,kFAAkF;QAClF,IAAI,mBAA2B,CAAC;QAEhC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE;YACxC,kDAAkD;YAClD,mBAAmB,GAAG,CAAC,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;SACpG;aAAM;YACH,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,EAAE;gBACZ,eAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;gBACvE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,CAAC;aACrF;YAED,mBAAmB,GAAG,MAAM,6CAAqB,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5F,IAAI,mBAAmB,KAAK,CAAC,EAAE;gBAC3B,eAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,kEAAkE,CAAC,CAAC;aACjK;SACJ;QAED,MAAM,OAAO,GAAG,MAAM,6CAAqB,CAAC,6BAA6B,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;QAExG,IAAI,OAAO,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,iEAAiE,OAAO,GAAG,CAAC,CAAC;YACzF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,gDAAgD;SAC3E;aAAM;YACH,eAAM,CAAC,IAAI,CAAC,wCAAwC,OAAO,iCAAiC,CAAC,CAAC;YAC9F,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,CAAC;SACtF;KACJ;IAAC,OAAO,GAAG,EAAE;QACV,eAAM,CAAC,KAAK,CAAC,0CAA0C,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,GAAY,CAAC;QAC3B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,iDAAiD,EAAE,EAAE,CAAC;KACnH;AACL,CAAC;AA/DD,4CA+DC;AAED,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,wBAAwB;IAC/B,OAAO,EAAE,gBAAgB,CAAC,wDAAwD;CACrF,CAAC,CAAC"}