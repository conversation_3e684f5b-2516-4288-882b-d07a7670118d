{"version": "2.0.0", "tasks": [{"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "npm watch (functions)"}, {"type": "shell", "label": "npm build (functions)", "command": "npm run build", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc"}, {"type": "shell", "label": "npm watch (functions)", "command": "npm run watch", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc-watch", "group": {"kind": "build", "isDefault": true}, "isBackground": true}, {"type": "shell", "label": "npm install (functions)", "command": "npm install"}, {"type": "shell", "label": "npm prune (functions)", "command": "npm prune --production", "dependsOn": "npm build (functions)", "problemMatcher": []}]}