-- Incremental OAuth Token Management Schema Deployment
-- Database: fp-sqldb-falcon-dev-cin-001
-- Server: fp-sql-falcon-dev-cin-001.database.windows.net

-- Note: This script safely adds only missing components

PRINT 'Starting incremental OAuth schema deployment...';

-- Check what tables exist
DECLARE @HasOAuthTokens BIT = 0;
DECLARE @HasOAuthConfigurations BIT = 0;
DECLARE @HasOAuthTokenUsage BIT = 0;

SELECT @HasOAuthTokens = 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'OAuthTokens';
SELECT @HasOAuthConfigurations = 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'OAuthConfigurations';
SELECT @HasOAuthTokenUsage = 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'OAuthTokenUsage';

PRINT 'Existing tables check:';
IF @HasOAuthTokens = 1 PRINT '✅ OAuthTokens table exists';
IF @HasOAuthConfigurations = 1 PRINT '✅ OAuthConfigurations table exists';
IF @HasOAuthTokenUsage = 1 PRINT '✅ OAuthTokenUsage table exists';

-- Create missing tables only
IF @HasOAuthConfigurations = 0
BEGIN
    PRINT 'Creating OAuthConfigurations table...';
    
    CREATE TABLE [dbo].[OAuthConfigurations] (
        [ConfigId] INT IDENTITY(1,1) PRIMARY KEY,
        [ServiceProvider] NVARCHAR(100) NOT NULL,
        [ServiceType] NVARCHAR(100) NOT NULL,
        [ConfigKey] NVARCHAR(255) NOT NULL,
        [ConfigValue] NVARCHAR(MAX) NOT NULL,
        [IsEncrypted] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT [UQ_OAuthConfigurations_Service_Key] UNIQUE ([ServiceProvider], [ServiceType], [ConfigKey])
    );
    
    CREATE NONCLUSTERED INDEX [IX_OAuthConfigurations_Service] 
    ON [dbo].[OAuthConfigurations] ([ServiceProvider], [ServiceType], [IsActive]);
    
    PRINT '✅ OAuthConfigurations table created';
END

IF @HasOAuthTokenUsage = 0
BEGIN
    PRINT 'Creating OAuthTokenUsage table...';
    
    CREATE TABLE [dbo].[OAuthTokenUsage] (
        [UsageId] BIGINT IDENTITY(1,1) PRIMARY KEY,
        [TokenId] INT NOT NULL,
        [RequestEndpoint] NVARCHAR(500) NULL,
        [RequestTime] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [ResponseStatus] INT NULL,
        [ErrorMessage] NVARCHAR(MAX) NULL,
        
        CONSTRAINT [FK_OAuthTokenUsage_TokenId] FOREIGN KEY ([TokenId]) REFERENCES [dbo].[OAuthTokens]([TokenId])
    );
    
    CREATE NONCLUSTERED INDEX [IX_OAuthTokenUsage_Token] 
    ON [dbo].[OAuthTokenUsage] ([TokenId], [RequestTime]);
    
    CREATE NONCLUSTERED INDEX [IX_OAuthTokenUsage_Time] 
    ON [dbo].[OAuthTokenUsage] ([RequestTime]);
    
    PRINT '✅ OAuthTokenUsage table created';
END
GO

-- Always recreate procedures and functions (safe to drop/recreate)
PRINT 'Updating stored procedures and functions...';

DROP FUNCTION IF EXISTS [dbo].[NeedsTokenRefresh];
DROP PROCEDURE IF EXISTS [dbo].[SaveOAuthToken];
DROP PROCEDURE IF EXISTS [dbo].[GetActiveOAuthToken];
GO

CREATE PROCEDURE [dbo].[GetActiveOAuthToken]
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1
        [TokenId],
        [AccessToken],
        [RefreshToken],
        [ExpiresAt],
        [Scope],
        [TokenType],
        [CreatedAt],
        [UpdatedAt]
    FROM [dbo].[OAuthTokens]
    WHERE [UserId] = @UserId
        AND [ServiceProvider] = @ServiceProvider
        AND [ServiceType] = @ServiceType
        AND [IsActive] = 1
        AND [ExpiresAt] > GETUTCDATE()
    ORDER BY [CreatedAt] DESC;
END
GO

CREATE PROCEDURE [dbo].[SaveOAuthToken]
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100),
    @AccessToken NVARCHAR(MAX),
    @RefreshToken NVARCHAR(MAX) = NULL,
    @ExpiresIn INT = 3600,
    @Scope NVARCHAR(500) = NULL,
    @TokenType NVARCHAR(50) = 'Bearer'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ExpiresAt DATETIME2 = DATEADD(SECOND, @ExpiresIn, GETUTCDATE());
    DECLARE @TokenId INT;
    
    -- Deactivate existing tokens for this user/service combination
    UPDATE [dbo].[OAuthTokens] 
    SET [IsActive] = 0, [UpdatedAt] = GETUTCDATE()
    WHERE [UserId] = @UserId 
        AND [ServiceProvider] = @ServiceProvider 
        AND [ServiceType] = @ServiceType 
        AND [IsActive] = 1;
    
    -- Insert new token
    INSERT INTO [dbo].[OAuthTokens] (
        [UserId], [ServiceProvider], [ServiceType], [AccessToken], [RefreshToken],
        [ExpiresAt], [Scope], [TokenType], [IsActive]
    )
    VALUES (
        @UserId, @ServiceProvider, @ServiceType, @AccessToken, @RefreshToken,
        @ExpiresAt, @Scope, @TokenType, 1
    );
    
    SET @TokenId = SCOPE_IDENTITY();
    
    SELECT @TokenId as TokenId;
END
GO

CREATE FUNCTION [dbo].[NeedsTokenRefresh](
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
)
RETURNS BIT
AS
BEGIN
    DECLARE @Result BIT = 1; -- Default to needs refresh
    DECLARE @ExpiresAt DATETIME2;
    
    SELECT @ExpiresAt = [ExpiresAt]
    FROM [dbo].[OAuthTokens]
    WHERE [UserId] = @UserId
        AND [ServiceProvider] = @ServiceProvider
        AND [ServiceType] = @ServiceType
        AND [IsActive] = 1;
    
    IF @ExpiresAt IS NOT NULL AND @ExpiresAt > DATEADD(MINUTE, 5, GETUTCDATE())
    BEGIN
        SET @Result = 0; -- Token is valid for more than 5 minutes
    END
    
    RETURN @Result;
END
GO

-- Add ZohoDesk configuration if not exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[OAuthConfigurations] WHERE [ServiceProvider] = 'zoho' AND [ServiceType] = 'desk')
BEGIN
    PRINT 'Adding ZohoDesk OAuth configuration...';
    
    INSERT INTO [dbo].[OAuthConfigurations] ([ServiceProvider], [ServiceType], [ConfigKey], [ConfigValue], [IsEncrypted])
    VALUES 
        ('zoho', 'desk', 'client_id', '1000.03IZH9ZAA9U4H8OTTE0J2LNKB3U9VO', 0),
        ('zoho', 'desk', 'client_secret', '1acd0b7dfbbbb332a1b226a97fae998cfe4a28a3f3', 1),
        ('zoho', 'desk', 'redirect_uri', 'http://localhost:7071/api/auth/zoho-desk/callback', 0),
        ('zoho', 'desk', 'base_url', 'https://accounts.zoho.in/oauth/v2', 0),
        ('zoho', 'desk', 'scopes', 'Desk.tickets.ALL,Desk.contacts.ALL,Desk.basic.READ,Desk.settings.READ,Desk.agents.READ,Desk.departments.READ,Desk.categories.READ,Desk.threads.ALL,Desk.attachments.READ', 0);
    
    PRINT '✅ ZohoDesk OAuth configuration added';
END
ELSE
BEGIN
    PRINT '✅ ZohoDesk OAuth configuration already exists';
END

PRINT '';
PRINT '=== Incremental OAuth Schema Deployment Complete ===';
PRINT 'Status:';
PRINT '✅ Preserved existing OAuthTokens table and data';
PRINT '✅ Added missing tables (if any)';
PRINT '✅ Updated procedures: GetActiveOAuthToken, SaveOAuthToken';  
PRINT '✅ Updated function: NeedsTokenRefresh';
PRINT '✅ Ensured ZohoDesk OAuth configuration exists';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Your Azure Functions code is ready';
PRINT '2. Visit: http://localhost:7071/api/auth/zoho-desk/authorize';
PRINT '3. Complete OAuth flow to store tokens in database';
PRINT '4. Your existing tokens (if any) are preserved'; 