"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPortalUserById = getPortalUserById;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const authUtils_1 = require("../shared/authUtils");
const logger_1 = require("../shared/utils/logger");
function getPortalUserById(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log = logger_1.logger.info;
        logger_1.logger.info(`Http function processed request for url "${request.url}" to get user by ID.`);
        // 1. Authentication (basic check, adapt as needed)
        // For local development, you might want to bypass this if headers are not easily emulated.
        if (process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test') { // Added 'test' to bypass in tests too
            const principal = (0, authUtils_1.getClientPrincipal)(request);
            if (!principal) {
                logger_1.logger.warn("GetPortalUserById: Unauthenticated access attempt.");
                return { status: 401, jsonBody: { error: "Unauthorized" } };
            }
            // TODO: Add role checks if necessary, e.g., only admins or the user themselves
            // Example: if (!hasRequiredRole(principal, ['PortalAdmin']) && principal.userId !== entraId) { return { status: 403, ... }}
        }
        else {
            logger_1.logger.warn("GetPortalUserById: Bypassing auth in development/test mode.");
        }
        // 2. Extract Entra ID from route parameter
        const entraId = request.params.entraId;
        if (!entraId) {
            logger_1.logger.warn("GetPortalUserById: Entra ID missing from request parameters.");
            return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
        }
        try {
            // 3. Call Service Method
            const user = yield userManagementService_1.userManagementService.getPortalUserByEntraId(entraId);
            // 4. Return Response
            if (user) {
                return { status: 200, jsonBody: user };
            }
            else {
                logger_1.logger.warn(`GetPortalUserById: User with Entra ID ${entraId} not found.`);
                return { status: 404, jsonBody: { error: "User not found" } };
            }
        }
        catch (err) {
            logger_1.logger.error(`Error in GetPortalUserById for Entra ID ${entraId}:`, err);
            const error = err;
            // Check if the error message is one that should be propagated or a generic one
            const message = error.message.startsWith("Database error while fetching user") ? error.message : "Failed to fetch user due to an internal error.";
            return { status: 500, jsonBody: { error: message } };
        }
    });
}
functions_1.app.http('GetPortalUserById', {
    methods: ['GET'],
    route: 'portal-users/{entraId}',
    authLevel: 'anonymous',
    handler: getPortalUserById
});
//# sourceMappingURL=GetPortalUserById.js.map