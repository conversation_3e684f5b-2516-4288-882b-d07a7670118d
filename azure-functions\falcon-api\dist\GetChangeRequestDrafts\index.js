"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetChangeRequestDrafts = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const cors_1 = require("../shared/cors");
async function GetChangeRequestDrafts(request, context) {
    context.log('GetChangeRequestDrafts function processed a request.');
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: ''
        });
    }
    try {
        // Get user ID from query parameters
        const userId = request.query.get('userId');
        if (!userId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'User ID is required'
                }
            });
        }
        // Get database connection
        const pool = await (0, db_1.getPool)();
        // Simple query to get user's drafts without joins for now
        const query = `
      SELECT 
        d.DraftID as draftId,
        d.Title as title,
        d.Description as description,
        d.TypeID as typeId,
        'General' as typeName,
        d.Priority as priority,
        d.BusinessJustification as businessJustification,
        d.ExpectedBenefit as expectedBenefit,
        d.RequestedCompletionDate as requestedCompletionDate,
        d.RichContent as richContent,
        d.RequestedBy as requestedBy,
        'User' as requestedByName,
        d.CreatedDate as createdDate,
        d.LastModified as lastModified
      FROM ChangeRequestDrafts d
      WHERE d.RequestedBy = @userId
      ORDER BY d.LastModified DESC
    `;
        const result = await pool.request()
            .input('userId', parseInt(userId))
            .query(query);
        // Process the results
        const drafts = result.recordset.map(row => ({
            draftId: row.draftId,
            title: row.title,
            description: row.description,
            typeId: row.typeId,
            typeName: row.typeName,
            priority: row.priority,
            businessJustification: row.businessJustification,
            expectedBenefit: row.expectedBenefit,
            requestedCompletionDate: row.requestedCompletionDate,
            richContent: row.richContent ? JSON.parse(row.richContent) : [],
            requestedBy: row.requestedBy,
            requestedByName: row.requestedByName,
            createdDate: row.createdDate,
            lastModified: row.lastModified
        }));
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            jsonBody: {
                success: true,
                drafts: drafts,
                count: drafts.length
            }
        });
    }
    catch (error) {
        context.log('Error fetching drafts:', error);
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            jsonBody: {
                success: false,
                message: 'Internal server error while fetching drafts',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
}
exports.GetChangeRequestDrafts = GetChangeRequestDrafts;
functions_1.app.http('GetChangeRequestDrafts', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    handler: GetChangeRequestDrafts
});
//# sourceMappingURL=index.js.map