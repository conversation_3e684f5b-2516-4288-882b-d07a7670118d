"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetMocks = exports.executeQuery = exports.getPool = void 0;
// Create a mock recordset that satisfies the IRecordSet interface
const createMockRecordset = () => {
    return Object.assign([], { toTable: jest.fn(), columns: {} });
};
// Mock pool for testing
const mockPool = {
    connected: true,
    request: jest.fn().mockReturnThis(),
    input: jest.fn().mockReturnThis(),
    query: jest.fn(),
    connect: jest.fn().mockResolvedValue(true),
    close: jest.fn().mockResolvedValue(true),
    on: jest.fn(),
};
// Mock getPool function
exports.getPool = jest.fn().mockResolvedValue(mockPool);
// Mock executeQuery function
exports.executeQuery = jest.fn().mockImplementation((query, params) => __awaiter(void 0, void 0, void 0, function* () {
    // Default empty result that satisfies the IResult interface
    const mockRecordset = createMockRecordset();
    return {
        recordset: mockRecordset,
        recordsets: [mockRecordset],
        output: {},
        rowsAffected: [0],
    };
}));
// Reset all mocks between tests
const resetMocks = () => {
    exports.getPool.mockClear();
    exports.executeQuery.mockClear();
    mockPool.request.mockClear();
    mockPool.input.mockClear();
    mockPool.query.mockClear();
    mockPool.connect.mockClear();
    mockPool.close.mockClear();
    mockPool.on.mockClear();
};
exports.resetMocks = resetMocks;
//# sourceMappingURL=db.js.map