"use client";
import React from "react";
// import { Link, useNavigate } from "react-router-dom"; // Unused imports
import {
  Tool,
  Clipboard,
  CheckCircle,
  FileText,
  Settings,
  Database,
  // AlertCircle, // Unused import
  // Truck, // Unused import
  Archive,
  BarChart2,
  Activity,
  Shield,
  // ArrowRight, // Unused import
  // ExternalLink, // Unused import
  // Home, // Unused import
} from "feather-icons-react";

const Hr_and_Admin: React.FC = () => {
  const host_var = "http://**************/"; // Or your actual host

  const handleToolClick = (tool: any) => {
    const reactBaseURL = window.location.origin;
    const token = localStorage.getItem("authToken");

    if (!token) {
      alert("You must be logged in to access this tool.");
      return;
    }

    if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(
        reactBaseURL
      )}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      window.location.href = targetURL;
    }
  };

  const toolsList = [
    {
      label: "Visitor Control System",
      name: "vcs",
      link: host_var + "vcs/index.php",
      desc: "VISITOR CONTROL SYSTEM for managing visitor access",
      icon: <Clipboard />,
      category: "HR AND ADMIN",
      priority: "high",
      type: "internal",
    },
    {
      label: "Travel Server",
      name: "travel_server",
      link: host_var + "travel_server/index.php",
      desc: "Manage and track employee travel requests and expenses",
      icon: <CheckCircle />,
      category: "HR AND ADMIN",
      priority: "high",
      type: "internal",
    },
    {
      label: "Training Server",
      name: "training_server",
      link: host_var + "training_server/index.php",
      desc: "Manage employee training programs and certifications",
      icon: <FileText />,
      category: "HR AND ADMIN",
      priority: "high",
      type: "internal",
    },
    {
      label: "Annual appraisal",
      name: "kra_server",
      link: host_var + "kra_server/index.php",
      desc: "Manage and track annual employee appraisals",
      icon: <BarChart2 />,
      category: "HR AND ADMIN",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Employee Advance",
      name: "employee_advance",
      link: host_var + "employee_advance/index.php",
      desc: "Manage employee advance requests and approvals",
      icon: <Tool />,
      category: "HR AND ADMIN",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Employee Database",
      name: "employee_db",
      link: host_var + "employees_form/index.php",
      desc: "Manage employee records and information",
      icon: <Database />,
      category: "HR AND ADMIN",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Code of Conduct",
      name: "exam_portal",
      link: host_var + "exam_portal/index.php",
      desc: "Review and acknowledge the company's code of conduct",
      icon: <Settings />,
      category: "HR AND ADMIN",
      priority: "high",
      type: "internal",
    },
    {
      label: "Stationery Request",
      name: "stationery_request",
      link: host_var + "stationery_request/index.php",
      desc: "Submit and manage stationery requests",
      icon: <Activity />,
      category: "HR AND ADMIN",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Local Expense Form",
      name: "local_expense_form",
      link: host_var + "local_expense_form/index.php",
      desc: "Submit and manage local expense claims",
      icon: <Archive />,
      category: "HR AND ADMIN",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Online Induction Portal",
      name: "online_induction_portal",
      link: host_var + "online_induction_portal/index.php",
      desc: "Complete online induction training for new employees",
      icon: <Shield />,
      category: "HR AND ADMIN",
      priority: "high",
      type: "internal",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">Human Resources</h1>
              <p className="text-gray-600 text-lg">
                Comprehensive human resources management and tracking systems
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tools Grid */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                transition-all duration-300 ease-in-out
                hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Hr_and_Admin;
