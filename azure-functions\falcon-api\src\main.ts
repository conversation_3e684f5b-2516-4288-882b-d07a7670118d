// main.ts - Single source of truth for function registration

// --- Core & System ---
import './functions/GetCompanies';
import './functions/ServeImage';
import './UploadImage/index';

// --- Role Management ---
import './functions/GetRoles';
import './functions/CreateRole';
import './functions/UpdateRole';
import './functions/DeleteRole';

// --- User Management ---
import './functions/GetPortalUsers';
import './functions/GetPortalUserById';
import './functions/CreatePortalUser';
import './functions/UpdatePortalUser';
import './functions/DeletePortalUser';
import './functions/GetCurrentUser';
import './GetUserOverrides/index';

// --- Change Management ---
import './GetChangeRequests/index';
import './CreateChangeRequest/index';
import './GetChangeRequestById/index';
import './UpdateChangeRequest/index';
import './SubmitChangeRequest/index';
import './ApproveChangeRequest/index';
import './RejectChangeRequest/index';
import './GetChangeRequestComments/index';
import './AddChangeRequestComment/index';
import './GetChangeRequestDashboardStats/index';
import './AssignChangeRequest/index';
import './RequestMoreInfoChangeRequest/index';
import './UpdateRequestedCompletionDate/index';

// --- Draft Management ---
import './GetChangeRequestDrafts/index';
import './SaveChangeRequestDraft/index';
import './DeleteChangeRequestDraft/index';

// --- Zoho Desk Integration ---
import './functions/ZohoDeskAuth';
import './functions/ZohoDeskAPI';
import './functions/TokenRefreshScheduler';
import './functions/TokenHealthCheck';

// --- Test Functions ---
import './TestSimple/index';
import './SimpleTest';
