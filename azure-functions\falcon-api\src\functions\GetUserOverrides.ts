import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { logger } from "../shared/utils/logger";
import * as sql from 'mssql';
import { QueryParameter } from "../shared/db";

export async function getUserOverrides(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const entraId = request.params.entraId;
    context.log(`Http function processed request for GetUserOverrides: ${entraId}`);

    if (!entraId) {
        return {
            status: 400,
            jsonBody: { message: "EntraID parameter is required." }
        };
    }

    try {
        // 1. Get UserID from EntraID
        const userQuery = `SELECT UserID FROM Users WHERE EntraID = @EntraID AND IsActive = 1`;
        const userParams: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVar<PERSON>har, value: entraId }
        ];
        const userResult = await executeQuery(userQuery, userParams);
        
        if (!userResult.recordset || userResult.recordset.length === 0) {
            logger.warn(`GetUserOverrides: User not found or inactive for EntraID: ${entraId}`);
            return { 
                status: 404, 
                jsonBody: { 
                    message: "User not found in the portal database." 
                } 
            };
        }

        const userId = userResult.recordset[0].UserID;

        // 2. Get user's active roles
        const rolesQuery = `
            SELECT r.RoleID, r.RoleName
            FROM Roles r
            JOIN UserRoles ur ON r.RoleID = ur.RoleID 
            WHERE ur.UserID = @UserID AND ur.IsActive = 1 AND r.IsActive = 1
        `;
        const rolesParams: QueryParameter[] = [
            { name: 'UserID', type: sql.Int, value: userId }
        ];
        const rolesResult = await executeQuery(rolesQuery, rolesParams);
        
        const assignedRoles = rolesResult.recordset.map(row => ({ id: row.RoleID, name: row.RoleName }));

        return {
            status: 200,
            jsonBody: {
                roles: assignedRoles
            }
        };
    } catch (error) {
        context.error(`Error fetching user overrides: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: { 
                message: "Error fetching user overrides.", 
                error: error instanceof Error ? error.message : "An unknown error occurred." 
            }
        };
    }
}

app.http('GetUserOverrides', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'users/overrides/{entraId}',
    handler: getUserOverrides
});
