"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const client_1 = require("react-dom/client");
require("./index.css");
const App_tsx_1 = require("./App.tsx");
// MSAL Imports
const msal_browser_1 = require("@azure/msal-browser");
const msal_react_1 = require("@azure/msal-react");
const authConfig_1 = require("./authConfig");
// MSAL instance
const msalInstance = new msal_browser_1.PublicClientApplication(authConfig_1.msalConfig);
(0, client_1.createRoot)(document.getElementById('root')).render(<react_1.StrictMode>
    <msal_react_1.MsalProvider instance={msalInstance}>
      <App_tsx_1.default />
    </msal_react_1.MsalProvider>
  </react_1.StrictMode>);
//# sourceMappingURL=main.js.map