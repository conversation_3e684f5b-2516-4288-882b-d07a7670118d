-- =============================================
-- Change Management Module Database Schema
-- Execute this in your Azure SQL Database
-- Database: fp-sqldb-falcon-dev-cin-001
-- =============================================

-- =============================================
-- 1. Change Request Types Configuration Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestTypes')
BEGIN
    CREATE TABLE ChangeRequestTypes (
        TypeID INT IDENTITY(1,1) PRIMARY KEY,
        TypeName NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500),
        RequiresApproval BIT DEFAULT 1,
        ApprovalRoleID INT, -- References Roles.RoleID
        EstimatedDays INT DEFAULT 5,
        FormSchema NVARCHAR(MAX), -- JSON for dynamic forms
        IsActive BIT DEFAULT 1,
        CreatedBy INT, -- References PortalUsers.UserID
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedDate DATETIME2 DEFAULT GETDATE()
    );
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestTypes_IsActive ON ChangeRequestTypes(IsActive);
    PRINT 'Created table: ChangeRequestTypes';
END

-- =============================================  
-- 2. Main Change Requests Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequests')
BEGIN
    CREATE TABLE ChangeRequests (
        RequestID INT IDENTITY(1,1) PRIMARY KEY,
        RequestNumber AS ('CR-' + RIGHT('00000' + CAST(RequestID AS VARCHAR(5)), 5)) PERSISTED,
        Title NVARCHAR(255) NOT NULL,
        Description NVARCHAR(MAX),
        TypeID INT NOT NULL,
        Priority NVARCHAR(20) NOT NULL DEFAULT 'Medium',
        Status NVARCHAR(30) NOT NULL DEFAULT 'Draft',
        BusinessJustification NVARCHAR(MAX),
        ExpectedBenefit NVARCHAR(MAX),
        RequestedCompletionDate DATE,
        RequestedBy INT NOT NULL, -- References PortalUsers.UserID
        CompanyID INT, -- References Companies.CompanyID  
        DepartmentID INT, -- References Departments.DepartmentID
        ApprovedBy INT, -- References PortalUsers.UserID
        ApprovedDate DATETIME2,
        RejectionReason NVARCHAR(MAX),
        AssignedToDevID INT, -- References PortalUsers.UserID (Application Developer)
        AssignedDate DATETIME2,
        PlannedStartDate DATE,
        PlannedCompletionDate DATE,
        ActualStartDate DATE,  
        ActualCompletionDate DATE,
        DevelopmentProgress INT DEFAULT 0, -- Percentage (0-100)
        DevelopmentNotes NVARCHAR(MAX),
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedDate DATETIME2 DEFAULT GETDATE(),
        
        CONSTRAINT CK_ChangeRequests_Priority CHECK (Priority IN ('Low', 'Medium', 'High', 'Critical')),
        CONSTRAINT CK_ChangeRequests_Status CHECK (Status IN (
            'Draft', 'Submitted', 'Under Review', 'Approved', 'Rejected', 
            'Assigned', 'In Development', 'Code Review', 'Testing', 
            'UAT', 'Ready for Deployment', 'Deployed', 'Completed', 'Cancelled'
        )),
        CONSTRAINT CK_ChangeRequests_Progress CHECK (DevelopmentProgress >= 0 AND DevelopmentProgress <= 100)
    );
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequests_Status ON ChangeRequests(Status);
    CREATE NONCLUSTERED INDEX IX_ChangeRequests_RequestedBy ON ChangeRequests(RequestedBy);
    CREATE NONCLUSTERED INDEX IX_ChangeRequests_CompanyID ON ChangeRequests(CompanyID);
    CREATE UNIQUE NONCLUSTERED INDEX IX_ChangeRequests_RequestNumber ON ChangeRequests(RequestNumber);
    PRINT 'Created table: ChangeRequests';
END

-- =============================================
-- 3. Rich Content Storage
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestContent')
BEGIN
    CREATE TABLE ChangeRequestContent (
        ContentID INT IDENTITY(1,1) PRIMARY KEY,
        RequestID INT NOT NULL,
        ContentType NVARCHAR(50) NOT NULL, -- 'text', 'heading', 'image', 'code', 'list'
        ContentData NVARCHAR(MAX), -- JSON structure or HTML content
        SortOrder INT NOT NULL DEFAULT 0,
        ImageUrl NVARCHAR(500), -- Azure Blob Storage URL
        ImageCaption NVARCHAR(255),
        ImageAltText NVARCHAR(255),
        OriginalImageSize INT, -- In bytes
        CompressedImageSize INT, -- In bytes
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        
        CONSTRAINT CK_ChangeRequestContent_Type CHECK (ContentType IN ('text', 'heading', 'image', 'code', 'list'))
    );
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestContent_RequestID ON ChangeRequestContent(RequestID, SortOrder);
    PRINT 'Created table: ChangeRequestContent';
END

-- =============================================
-- 4. Status History Tracking
-- =============================================  
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestHistory')
BEGIN
    CREATE TABLE ChangeRequestHistory (
        HistoryID INT IDENTITY(1,1) PRIMARY KEY,
        RequestID INT NOT NULL,
        StatusFrom NVARCHAR(30),
        StatusTo NVARCHAR(30) NOT NULL,
        ChangedBy INT NOT NULL, -- References PortalUsers.UserID
        ChangeDate DATETIME2 DEFAULT GETDATE(),
        Comments NVARCHAR(MAX),
        ProgressFrom INT, -- Previous progress percentage
        ProgressTo INT -- New progress percentage
    );
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestHistory_RequestID ON ChangeRequestHistory(RequestID, ChangeDate);
    PRINT 'Created table: ChangeRequestHistory';
END

-- =============================================
-- 5. Comments and Communication
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestComments')
BEGIN
    CREATE TABLE ChangeRequestComments (
        CommentID INT IDENTITY(1,1) PRIMARY KEY,
        RequestID INT NOT NULL,
        CommentText NVARCHAR(MAX) NOT NULL,
        CommentType NVARCHAR(30) DEFAULT 'General', -- 'General', 'ApprovalNote', 'DevUpdate'
        IsInternal BIT DEFAULT 0, -- Public vs internal comments
        ParentCommentID INT, -- For threading
        CreatedBy INT NOT NULL, -- References PortalUsers.UserID
        CreatedDate DATETIME2 DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedDate DATETIME2,
        IsEdited BIT DEFAULT 0,
        
        CONSTRAINT CK_ChangeRequestComments_Type CHECK (CommentType IN ('General', 'ApprovalNote', 'DevUpdate', 'Question', 'Answer'))
    );
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestComments_RequestID ON ChangeRequestComments(RequestID, CreatedDate);
    PRINT 'Created table: ChangeRequestComments';
END

-- =============================================
-- 6. Change Request Drafts Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name='ChangeRequestDrafts')
BEGIN
    CREATE TABLE ChangeRequestDrafts (
        DraftID NVARCHAR(50) PRIMARY KEY,
        Title NVARCHAR(255) NOT NULL,
        Description NVARCHAR(MAX),
        TypeID INT NOT NULL,
        Priority NVARCHAR(20) NOT NULL,
        BusinessJustification NVARCHAR(MAX),
        ExpectedBenefit NVARCHAR(MAX),
        RequestedCompletionDate DATE,
        RichContent NVARCHAR(MAX), -- JSON string containing rich content blocks
        RequestedBy INT NOT NULL,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        -- Foreign key constraints
        CONSTRAINT FK_ChangeRequestDrafts_RequestTypes 
            FOREIGN KEY (TypeID) REFERENCES ChangeRequestTypes(TypeID),
        CONSTRAINT FK_ChangeRequestDrafts_Users 
            FOREIGN KEY (RequestedBy) REFERENCES PortalUsers(UserID)
    );
    
    -- Indexes for better performance
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_RequestedBy 
        ON ChangeRequestDrafts(RequestedBy);
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_LastModified 
        ON ChangeRequestDrafts(LastModified DESC);
    
    PRINT 'Created table: ChangeRequestDrafts';
END
GO

-- =============================================
-- 7. Insert Default Change Request Types
-- =============================================
IF NOT EXISTS (SELECT 1 FROM ChangeRequestTypes)
BEGIN
    INSERT INTO ChangeRequestTypes (TypeName, Description, RequiresApproval, ApprovalRoleID, EstimatedDays, CreatedBy)
    VALUES 
    ('New Application Development', 'Request for development of a completely new application or system', 1, 1, 30, 1),
    ('System Enhancement', 'Request to modify or enhance existing system functionality', 1, 12, 14, 1), -- Change Manager
    ('Bug Fix Request', 'Request to fix a bug or issue in existing system', 1, 5, 7, 1), -- IT Admin
    ('Data & Reporting Request', 'Request for data extraction, reporting, or database changes', 1, 5, 5, 1), -- IT Admin
    ('Infrastructure Change', 'Request for infrastructure, server, or environment changes', 1, 1, 10, 1); -- Administrator
    
    PRINT 'Inserted default Change Request Types';
END
GO

-- =============================================
-- 8. Create View for Easy Data Access
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_ChangeRequestsDetailed')
    DROP VIEW vw_ChangeRequestsDetailed;
GO

CREATE VIEW vw_ChangeRequestsDetailed AS
SELECT 
    cr.RequestID,
    cr.RequestNumber,
    cr.Title,
    cr.Description,
    cr.Priority,
    cr.Status,
    cr.BusinessJustification,
    cr.ExpectedBenefit,
    cr.RequestedCompletionDate,
    cr.DevelopmentProgress,
    cr.CreatedDate,
    cr.ApprovedDate,
    crt.TypeName,
    crt.Description AS TypeDescription,
    crt.EstimatedDays,
    CONCAT(requester.FirstName, ' ', requester.LastName) AS RequesterName,
    requester.Email AS RequesterEmail,
    c.CompanyName,
    d.DepartmentName,
    CONCAT(approver.FirstName, ' ', approver.LastName) AS ApproverName,
    CONCAT(developer.FirstName, ' ', developer.LastName) AS DeveloperName
FROM ChangeRequests cr
    LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
    LEFT JOIN PortalUsers requester ON cr.RequestedBy = requester.UserID
    LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
    LEFT JOIN Departments d ON cr.DepartmentID = d.DepartmentID
    LEFT JOIN PortalUsers approver ON cr.ApprovedBy = approver.UserID  
    LEFT JOIN PortalUsers developer ON cr.AssignedToDevID = developer.UserID;

GO

PRINT 'Created view: vw_ChangeRequestsDetailed';

PRINT '===========================================';
PRINT 'Change Management Schema Creation Complete!';
PRINT '==========================================='; 