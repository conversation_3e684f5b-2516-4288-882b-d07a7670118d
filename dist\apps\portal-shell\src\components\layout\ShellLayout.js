"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom"); // Outlet renders the matched child route component
const Sidebar_1 = require("./Sidebar");
const Topbar_1 = require("./Topbar"); // Corrected back to Topbar
const react_hot_toast_1 = require("react-hot-toast"); // Removed unused toast import
// Import CSS for scrollbar hiding if needed, or rely on Tailwind plugin
// import './scrollbar-hide.css'; 
const ShellLayout = () => {
    return (<div className="flex h-screen bg-white"> {/* Changed outer background to white */}
      <Sidebar_1.default />
      <div className="flex-1 flex flex-col overflow-hidden ml-64">
        <Topbar_1.default /> {/* Use Topbar */}
        {/* Changed main background to white and added scrollbar hiding class */}
        {/* Note: scrollbar-hide might require a Tailwind plugin or custom CSS */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-white p-6 scrollbar-hide">
          <react_router_dom_1.Outlet /> {/* Child routes will render here */}
        </main>
        <react_hot_toast_1.Toaster position="top-right" reverseOrder={false} toastOptions={{
            duration: 5000,
            style: {
                background: '#333',
                color: '#fff',
                fontSize: '14px',
            },
            success: {
                duration: 3000,
                // Use iconTheme or style directly for success
                iconTheme: {
                    primary: '#10B981', // Tailwind green-500
                    secondary: '#fff',
                },
                style: {
                    background: '#D1FAE5', // Tailwind green-100
                    color: '#065F46', // Tailwind green-800
                }
            },
            error: {
                duration: 5000,
                iconTheme: {
                    primary: '#EF4444', // Tailwind red-500
                    secondary: '#fff',
                },
                style: {
                    background: '#FEE2E2', // Tailwind red-100
                    color: '#991B1B', // Tailwind red-800
                }
            },
        }}/>
      </div>
    </div>);
};
exports.default = ShellLayout;
//# sourceMappingURL=ShellLayout.js.map