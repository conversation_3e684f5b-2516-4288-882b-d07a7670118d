"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPortalUsers = getPortalUsers;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService"); // Import the service
const authUtils_1 = require("../shared/authUtils"); // Import auth utils
const validationSchemas_1 = require("../shared/validationSchemas"); // Import validation
const logger_1 = require("../shared/utils/logger"); // Import logger
// Helper to map database result to PortalUser
// TODO: Adjust property names based on actual SQL query results
function mapDbUserToPortalUser(dbUser) {
    return {
        id: dbUser.EntraID, // Assuming EntraID is the primary link
        internalId: dbUser.UserID, // Internal DB UserID
        name: dbUser.Username,
        email: dbUser.Email,
        company: dbUser.CompanyName, // Assuming join brings CompanyName
        companyId: dbUser.CompanyID, // Assuming join brings CompanyID
        roles: dbUser.Roles ? dbUser.Roles.split(',') : [], // Assuming roles are aggregated into a comma-separated string
        status: dbUser.IsActive ? 'Active' : 'Inactive', // Assuming IsActive column
        lastLogin: dbUser.LastLogin ? dbUser.LastLogin.toISOString() : undefined
    };
}
function getPortalUsers(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log = logger_1.logger.info; // Use shared logger
        logger_1.logger.info(`Http function processed request for url "${request.url}"`);
        // 1. Authentication & Authorization Check
        // Log the environment variable value for debugging
        logger_1.logger.warn(`Current NODE_ENV: [${process.env.NODE_ENV}]`);
        // Bypass auth check for local development where header is not present
        if (process.env.NODE_ENV !== 'development') {
            const principal = (0, authUtils_1.getClientPrincipal)(request);
            if (!principal) {
                logger_1.logger.warn("GetPortalUsers: Unauthenticated access attempt.");
                return { status: 401, jsonBody: { error: "Unauthorized" } };
            }
            // Optional: Add role check if needed, e.g., Portal Admin
            // if (!hasRequiredRole(principal, ['Portal Admin'])) {
            //     logger.warn(`GetPortalUsers: User ${principal.userDetails} lacks required role.`);
            //     return { status: 403, jsonBody: { error: "Forbidden" } };
            // }
        }
        else {
            logger_1.logger.warn("GetPortalUsers: Bypassing authentication check in development mode.");
        }
        // 2. Input Validation
        // Pass the data, context, and type description
        const validationErrorResponse = (0, validationSchemas_1.validateRequest)(validationSchemas_1.getPortalUsersSchema.shape.query, request.query, context, "query parameters");
        if (validationErrorResponse) {
            // If validation fails, return the 400 response generated by validateRequest
            logger_1.logger.warn("GetPortalUsers: Invalid request parameters."); // Log is already done in validateRequest
            return validationErrorResponse;
        }
        // If validation succeeded, parse the data again safely using the schema to get typed output
        // We know this parse will succeed because validateRequest passed.
        const parseResult = validationSchemas_1.getPortalUsersSchema.shape.query.safeParse(request.query);
        // Add a check just in case, although theoretically unreachable if validateRequest passed
        if (!parseResult.success) {
            logger_1.logger.error("GetPortalUsers: Zod parse failed after successful validation check. This should not happen.", parseResult.error);
            return { status: 500, jsonBody: { error: "Internal validation error" } };
        }
        // Extract validated & typed data 
        const { page, pageSize, searchTerm, companyId, roleId, status } = parseResult.data;
        try {
            // 3. Call Service Method
            const result = yield userManagementService_1.userManagementService.getPortalUsersPaginated({ searchTerm, companyId, roleId, status }, { page, pageSize });
            // 4. Return Response
            return {
                status: 200, // Explicitly set status 200
                jsonBody: {
                    users: result.users,
                    totalCount: result.totalCount,
                    page: page,
                    pageSize: pageSize
                }
            };
        }
        catch (err) {
            logger_1.logger.error(`Error fetching portal users: ${err}`);
            const error = err;
            return {
                status: 500,
                jsonBody: {
                    error: "Failed to fetch portal users",
                    details: error.message
                }
            };
        }
    });
}
functions_1.app.http('GetPortalUsers', {
    methods: ['GET'],
    route: 'portal-users', // Ensure this matches the frontend API call path (/api/portal-users via proxy)
    // Use 'function' authLevel if relying on Azure Function App's Entra ID integration
    // Keep 'anonymous' if handling auth solely within the function code (as done above)
    authLevel: 'anonymous',
    handler: getPortalUsers
});
//# sourceMappingURL=GetPortalUsers.js.map