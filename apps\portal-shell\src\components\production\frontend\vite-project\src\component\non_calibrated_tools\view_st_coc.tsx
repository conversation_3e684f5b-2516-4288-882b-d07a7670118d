import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface CocEntry {
  fg_partnumber: string;
  fg_partnumber_type: string;
  fg_partnumber_file: string;
  create_userid: string;
  create_username: string;
}

const View_St_Coc: React.FC = () => {
  const [data, setData] = useState<CocEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    axios.get('http://localhost:3000/api/foam_board_coc_table')
      .then(response => {
        setData(response.data);
        setLoading(false);
      })
      .catch(_err => {
        setError('Failed to load data');
        setLoading(false);
      });
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-6xl mx-auto bg-white p-8 rounded-xl shadow-lg">
        <h1 className="text-2xl font-bold text-center text-blue-700 mb-6">VIEW SERVICEABLE TAG / COC</h1>

        {loading && <p className="text-center text-gray-500">Loading...</p>}
        {error && <p className="text-center text-red-500">{error}</p>}

        {!loading && !error && (
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200 text-sm rounded-lg overflow-hidden">
              <thead className="bg-blue-600 text-white">
                <tr>
                  <th className="py-3 px-4 text-left">Part Number</th>
                  <th className="py-3 px-4 text-left">File Type</th>
                  <th className="py-3 px-4 text-left">File</th>
                  <th className="py-3 px-4 text-left">User ID</th>
                  <th className="py-3 px-4 text-left">Username</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((entry, index) => (
                  <tr key={index} className="hover:bg-blue-50 transition">
                    <td className="py-3 px-4">{entry.fg_partnumber}</td>
                    <td className="py-3 px-4">{entry.fg_partnumber_type}</td>
                    <td className="py-3 px-4">
                      <a
                        href={`http://localhost:3000/ST_MC_ATTACHMENTS/${entry.fg_partnumber_file}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 underline hover:text-blue-800"
                      >
                        View File
                      </a>
                    </td>
                    <td className="py-3 px-4">{entry.create_userid}</td>
                    <td className="py-3 px-4">{entry.create_username}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {data.length === 0 && (
              <p className="text-center py-4 text-gray-500">No records found.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default View_St_Coc;
