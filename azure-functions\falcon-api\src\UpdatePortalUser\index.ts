import { HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal } from "../shared/authUtils";
import { assignRoleToUser, removeRoleFromUser } from "../shared/services/userManagementService";
import * as sql from 'mssql';

// Interface for the expected request body
interface UpdatePortalUserRequest {
    roles?: string[]; // Array of role NAMES
    isActive?: boolean;
    name?: string; // User's display name (will be split into FirstName/LastName)
    company?: string; // Company name (will be mapped to CompanyID)
}

// Define required role(s) for authorization
const REQUIRED_ROLE = 'Administrator'; // Users who can update other users

export default async function updatePortalUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function UpdatePortalUser processed request for url "${request.url}"`);
    logger.info('UpdatePortalUser function invoked.');

    // 1. Authentication & Authorization
    let authenticatedUserId: number;
    
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        logger.warn('UpdatePortalUser: Bypassing authentication check in development/test mode.');
        authenticatedUserId = 1; // Use default user ID for development
    } else {
        const principal = getClientPrincipal(request);
        if (!principal) {
            logger.warn("UpdatePortalUser: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }
        
        if (!hasRequiredRole(principal, [REQUIRED_ROLE])) {
            logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted to update user without required role '${REQUIRED_ROLE}'.`);
            return { status: 403, jsonBody: { error: "Forbidden. User does not have the required permissions." } };
        }
        
        const authUserId = await getUserIdFromPrincipal(principal, context);
        if (!authUserId) {
            logger.error(`UpdatePortalUser: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
            return { status: 403, jsonBody: { error: "Forbidden. Authenticated user not found or inactive in the portal database." } };
        }
        authenticatedUserId = authUserId;
    }

    logger.info(`UpdatePortalUser invoked by UserID: ${authenticatedUserId}`);

    // 2. Extract Entra ID from route parameter
    const entraId = request.params.entraId;
    if (!entraId) {
        logger.warn("UpdatePortalUser: Entra ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
    }

    // 3. Parse and validate request body
    let parsedBody: UpdatePortalUserRequest;
    try {
        parsedBody = await request.json() as UpdatePortalUserRequest;
    } catch (error) {
        logger.error('UpdatePortalUser: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { error: "Invalid JSON in request body." } };
    }

    // Basic validation
    if (!parsedBody || typeof parsedBody !== 'object') {
        return { status: 400, jsonBody: { error: "Request body must be a valid object." } };
    }

    const { roles: requestedRoleNames, isActive: requestedIsActive, name: requestedName, company: requestedCompany } = parsedBody;

    try {
        // 4. Get UserID and current status/roles for the target user (by EntraID)
        const getUserInfoQuery = `
            SELECT u.UserID, u.IsActive, u.FirstName, u.LastName, u.CompanyID,
                   STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) as CurrentRoleNames
            FROM Users u
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE u.EntraID = @EntraID
            GROUP BY u.UserID, u.IsActive, u.FirstName, u.LastName, u.CompanyID;
        `;
        const userInfoParams: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const userInfoResult = await executeQuery(getUserInfoQuery, userInfoParams);

        if (!userInfoResult.recordset || userInfoResult.recordset.length === 0) {
            logger.warn(`UpdatePortalUser: Target user with EntraID ${entraId} not found.`);
            return { status: 404, jsonBody: { error: `User with EntraID ${entraId} not found.` } };
        }

        const userId = userInfoResult.recordset[0].UserID;
        const currentIsActive = userInfoResult.recordset[0].IsActive;
        const currentRoleNames: string[] = userInfoResult.recordset[0].CurrentRoleNames 
            ? userInfoResult.recordset[0].CurrentRoleNames.split(',').filter((r: string) => r.trim())
            : [];
        
        logger.info(`Updating UserID: ${userId}. Current Status: ${currentIsActive}, Current Roles: [${currentRoleNames.join(',')}]`);
        logger.info(`Requested Status: ${requestedIsActive}, Requested Roles: [${requestedRoleNames?.join(',') || 'no change'}]`);

        // 5. Update User Status (IsActive) if provided
        if (typeof requestedIsActive === 'boolean' && requestedIsActive !== currentIsActive) {
            logger.info(`Updating IsActive status for UserID ${userId} to ${requestedIsActive}`);
            const updateStatusQuery = `UPDATE Users SET IsActive = @IsActive, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
            const statusParams: QueryParameter[] = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'IsActive', type: sql.Bit, value: requestedIsActive },
                { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
            ];
            await executeQuery(updateStatusQuery, statusParams);
            logger.info(`Successfully updated IsActive status for UserID ${userId}`);
        }

        // 6. Update User Name (FirstName/LastName) if provided
        if (requestedName && typeof requestedName === 'string' && requestedName.trim()) {
            logger.info(`Updating name for UserID ${userId} to '${requestedName}'`);
            const nameParts = requestedName.trim().split(/\s+/);
            const firstName = nameParts[0] || '';
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
            
            const updateNameQuery = `UPDATE Users SET FirstName = @FirstName, LastName = @LastName, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
            const nameParams: QueryParameter[] = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'FirstName', type: sql.NVarChar, value: firstName },
                { name: 'LastName', type: sql.NVarChar, value: lastName },
                { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
            ];
            await executeQuery(updateNameQuery, nameParams);
            logger.info(`Successfully updated name for UserID ${userId}`);
        }

        // 7. Update User Company (CompanyID) if provided
        if (requestedCompany && typeof requestedCompany === 'string' && requestedCompany.trim()) {
            logger.info(`Updating company for UserID ${userId} to '${requestedCompany}'`);
            const getCompanyIdQuery = `SELECT CompanyID FROM Companies WHERE CompanyName = @CompanyName AND IsActive = 1;`;
            const companyParams: QueryParameter[] = [
                { name: 'CompanyName', type: sql.NVarChar, value: requestedCompany.trim() }
            ];
            const companyResult = await executeQuery(getCompanyIdQuery, companyParams);
            
            if (companyResult.recordset && companyResult.recordset.length > 0) {
                const companyId = companyResult.recordset[0].CompanyID;
                const updateCompanyQuery = `UPDATE Users SET CompanyID = @CompanyID, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
                const updateCompanyParams: QueryParameter[] = [
                    { name: 'UserID', type: sql.Int, value: userId },
                    { name: 'CompanyID', type: sql.Int, value: companyId },
                    { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
                ];
                await executeQuery(updateCompanyQuery, updateCompanyParams);
                logger.info(`Successfully updated CompanyID for UserID ${userId} to ${companyId}`);
            } else {
                logger.warn(`Company '${requestedCompany}' not found or inactive. Skipping company update for UserID ${userId}.`);
            }
        }

        // 8. Handle Role Updates if provided
        if (requestedRoleNames && Array.isArray(requestedRoleNames)) {
            logger.info(`Processing role updates for UserID ${userId}`);
            
            const desiredRoles = new Set(requestedRoleNames.map(role => String(role).trim()).filter(r => r));
            const currentRoles = new Set(currentRoleNames.map((role: string) => String(role).trim()).filter((r: string) => r));

            const rolesToAdd = [...desiredRoles].filter(role => !currentRoles.has(role));
            const rolesToRemove = [...currentRoles].filter(role => !desiredRoles.has(role));

            logger.info(`Roles to add: [${rolesToAdd.join(', ')}], Roles to remove: [${rolesToRemove.join(', ')}]`);

            if (rolesToAdd.length > 0 || rolesToRemove.length > 0) {
                const allRoleNames = [...rolesToAdd, ...rolesToRemove];
                
                if (allRoleNames.length > 0) {
                    // Get role IDs for the role names
                    const getRoleIdsQuery = `SELECT RoleName, RoleID FROM Roles WHERE RoleName IN (${allRoleNames.map((_, i) => `@RoleName${i}`).join(', ')}) AND IsActive = 1`;
                    const roleIdParams: QueryParameter[] = allRoleNames.map((name, i) => ({
                        name: `RoleName${i}`,
                        type: sql.NVarChar,
                        value: name
                    }));
                    
                    const roleIdsResult = await executeQuery(getRoleIdsQuery, roleIdParams);
                    const roleIdMap = new Map<string, number>();
                    roleIdsResult.recordset.forEach((row: any) => {
                        roleIdMap.set(row.RoleName as string, row.RoleID as number);
                    });

                    // Process role removals
                    for (const roleName of rolesToRemove) {
                        const roleId = roleIdMap.get(roleName as string);
                        if (roleId) {
                            logger.info(`Removing role '${roleName}' (ID: ${roleId}) from UserID ${userId}`);
                            await removeRoleFromUser(userId, roleId, authenticatedUserId);
                        } else {
                            logger.warn(`Role name '${roleName}' not found in Roles table. Skipping removal.`);
                        }
                    }

                    // Process role additions
                    for (const roleName of rolesToAdd) {
                        const roleId = roleIdMap.get(roleName as string);
                        if (roleId) {
                            logger.info(`Adding role '${roleName}' (ID: ${roleId}) to UserID ${userId}`);
                            await assignRoleToUser(userId, roleId, authenticatedUserId);
                        } else {
                            logger.warn(`Role name '${roleName}' not found in Roles table. Skipping addition.`);
                        }
                    }
                }
            }
        }

        logger.info(`Successfully updated portal user with UserID ${userId}.`);
        
        return {
            status: 200,
            jsonBody: { 
                message: "User updated successfully.",
                userId: userId
            }
        };

    } catch (error) {
        logger.error(`Error in UpdatePortalUser function for EntraID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                error: "An unexpected error occurred while updating the user.",
                details: errorMessage
            }
        };
    }
} 