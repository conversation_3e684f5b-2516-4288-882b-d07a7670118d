"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addCorsHeaders = exports.corsHeaders = void 0;
exports.corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-ms-client-principal',
    'Access-Control-Max-Age': '86400'
};
function addCorsHeaders(response) {
    return {
        ...response,
        headers: {
            ...response.headers,
            ...exports.corsHeaders
        }
    };
}
exports.addCorsHeaders = addCorsHeaders;
//# sourceMappingURL=cors.js.map