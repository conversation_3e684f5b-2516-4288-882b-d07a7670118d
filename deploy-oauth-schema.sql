-- OAuth Token Management Schema Deployment Script
-- Run this script to deploy the persistent token storage solution

USE FalconPortal;
GO

-- Check if tables already exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[OAuthTokens]') AND type in (N'U'))
BEGIN
    PRINT 'Creating OAuthTokens table...';
    
    CREATE TABLE OAuthTokens (
        TokenId INT IDENTITY(1,1) PRIMARY KEY,
        UserId NVARCHAR(255) NOT NULL,
        ServiceProvider NVARCHAR(100) NOT NULL, -- 'zoho', 'microsoft', etc.
        ServiceType NVARCHAR(100) NOT NULL, -- 'desk', 'crm', 'people', etc.
        AccessToken NVARCHAR(MAX) NOT NULL,
        RefreshToken NVARCHAR(MAX) NULL,
        ExpiresAt DATETIME2 NOT NULL,
        Scope NVARCHAR(500) NULL,
        TokenType NVARCHAR(50) NOT NULL DEFAULT 'Bearer',
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Indexes for performance
        INDEX IX_OAuthTokens_UserService NONCLUSTERED (UserId, ServiceProvider, ServiceType, IsActive, ExpiresAt),
        INDEX IX_OAuthTokens_Expiry NONCLUSTERED (ExpiresAt, IsActive) INCLUDE (UserId, ServiceProvider, ServiceType)
    );
    
    PRINT 'OAuthTokens table created successfully.';
END
ELSE
BEGIN
    PRINT 'OAuthTokens table already exists.';
END

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[OAuthConfigurations]') AND type in (N'U'))
BEGIN
    PRINT 'Creating OAuthConfigurations table...';
    
    CREATE TABLE OAuthConfigurations (
        ConfigId INT IDENTITY(1,1) PRIMARY KEY,
        ServiceProvider NVARCHAR(100) NOT NULL,
        ServiceType NVARCHAR(100) NOT NULL,
        ConfigKey NVARCHAR(255) NOT NULL,
        ConfigValue NVARCHAR(MAX) NOT NULL,
        IsEncrypted BIT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        UNIQUE (ServiceProvider, ServiceType, ConfigKey),
        INDEX IX_OAuthConfigurations_Service NONCLUSTERED (ServiceProvider, ServiceType, IsActive)
    );
    
    PRINT 'OAuthConfigurations table created successfully.';
END
ELSE
BEGIN
    PRINT 'OAuthConfigurations table already exists.';
END

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[OAuthTokenUsage]') AND type in (N'U'))
BEGIN
    PRINT 'Creating OAuthTokenUsage table...';
    
    CREATE TABLE OAuthTokenUsage (
        UsageId BIGINT IDENTITY(1,1) PRIMARY KEY,
        TokenId INT NOT NULL,
        RequestEndpoint NVARCHAR(500) NULL,
        RequestTime DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ResponseStatus INT NULL,
        ErrorMessage NVARCHAR(MAX) NULL,
        
        FOREIGN KEY (TokenId) REFERENCES OAuthTokens(TokenId),
        INDEX IX_OAuthTokenUsage_Token NONCLUSTERED (TokenId, RequestTime),
        INDEX IX_OAuthTokenUsage_Time NONCLUSTERED (RequestTime)
    );
    
    PRINT 'OAuthTokenUsage table created successfully.';
END
ELSE
BEGIN
    PRINT 'OAuthTokenUsage table already exists.';
END

-- Create stored procedures
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetActiveOAuthToken]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[GetActiveOAuthToken];
    PRINT 'Dropped existing GetActiveOAuthToken procedure.';
END

PRINT 'Creating GetActiveOAuthToken procedure...';
GO

CREATE PROCEDURE GetActiveOAuthToken
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1
        TokenId,
        AccessToken,
        RefreshToken,
        ExpiresAt,
        Scope,
        TokenType,
        CreatedAt,
        UpdatedAt
    FROM OAuthTokens
    WHERE UserId = @UserId
        AND ServiceProvider = @ServiceProvider
        AND ServiceType = @ServiceType
        AND IsActive = 1
        AND ExpiresAt > GETUTCDATE()
    ORDER BY CreatedAt DESC;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SaveOAuthToken]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[SaveOAuthToken];
    PRINT 'Dropped existing SaveOAuthToken procedure.';
END

PRINT 'Creating SaveOAuthToken procedure...';
GO

CREATE PROCEDURE SaveOAuthToken
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100),
    @AccessToken NVARCHAR(MAX),
    @RefreshToken NVARCHAR(MAX) = NULL,
    @ExpiresIn INT = 3600,
    @Scope NVARCHAR(500) = NULL,
    @TokenType NVARCHAR(50) = 'Bearer'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ExpiresAt DATETIME2 = DATEADD(SECOND, @ExpiresIn, GETUTCDATE());
    DECLARE @TokenId INT;
    
    -- Deactivate existing tokens for this user/service combination
    UPDATE OAuthTokens 
    SET IsActive = 0, UpdatedAt = GETUTCDATE()
    WHERE UserId = @UserId 
        AND ServiceProvider = @ServiceProvider 
        AND ServiceType = @ServiceType 
        AND IsActive = 1;
    
    -- Insert new token
    INSERT INTO OAuthTokens (
        UserId, ServiceProvider, ServiceType, AccessToken, RefreshToken,
        ExpiresAt, Scope, TokenType, IsActive
    )
    VALUES (
        @UserId, @ServiceProvider, @ServiceType, @AccessToken, @RefreshToken,
        @ExpiresAt, @Scope, @TokenType, 1
    );
    
    SET @TokenId = SCOPE_IDENTITY();
    
    SELECT @TokenId as TokenId;
END
GO

-- Create function
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[NeedsTokenRefresh]') AND type in (N'FN'))
BEGIN
    DROP FUNCTION [dbo].[NeedsTokenRefresh];
    PRINT 'Dropped existing NeedsTokenRefresh function.';
END

PRINT 'Creating NeedsTokenRefresh function...';
GO

CREATE FUNCTION NeedsTokenRefresh(
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
)
RETURNS BIT
AS
BEGIN
    DECLARE @Result BIT = 1; -- Default to needs refresh
    DECLARE @ExpiresAt DATETIME2;
    
    SELECT @ExpiresAt = ExpiresAt
    FROM OAuthTokens
    WHERE UserId = @UserId
        AND ServiceProvider = @ServiceProvider
        AND ServiceType = @ServiceType
        AND IsActive = 1;
    
    IF @ExpiresAt IS NOT NULL AND @ExpiresAt > DATEADD(MINUTE, 5, GETUTCDATE())
    BEGIN
        SET @Result = 0; -- Token is valid for more than 5 minutes
    END
    
    RETURN @Result;
END
GO

-- Insert sample OAuth configuration for ZohoDesk
IF NOT EXISTS (SELECT 1 FROM OAuthConfigurations WHERE ServiceProvider = 'zoho' AND ServiceType = 'desk' AND ConfigKey = 'client_id')
BEGIN
    PRINT 'Inserting sample ZohoDesk OAuth configuration...';
    
    INSERT INTO OAuthConfigurations (ServiceProvider, ServiceType, ConfigKey, ConfigValue, IsEncrypted)
    VALUES 
        ('zoho', 'desk', 'client_id', '1000.03IZH9ZAA9U4H8OTTE0J2LNKB3U9VO', 0),
        ('zoho', 'desk', 'client_secret', '1acd0b7dfbbbb332a1b226a97fae998cfe4a28a3f3', 1),
        ('zoho', 'desk', 'redirect_uri', 'http://localhost:7071/api/auth/zoho-desk/callback', 0),
        ('zoho', 'desk', 'base_url', 'https://accounts.zoho.in/oauth/v2', 0),
        ('zoho', 'desk', 'scopes', 'Desk.tickets.ALL,Desk.contacts.ALL,Desk.basic.READ,Desk.settings.READ,Desk.agents.READ,Desk.departments.READ,Desk.categories.READ,Desk.threads.ALL,Desk.attachments.READ', 0);
    
    PRINT 'ZohoDesk OAuth configuration inserted.';
END
ELSE
BEGIN
    PRINT 'ZohoDesk OAuth configuration already exists.';
END

PRINT '';
PRINT '=== OAuth Token Management Schema Deployment Complete ===';
PRINT 'Status:';
PRINT '✅ Tables: OAuthTokens, OAuthConfigurations, OAuthTokenUsage';
PRINT '✅ Procedures: GetActiveOAuthToken, SaveOAuthToken';  
PRINT '✅ Function: NeedsTokenRefresh';
PRINT '✅ Sample Configuration: ZohoDesk OAuth settings';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Deploy the updated Azure Functions code';
PRINT '2. Test OAuth flow with persistent token storage';
PRINT '3. Verify tokens survive server restarts';
PRINT '4. Monitor OAuthTokenUsage table for API usage patterns'; 