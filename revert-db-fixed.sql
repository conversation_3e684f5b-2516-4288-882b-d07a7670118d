-- Check if DeploymentDate exists and remove all the fields I added
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ChangeRequests' AND COLUMN_NAME = 'DeploymentDate')
BEGIN
    PRINT 'Reverting database changes...';
    
    -- Drop index if exists
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChangeRequests_DeploymentDate')
    BEGIN
        DROP INDEX IX_ChangeRequests_DeploymentDate ON ChangeRequests;
        PRINT 'Dropped index IX_ChangeRequests_DeploymentDate';
    END
    
    -- Drop constraints if they exist
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentLocation')
    BEGIN
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentLocation;
        PRINT 'Dropped constraint CK_ChangeRequests_DeploymentLocation';
    END
    
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentType')
    BEGIN
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentType;
        PRINT 'Dropped constraint CK_ChangeRequests_DeploymentType';
    END
    
    -- Drop default constraint for RequiresSystemDowntime
    DECLARE @ConstraintName NVARCHAR(255)
    SELECT @ConstraintName = dc.name
    FROM sys.default_constraints dc
    INNER JOIN sys.columns c ON dc.parent_object_id = c.object_id AND dc.parent_column_id = c.column_id
    WHERE OBJECT_NAME(dc.parent_object_id) = 'ChangeRequests' AND c.name = 'RequiresSystemDowntime'
    
    IF @ConstraintName IS NOT NULL
    BEGIN
        DECLARE @SQL NVARCHAR(MAX) = 'ALTER TABLE ChangeRequests DROP CONSTRAINT ' + QUOTENAME(@ConstraintName)
        EXEC sp_executesql @SQL
        PRINT 'Dropped default constraint for RequiresSystemDowntime';
    END
    
    -- Remove the columns I incorrectly added
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentDate;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentDuration;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentLocation;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentType;
    ALTER TABLE ChangeRequests DROP COLUMN CalendarColor;
    ALTER TABLE ChangeRequests DROP COLUMN RequiresSystemDowntime;
    
    PRINT 'Successfully reverted database to working state - all deployment fields removed';
END
ELSE
BEGIN
    PRINT 'No deployment fields found - database is already in correct state';
END 