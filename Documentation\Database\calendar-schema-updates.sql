-- =============================================
-- Change Management Calendar Schema Updates
-- Date: January 24, 2025
-- Purpose: Add calendar-specific fields to support deployment scheduling
-- =============================================

-- Add calendar-specific fields to ChangeRequests table
ALTER TABLE ChangeRequests 
ADD 
    -- Deployment scheduling fields
    DeploymentDate DATETIME2 NULL,
    DeploymentDuration INT NULL, -- Duration in minutes
    DeploymentLocation NVARCHAR(100) NULL, -- 'Production', 'Staging', 'Development'
    DeploymentType NVARCHAR(50) NULL, -- 'Automatic', 'Manual', 'Emergency'
    
    -- Calendar display fields
    CalendarColor NVARCHAR(7) NULL, -- Hex color for calendar events
    IsRecurring BIT DEFAULT 0,
    RecurrencePattern NVARCHAR(MAX) NULL, -- JSON for recurring events
    
    -- Conflict detection fields
    RequiresSystemDowntime BIT DEFAULT 0,
    ConflictingResources NVARCHAR(MAX) NULL, -- JSON array of conflicting resources
    
    -- Notification settings
    NotifyBeforeDeployment INT NULL, -- Minutes before deployment to send notification
    NotificationEmailList NVARCHAR(MAX) NULL, -- JSON array of additional email addresses
    
    -- Calendar metadata
    CalendarEventId NVARCHAR(50) NULL, -- External calendar integration ID
    LastCalendarSync DATETIME2 NULL;

-- Add check constraints for new fields
ALTER TABLE ChangeRequests 
ADD CONSTRAINT CK_ChangeRequests_DeploymentLocation 
    CHECK (DeploymentLocation IN ('Development', 'Staging', 'Production', 'All Environments') OR DeploymentLocation IS NULL);

ALTER TABLE ChangeRequests 
ADD CONSTRAINT CK_ChangeRequests_DeploymentType 
    CHECK (DeploymentType IN ('Automatic', 'Manual', 'Emergency', 'Scheduled') OR DeploymentType IS NULL);

ALTER TABLE ChangeRequests 
ADD CONSTRAINT CK_ChangeRequests_DeploymentDuration 
    CHECK (DeploymentDuration > 0 OR DeploymentDuration IS NULL);

ALTER TABLE ChangeRequests 
ADD CONSTRAINT CK_ChangeRequests_NotifyBefore 
    CHECK (NotifyBeforeDeployment >= 0 OR NotifyBeforeDeployment IS NULL);

-- Create indexes for calendar queries
CREATE NONCLUSTERED INDEX IX_ChangeRequests_DeploymentDate 
    ON ChangeRequests(DeploymentDate) 
    WHERE DeploymentDate IS NOT NULL;

CREATE NONCLUSTERED INDEX IX_ChangeRequests_Calendar_Status 
    ON ChangeRequests(Status, DeploymentDate) 
    WHERE Status IN ('Approved', 'Ready for Deployment', 'Deployed') 
    AND DeploymentDate IS NOT NULL;

-- =============================================
-- Calendar Conflict Detection View
-- =============================================
CREATE OR ALTER VIEW vw_CalendarConflicts AS
SELECT 
    cr1.RequestID AS RequestID1,
    cr1.RequestNumber AS RequestNumber1,
    cr1.Title AS Title1,
    cr1.DeploymentDate AS DeploymentDate1,
    cr1.DeploymentDuration AS Duration1,
    cr1.DeploymentLocation AS Location1,
    
    cr2.RequestID AS RequestID2,
    cr2.RequestNumber AS RequestNumber2,
    cr2.Title AS Title2,
    cr2.DeploymentDate AS DeploymentDate2,
    cr2.DeploymentDuration AS Duration2,
    cr2.DeploymentLocation AS Location2,
    
    -- Calculate overlap
    CASE 
        WHEN cr1.DeploymentLocation = cr2.DeploymentLocation 
        AND cr1.RequiresSystemDowntime = 1 
        AND cr2.RequiresSystemDowntime = 1
        THEN 'High Risk - System Downtime Conflict'
        
        WHEN cr1.DeploymentLocation = cr2.DeploymentLocation
        THEN 'Medium Risk - Same Environment'
        
        ELSE 'Low Risk - Different Environments'
    END AS ConflictLevel,
    
    -- Time overlap calculation
    DATEDIFF(MINUTE, 
        CASE WHEN cr1.DeploymentDate > cr2.DeploymentDate THEN cr1.DeploymentDate ELSE cr2.DeploymentDate END,
        CASE WHEN DATEADD(MINUTE, ISNULL(cr1.DeploymentDuration, 60), cr1.DeploymentDate) < 
                  DATEADD(MINUTE, ISNULL(cr2.DeploymentDuration, 60), cr2.DeploymentDate) 
             THEN DATEADD(MINUTE, ISNULL(cr1.DeploymentDuration, 60), cr1.DeploymentDate) 
             ELSE DATEADD(MINUTE, ISNULL(cr2.DeploymentDuration, 60), cr2.DeploymentDate) 
        END
    ) AS OverlapMinutes

FROM ChangeRequests cr1
INNER JOIN ChangeRequests cr2 ON cr1.RequestID < cr2.RequestID
WHERE 
    cr1.DeploymentDate IS NOT NULL 
    AND cr2.DeploymentDate IS NOT NULL
    AND cr1.Status IN ('Approved', 'Ready for Deployment', 'Deployed')
    AND cr2.Status IN ('Approved', 'Ready for Deployment', 'Deployed')
    -- Check for time overlap
    AND cr1.DeploymentDate < DATEADD(MINUTE, ISNULL(cr2.DeploymentDuration, 60), cr2.DeploymentDate)
    AND cr2.DeploymentDate < DATEADD(MINUTE, ISNULL(cr1.DeploymentDuration, 60), cr1.DeploymentDate);

-- =============================================
-- Calendar Events View for Easy API Access
-- =============================================
CREATE OR ALTER VIEW vw_CalendarEvents AS
SELECT 
    cr.RequestID,
    cr.RequestNumber,
    cr.Title,
    cr.Description,
    cr.Priority,
    cr.Status,
    cr.DeploymentDate AS StartDate,
    DATEADD(MINUTE, ISNULL(cr.DeploymentDuration, 60), cr.DeploymentDate) AS EndDate,
    cr.DeploymentDuration,
    cr.DeploymentLocation,
    cr.DeploymentType,
    ISNULL(cr.CalendarColor, 
        CASE cr.Priority
            WHEN 'Critical' THEN '#FF4444'
            WHEN 'High' THEN '#FF8800'
            WHEN 'Medium' THEN '#4488FF'
            WHEN 'Low' THEN '#44AA44'
            ELSE '#666666'
        END
    ) AS CalendarColor,
    cr.RequiresSystemDowntime,
    cr.IsRecurring,
    cr.RecurrencePattern,
    
    -- User information
    CONCAT(requester.FirstName, ' ', requester.LastName) AS RequesterName,
    CONCAT(assignee.FirstName, ' ', assignee.LastName) AS AssigneeName,
    
    -- Company information
    c.CompanyName,
    c.CompanyID,
    
    -- Type information
    crt.TypeName,
    crt.EstimatedDays,
    
    -- Conflict indication
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM vw_CalendarConflicts 
            WHERE RequestID1 = cr.RequestID OR RequestID2 = cr.RequestID
        ) THEN 1 
        ELSE 0 
    END AS HasConflicts

FROM ChangeRequests cr
LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
LEFT JOIN Users assignee ON cr.AssignedToDevID = assignee.UserID
LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
WHERE 
    cr.DeploymentDate IS NOT NULL
    AND cr.Status IN ('Approved', 'Ready for Deployment', 'Deployed', 'Completed');

-- =============================================
-- Default Calendar Colors by Priority
-- =============================================
UPDATE ChangeRequests 
SET CalendarColor = CASE Priority
    WHEN 'Critical' THEN '#FF4444'
    WHEN 'High' THEN '#FF8800'
    WHEN 'Medium' THEN '#4488FF'
    WHEN 'Low' THEN '#44AA44'
    ELSE '#666666'
END
WHERE CalendarColor IS NULL;

-- =============================================
-- Comments for Documentation
-- =============================================
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Scheduled deployment date and time for the change request',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequests',
    @level2type = N'COLUMN', @level2name = N'DeploymentDate';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Expected duration of deployment in minutes',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequests',
    @level2type = N'COLUMN', @level2name = N'DeploymentDuration';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Hex color code for calendar event display',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequests',
    @level2type = N'COLUMN', @level2name = N'CalendarColor';

PRINT 'Calendar schema updates completed successfully!'; 