import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { BlobServiceClient, StorageSharedKeyCredential } from '@azure/storage-blob';
import { v4 as uuidv4 } from 'uuid';
import { addCorsHeaders } from '../shared/cors';

// Configuration
const STORAGE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'falconhubstorage';
const STORAGE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const CONTAINER_NAME = 'change-request-images';

// Allowed image types and max file size (5MB)
const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

interface UploadResponse {
  success: boolean;
  message: string;
  imageUrl?: string;
  blobName?: string;
  error?: string;
  dataUrl?: string; // For immediate preview
}

export async function UploadImage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  context.log('UploadImage function processed a request.');

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      body: ''
    });
  }

  try {
    // Parse the multipart form data
    const formData = await request.formData();
    const fileEntry = formData.get('image');
    const userId = formData.get('userId') as string;
    const requestId = formData.get('requestId') as string; // Optional: for associating with specific requests

    // Type guard for File
    if (!fileEntry || typeof fileEntry === 'string') {
      return addCorsHeaders({
        status: 400,
        jsonBody: {
          success: false,
          message: 'No image file provided'
        } as UploadResponse
      });
    }

    // Use the undici File type (which is what we get from FormData)
    const file = fileEntry as any; // Using any to avoid type conflicts between DOM File and undici File

    if (!userId) {
      return addCorsHeaders({
        status: 400,
        jsonBody: {
          success: false,
          message: 'User ID is required'
        } as UploadResponse
      });
    }

    // Validate file type
    if (!ALLOWED_MIME_TYPES.includes(file.type)) {
      return addCorsHeaders({
        status: 400,
        jsonBody: {
          success: false,
          message: `Invalid file type. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}`
        } as UploadResponse
      });
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return addCorsHeaders({
        status: 400,
        jsonBody: {
          success: false,
          message: `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
        } as UploadResponse
      });
    }

    // Convert File to ArrayBuffer for both storage and data URL
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Create data URL for immediate preview (works without storage account public access)
    const base64Data = buffer.toString('base64');
    const dataUrl = `data:${file.type};base64,${base64Data}`;

    // Generate unique blob name for storage
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = uuidv4().substring(0, 8);
    const blobName = `${userId}/${timestamp}-${uniqueId}.${fileExtension}`;

    let imageUrl = dataUrl; // Default to data URL for preview
    let uploadSuccess = true;

    // Try to upload to blob storage (but don't fail if storage is not configured)
    if (STORAGE_ACCOUNT_KEY) {
      try {
        // Create storage shared key credential
        const sharedKeyCredential = new StorageSharedKeyCredential(STORAGE_ACCOUNT_NAME, STORAGE_ACCOUNT_KEY);

        // Create blob service client
        const blobServiceClient = new BlobServiceClient(
          `https://${STORAGE_ACCOUNT_NAME}.blob.core.windows.net`,
          sharedKeyCredential
        );

        // Get container client
        const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);

        // Ensure container exists
        try {
          await containerClient.createIfNotExists();
        } catch (error) {
          context.log('Container creation/check failed:', error);
          // Continue if container already exists
        }

        // Upload file to blob storage
        const blockBlobClient = containerClient.getBlockBlobClient(blobName);

        // Set blob metadata
        const metadata = {
          userId: userId,
          originalName: file.name,
          uploadDate: new Date().toISOString(),
          requestId: requestId || '',
          fileSize: file.size.toString(),
          mimeType: file.type
        };

        // Upload with metadata and content type
        await blockBlobClient.upload(buffer, buffer.length, {
          blobHTTPHeaders: {
            blobContentType: file.type,
            blobContentDisposition: `inline; filename="${file.name}"`
          },
          metadata: metadata,
          tags: {
            userId: userId,
            category: 'change-request',
            uploadDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format for tags
          }
        });

        // Use blob URL if upload successful (but keep data URL as fallback)
        const blobUrl = blockBlobClient.url;
        context.log(`Image uploaded to blob storage: ${blobUrl}`);
        
      } catch (error) {
        context.log('Blob storage upload failed, using data URL:', error);
        uploadSuccess = false;
      }
    } else {
      context.log('Azure Storage not configured, using data URL for preview');
      uploadSuccess = false;
    }

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        message: uploadSuccess ? 'Image uploaded successfully' : 'Image processed for preview (storage not available)',
        imageUrl: imageUrl, // Return data URL for immediate preview
        dataUrl: dataUrl, // Explicit data URL field
        blobName: uploadSuccess ? blobName : undefined
      } as UploadResponse
    });

  } catch (error) {
    context.log('Error uploading image:', error);
    
    return addCorsHeaders({
      status: 500,
      jsonBody: {
        success: false,
        message: 'Internal server error while uploading image',
        error: error instanceof Error ? error.message : 'Unknown error'
      } as UploadResponse
    });
  }
}

app.http('UploadImage', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  handler: UploadImage
}); 