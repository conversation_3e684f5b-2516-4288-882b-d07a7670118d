{"version": 3, "file": "CreatePortalUser.js", "sourceRoot": "", "sources": ["../../src/functions/CreatePortalUser.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAyF;AACzF,6BAAwB;AACxB,qCAAuC;AACvC,kDAAwB;AAExB,qDAAqD;AACrD,MAAM,sBAAsB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAChD,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,sCAAsC,CAAC;IACzE,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;CACtE,CAAC,CAAC;AAKI,KAAK,UAAU,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;IACxF,OAAO,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;IAEzF,2CAA2C;IAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE;QACxC,OAAO,CAAC,IAAI,CAAC,+FAA+F,CAAC,CAAC;KACjH;SAAM;QACH,OAAO,CAAC,KAAK,CAAC,+EAA+E,CAAC,CAAC;QAC/F,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,gDAAgD,EAAE,CAAC;KAClF;IAED,IAAI,WAAwC,CAAC;IAE7C,kCAAkC;IAClC,IAAI;QACA,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACrC,WAAW,GAAG,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC,sDAAsD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;KACrG;IAAC,OAAO,KAAK,EAAE;QACZ,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,oDAAoD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAClG,OAAO;gBACJ,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,gFAAgF;oBACzF,MAAM,EAAE,KAAK,CAAC,MAAM;iBACvB;aACJ,CAAC;SACL;aAAM,IAAI,KAAK,YAAY,KAAK,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,8DAA8D,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5F,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;SAC1G;aAAM;YACF,OAAO,CAAC,KAAK,CAAC,yDAAyD,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACzF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;SAClF;KACJ;IAED,6BAA6B;IAC7B,oFAAoF;IACpF,iGAAiG;IACjG,MAAM,kBAAkB,GAAG,eAAe,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,8BAA8B;IACjI,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,MAAM,oBAAoB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,uCAAuC;IAC9G,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,4CAA4C;IAC7G,MAAM,mBAAmB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,gCAAgC;IAC/E,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC,yCAAyC;IACzE,MAAM,sBAAsB,GAAG,CAAC,CAAC,CAAC,mDAAmD;IACrF,MAAM,mBAAmB,GAAG,sCAAsC,CAAC,CAAC,wBAAwB;IAC5F,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,4BAA4B;IAC9E,OAAO,CAAC,IAAI,CAAC,uEAAuE,kBAAkB,gBAAgB,mBAAmB,iBAAiB,oBAAoB,gBAAgB,mBAAmB,iBAAiB,oBAAoB,mBAAmB,sBAAsB,gBAAgB,mBAAmB,wBAAwB,CAAC,CAAC;IAC5V,gCAAgC;IAEhC,IAAI,WAAW,GAA2B,IAAI,CAAC;IAC/C,IAAI;QACA,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;QAE7B,kDAAkD;QAClD,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;QACpC,MAAM,SAAS,GAAG,yDAAyD,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;QAC/I,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACnC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;YAC9B,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,eAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEtD,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,OAAO,CAAC,KAAK,CAAC,4DAA4D,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,oDAAoD,EAAE,EAAE,CAAC;SACvG;QAED,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;YAClD,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,KAAK,CAAC,wDAAwD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,uBAAuB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;SACnG;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACxD,OAAO,CAAC,IAAI,CAAC,iDAAiD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEvH,+CAA+C;QAC/C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;aACnC,KAAK,CAAC,OAAO,EAAE,eAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;aAC/C,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAE5D,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,iDAAiD,WAAW,CAAC,KAAK,kBAAkB,CAAC,CAAC;YACnG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,EAAE,CAAC;SACzF;QAED,oBAAoB;QACpB,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAEnE,2DAA2D;QAC3D,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE;aAC/C,KAAK,CAAC,SAAS,EAAE,eAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC;aACjD,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAG,0BAA0B;aACjF,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC,kBAAkB;aACzE,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAG,iBAAiB;aACxE,KAAK,CAAC,OAAO,EAAE,eAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC;aAC/C,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,oBAAoB,CAAC;aACjD,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAG,iBAAiB;aACvE,KAAK,CAAC,UAAU,EAAE,eAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;aACpC,KAAK,CAAC,eAAe,EAAE,eAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;aAC1C,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC,kBAAkB;aACtE,KAAK,CAAC;;;;aAIN,CAAC,CAAC;QAEP,IAAI,gBAAgB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YAClF,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SACpE;QACD,MAAM,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,4DAA4D,SAAS,EAAE,CAAC,CAAC;QAEtF,4CAA4C;QAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,MAAM,WAAW,CAAC,OAAO,EAAE;iBACtB,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,GAAG,EAAE,SAAS,CAAC;iBACnC,KAAK,CAAC,QAAQ,EAAE,eAAG,CAAC,GAAG,EAAE,MAAM,CAAC;iBAChC,KAAK,CAAC,WAAW,EAAE,eAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC;iBACnD,KAAK,CAAC,0FAA0F,CAAC,CAAC;SAC1G;QACD,OAAO,CAAC,IAAI,CAAC,0CAA0C,OAAO,CAAC,MAAM,oBAAoB,CAAC,CAAC;QAE3F,qBAAqB;QACrB,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;QAElF,+BAA+B;QAC/B,MAAM,YAAY,GAAG;YACjB,OAAO,EAAE,4BAA4B;YACrC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,kBAAkB;YAC3B,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,WAAW,CAAC,MAAM;SAC7B,CAAC;QAEF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;KAElD;IAAC,OAAO,KAAK,EAAE;QACZ,uBAAuB;QACvB,IAAI,WAAW,EAAE;YACb,IAAI;gBACA,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;aACvF;YAAC,OAAO,aAAa,EAAE;gBACnB,MAAM,UAAU,GAAG,CAAC,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBACrG,OAAO,CAAC,KAAK,CAAC,kEAAkE,UAAU,EAAE,CAAC,CAAC;aACjG;SACJ;QAED,wBAAwB;QACxB,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9E,OAAO,CAAC,KAAK,CAAC,8DAA8D,YAAY,EAAE,CAAC,CAAC;QAE5F,IAAI,KAAK,YAAY,eAAG,CAAC,YAAY,IAAI,KAAK,YAAY,eAAG,CAAC,gBAAgB,EAAE;YAC3E,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;gBAC3I,OAAO,CAAC,KAAK,CAAC,iEAAiE,YAAY,EAAE,CAAC,CAAC;gBAC/F,MAAM,aAAa,GAAG,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBAC9C,CAAC,CAAC,sCAAsC;oBACxC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC;wBAC7C,CAAC,CAAC,2CAA2C;wBAC7C,CAAC,CAAC,gCAAgC,CAAC;gBACtD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;aACtF;YACF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+CAA+C,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;SACxH;aAAM;YACH,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,uDAAuD,EAAE,MAAM,EAAE,YAAY,EAAE;aACvG,CAAC;SACL;KACJ;AACL,CAAC;AApLD,sDAoLC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE;IAC9B,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,KAAK,EAAE,cAAc;IACrB,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,qBAAqB;CACjC,CAAC,CAAC"}