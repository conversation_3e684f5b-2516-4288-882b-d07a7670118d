<svg viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1000" height="600" fill="#f8f9fa" />
  
  <!-- Title -->
  <text x="500" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle">Falcon Portal API Integration Flows</text>
  
  <!-- FreshService Integration Flow -->
  <text x="500" y="80" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle">FreshService Integration</text>
  
  <!-- Components -->
  <rect x="50" y="120" width="160" height="60" rx="5" fill="#b3e0ff" stroke="#0066cc" stroke-width="2" />
  <text x="130" y="155" font-family="Arial" font-size="14" text-anchor="middle">Falcon Portal UI</text>

  <rect x="290" y="120" width="160" height="60" rx="5" fill="#ffcc99" stroke="#ff8000" stroke-width="2" />
  <text x="370" y="155" font-family="Arial" font-size="14" text-anchor="middle">API Gateway</text>

  <rect x="530" y="120" width="160" height="60" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="610" y="155" font-family="Arial" font-size="14" text-anchor="middle">Integration Service</text>

  <rect x="770" y="120" width="160" height="60" rx="5" fill="#ffe6cc" stroke="#ff9933" stroke-width="2" />
  <text x="850" y="155" font-family="Arial" font-size="14" text-anchor="middle">FreshService API</text>

  <!-- Arrows and Flow -->
  <line x1="210" y1="150" x2="290" y2="150" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="450" y1="150" x2="530" y2="150" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="690" y1="150" x2="770" y2="150" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />

  <!-- API Call Labels -->
  <text x="250" y="135" font-family="Arial" font-size="12" text-anchor="middle">1. Create Ticket</text>
  <text x="490" y="135" font-family="Arial" font-size="12" text-anchor="middle">2. Forward Request</text>
  <text x="730" y="135" font-family="Arial" font-size="12" text-anchor="middle">3. API Call</text>

  <!-- Return Flow -->
  <line x1="770" y1="170" x2="690" y2="170" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <line x1="530" y1="170" x2="450" y2="170" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <line x1="290" y1="170" x2="210" y2="170" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />

  <text x="730" y="185" font-family="Arial" font-size="12" text-anchor="middle">4. Ticket Data</text>
  <text x="490" y="185" font-family="Arial" font-size="12" text-anchor="middle">5. Format Response</text>
  <text x="250" y="185" font-family="Arial" font-size="12" text-anchor="middle">6. Display Ticket</text>

  <!-- SSO Flow -->
  <path d="M 130,180 Q 130,230 360,230 Q 590,230 850,180" fill="none" stroke="#339933" stroke-width="2" stroke-dasharray="10,3" />
  <text x="480" y="245" font-family="Arial" font-size="12" text-anchor="middle" fill="#339933">SSO Authentication Flow</text>

  <!-- PeopleStrong Integration Flow -->
  <text x="500" y="290" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle">PeopleStrong Integration</text>
  
  <!-- Components -->
  <rect x="50" y="330" width="160" height="60" rx="5" fill="#b3e0ff" stroke="#0066cc" stroke-width="2" />
  <text x="130" y="365" font-family="Arial" font-size="14" text-anchor="middle">Falcon Portal UI</text>

  <rect x="290" y="330" width="160" height="60" rx="5" fill="#ffcc99" stroke="#ff8000" stroke-width="2" />
  <text x="370" y="365" font-family="Arial" font-size="14" text-anchor="middle">API Gateway</text>

  <rect x="530" y="330" width="160" height="60" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="610" y="365" font-family="Arial" font-size="14" text-anchor="middle">Integration Service</text>

  <rect x="770" y="330" width="160" height="60" rx="5" fill="#ffe6cc" stroke="#ff9933" stroke-width="2" />
  <text x="850" y="365" font-family="Arial" font-size="14" text-anchor="middle">PeopleStrong API</text>

  <!-- Arrows and Flow -->
  <line x1="210" y1="360" x2="290" y2="360" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="450" y1="360" x2="530" y2="360" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="690" y1="360" x2="770" y2="360" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />

  <!-- API Call Labels -->
  <text x="250" y="345" font-family="Arial" font-size="12" text-anchor="middle">1. Request HR Data</text>
  <text x="490" y="345" font-family="Arial" font-size="12" text-anchor="middle">2. Forward Request</text>
  <text x="730" y="345" font-family="Arial" font-size="12" text-anchor="middle">3. API Call</text>

  <!-- Return Flow -->
  <line x1="770" y1="380" x2="690" y2="380" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <line x1="530" y1="380" x2="450" y2="380" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <line x1="290" y1="380" x2="210" y2="380" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />

  <text x="730" y="395" font-family="Arial" font-size="12" text-anchor="middle">4. HR Data</text>
  <text x="490" y="395" font-family="Arial" font-size="12" text-anchor="middle">5. Transform Data</text>
  <text x="250" y="395" font-family="Arial" font-size="12" text-anchor="middle">6. Display HR Info</text>

  <!-- Deep Linking -->
  <path d="M 130,390 C 130,420 250,420 440,420 S 750,420 850,390" fill="none" stroke="#9933cc" stroke-width="2" stroke-dasharray="10,3" />
  <text x="480" y="435" font-family="Arial" font-size="12" text-anchor="middle" fill="#9933cc">Deep Linking with Context Transfer</text>

  <!-- Authentication Flow Diagram -->
  <text x="500" y="480" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle">Authentication Flow</text>
  
  <rect x="50" y="520" width="130" height="50" rx="5" fill="#b3e0ff" stroke="#0066cc" stroke-width="2" />
  <text x="115" y="550" font-family="Arial" font-size="14" text-anchor="middle">User</text>

  <rect x="230" y="520" width="130" height="50" rx="5" fill="#b3e0ff" stroke="#0066cc" stroke-width="2" />
  <text x="295" y="550" font-family="Arial" font-size="14" text-anchor="middle">Falcon Portal</text>

  <rect x="410" y="520" width="130" height="50" rx="5" fill="#c2f0c2" stroke="#339933" stroke-width="2" />
  <text x="475" y="550" font-family="Arial" font-size="14" text-anchor="middle">Entra ID</text>

  <rect x="590" y="520" width="130" height="50" rx="5" fill="#ffcc99" stroke="#ff8000" stroke-width="2" />
  <text x="655" y="550" font-family="Arial" font-size="14" text-anchor="middle">API Gateway</text>

  <rect x="770" y="520" width="130" height="50" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="835" y="550" font-family="Arial" font-size="14" text-anchor="middle">API Services</text>

  <!-- Auth Flow Arrows -->
  <line x1="180" y1="545" x2="230" y2="545" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <text x="205" y="535" font-family="Arial" font-size="10" text-anchor="middle">1. Access</text>

  <line x1="360" y1="535" x2="410" y2="535" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <text x="385" y="525" font-family="Arial" font-size="10" text-anchor="middle">2. Redirect</text>

  <line x1="410" y1="555" x2="360" y2="555" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <text x="385" y="570" font-family="Arial" font-size="10" text-anchor="middle">3. JWT Token</text>

  <line x1="360" y1="545" x2="590" y2="545" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <text x="475" y="535" font-family="Arial" font-size="10" text-anchor="middle">4. API Request + Token</text>

  <line x1="720" y1="545" x2="770" y2="545" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <text x="745" y="535" font-family="Arial" font-size="10" text-anchor="middle">5. Validated Request</text>

  <line x1="770" y1="555" x2="720" y2="555" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <text x="745" y="570" font-family="Arial" font-size="10" text-anchor="middle">6. Response</text>

  <line x1="590" y1="555" x2="360" y2="555" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" stroke-dasharray="5,5" />
  <text x="475" y="570" font-family="Arial" font-size="10" text-anchor="middle">7. Formatted Response</text>

  <!-- Arrows definition -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#666666" />
    </marker>
  </defs>
</svg>