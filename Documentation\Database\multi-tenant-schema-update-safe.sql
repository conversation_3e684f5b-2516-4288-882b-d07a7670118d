-- Safe Multi-Tenant Identity Management Database Schema Update
-- Purpose: Add TenantID column to Users table for proper multi-tenant user identification
-- Date: January 2025
-- Author: Falcon Portal Development Team
-- Note: Safe version that checks for existing columns and indexes

-- Step 1: Add TenantID column to Users table (only if it doesn't exist)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
BEGIN
    ALTER TABLE [dbo].[Users] 
    ADD [TenantID] NVARCHAR(100) NULL;
    PRINT 'TenantID column added to Users table';
END
ELSE
BEGIN
    PRINT 'TenantID column already exists in Users table - skipping';
END
GO

-- Step 2: Update existing users with a default TenantID (Avirata tenant)
-- This ensures existing users continue to work without issues
UPDATE [dbo].[Users] 
SET [TenantID] = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' 
WHERE [TenantID] IS NULL;

DECLARE @UpdatedRows INT = @@ROWCOUNT;
PRINT 'Updated ' + CAST(@UpdatedRows AS NVARCHAR(10)) + ' users with default TenantID';
GO

-- Step 3: Make TenantID NOT NULL after setting default values (only if it's currently nullable)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID' AND IS_NULLABLE = 'YES')
BEGIN
    ALTER TABLE [dbo].[Users] 
    ALTER COLUMN [TenantID] NVARCHAR(100) NOT NULL;
    PRINT 'TenantID column set to NOT NULL';
END
ELSE
BEGIN
    PRINT 'TenantID column is already NOT NULL - skipping';
END
GO

-- Step 4: Create composite unique index on EntraID + TenantID (only if it doesn't exist)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_EntraID_TenantID] 
    ON [dbo].[Users] ([EntraID], [TenantID]) 
    WHERE [EntraID] IS NOT NULL;
    PRINT 'Composite unique index IX_Users_EntraID_TenantID created';
END
ELSE
BEGIN
    PRINT 'Composite unique index IX_Users_EntraID_TenantID already exists - skipping';
END
GO

-- Step 5: Add index on TenantID for better query performance (only if it doesn't exist)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Users_TenantID] 
    ON [dbo].[Users] ([TenantID]) 
    INCLUDE ([UserID], [Email], [CompanyID], [IsActive]);
    PRINT 'Performance index IX_Users_TenantID created';
END
ELSE
BEGIN
    PRINT 'Performance index IX_Users_TenantID already exists - skipping';
END
GO

-- Step 6: Update the EntraID unique constraint to be composite
-- First, drop the existing unique constraint if it exists
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    DROP INDEX [IX_Users_EntraID] ON [dbo].[Users];
    PRINT 'Dropped old IX_Users_EntraID index';
END
ELSE
BEGIN
    PRINT 'Old IX_Users_EntraID index does not exist - skipping';
END
GO

-- Step 7: Add comment to document the multi-tenant approach (only if not already exists)
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('dbo.Users') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Users') AND name = 'TenantID') AND name = 'MS_Description')
BEGIN
    EXEC sp_addextendedproperty 
        @name = N'MS_Description', 
        @value = N'Azure Entra ID Tenant ID for multi-tenant user identification. Forms composite key with EntraID.', 
        @level0type = N'Schema', @level0name = 'dbo', 
        @level1type = N'Table', @level1name = 'Users', 
        @level2type = N'Column', @level2name = 'TenantID';
    PRINT 'Added description to TenantID column';
END
ELSE
BEGIN
    PRINT 'TenantID column description already exists - skipping';
END
GO

-- Step 8: Verify the schema update
PRINT 'Current Users table schema:';
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
    AND COLUMN_NAME IN ('EntraID', 'TenantID', 'Email', 'CompanyID')
ORDER BY COLUMN_NAME;

-- Step 9: Display current indexes on Users table
PRINT 'Current indexes on Users table:';
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    STRING_AGG(c.name, ', ') AS Columns
FROM sys.indexes i
JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.Users')
    AND i.name IS NOT NULL
GROUP BY i.name, i.type_desc, i.is_unique
ORDER BY i.name;

-- Step 10: Show user statistics
PRINT 'User statistics:';
SELECT 
    COUNT(*) as TotalUsers,
    COUNT(TenantID) as UsersWithTenantID,
    COUNT(DISTINCT TenantID) as UniqueTenantIDs
FROM dbo.Users;

PRINT 'Multi-tenant database schema update completed successfully!';
PRINT 'Next steps:';
PRINT '1. Update GetCurrentUser API to use composite key (EntraID, TenantID)';
PRINT '2. Update userManagementService queries to include TenantID';
PRINT '3. Test with users from different tenants'; 