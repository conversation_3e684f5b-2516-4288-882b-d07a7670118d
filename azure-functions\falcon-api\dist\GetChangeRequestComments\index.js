"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChangeRequestComments = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
async function getChangeRequestComments(request, context) {
    context.log('GetChangeRequestComments function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        // First, verify the change request exists
        const checkQuery = `
            SELECT RequestID 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        // Get all comments for the request
        const query = `
            SELECT 
                c.CommentID as commentId,
                c.RequestID as requestId,
                c.CommentText as commentText,
                c.CommentType as commentType,
                c.IsInternal as isInternal,
                c.ParentCommentID as parentCommentId,
                c.CreatedBy as createdBy,
                CONCAT(u.FirstName, ' ', u.LastName) as createdByName,
                c.CreatedDate as createdDate,
                c.ModifiedBy as modifiedBy,
                c.ModifiedDate as modifiedDate,
                c.IsEdited as isEdited
            FROM ChangeRequestComments c
                LEFT JOIN Users u ON c.CreatedBy = u.UserID
            WHERE c.RequestID = @requestId
            ORDER BY c.CreatedDate ASC
        `;
        const params = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const result = await (0, db_1.executeQuery)(query, params);
        context.log(`Retrieved ${result.recordset.length} comments for change request ${requestId}`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                data: result.recordset
            }
        };
    }
    catch (error) {
        context.error('Error in GetChangeRequestComments:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while retrieving comments',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.getChangeRequestComments = getChangeRequestComments;
functions_1.app.http('GetChangeRequestComments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/comments',
    handler: getChangeRequestComments
});
//# sourceMappingURL=index.js.map