import React from 'react';
import DashboardSection from './DashboardSection';

interface MainContentProps {
  activeSection: string;
}

// Placeholder components for different sections
// const DashboardSection: React.FC = () => <div>Dashboard Content Placeholder</div>; // Remove old placeholder
const KnowledgeSection: React.FC = () => <div>Knowledge Hub Content Placeholder</div>;
const ITSection: React.FC = () => <div>IT Hub Content Placeholder</div>;
const HRSection: React.FC = () => <div>HR Hub Content Placeholder</div>;
const AdminSection: React.FC = () => <div>Admin Hub Content Placeholder</div>;
const CommunicationSection: React.FC = () => <div>Communication Hub Content Placeholder</div>;

const MainContent: React.FC<MainContentProps> = ({ activeSection }) => {

  const renderSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <DashboardSection />;
      case 'knowledge':
        return <KnowledgeSection />;
      case 'it':
        return <ITSection />;
      case 'hr':
        return <HRSection />;
      case 'admin':
        return <AdminSection />;
      case 'communication':
        return <CommunicationSection />;
      default:
        return <DashboardSection />; // Default to dashboard
    }
  };

  return (
    <main className="flex-1 overflow-y-auto p-6 bg-gray-100">
      {/* Render active section component here based on state/routing */}
      {renderSection()}
    </main>
  );
};

export default MainContent;