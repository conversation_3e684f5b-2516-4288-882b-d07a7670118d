{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/MonitorUnauthorizedAdminAccess/index.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,kDAAwB;AAExB,sEAAsE;AACtE,MAAM,sBAAsB,GAAG;IAC3B,8BAA8B;IAC9B,sBAAsB;CACzB,CAAC;AAWF;;;GAGG;AACI,KAAK,UAAU,8BAA8B,CAChD,OAAoB,EACpB,OAA0B;IAE1B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAEhE,IAAI;QACA,yEAAyE;QACzE,MAAM,KAAK,GAAG;;;;;;;;;;;;;;;;;SAiBb,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;YACtE,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,MAAM,EAAE,SAAS;oBACjB,OAAO,EAAE,uCAAuC;oBAChD,iBAAiB,EAAE,CAAC;oBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACtC;aACJ,CAAC;SACL;QAED,mEAAmE;QACnE,MAAM,kBAAkB,GAA6B,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9E,SAAS,EAAE,GAAG,CAAC,KAAK;YACpB,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,YAAY,EAAE,GAAG,CAAC,YAAY;YAC9B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,WAAW,EAAE,GAAG,CAAC,WAAW;SAC/B,CAAC,CAAC,CAAC;QAEJ,qBAAqB;QACrB,eAAM,CAAC,IAAI,CAAC,sBAAsB,kBAAkB,CAAC,MAAM,wDAAwD,CAAC,CAAC;QAErH,KAAK,MAAM,KAAK,IAAI,kBAAkB,EAAE;YACpC,eAAM,CAAC,IAAI,CAAC,uBAAuB,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,QAAQ,QAAQ,KAAK,CAAC,WAAW,iBAAiB,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;SACxI;QAED,2CAA2C;QAC3C,MAAM,sBAAsB,GAAG;;;;;;;;;;;sCAWD,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;SAC1F,CAAC;QAEF,MAAM,gBAAgB,GAAqB,kBAAkB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7E,IAAI,EAAE,aAAa,CAAC,EAAE;YACtB,IAAI,EAAE,eAAG,CAAC,GAAG;YACb,KAAK,EAAE,KAAK,CAAC,UAAU;SAC1B,CAAC,CAAC,CAAC;QAEJ,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;QAEvF,kEAAkE;QAClE,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,MAAM,CAAC;QAEpE,IAAI,aAAa,EAAE;YACf,eAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;YAEtF,MAAM,uBAAuB,GAAG;;;;;;;aAO/B,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,IAAA,iBAAY,EAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YACrE,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAEzD,eAAM,CAAC,IAAI,CAAC,uCAAuC,YAAY,8CAA8C,CAAC,CAAC;YAE/G,2CAA2C;YAC3C,MAAM,uBAAuB,GAAG;;;;;;;oCAOR,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;;;;;;;aAO/E,CAAC;YAEF,MAAM,cAAc,GAAqB,kBAAkB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC3E,IAAI,EAAE,QAAQ,CAAC,EAAE;gBACjB,IAAI,EAAE,eAAG,CAAC,QAAQ;gBAClB,KAAK,EAAE,KAAK,CAAC,SAAS;aACzB,CAAC,CAAC,CAAC;YAEJ,MAAM,cAAc,GAAG,MAAM,IAAA,iBAAY,EAAC,uBAAuB,EAAE,cAAc,CAAC,CAAC;YACnF,MAAM,aAAa,GAAG,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAE5D,eAAM,CAAC,IAAI,CAAC,6BAA6B,aAAa,QAAQ,CAAC,CAAC;SACnE;QAED,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO;gBAC9C,OAAO,EAAE,SAAS,kBAAkB,CAAC,MAAM,8CAA8C;gBACzF,iBAAiB,EAAE,kBAAkB,CAAC,MAAM;gBAC5C,kBAAkB,EAAE,kBAAkB;gBACtC,iBAAiB,EAAE,iBAAiB,CAAC,SAAS;gBAC9C,cAAc,EAAE,aAAa;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,6BAA6B;aAC3F;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QAChE,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,6CAA6C;gBACtD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC;SACJ,CAAC;KACL;AACL,CAAC;AA7JD,wEA6JC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,gCAAgC,EAAE;IACvC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACxB,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,8BAA8B;CAC1C,CAAC,CAAC"}