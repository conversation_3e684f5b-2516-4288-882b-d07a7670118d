import React from 'react';
import { X } from 'feather-icons-react';

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    children: React.ReactNode;
    // Optional footer for buttons
    footer?: React.ReactNode; 
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, footer }) => {
    if (!isOpen) return null;

    return (
        <div 
            className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
            onClick={onClose} // Close on overlay click
        >
            <div 
                className="bg-white rounded-lg shadow-xl w-full max-w-md m-4 relative"
                onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside modal content
            >
                {/* Header */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
                    <button 
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <X size={20} />
                    </button>
                </div>

                {/* Body */}
                <div className="p-4">
                    {children}
                </div>

                {/* Footer (Optional) */}
                {footer && (
                    <div className="flex justify-end p-4 border-t border-gray-200 space-x-2">
                        {footer}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Modal; 