import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Users, Shield, UserCheck, Plus, Settings, BarChart } from 'feather-icons-react';
import { useMsal } from '@azure/msal-react';
import { 
  fetchPortalUsers, 
  fetchRoleDefinitions, 
  setMsalContext,
  type PortalUser, 
  type RoleDefinition 
} from '../../services/adminApi';

interface AdminStats {
  totalUsers: number;
  totalRoles: number;
  activeSessions: number;
  activeUsers: number;
  inactiveUsers: number;
  administrators: number;
  systemRoles: number;
  customRoles: number;
  lastRoleUpdate: string;
}

interface RecentActivity {
  id: string;
  type: 'user' | 'role' | 'assignment';
  title: string;
  description: string;
  timestamp: string;
}

const PortalAdminPage: React.FC = () => {
  const { instance, accounts } = useMsal();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set MSAL context for API calls
    setMsalContext(instance, accounts);
    fetchAdminData();
  }, [instance, accounts]);

  const fetchAdminData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all users data for statistics using authenticated API
      const usersData = await fetchPortalUsers(undefined, undefined, undefined, 'All', 1, 1000);
      const users = usersData.users;
      const totalUsersCount = usersData.totalCount;

      // Fetch roles data using authenticated API
      const roles = await fetchRoleDefinitions();

      // Calculate statistics from real data
      const activeUsers = users.filter((user: PortalUser) => user.status === 'Active').length;
      const inactiveUsers = users.filter((user: PortalUser) => user.status === 'Inactive').length;
      const administrators = users.filter((user: PortalUser) => 
        user.roles && user.roles.some((role: string) => role === 'Administrator' || role === 'Super Admin')
      ).length;

      // Categorize roles (system vs custom)
      const systemRoleNames = ['Administrator', 'Super Admin', 'Employee'];
      const systemRoles = roles.filter((role: RoleDefinition) => systemRoleNames.includes(role.name)).length;
      const customRoles = roles.length - systemRoles;

      const adminStats: AdminStats = {
        totalUsers: totalUsersCount,
        totalRoles: roles.length,
        activeSessions: activeUsers, // Approximation - active users as sessions
        activeUsers: activeUsers,
        inactiveUsers: inactiveUsers,
        administrators: administrators,
        systemRoles: systemRoles,
        customRoles: customRoles,
        lastRoleUpdate: new Date().toLocaleDateString() // Placeholder - would need audit log
      };

      setStats(adminStats);

      // Generate recent activity from real data (placeholder implementation)
      const activity: RecentActivity[] = [
        {
          id: '1',
          type: 'user',
          title: 'Live data loaded',
          description: `${totalUsersCount} users available in the system`,
          timestamp: 'Just now'
        },
        {
          id: '2',
          type: 'role',
          title: 'Roles synchronized',
          description: `${roles.length} roles loaded from database`,
          timestamp: 'Just now'
        }
      ];

      setRecentActivity(activity);
    } catch (err) {
      console.error('Error fetching admin data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load admin data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Portal Administration</h1>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Portal Administration</h1>
          <p className="text-red-600">Error: {error}</p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">
            Failed to load admin data. Please ensure the backend API is running and accessible.
          </p>
          <button 
            onClick={fetchAdminData}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Portal Administration</h1>
        <p className="text-gray-600">Manage users, roles, and system settings for Falcon Hub</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Users size={24} className="text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Total Users</h3>
              <p className="text-3xl font-bold text-blue-600">{stats?.totalUsers || 0}</p>
              <p className="text-sm text-gray-500">Active portal users</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-lg">
              <Shield size={24} className="text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Total Roles</h3>
              <p className="text-3xl font-bold text-green-600">{stats?.totalRoles || 0}</p>
              <p className="text-sm text-gray-500">Defined user roles</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-lg">
              <UserCheck size={24} className="text-purple-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Active Sessions</h3>
              <p className="text-3xl font-bold text-purple-600">{stats?.activeSessions || 0}</p>
              <p className="text-sm text-gray-500">Current user sessions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Management */}
        <Link to="/portal-admin/user-management" className="group">
          <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="bg-blue-100 p-3 rounded-lg group-hover:bg-blue-200 transition-colors">
                <Users size={24} className="text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                  User Management
                </h3>
                <p className="text-sm text-gray-600">Manage portal users, roles, and permissions</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Active Users</span>
                <span className="font-medium text-green-600">{stats?.activeUsers || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Inactive Users</span>
                <span className="font-medium text-orange-600">{stats?.inactiveUsers || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Administrators</span>
                <span className="font-medium text-blue-600">{stats?.administrators || 0}</span>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Manage users</span>
                <span className="text-sm text-blue-600 group-hover:underline">View all →</span>
              </div>
            </div>
          </div>
        </Link>

        {/* Role Management */}
        <Link to="/portal-admin/role-management" className="group">
          <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="bg-green-100 p-3 rounded-lg group-hover:bg-green-200 transition-colors">
                <Shield size={24} className="text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                  Role Management
                </h3>
                <p className="text-sm text-gray-600">Define and manage user roles and permissions</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">System Roles</span>
                <span className="font-medium text-gray-900">{stats?.systemRoles || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Custom Roles</span>
                <span className="font-medium text-gray-900">{stats?.customRoles || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Last Updated</span>
                <span className="font-medium text-gray-900">{stats?.lastRoleUpdate || 'N/A'}</span>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Manage roles</span>
                <span className="text-sm text-green-600 group-hover:underline">View all →</span>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link 
            to="/portal-admin/add-user" 
            className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="bg-blue-100 p-2 rounded group-hover:bg-blue-200 transition-colors">
              <Plus size={18} className="text-blue-600" />
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
              Add New User
            </span>
          </Link>

          <Link 
            to="/portal-admin/role-management" 
            className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="bg-green-100 p-2 rounded group-hover:bg-green-200 transition-colors">
              <UserCheck size={18} className="text-green-600" />
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors">
              Create Role
            </span>
          </Link>

          <Link 
            to="/it/setup" 
            className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="bg-purple-100 p-2 rounded group-hover:bg-purple-200 transition-colors">
              <Settings size={18} className="text-purple-600" />
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-purple-600 transition-colors">
              System Settings
            </span>
          </Link>

          <button 
            onClick={fetchAdminData}
            className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <div className="bg-orange-100 p-2 rounded group-hover:bg-orange-200 transition-colors">
              <BarChart size={18} className="text-orange-600" />
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-orange-600 transition-colors">
              Refresh Data
            </span>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {recentActivity.length > 0 ? (
            recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                <div className={`p-2 rounded ${
                  activity.type === 'user' ? 'bg-blue-100' :
                  activity.type === 'role' ? 'bg-green-100' : 'bg-purple-100'
                }`}>
                  {activity.type === 'user' && <Users size={16} className="text-blue-600" />}
                  {activity.type === 'role' && <Shield size={16} className="text-green-600" />}
                  {activity.type === 'assignment' && <UserCheck size={16} className="text-purple-600" />}
                </div>
                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <p className="text-xs text-gray-500">{activity.description}</p>
                </div>
                <span className="text-xs text-gray-500">{activity.timestamp}</span>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No recent activity to display</p>
            </div>
          )}
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-100">
          <Link 
            to="/portal-admin/user-management" 
            className="text-sm text-blue-600 hover:underline"
          >
            View all activity →
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PortalAdminPage; 