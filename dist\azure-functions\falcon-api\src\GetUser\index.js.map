{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/GetUser/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAqBA,0BA8GC;AAnID,gDAAyF;AACzF,mDAAgD;AAChD,6BAA6B,CAAC,mBAAmB;AACjD,qCAA4D,CAAC,gEAAgE;AAC7H,mEAAuF;AACvF,mDAAyD,CAAC,+BAA+B;AAgBzF,SAAsB,OAAO,CAAC,OAAoB,EAAE,OAA0B;;QAC1E,OAAO,CAAC,GAAG,CAAC,oDAAoD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAChF,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEzC,gCAAgC;QAChC,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,6DAA6D;YAC7D,+EAA+E;YAC/E,+EAA+E;YAC/E,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBAC9C,eAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;gBACrF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;YAC9F,CAAC;iBAAM,CAAC;gBACH,yEAAyE;gBACzE,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,CAAC;YACnE,CAAC;QACL,CAAC;QACD,+EAA+E;QAC/E,oBAAoB;QAEpB,4BAA4B;QAC5B,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,yBAAyB;QAClF,yCAAyC;QACzC,MAAM,eAAe,GAAG,IAAA,mCAAe,EAAC,2CAAuB,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC3G,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAE5C,wCAAwC;QACxC,MAAM,oBAAoB,GAAG,2CAAuB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8BAA8B;QACvG,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC,mBAAmB;QAEjE,4BAA4B;QAC5B,0BAA0B;QAE1B,IAAI,CAAC;YACD,sFAAsF;YACtF,MAAM,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;SAqBb,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC;YAC/D,MAAM,UAAU,GAAqB;gBACjC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;aAC1D,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAErD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,yCAAyC,OAAO,EAAE,CAAC,CAAC;gBAChE,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,qBAAqB,OAAO,aAAa,EAAE;iBACnE,CAAC;YACN,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEnC,gDAAgD;YAChD,MAAM,UAAU,GAAe;gBAC3B,EAAE,EAAE,MAAM,CAAC,MAAM;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,IAAI,EAAE,GAAG,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE;gBAC9C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,OAAO,EAAE,MAAM,CAAC,WAAW;gBAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,UAAU,EAAE,MAAM,CAAC,cAAc,IAAI,SAAS;gBAC9C,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1D,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;aAClD,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,mDAAmD,OAAO,aAAa,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC;YAErG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,UAAU;aACvB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,yCAAyC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACN,OAAO,EAAE,gCAAgC;oBACzC,KAAK,EAAE,YAAY;iBACtB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,SAAS,EAAE;IAChB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW,EAAE,oCAAoC;IAC5D,KAAK,EAAE,wBAAwB,EAAE,sBAAsB;IACvD,OAAO,EAAE,OAAO;CACnB,CAAC,CAAC"}