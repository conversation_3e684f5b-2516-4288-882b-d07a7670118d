import { EmailClient } from '@azure/communication-email';
import { logger } from '../utils/logger';
import { executeQuery, QueryParameter } from '../db';
import * as sql from 'mssql';

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent?: string;
}

export interface EmailRecipient {
  email: string;
  displayName?: string;
}

export interface EmailNotificationData {
  requestId: number;
  requestNumber: string;
  title: string;
  description: string;
  priority: string;
  status: string;
  requesterName: string;
  requesterEmail: string;
  assigneeName?: string;
  assigneeEmail?: string;
  companyName: string;
  comments?: string;
  approverName?: string;
  actionUrl: string;
  createdDate: Date;
  dueDate?: Date;
}

export class EmailService {
  private static instance: EmailService;
  private emailClient: EmailClient | null = null;
  private readonly fromEmail = '<EMAIL>';
  private readonly fromDisplayName = 'FalconHub Notifications';
  private readonly portalUrl = process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net';
  private readonly isDevelopment: boolean;

  private constructor() {
    // Check if we're in development mode
    this.isDevelopment = process.env.AZURE_FUNCTIONS_ENVIRONMENT === 'Development' || 
                        process.env.NODE_ENV === 'development' || 
                        !process.env.AZURE_FUNCTIONS_ENVIRONMENT;

    const connectionString = process.env.AZURE_COMMUNICATION_SERVICES_CONNECTION_STRING;
    if (connectionString) {
      try {
        this.emailClient = new EmailClient(connectionString);
        logger.info('EmailService: Successfully initialized with Azure Communication Services');
      } catch (error) {
        logger.error('EmailService: Failed to initialize with provided connection string', error);
        this.emailClient = null;
      }
    } else {
      if (this.isDevelopment) {
        logger.warn('EmailService: Azure Communication Services connection string not configured - running in development mode without email integration');
      } else {
        logger.error('EmailService: Azure Communication Services connection string not configured in production environment');
      }
    }
  }

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  private checkEmailAvailability(): boolean {
    if (!this.emailClient) {
      if (this.isDevelopment) {
        logger.warn('EmailService: Email service not available in development mode - Azure Communication Services not configured');
      } else {
        logger.error('EmailService: Email service not available - Azure Communication Services not configured');
      }
      return false;
    }
    return true;
  }

  /**
   * Sends an email using Azure Communication Services
   */
  public async sendEmail(
    recipients: EmailRecipient[],
    subject: string,
    htmlContent: string,
    textContent?: string
  ): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info(`EmailService.sendEmail: Would send email to ${recipients.length} recipients: ${subject} (Email service not configured in development)`);
        return true; // Return true in development to not break functionality
      } else {
        throw new Error('Email service is not configured');
      }
    }

    try {
      const emailMessage = {
        senderAddress: this.fromEmail,
        content: {
          subject: subject,
          html: htmlContent,
          plainText: textContent || this.htmlToText(htmlContent)
        },
        recipients: {
          to: recipients.map(recipient => ({
            address: recipient.email,
            displayName: recipient.displayName || recipient.email
          }))
        }
      };

      logger.info(`Sending email to ${recipients.length} recipients: ${subject}`);
      const response = await this.emailClient!.beginSend(emailMessage);
      const result = await response.pollUntilDone();

      if (result.status === 'Succeeded') {
        logger.info(`Email sent successfully. Operation ID: ${result.id}`);
        return true;
      } else {
        logger.error(`Email sending failed. Status: ${result.status}, Operation ID: ${result.id}`);
        return false;
      }
    } catch (error) {
      logger.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Sends change request submitted notification to Change Managers
   */
  public async sendChangeRequestSubmitted(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestSubmitted: Would send change request submitted notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients = await this.getChangeManagers();
    const template = this.getChangeRequestSubmittedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends change request approved notification to requestor and assignee
   */
  public async sendChangeRequestApproved(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestApproved: Would send change request approved notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];
    
    if (data.assigneeEmail && data.assigneeName) {
      recipients.push({ email: data.assigneeEmail, displayName: data.assigneeName });
    }

    const template = this.getChangeRequestApprovedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends change request rejected notification to requestor
   */
  public async sendChangeRequestRejected(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestRejected: Would send change request rejected notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];

    const template = this.getChangeRequestRejectedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends more information requested notification to requestor
   */
  public async sendChangeRequestInfoRequested(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestInfoRequested: Would send change request info requested notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];

    const template = this.getChangeRequestInfoRequestedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends change request assigned notification to assignee
   */
  public async sendChangeRequestAssigned(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestAssigned: Would send change request assigned notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    if (!data.assigneeEmail || !data.assigneeName) {
      logger.warn('Cannot send assignment notification: assignee email/name not provided');
      return false;
    }

    const recipients: EmailRecipient[] = [
      { email: data.assigneeEmail, displayName: data.assigneeName }
    ];

    const template = this.getChangeRequestAssignedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends comment added notification to relevant parties
   */
  public async sendChangeRequestCommentAdded(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestCommentAdded: Would send change request comment added notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];
    
    if (data.assigneeEmail && data.assigneeName) {
      recipients.push({ email: data.assigneeEmail, displayName: data.assigneeName });
    }

    const template = this.getChangeRequestCommentAddedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends status updated notification to requestor
   */
  public async sendChangeRequestStatusUpdated(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestStatusUpdated: Would send change request status updated notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];

    const template = this.getChangeRequestStatusUpdatedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends due date reminder notification to assignee
   */
  public async sendChangeRequestDueReminder(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestDueReminder: Would send change request due reminder notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    if (!data.assigneeEmail || !data.assigneeName) {
      logger.warn('Cannot send due reminder: assignee email/name not provided');
      return false;
    }

    const recipients: EmailRecipient[] = [
      { email: data.assigneeEmail, displayName: data.assigneeName }
    ];

    const template = this.getChangeRequestDueReminderTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Sends change request completed notification to requestor
   */
  public async sendChangeRequestCompleted(data: EmailNotificationData): Promise<boolean> {
    if (!this.checkEmailAvailability()) {
      if (this.isDevelopment) {
        logger.info('EmailService.sendChangeRequestCompleted: Would send change request completed notification (Email service not configured in development)');
        return true;
      } else {
        throw new Error('Email service is not configured');
      }
    }

    const recipients: EmailRecipient[] = [
      { email: data.requesterEmail, displayName: data.requesterName }
    ];

    const template = this.getChangeRequestCompletedTemplate(data);
    
    return this.sendEmail(
      recipients,
      template.subject,
      template.htmlContent,
      template.textContent
    );
  }

  /**
   * Gets Change Managers from the database
   */
  private async getChangeManagers(): Promise<EmailRecipient[]> {
    try {
      const query = `
        SELECT DISTINCT u.Email, u.FirstName, u.LastName
        FROM Users u
        INNER JOIN UserRoles ur ON u.UserID = ur.UserID
        INNER JOIN Roles r ON ur.RoleID = r.RoleID
        WHERE r.RoleName IN ('Change Manager', 'Administrator') 
        AND u.IsActive = 1 
        AND ur.IsActive = 1
        AND u.Email IS NOT NULL
      `;

      const result = await executeQuery(query, []);
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset.map(user => ({
          email: user.Email,
          displayName: `${user.FirstName} ${user.LastName}`.trim()
        }));
      } else {
        logger.warn('No Change Managers found in database, using default admin email');
        return [
          { email: '<EMAIL>', displayName: 'System Administrator' }
        ];
      }
    } catch (error) {
      logger.error('Error fetching Change Managers from database:', error);
      // Fallback to default admin email
      return [
        { email: '<EMAIL>', displayName: 'System Administrator' }
      ];
    }
  }

  /**
   * Converts HTML content to plain text
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }

  /**
   * Gets the base email template with FalconHub branding
   */
  private getBaseTemplate(title: string, content: string): string {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 20px auto;
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
            }
            .header .subtitle {
                margin: 5px 0 0 0;
                font-size: 14px;
                opacity: 0.9;
            }
            .content {
                padding: 30px 20px;
            }
            .change-request-card {
                background-color: #f8f9fa;
                border-left: 4px solid #667eea;
                padding: 20px;
                margin: 20px 0;
                border-radius: 0 4px 4px 0;
            }
            .priority-high { border-left-color: #dc3545; }
            .priority-medium { border-left-color: #ffc107; }
            .priority-low { border-left-color: #28a745; }
            .status-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
            }
            .status-submitted { background-color: #e3f2fd; color: #1976d2; }
            .status-approved { background-color: #e8f5e8; color: #2e7d32; }
            .status-rejected { background-color: #ffebee; color: #c62828; }
            .status-in-progress { background-color: #fff3e0; color: #ef6c00; }
            .status-completed { background-color: #e8f5e8; color: #2e7d32; }
            .action-button {
                display: inline-block;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-decoration: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: 600;
                margin: 20px 0;
                text-align: center;
            }
            .action-button:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
            .footer {
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #e9ecef;
            }
            .details-table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
            }
            .details-table td {
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }
            .details-table td:first-child {
                font-weight: 600;
                width: 30%;
                color: #495057;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔧 FalconHub</h1>
                <div class="subtitle">IT Change Management System</div>
            </div>
            <div class="content">
                ${content}
            </div>
            <div class="footer">
                <p>This is an automated notification from FalconHub. Please do not reply to this email.</p>
                <p>© ${new Date().getFullYear()} SASMOS Group. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Template methods for each notification type
  private getChangeRequestSubmittedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: New Request Submitted - Review Required`;
    
    const content = `
      <h2>📝 New Change Request Submitted</h2>
      <p>A new change request has been submitted and requires your review.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Submitted By:</td><td>${data.requesterName} (${data.companyName})</td></tr>
          <tr><td>Priority:</td><td><span class="priority-${data.priority.toLowerCase()}">${data.priority}</span></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-submitted">${data.status}</span></td></tr>
          <tr><td>Created:</td><td>${data.createdDate.toLocaleDateString()}</td></tr>
        </table>
        
        <div style="margin-top: 15px;">
          <strong>Description:</strong>
          <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px;">
            ${data.description}
          </div>
        </div>
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📋 Review Request</a>
      </div>
      
      <p>Please review this request and take appropriate action (Approve, Request More Information, or Reject).</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Change Request Submitted', content),
      textContent: `New Change Request: ${data.requestNumber}\nTitle: ${data.title}\nSubmitted by: ${data.requesterName}\nPriority: ${data.priority}\n\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestApprovedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: Approved - Ready for Implementation`;
    
    const content = `
      <h2>✅ Change Request Approved</h2>
      <p>Your change request has been approved and is ready for implementation.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-approved">${data.status}</span></td></tr>
          <tr><td>Approved By:</td><td>${data.approverName || 'System'}</td></tr>
          ${data.assigneeName ? `<tr><td>Assigned To:</td><td>${data.assigneeName}</td></tr>` : ''}
          ${data.dueDate ? `<tr><td>Due Date:</td><td>${data.dueDate.toLocaleDateString()}</td></tr>` : ''}
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>Approval Notes:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📋 View Request Details</a>
      </div>
      
      <p>The development team will begin working on your request. You will receive updates as progress is made.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Change Request Approved', content),
      textContent: `Change Request Approved: ${data.requestNumber}\nTitle: ${data.title}\nApproved by: ${data.approverName || 'System'}\n\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestRejectedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: Rejected`;
    
    const content = `
      <h2>❌ Change Request Rejected</h2>
      <p>Unfortunately, your change request has been rejected.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-rejected">${data.status}</span></td></tr>
          <tr><td>Rejected By:</td><td>${data.approverName || 'System'}</td></tr>
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>Rejection Reason:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px; border-left: 3px solid #dc3545;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📋 View Request Details</a>
      </div>
      
      <p>If you have questions about this decision, please contact the IT team. You may submit a new request if needed.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Change Request Rejected', content),
      textContent: `Change Request Rejected: ${data.requestNumber}\nTitle: ${data.title}\nRejected by: ${data.approverName || 'System'}\nReason: ${data.comments || 'No reason provided'}\n\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestInfoRequestedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: More Information Required - Action Needed`;
    
    const content = `
      <h2>🔍 More Information Required</h2>
      <p>Your change request requires additional information before it can be processed.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-submitted">Waiting for Clarification</span></td></tr>
          <tr><td>Requested By:</td><td>${data.approverName || 'System'}</td></tr>
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>Information Requested:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px; border-left: 3px solid #ffc107;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📝 Update Request</a>
      </div>
      
      <p><strong>Action Required:</strong> Please review the request and provide the additional information. Once updated, resubmit your request for review.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('More Information Required', content),
      textContent: `More Information Required: ${data.requestNumber}\nTitle: ${data.title}\nRequested by: ${data.approverName || 'System'}\nDetails: ${data.comments || 'Additional information needed'}\n\nUpdate request: ${data.actionUrl}`
    };
  }

  private getChangeRequestAssignedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: Assigned to You - ${data.title}`;
    
    const content = `
      <h2>👨‍💻 Change Request Assigned</h2>
      <p>A change request has been assigned to you for implementation.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Requested By:</td><td>${data.requesterName} (${data.companyName})</td></tr>
          <tr><td>Priority:</td><td><span class="priority-${data.priority.toLowerCase()}">${data.priority}</span></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-approved">${data.status}</span></td></tr>
          ${data.dueDate ? `<tr><td>Due Date:</td><td>${data.dueDate.toLocaleDateString()}</td></tr>` : ''}
        </table>
        
        <div style="margin-top: 15px;">
          <strong>Description:</strong>
          <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px;">
            ${data.description}
          </div>
        </div>
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">🚀 Start Working</a>
      </div>
      
      <p>Please review the requirements and begin implementation. Update the status as you make progress.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Change Request Assigned', content),
      textContent: `Change Request Assigned: ${data.requestNumber}\nTitle: ${data.title}\nRequestor: ${data.requesterName}\nPriority: ${data.priority}\n${data.dueDate ? `Due: ${data.dueDate.toLocaleDateString()}\n` : ''}\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestCommentAddedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: New Comment Added`;
    
    const content = `
      <h2>💬 New Comment Added</h2>
      <p>A new comment has been added to your change request.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-${data.status.toLowerCase().replace(' ', '-')}">${data.status}</span></td></tr>
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>New Comment:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px; border-left: 3px solid #667eea;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">💬 View Comments</a>
      </div>
      
      <p>Check the full conversation and respond if needed.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('New Comment Added', content),
      textContent: `New Comment on Change Request: ${data.requestNumber}\nTitle: ${data.title}\nComment: ${data.comments || 'New comment added'}\n\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestStatusUpdatedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: Status Updated to ${data.status}`;
    
    const content = `
      <h2>🔄 Status Updated</h2>
      <p>The status of your change request has been updated.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>New Status:</td><td><span class="status-badge status-${data.status.toLowerCase().replace(' ', '-')}">${data.status}</span></td></tr>
          ${data.assigneeName ? `<tr><td>Assigned To:</td><td>${data.assigneeName}</td></tr>` : ''}
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>Update Notes:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📋 View Progress</a>
      </div>
      
      <p>Your change request is progressing through the implementation process.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Status Updated', content),
      textContent: `Status Update for Change Request: ${data.requestNumber}\nTitle: ${data.title}\nNew Status: ${data.status}\n${data.comments ? `Notes: ${data.comments}\n` : ''}\nView details: ${data.actionUrl}`
    };
  }

  private getChangeRequestDueReminderTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `🚨 Change Request ${data.requestNumber}: Due Date Approaching`;
    
    const content = `
      <h2>⏰ Due Date Reminder</h2>
      <p>This change request is approaching its due date and needs attention.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Due Date:</td><td><strong style="color: #dc3545;">${data.dueDate?.toLocaleDateString()}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-${data.status.toLowerCase().replace(' ', '-')}">${data.status}</span></td></tr>
          <tr><td>Priority:</td><td><span class="priority-${data.priority.toLowerCase()}">${data.priority}</span></td></tr>
        </table>
        
        <div style="margin-top: 15px;">
          <strong>Description:</strong>
          <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px;">
            ${data.description}
          </div>
        </div>
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">🏃‍♂️ Update Progress</a>
      </div>
      
      <p><strong>Action Required:</strong> Please review the progress and update the status or request an extension if needed.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Due Date Reminder', content),
      textContent: `Due Date Reminder: ${data.requestNumber}\nTitle: ${data.title}\nDue: ${data.dueDate?.toLocaleDateString()}\nStatus: ${data.status}\n\nUpdate progress: ${data.actionUrl}`
    };
  }

  private getChangeRequestCompletedTemplate(data: EmailNotificationData): EmailTemplate {
    const subject = `Change Request ${data.requestNumber}: Completed Successfully ✅`;
    
    const content = `
      <h2>🎉 Change Request Completed</h2>
      <p>Great news! Your change request has been completed successfully.</p>
      
      <div class="change-request-card priority-${data.priority.toLowerCase()}">
        <h3>${data.title}</h3>
        <table class="details-table">
          <tr><td>Request ID:</td><td><strong>${data.requestNumber}</strong></td></tr>
          <tr><td>Status:</td><td><span class="status-badge status-completed">${data.status}</span></td></tr>
          <tr><td>Completed By:</td><td>${data.assigneeName || 'Development Team'}</td></tr>
        </table>
        
        ${data.comments ? `
          <div style="margin-top: 15px;">
            <strong>Completion Notes:</strong>
            <div style="margin-top: 5px; padding: 10px; background-color: #ffffff; border-radius: 4px; border-left: 3px solid #28a745;">
              ${data.comments}
            </div>
          </div>
        ` : ''}
      </div>
      
      <div style="text-align: center;">
        <a href="${data.actionUrl}" class="action-button">📋 View Final Details</a>
      </div>
      
      <p>Thank you for using FalconHub's Change Management system. If you have any questions or feedback, please don't hesitate to reach out to the IT team.</p>
    `;

    return {
      subject,
      htmlContent: this.getBaseTemplate('Change Request Completed', content),
      textContent: `Change Request Completed: ${data.requestNumber}\nTitle: ${data.title}\nCompleted by: ${data.assigneeName || 'Development Team'}\n${data.comments ? `Notes: ${data.comments}\n` : ''}\nView details: ${data.actionUrl}`
    };
  }
}