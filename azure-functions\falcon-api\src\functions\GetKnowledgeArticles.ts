import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

interface KnowledgeArticle {
    id: string;
    title: string;
    summary: string;
    content: string;
    category: {
        id: string;
        name: string;
    };
    company: string;
    isPublished: boolean;
    viewCount: number;
    rating: number;
    createdBy: {
        name: string;
        email: string;
    };
    createdDate: string;
    lastModified?: string;
    tags: string[];
}

export async function getKnowledgeArticles(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`GetKnowledgeArticles function invoked.`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = await getUserIdFromPrincipal(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }

        // Parse query parameters
        const url = new URL(request.url);
        const search = url.searchParams.get('search') || '';
        const categoryId = url.searchParams.get('categoryId');
        const companyId = url.searchParams.get('companyId');
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '20');
        const offset = (page - 1) * limit;

        // Build query with parameters
        let whereConditions = ['ka.IsActive = 1', 'ka.IsPublished = 1'];
        const queryParams: QueryParameter[] = [
            { name: 'offset', type: sql.Int, value: offset },
            { name: 'limit', type: sql.Int, value: limit }
        ];

        // Add search filter
        if (search) {
            whereConditions.push(`(ka.Title LIKE @search OR ka.Summary LIKE @search OR ka.Content LIKE @search OR EXISTS (
                SELECT 1 FROM KnowledgeArticleTags kat 
                JOIN DocumentTags dt ON kat.TagID = dt.TagID 
                WHERE kat.ArticleID = ka.ArticleID AND dt.TagName LIKE @search
            ))`);
            queryParams.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        // Add category filter
        if (categoryId && categoryId !== 'all') {
            whereConditions.push('ka.CategoryID = @categoryId');
            queryParams.push({ name: 'categoryId', type: sql.Int, value: parseInt(categoryId) });
        }

        // Add company filter
        if (companyId && companyId !== 'all') {
            whereConditions.push('ka.CompanyID = @companyId');
            queryParams.push({ name: 'companyId', type: sql.Int, value: parseInt(companyId) });
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const query = `
            WITH ArticlesWithTags AS (
                SELECT 
                    ka.ArticleID,
                    ka.Title,
                    ka.Summary,
                    ka.Content,
                    ka.CategoryID,
                    dc.CategoryName,
                    ka.CompanyID,
                    c.CompanyName,
                    ka.IsPublished,
                    ka.ViewCount,
                    ka.CreatedBy,
                    u.FirstName + ' ' + u.LastName as CreatedByName,
                    u.Email as CreatedByEmail,
                    ka.CreatedDate,
                    ka.ModifiedDate,
                    STRING_AGG(dt.TagName, ',') WITHIN GROUP (ORDER BY dt.TagName) as Tags,
                    COALESCE(AVG(CAST(kaf.Rating as FLOAT)), 0) as AverageRating
                FROM KnowledgeArticles ka
                LEFT JOIN DocumentCategories dc ON ka.CategoryID = dc.CategoryID
                LEFT JOIN Companies c ON ka.CompanyID = c.CompanyID
                LEFT JOIN Users u ON ka.CreatedBy = u.UserID
                LEFT JOIN KnowledgeArticleTags kat ON ka.ArticleID = kat.ArticleID
                LEFT JOIN DocumentTags dt ON kat.TagID = dt.TagID
                LEFT JOIN KnowledgeArticleFeedback kaf ON ka.ArticleID = kaf.ArticleID
                ${whereClause}
                GROUP BY 
                    ka.ArticleID, ka.Title, ka.Summary, ka.Content, ka.CategoryID, 
                    dc.CategoryName, ka.CompanyID, c.CompanyName, ka.IsPublished, 
                    ka.ViewCount, ka.CreatedBy, u.FirstName, u.LastName, u.Email, 
                    ka.CreatedDate, ka.ModifiedDate
            )
            SELECT * FROM ArticlesWithTags
            ORDER BY CreatedDate DESC
            OFFSET @offset ROWS
            FETCH NEXT @limit ROWS ONLY;

            -- Get total count for pagination
            SELECT COUNT(*) as TotalCount
            FROM KnowledgeArticles ka
            LEFT JOIN DocumentCategories dc ON ka.CategoryID = dc.CategoryID
            LEFT JOIN Companies c ON ka.CompanyID = c.CompanyID
            ${whereClause};
        `;

        const result = await executeQuery(query, queryParams);
        
        // Access recordsets properly
        const articlesData = Array.isArray(result.recordsets) ? result.recordsets[0] : result.recordset;
        const countData = Array.isArray(result.recordsets) ? result.recordsets[1] : null;
        
        const articles: KnowledgeArticle[] = articlesData.map((row: any) => ({
            id: row.ArticleID.toString(),
            title: row.Title,
            summary: row.Summary || '',
            content: row.Content,
            category: {
                id: row.CategoryID?.toString() || '',
                name: row.CategoryName || 'Uncategorized'
            },
            company: row.CompanyName || 'Unknown',
            isPublished: row.IsPublished,
            viewCount: row.ViewCount,
            rating: Math.round((row.AverageRating || 0) * 10) / 10, // Round to 1 decimal place
            createdBy: {
                name: row.CreatedByName || 'Unknown',
                email: row.CreatedByEmail || ''
            },
            createdDate: row.CreatedDate,
            lastModified: row.ModifiedDate,
            tags: row.Tags ? row.Tags.split(',') : []
        }));

        const totalCount = countData?.[0]?.TotalCount || 0;
        const totalPages = Math.ceil(totalCount / limit);

        logger.info(`GetKnowledgeArticles: Retrieved ${articles.length} articles for user ${userId || 'dev-user'}`);

        return {
            status: 200,
            jsonBody: {
                articles,
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        };

    } catch (error) {
        logger.error("GetKnowledgeArticles: Error retrieving articles:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error retrieving knowledge articles.",
                error: errorMessage
            }
        };
    }
}

// Register the function
app.http('GetKnowledgeArticles', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'knowledge/articles',
    handler: getKnowledgeArticles
}); 