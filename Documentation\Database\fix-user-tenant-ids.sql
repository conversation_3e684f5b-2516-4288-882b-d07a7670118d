-- Fix User Tenant IDs Based on Company Mapping
-- This script updates existing user records to have the correct TenantID based on their CompanyID

-- First, let's check the current state
PRINT 'Current User-Company-Tenant Mapping (BEFORE FIX):';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    c.Company<PERSON>ame,
    u.TenantID as CurrentUserTenantID,
    c.TenantID as CorrectCompanyTenantID,
    CASE 
        WHEN u.TenantID = c.TenantID THEN 'CORRECT'
        ELSE 'NEEDS UPDATE'
    END as Status
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
ORDER BY u.Email;

PRINT '';
PRINT 'Starting User TenantID Updates...';

-- Update users based on their company's correct tenant ID
UPDATE Users 
SET TenantID = 
    CASE 
        WHEN CompanyID = 1 THEN 'ecb4a448-4a99-443b-aaff-063150b6c9ea'  -- Avirata Defence Systems
        WHEN CompanyID = 2 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS HET
        WHEN CompanyID = 3 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS Group (same as HET)
        WHEN CompanyID = 4 THEN 'd6a5d909-b6c5-4724-a46d-2641d73acff1'  -- FE-SIL
        WHEN CompanyID = 5 THEN '7732add2-c45c-472b-8da8-4e2b4699bbb0'  -- Glodesi
        WHEN CompanyID = 6 THEN 'a8dcc1ff-5cc0-4432-827b-9da18737a775'  -- Hanuka
        WHEN CompanyID = 7 THEN 'PLACEHOLDER_WWH_TENANT_ID'              -- West Wire Harnessing (placeholder)
        ELSE TenantID -- Keep current if unknown company
    END,
    ModifiedDate = GETUTCDATE(),
    ModifiedBy = 1  -- System update
WHERE CompanyID IN (1, 2, 3, 4, 5, 6, 7);

PRINT 'User TenantID updates completed.';

-- Alternative approach using JOIN (more robust):
UPDATE u
SET u.TenantID = c.TenantID,
    u.ModifiedDate = GETUTCDATE(),
    u.ModifiedBy = 1
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE u.TenantID != c.TenantID OR u.TenantID IS NULL;

PRINT 'Alternative JOIN-based update completed.';

-- Verification: Check updated state
PRINT '';
PRINT 'User-Company-Tenant Mapping (AFTER FIX):';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    c.CompanyName,
    u.TenantID as UpdatedUserTenantID,
    c.TenantID as CompanyTenantID,
    CASE 
        WHEN u.TenantID = c.TenantID THEN 'CORRECT ✓'
        ELSE 'MISMATCH ✗'
    END as Status
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
ORDER BY u.Email;

-- Count users by tenant after fix
PRINT '';
PRINT 'User Distribution by Tenant (AFTER FIX):';
SELECT 
    c.CompanyName,
    c.TenantID,
    COUNT(u.UserID) as UserCount,
    STRING_AGG(u.Email, ', ') as UserEmails
FROM Companies c
LEFT JOIN Users u ON c.CompanyID = u.CompanyID
GROUP BY c.CompanyName, c.TenantID
ORDER BY c.CompanyName;

-- Check for any remaining mismatches
PRINT '';
PRINT 'Remaining Mismatches (should be empty):';
SELECT 
    u.UserID,
    u.Email,
    u.CompanyID,
    c.CompanyName,
    u.TenantID as UserTenantID,
    c.TenantID as CompanyTenantID
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE u.TenantID != c.TenantID;

PRINT '';
PRINT 'User TenantID fix completed successfully!';
PRINT '';
PRINT 'IMPORTANT NOTES:';
PRINT '- Users in WestWire Harnessing (CompanyID 7) have placeholder TenantID';
PRINT '- Update WWH TenantID once you have the actual tenant ID';
PRINT '- All other users should now have correct tenant IDs matching their company'; 