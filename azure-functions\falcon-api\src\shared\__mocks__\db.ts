// Mock implementation of the database module
import * as sql from 'mssql';

// Create a mock recordset that satisfies the IRecordSet interface
const createMockRecordset = () => {
  return Object.assign([], { toTable: jest.fn(), columns: {} });
};

// Mock pool for testing
const mockPool = {
  connected: true,
  request: jest.fn().mockReturnThis(),
  input: jest.fn().mockReturnThis(),
  query: jest.fn(),
  connect: jest.fn().mockResolvedValue(true),
  close: jest.fn().mockResolvedValue(true),
  on: jest.fn(),
};

// Mock getPool function
export const getPool = jest.fn().mockResolvedValue(mockPool);

// Mock executeQuery function
export const executeQuery = jest.fn().mockImplementation(async (query: string, params?: { [key: string]: any }): Promise<sql.IResult<any>> => {
  // Default empty result that satisfies the IResult interface
  const mockRecordset = createMockRecordset();

  return {
    recordset: mockRecordset,
    recordsets: [mockRecordset],
    output: {},
    rowsAffected: [0],
  };
});

// Reset all mocks between tests
export const resetMocks = () => {
  getPool.mockClear();
  executeQuery.mockClear();
  mockPool.request.mockClear();
  mockPool.input.mockClear();
  mockPool.query.mockClear();
  mockPool.connect.mockClear();
  mockPool.close.mockClear();
  mockPool.on.mockClear();
};
