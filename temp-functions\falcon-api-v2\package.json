{"name": "", "version": "1.0.0", "description": "", "main": "dist/src/functions/*.js", "scripts": {"build": "tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "func start", "test": "echo \"No tests yet...\""}, "dependencies": {"@azure/functions": "^4.0.0"}, "devDependencies": {"azure-functions-core-tools": "^4.x", "@types/node": "18.x", "typescript": "^4.0.0", "rimraf": "^5.0.0"}}