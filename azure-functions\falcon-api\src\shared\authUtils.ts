import { HttpRequest, InvocationContext } from "@azure/functions";
import { logger } from "./utils/logger";
import * as sql from 'mssql';
import { QueryParameter, executeQuery } from "./db";
import { updateUserLastLogin } from "./services/userManagementService";

// Interface for the structure within X-MS-CLIENT-PRINCIPAL
// Based on common structure, might need adjustment based on actual claims
export interface ClientPrincipal {
    identityProvider: string;
    userId: string; // User's unique ID within the provider
    userDetails: string; // Often the user's display name or email
    userRoles: string[]; // Roles assigned (e.g., Anonymous, Authenticated, custom roles/groups)
    claims?: { typ: string; val: string }[]; // Array of claims
}

/**
 * Parses the X-MS-CLIENT-PRINCIPAL header to get user information.
 * 
 * @param request - The incoming HttpRequest object.
 * @returns The parsed ClientPrincipal object or null if the header is missing or invalid.
 */
export function getClientPrincipal(request: HttpRequest): ClientPrincipal | null {
    const header = request.headers.get('x-ms-client-principal');
    if (!header) {
        logger.warn("getClientPrincipal: Missing x-ms-client-principal header.");
        return null;
    }

    try {
        const decoded = Buffer.from(header, 'base64').toString('utf-8');
        const principal: ClientPrincipal = JSON.parse(decoded);
        // Basic validation
        if (!principal || !principal.userId || !principal.userRoles) {
             logger.warn("getClientPrincipal: Parsed principal is missing required fields (userId, userRoles).");
             return null;
        }
        return principal;
    } catch (error) {
        logger.error("getClientPrincipal: Error parsing x-ms-client-principal header:", error);
        return null;
    }
}

/**
 * Checks if the authenticated user has at least one of the required roles.
 * Assumes roles/groups are passed in the userRoles array or specific claims.
 * 
 * @param principal - The ClientPrincipal object.
 * @param requiredRoles - An array of role names that are allowed.
 * @returns True if the user has permission, false otherwise.
 */
export function hasRequiredRole(principal: ClientPrincipal | null, requiredRoles: string[]): boolean {
    if (!principal) {
        return false; // Not authenticated
    }
    // Check direct userRoles first (common for App Service Auth roles)
    if (principal.userRoles.some(role => requiredRoles.includes(role))) {
        return true;
    }
    // Check claims for roles/groups (common for Entra ID group membership)
    // The claim type for roles/groups might vary (e.g., 'roles', 'groups')
    if (principal.claims) {
        if (principal.claims.some(claim => 
            (claim.typ === 'roles' || claim.typ === 'groups') && 
            requiredRoles.includes(claim.val)
        )) {
            return true;
        }
    }

    return false;
}

/**
 * Checks if the authenticated user has admin privileges.
 * 
 * @param principal - The ClientPrincipal object.
 * @returns True if the user is an admin, false otherwise.
 */
export function isAdmin(principal: ClientPrincipal | null): boolean {
    return hasRequiredRole(principal, ['Admin', 'Administrator', 'admin']);
}

/**
 * Retrieves the internal UserID from the database based on the Entra ID Object ID (oid).
 * Also updates the user's LastLoginDate upon successful retrieval.
 * 
 * @param principal - The ClientPrincipal object.
 * @param context - InvocationContext for logging.
 * @returns The internal UserID or null if not found or an error occurs.
 */
export async function getUserIdFromPrincipal(principal: ClientPrincipal | null, context: InvocationContext): Promise<number | null> {
    if (!principal) {
        context.log("getUserIdFromPrincipal: Cannot get UserID, client principal is null.");
        return null;
    }

    // Find the Entra Object ID (oid) claim
    const oidClaim = principal.claims?.find(claim => claim.typ === 'http://schemas.microsoft.com/identity/claims/objectidentifier');
    const entraId = oidClaim?.val;

    if (!entraId) {
        context.log("getUserIdFromPrincipal: Could not find Entra ID (oid claim) in principal.");
        logger.warn("getUserIdFromPrincipal: Could not find Entra ID (oid claim) in principal.", principal);
        return null;
    }

    try {
        const query = `
            SELECT u.UserID, u.IsActive 
            WHERE u.EntraID = @EntraID
        `;
        const params: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const result = await executeQuery(query, params);

        if (!result.recordset || result.recordset.length === 0) {
            logger.warn(`User lookup failed in checkUserExists: EntraID ${entraId} not found in local DB.`);
            return null;
        }

        const userId = result.recordset[0].UserID;
        
        // Update LastLoginDate asynchronously (fire and forget - don't wait for it)
        updateUserLastLogin(userId).catch(err => {
            logger.error(`getUserIdFromPrincipal: Failed to update LastLoginDate for UserID ${userId}:`, err);
            // Log error but don't block the main function return
        });

        return userId;
    } catch (error) {
        context.log(`getUserIdFromPrincipal: Error fetching UserID for EntraID ${entraId}:`, error);
        logger.error(`getUserIdFromPrincipal: Error fetching UserID for EntraID ${entraId}:`, error);
        return null;
    }
} 