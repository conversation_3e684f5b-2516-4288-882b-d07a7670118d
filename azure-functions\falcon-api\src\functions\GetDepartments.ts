import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

// Department interface
interface Department {
    id: number;
    name: string;
    companyId: number;
    companyName: string;
    isActive: boolean;
    employeeCount?: number;
}

async function getDepartments(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info("GetDepartments: Processing request");
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        
        if (!isDevelopment && principal) {
            const internalUserId = await getUserIdFromPrincipal(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams: QueryParameter[] = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await executeQuery(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        
        logger.info(`GetDepartments: Processing request for user: ${userId}, company: ${userCompanyId}`);
        
        // Query to get departments with employee counts
        const query = `
            SELECT 
                d.DepartmentID,
                d.DepartmentName,
                d.CompanyID,
                c.CompanyName,
                d.IsActive,
                COUNT(u.UserID) as EmployeeCount
            FROM Departments d
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            LEFT JOIN Users u ON d.DepartmentID = u.DepartmentID AND u.IsActive = 1
            WHERE d.CompanyID = @CompanyID AND d.IsActive = 1
            GROUP BY d.DepartmentID, d.DepartmentName, d.CompanyID, c.CompanyName, d.IsActive
            ORDER BY d.DepartmentName;
        `;

        const parameters: QueryParameter[] = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];

        logger.info(`GetDepartments: Executing query for company ${userCompanyId}`);
        
        const result = await executeQuery(query, parameters);
        
        if (!result.recordset) {
            logger.info(`GetDepartments: No departments found`);
            return {
                status: 200,
                jsonBody: {
                    departments: []
                }
            };
        }

        // Map results to Department interface
        const departments: Department[] = result.recordset.map((row: any) => ({
            id: row.DepartmentID,
            name: row.DepartmentName,
            companyId: row.CompanyID,
            companyName: row.CompanyName,
            isActive: row.IsActive,
            employeeCount: row.EmployeeCount || 0
        }));

        logger.info(`GetDepartments: Found ${departments.length} departments`);

        return {
            status: 200,
            jsonBody: {
                departments
            }
        };

    } catch (error) {
        logger.error("GetDepartments: Error processing request:", error);
        return { 
            status: 500, 
            jsonBody: { 
                error: "Internal server error", 
                details: error instanceof Error ? error.message : String(error) 
            } 
        };
    }
}

// Register the function
app.http('GetDepartments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'departments',
    handler: getDepartments
});

export { getDepartments }; 