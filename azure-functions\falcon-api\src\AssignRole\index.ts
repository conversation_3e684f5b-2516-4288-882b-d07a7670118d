import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { assignRoleToUser } from "../shared/services/userManagementService";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal } from "../shared/authUtils"; // Import auth utils
import { validateRequest, userIdRouteParamSchema, assignRoleBodySchema } from "../shared/validationSchemas";

// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Administrator'; // Match the actual role name in database

export async function assignRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function AssignRole processed request for url "${request.url}"`);
    logger.info('AssignRole function invoked.');

    // --- Authentication & Authorization --- 
    const principal = getClientPrincipal(request);
    if (!principal) {
        return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
    }

    // TODO: Refine role check based on actual Entra ID roles/groups configuration
    if (!hasRequiredRole(principal, [REQUIRED_ROLE])) {
         logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
         return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
    }

    const authenticatedUserId = await getUserIdFromPrincipal(principal, context);
    if (!authenticatedUserId) {
        // This indicates the authenticated Entra ID user doesn't exist or isn't active in our Users table
        logger.error(`AssignRole: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
        return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
    }
    // NOTE: Using retrieved authenticatedUserId below for 'assignedByUserId' field.
    // Ensure getUserIdFromPrincipal correctly retrieves the UserID from your DB 
    // based on the oid claim provided by the configured authentication provider.
    logger.info(`AssignRole invoked by UserID: ${authenticatedUserId}`);
    // --- End Auth --- 

    // --- Input Validation --- 
    // Validate Route Parameters
    const routeParams = { userId: request.params.userId }; // Extract for validation
    let validationError = validateRequest(userIdRouteParamSchema, routeParams, context, "route parameters");
    if (validationError) return validationError;
    // Validation passed, use validated data
    const validatedRouteParams = userIdRouteParamSchema.parse(routeParams); // Use parse to get typed data
    const userId = validatedRouteParams.userId; // Already a number

    // Validate Request Body
    let parsedBody: any;
    try {
        parsedBody = await request.json();
    } catch (error) {
        logger.error('AssignRole: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { message: "Invalid JSON in request body." } };
    }

    validationError = validateRequest(assignRoleBodySchema, parsedBody, context, "request body");
    if (validationError) return validationError;
    
    // Validation passed, use validated data
    const validatedBody = assignRoleBodySchema.parse(parsedBody);
    const { roleId } = validatedBody; // Already a number
    // --- End Validation --- 

    try {
        // Use the retrieved authenticated user ID for auditing
        const success = await assignRoleToUser(userId, roleId, authenticatedUserId);

        if (success) {
            logger.info(`Successfully assigned/reactivated role ${roleId} for user ${userId} by UserID ${authenticatedUserId}.`);
            return {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: "Role assigned successfully." }
            };
        } else {
            // assignRoleToUser handles logging the specific error
            // Return a generic server error if it failed
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: "Failed to assign role." }
            };
        }

    } catch (error) {
        // This catch block is mainly for unexpected errors during the process
        logger.error(`Unexpected error in AssignRole function for User ${userId}, Role ${roleId} by UserID ${authenticatedUserId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "An unexpected error occurred while assigning the role.",
                error: errorMessage
            }
        };
    }
}

app.http('AssignRole', {
    methods: ['POST'],
    authLevel: 'anonymous', // Now handled by provider + code checks
    route: 'users/{userId}/roles', // Match function.json
    handler: assignRole
});