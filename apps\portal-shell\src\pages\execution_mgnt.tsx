// import React from 'react'; // Unused import
// import { Link } from 'react-router-dom'; // Unused import
import {
  FileText,
  Edit3,
  CheckSquare,
  RefreshCw,
  AlertOctagon,
  BarChart2,
  Clipboard,
  Archive,
  UploadCloud,
  MessageCircle,
  ShoppingCart,
  Layers,
  List,
} from "feather-icons-react";

const host_var = "http://**************/"//"http://**************/";
const handleToolClick = (tool) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);

const enggTools = [
  {
    label: 'Part Numbering',
    name: 'partnumbering',
    link: host_var + 'partnumbering/index.php',
    desc: 'Standard part number creation',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <FileText />,
  },
  {
    label: 'ECN',
    name: 'ecn_server',
    link: host_var + 'ecn_server/index.php',
    desc: 'Engineering Change Notice system',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <Edit3 />,
  },
  {
    label: 'Engineering QA Tracker',
    name: '',
    link: host_var + 'engineering_qa_tracker/index.php',
    desc: 'QA checklists and reports',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <CheckSquare />,
  },
  {
    label: 'UDS Conversion',
    name: 'engineering_qa_tracker',
    link: host_var + 'uds/index.php',
    desc: 'Legacy to UDS format',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <RefreshCw />,
  },
  {
    label: 'Issue Control',
    name: 'issue_control',
    link: host_var + 'issue_control/index.php',
    desc: 'Track engineering issues',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <AlertOctagon />,
  },
  {
    label: 'Make Buy Data',
    name: 'buy_data',
    link: host_var + 'buy_data/index.php',
    desc: 'Decision support tool',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <BarChart2 />,
  },
  {
    label: 'Contract Review',
    name: 'contract_review_server',
    link: host_var + 'contract_review_server/index.php',
    desc: 'Project & client contract review',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <Clipboard />,
  },
  {
    label: 'FG Masters',
    name: 'fg_masters',
    link: host_var + 'fg_masters/index.php',
    desc: 'Finished Goods master list',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <Archive />,
  },
  {
    label: 'Customer Input Documents',
    name: 'customer_input_document',
    link: host_var + 'customer_input_document/index.php',
    desc: 'Uploaded customer files',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <UploadCloud />,
  },
  {
    label: 'FA Feedback',
    name: 'fa_feedback',
    link: host_var + 'fa_feedback/index.php',
    desc: 'Feedback from First Article',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <MessageCircle />,
  },
  {
    label: 'Customer P.O Tracker',
    name: 'customer_po_tracker',
    link: host_var + 'customer_po_tracker/index.php',
    desc: 'Track purchase orders',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <ShoppingCart />,
  },
  {
    label: 'CC Matrix',
    name: 'ccm_server',
    link: host_var + 'ccm_server/index.php',
    desc: 'Cross-checking document matrix',
    category: 'Engineering Execution',
    priority: 'medium',
    type: 'internal',
    icon: <Layers />,
  },
  {
    label: 'Engg BOM Verification',
    name: 'Engg_bom_portal',
    link: host_var + 'Engg_bom_portal/index.php',
    desc: 'Validate engineering BOMs',
    category: 'Engineering Execution',
    priority: 'high',
    type: 'internal',
    icon: <List />,
  },
];
const EnggDashboard = () => {


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">Execution Management Tools</h1>
              <p className="text-gray-600 text-lg">Streamline engineering execution and project oversight processes</p>
            </div>

          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

       <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {enggTools.map((tool, index) => (
          <div
            key={index}
            onClick={() => handleToolClick(tool)}
            className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
          >
            <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
            <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
            <p className="text-gray-600 text-sm">{tool.desc}</p>
          </div>
        ))}
      </div>


        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default EnggDashboard;