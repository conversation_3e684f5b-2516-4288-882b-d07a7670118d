"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadDocument = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function downloadDocument(request, context) {
    context.log(`DownloadDocument function invoked.`);
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }
        // Get document ID from route parameters
        const documentId = request.params.documentId;
        if (!documentId) {
            return { status: 400, jsonBody: { error: "Document ID is required" } };
        }
        // Get document information from database
        const documentQuery = `
            SELECT 
                d.DocumentID,
                d.Title,
                d.FileName,
                d.FileExtension,
                d.BlobUrl,
                d.CompanyID,
                d.IsPublished,
                d.IsActive,
                c.CompanyName
            FROM Documents d
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            WHERE d.DocumentID = @documentId AND d.IsActive = 1
        `;
        const documentResult = await (0, db_1.executeQuery)(documentQuery, [
            { name: 'documentId', type: sql.Int, value: parseInt(documentId) }
        ]);
        if (!documentResult.recordset || documentResult.recordset.length === 0) {
            return { status: 404, jsonBody: { error: "Document not found" } };
        }
        const document = documentResult.recordset[0];
        // Check if document is published
        if (!document.IsPublished) {
            return { status: 403, jsonBody: { error: "Document is not published" } };
        }
        // TODO: Add company-based access control if needed
        // For now, allow access to published documents
        // Increment download count
        const updateQuery = `
            UPDATE Documents 
            SET DownloadCount = DownloadCount + 1 
            WHERE DocumentID = @documentId
        `;
        await (0, db_1.executeQuery)(updateQuery, [
            { name: 'documentId', type: sql.Int, value: parseInt(documentId) }
        ]);
        // In development mode or if blob URL is a mock URL, return a redirect to a placeholder
        if (isDevelopment || document.BlobUrl.startsWith('/api/files/')) {
            logger_1.logger.info(`DownloadDocument: Development mode - returning mock download for ${document.FileName}`);
            return {
                status: 200,
                jsonBody: {
                    message: "Development mode - file download not implemented",
                    documentId: document.DocumentID,
                    fileName: document.FileName,
                    downloadUrl: document.BlobUrl,
                    note: "In production, this would redirect to Azure Blob Storage"
                }
            };
        }
        // In production, redirect to the blob URL
        return {
            status: 302,
            headers: {
                'Location': document.BlobUrl,
                'Content-Disposition': `attachment; filename="${document.FileName}"`
            }
        };
    }
    catch (error) {
        logger_1.logger.error("DownloadDocument: Error downloading document:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error downloading document.",
                error: errorMessage
            }
        };
    }
}
exports.downloadDocument = downloadDocument;
// Register the function
functions_1.app.http('DownloadDocument', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents/{documentId}/download',
    handler: downloadDocument
});
//# sourceMappingURL=DownloadDocument.js.map