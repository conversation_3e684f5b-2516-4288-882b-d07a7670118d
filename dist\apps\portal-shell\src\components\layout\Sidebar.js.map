{"version": 3, "file": "Sidebar.js", "sourceRoot": "", "sources": ["../../../../../../apps/portal-shell/src/components/layout/Sidebar.tsx"], "names": [], "mappings": ";;AAAA,iCAAwC;AACxC,uDAAwD;AACxD,6DAAkJ;AAElJ,MAAM,eAAe,GAAG,QAAQ,CAAC;AAUjC,MAAM,OAAO,GAAa,GAAG,EAAE;IAC7B,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,IAAA,8BAAW,GAAE,CAAC;IAE/B,8CAA8C;IAC9C,MAAM,QAAQ,GAAc;QAC1B,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,0BAAI,EAAE;QACtD,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,+BAAS,EAAE;QAC/D,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,8BAAQ,EAAE;QAChD,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,2BAAK,EAAE;QAC7C,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,2BAAK,EAAE;QACvD;YACE,IAAI,EAAE,eAAe;YACrB,KAAK,EAAE,cAAc;YACrB,IAAI,EAAE,4BAAM;YACZ,QAAQ,EAAE;gBACR,EAAE,IAAI,EAAE,+BAA+B,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAE,2BAAK,EAAE;gBAChF,EAAE,IAAI,EAAE,+BAA+B,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAE,+BAAS,EAAE;aACrF;SACF;QACD,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,mBAAmB,EAAE,IAAI,EAAE,mCAAa,EAAE;KAC5E,CAAC;IAEF,MAAM,WAAW,GAAG,yFAAyF,CAAC;IAC9G,MAAM,aAAa,GAAG,sCAAsC,CAAC;IAC7D,MAAM,gBAAgB,GAAG,uGAAuG,CAAC;IACjI,MAAM,kBAAkB,GAAG,sCAAsC,CAAC;IAElE,MAAM,gBAAgB,GAAG,CAAC,KAAa,EAAE,EAAE;QACzC,WAAW,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,UAAkB,EAAE,EAAE;QAC1C,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,KAAK,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACvI,CAAC,CAAA;IAED,qDAAqD;IACrD,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACjB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,IAAI,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;QACL,uDAAuD;IACvD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CACL,CAAC,KAAK,CAAC,SAAS,CAAC,6EAA6E,CAC5F;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;QAAA,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CACjF;MAAA,EAAE,GAAG,CACL;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CACxB;QAAA,CAAC,EAAE,CACD;UAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CACpB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,CAClC;cAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CACf,EACE;kBAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAC5C,SAAS,CAAC,CAAC,GAAG,WAAW,2BAA2B,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAErN;oBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAChC;sBAAA,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,EACnD;sBAAA,CAAC,IAAI,CAAC,KAAK,CACb;oBAAA,EAAE,GAAG,CACL;oBAAA,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iCAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,kCAAY,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAG,CACnF;kBAAA,EAAE,MAAM,CACR;kBAAA,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,IAAI,CAC1B,CAAC,EAAE,CAAC,SAAS,CAAC,gBAAgB,CAC5B;sBAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAC1B,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CACjB;2BAAA,CAAC,0BAAO,CACP,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CACf,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAC1B,GAAG,gBAAgB,IAAI,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,EAC3D,CAAC,CAED;4BAAA,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,EACpD;4BAAA,CAAC,KAAK,CAAC,KAAK,CACb;2BAAA,EAAE,0BAAO,CACZ;wBAAA,EAAE,EAAE,CAAC,CACN,CAAC,CACJ;oBAAA,EAAE,EAAE,CAAC,CACN,CACH;gBAAA,GAAG,CACJ,CAAC,CAAC,CAAC,CACF,CAAC,0BAAO,CACN,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACd,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAC1B,GAAG,WAAW,IAAI,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EACjD,CAAC,CAED;kBAAA,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,EACnD;kBAAA,CAAC,IAAI,CAAC,KAAK,CACb;gBAAA,EAAE,0BAAO,CAAC,CACX,CACH;YAAA,EAAE,EAAE,CAAC,CACN,CAAC,CACJ;QAAA,EAAE,EAAE,CACN;MAAA,EAAE,GAAG,CACL;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yEAAyE,CACtF;gBAAQ,CAAC,eAAe,CAC1B;MAAA,EAAE,GAAG,CACP;IAAA,EAAE,KAAK,CAAC,CACT,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,OAAO,CAAC"}