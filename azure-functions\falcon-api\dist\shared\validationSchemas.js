"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequest = exports.getPortalUsersSchema = exports.updateUserSchema = exports.getUserSchema = exports.removeRoleSchema = exports.assignRoleSchema = exports.addUserSchema = exports.searchDirectoryUsersSchema = exports.getRolesSchema = exports.updateUserBodySchema = exports.assignRoleBodySchema = exports.addUserBodySchema = exports.userRoleRouteParamsSchema = exports.userIdRouteParamSchema = exports.entraIdRouteParamSchema = exports.listUsersQuerySchema = exports.searchQuerySchema = void 0;
const zod_1 = require("zod"); // Assuming Zod is available for validation
// --- Helper function to format Zod errors ---
const formatZodError = (error) => {
    return error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join('; ');
};
// --- Query Parameter Schemas --- 
// Schema for SearchDirectoryUsers query parameters
exports.searchQuerySchema = zod_1.z.object({
    q: zod_1.z.string().min(2, { message: "Search query parameter 'q' must be at least 2 characters long." }),
    limit: zod_1.z.preprocess((val) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val), zod_1.z.number().int().positive({ message: "Limit parameter must be a positive integer." })
        .optional()
        .default(10))
});
// Schema for GetUsers query parameters
exports.listUsersQuerySchema = zod_1.z.object({
    search: zod_1.z.string().optional(),
    company: zod_1.z.string().optional().default('All'),
    role: zod_1.z.string().optional().default('All'),
    status: zod_1.z.enum(['Active', 'Inactive', 'All']).optional().default('All'),
    page: zod_1.z.preprocess((val) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val), zod_1.z.number().int().min(1, { message: "Page parameter must be 1 or greater." })
        .optional()
        .default(1)),
    pageSize: zod_1.z.preprocess((val) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val), zod_1.z.number().int().min(1, { message: "PageSize parameter must be 1 or greater." }).max(100, { message: "PageSize cannot exceed 100." })
        .optional()
        .default(10))
});
// Schema for route parameter containing EntraID (e.g., /portal-users/{entraId})
exports.entraIdRouteParamSchema = zod_1.z.object({
    entraId: zod_1.z.string().min(1, { message: "EntraID route parameter cannot be empty." })
});
// Schema for route parameter containing UserID (e.g., /users/{userId}/...)
exports.userIdRouteParamSchema = zod_1.z.object({
    userId: zod_1.z.preprocess((val) => (typeof val === 'string' ? val.trim() : val), zod_1.z.string()
        .refine((val) => /^[1-9]\d*$/.test(val), {
        message: "UserID parameter must be a positive integer string."
    })
        .transform((val) => parseInt(val, 10)))
});
// Schema for route parameters containing UserID and RoleID (e.g., /users/{userId}/roles/{roleId})
// Note: Not currently used, Assign/Remove use body for roleId. Keeping for potential future use.
exports.userRoleRouteParamsSchema = zod_1.z.object({
    userId: zod_1.z.preprocess((val) => (typeof val === 'string' ? val.trim() : val), zod_1.z.string()
        .refine((val) => /^[1-9]\d*$/.test(val), {
        message: "UserID route parameter must be a positive integer string."
    })
        .transform((val) => parseInt(val, 10))),
    roleId: zod_1.z.preprocess((val) => (typeof val === 'string' ? val.trim() : val), zod_1.z.string()
        .refine((val) => /^[1-9]\d*$/.test(val), {
        message: "RoleID route parameter must be a positive integer string."
    })
        .transform((val) => parseInt(val, 10)))
});
// --- Request Body Schemas ---
// Schema for AddUser request body
exports.addUserBodySchema = zod_1.z.object({
    entraId: zod_1.z.string().min(1, { message: "entraId in request body cannot be empty." })
});
// Schema for AssignRole request body
exports.assignRoleBodySchema = zod_1.z.object({
    roleId: zod_1.z.number().int().positive({ message: "roleId must be a positive integer." })
});
// Schema for UpdateUser request body
exports.updateUserBodySchema = zod_1.z.object({
    // Allow empty array for roles
    roles: zod_1.z.array(zod_1.z.string({ invalid_type_error: "Role names must be strings." }), { invalid_type_error: "Roles must be an array." }),
    isActive: zod_1.z.boolean({ required_error: "isActive is required.", invalid_type_error: "isActive must be a boolean." })
});
// Schema for GetRoles (No input)
exports.getRolesSchema = zod_1.z.object({}); // Empty schema for routes with no input
// Schema for SearchDirectoryUsers
exports.searchDirectoryUsersSchema = zod_1.z.object({
    query: zod_1.z.object({
        searchQuery: zod_1.z.string().min(1, "Search query is required"),
        limit: zod_1.z.coerce.number().int().positive().optional().default(10)
    })
});
// Schema for AddUser
exports.addUserSchema = zod_1.z.object({
    body: zod_1.z.object({
        EntraID: zod_1.z.string().uuid("Invalid Entra ID format"),
    })
});
// Schema for AssignRole
exports.assignRoleSchema = zod_1.z.object({
    params: zod_1.z.object({
        userId: zod_1.z.string().min(1), // Or specific format if internal ID is used
    }),
    body: zod_1.z.object({
        roleId: zod_1.z.coerce.number().int().positive("Role ID must be a positive integer"),
    })
});
// Schema for RemoveRole
exports.removeRoleSchema = zod_1.z.object({
    params: zod_1.z.object({
        userId: zod_1.z.string().min(1),
        roleId: zod_1.z.string().regex(/^\d+$/, "Role ID in path must be numeric") // roleId from path
    }),
});
// Schema for GetUser
exports.getUserSchema = zod_1.z.object({
    params: zod_1.z.object({
        userId: zod_1.z.string().min(1) // Can be EntraID (GUID) or Internal ID (number)
        // Consider refining if type is known or add custom validation
    })
});
// Schema for UpdateUser (assuming only status update for now)
exports.updateUserSchema = zod_1.z.object({
    params: zod_1.z.object({
        userId: zod_1.z.string().min(1),
    }),
    body: zod_1.z.object({
        status: zod_1.z.enum(['Active', 'Inactive'])
        // Add other updatable fields here later, e.g., companyId
        // companyId: z.coerce.number().int().positive().optional(),
    })
});
// Schema for GetPortalUsers
exports.getPortalUsersSchema = zod_1.z.object({
    query: zod_1.z.object({
        page: zod_1.z.coerce.number().int().positive().optional().default(1),
        pageSize: zod_1.z.coerce.number().int().positive().optional().default(10),
        search: zod_1.z.string().optional(),
        company: zod_1.z.string().optional(),
        role: zod_1.z.string().optional(),
        status: zod_1.z.enum(['Active', 'Inactive', 'All']).optional(),
    })
});
/**
 * Validates request data (query parameters, route parameters, or body) using a provided Zod schema.
 *
 * @param schema - The Zod schema to use for validation.
 * @param data - The data object (e.g., request.query, request.params, parsed request body) to validate.
 * @param context - The InvocationContext for logging.
 * @param dataType - A string describing the type of data being validated (e.g., "query parameters", "request body") for logging.
 * @returns An HttpResponseInit object with status 400 if validation fails, otherwise null.
 */
function validateRequest(schema, data, context, dataType) {
    if (!schema) {
        context.log(`WARN: No validation schema provided for ${dataType}. Skipping validation.`);
        return null;
    }
    const result = schema.safeParse(data);
    if (!result.success) {
        const errorMessage = formatZodError(result.error);
        context.log(`Request validation failed for ${dataType}:`, errorMessage);
        // Return structured errors along with a general message
        return {
            status: 400,
            jsonBody: {
                message: `Invalid ${dataType}.`,
                errors: result.error.flatten().fieldErrors // Provides errors per field
            }
        };
    }
    // Validation succeeded
    context.log(`Validation successful for ${dataType}.`);
    return null;
}
exports.validateRequest = validateRequest;
//# sourceMappingURL=validationSchemas.js.map