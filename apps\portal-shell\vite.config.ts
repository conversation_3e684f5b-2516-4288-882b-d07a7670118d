import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { readFileSync } from 'fs'
import { resolve } from 'path'

// Read version from root package.json
const packageJson = JSON.parse(
  readFileSync(resolve(__dirname, '../../package.json'), 'utf-8')
)

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  define: {
    'import.meta.env.VITE_APP_VERSION': JSON.stringify(packageJson.version)
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:7071',  // Use IPv4 explicitly instead of localhost
        changeOrigin: true,
        secure: false,
        ws: true,
        timeout: 10000,
        // rewrite: (path) => path.replace(/^\\/api/, '') // Only if your Azure Function routes don't include /api
      }
    }
  }
})
