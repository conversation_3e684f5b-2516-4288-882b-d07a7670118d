"use strict";
// --- Types (based on docs/api/dashboard_endpoints.md) ---
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateQuickLinks = exports.fetchUpcomingEvents = exports.fetchRecentDocuments = exports.fetchAvailableQuickLinks = exports.fetchQuickLinks = exports.fetchPendingActions = exports.fetchAllAnnouncements = exports.fetchAnnouncements = exports.simulateNetworkDelay = void 0;
// --- Mock Data (Derived from DashboardSection.tsx placeholders) ---
const MOCK_ANNOUNCEMENTS = [
    {
        id: 'ann-1',
        title: 'System Maintenance Notice',
        description: 'Scheduled maintenance on May 5th from 10 PM to 2 AM',
        scope: 'Group-wide',
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        severity: 'high'
    },
    {
        id: 'ann-2',
        title: 'New Travel Policy Update',
        description: 'Important changes to domestic travel approval workflow',
        scope: 'SASMOS HET', // Example company-specific
        publishedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        severity: 'medium'
    },
    {
        id: 'ann-3',
        title: 'Q2 Town Hall Recording Available',
        description: 'The recording for the Q2 All-Hands meeting is now available on the Knowledge Hub.',
        scope: 'Group-wide',
        publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        severity: 'low',
        link: '/knowledge/recordings/q2-townhall'
    },
    {
        id: 'ann-4',
        title: 'Office Closure - Holiday',
        description: 'All SASMOS HET offices will be closed next Monday for the regional holiday.',
        scope: 'SASMOS HET',
        publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        severity: 'medium'
    },
];
const MOCK_PENDING_ACTIONS = [
    { id: 'act-1', type: 'Approval', title: 'Travel Request Approval', source: 'Rahul S.', details: 'Pending manager review', link: '/admin/travel/requests/123' },
    { id: 'act-2', type: 'Review', title: 'Document Review', source: 'Falcon Project', dueDate: new Date().toISOString(), link: '/knowledge/docs/falcon/review/456' },
    { id: 'act-3', type: 'Acknowledgment', title: 'HR Policy Acknowledgment', source: 'HRMS', details: 'Code of Conduct 2025', dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), link: '/hr/policies/coc2025/ack' },
];
let MOCK_QUICK_LINKS = [
    { id: 'ql-1', title: 'Submit IT Ticket', icon: 'FileText', targetHub: 'it', url: '/it/tickets/new' },
    { id: 'ql-2', title: 'Book Meeting Room', icon: 'Calendar', targetHub: 'admin', url: '/admin/facilities/rooms' },
    { id: 'ql-3', title: 'Request Travel', icon: 'Home', targetHub: 'admin', url: '/admin/travel/new' },
    { id: 'ql-4', title: 'Procurement', icon: 'ShoppingCart', targetHub: 'admin', url: '/admin/procurement/new' },
];
const MOCK_RECENT_DOCS = [
    { id: 'doc-1', name: 'Q1 Project Report.docx', lastOpenedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), link: '/knowledge/docs/projects/q1report' },
    { id: 'doc-2', name: 'Travel Expense Policy.pdf', lastOpenedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), link: '/hr/policies/travel-expense' },
    { id: 'doc-3', name: 'Meeting Minutes.docx', lastOpenedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), link: '/knowledge/docs/meetings/minutes-xyz' },
];
const MOCK_UPCOMING_EVENTS = [
    { id: 'evt-1', title: 'Company Town Hall', startDateTime: '2025-04-25T10:00:00Z', endDateTime: '2025-04-25T11:30:00Z', location: 'Virtual', scope: 'SASMOS HET', link: '/communication/events/townhall-q2' },
    { id: 'evt-2', title: 'IT Security Training', startDateTime: '2025-04-28T14:00:00Z', endDateTime: '2025-04-28T16:00:00Z', location: 'Training Room B', scope: 'Group-wide' },
    { id: 'evt-3', title: 'New Product Launch', startDateTime: '2025-05-05T09:00:00Z', endDateTime: '2025-05-05T12:00:00Z', location: 'Main Conference Room', scope: 'SASMOS HET' },
];
// Define all potentially available quick links
const ALL_AVAILABLE_QUICK_LINKS = [
    { id: 'ql-1', title: 'Submit IT Ticket', icon: 'FileText', targetHub: 'it', url: '/it/tickets/new' },
    { id: 'ql-2', title: 'Book Meeting Room', icon: 'Calendar', targetHub: 'admin', url: '/admin/facilities/rooms' },
    { id: 'ql-3', title: 'Request Travel', icon: 'Home', targetHub: 'admin', url: '/admin/travel/new' },
    { id: 'ql-4', title: 'Procurement', icon: 'ShoppingCart', targetHub: 'admin', url: '/admin/procurement/new' },
    { id: 'ql-5', title: 'View Payslip', icon: 'DollarSign', targetHub: 'hr', url: '/hr/payslips' },
    { id: 'ql-6', title: 'Apply for Leave', icon: 'Coffee', targetHub: 'hr', url: '/hr/leave/new' },
    { id: 'ql-7', title: 'Knowledge Base', icon: 'BookOpen', targetHub: 'knowledge', url: '/knowledge' },
    { id: 'ql-8', title: 'Company Directory', icon: 'Users', targetHub: 'communication', url: '/communication/directory' },
];
// --- Mock API Functions ---
// Export this helper
const simulateNetworkDelay = (delayMs = 500) => new Promise(resolve => setTimeout(resolve, delayMs));
exports.simulateNetworkDelay = simulateNetworkDelay;
const fetchAnnouncements = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (limit = 5, companyId) {
    yield (0, exports.simulateNetworkDelay)();
    // Simulate potential error
    // if (Math.random() > 0.8) {
    //   throw new Error("Failed to fetch announcements");
    // }
    const filtered = companyId
        ? MOCK_ANNOUNCEMENTS.filter(ann => ann.scope === companyId || ann.scope === 'Group-wide')
        : MOCK_ANNOUNCEMENTS;
    return filtered.slice(0, limit);
});
exports.fetchAnnouncements = fetchAnnouncements;
const fetchAllAnnouncements = (companyId) => __awaiter(void 0, void 0, void 0, function* () {
    yield (0, exports.simulateNetworkDelay)(600); // Slightly longer delay for more data
    // Simulate filtering based on company context
    const filtered = companyId
        ? MOCK_ANNOUNCEMENTS.filter(ann => ann.scope === companyId || ann.scope === 'Group-wide')
        : MOCK_ANNOUNCEMENTS;
    // TODO: Add pagination later
    return filtered;
});
exports.fetchAllAnnouncements = fetchAllAnnouncements;
const fetchPendingActions = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (limit = 5) {
    yield (0, exports.simulateNetworkDelay)(700); // Slightly different delay
    // TODO: Implement userId filtering later (implicit)
    return MOCK_PENDING_ACTIONS.slice(0, limit);
});
exports.fetchPendingActions = fetchPendingActions;
const fetchQuickLinks = () => __awaiter(void 0, void 0, void 0, function* () {
    yield (0, exports.simulateNetworkDelay)(300);
    return MOCK_QUICK_LINKS;
});
exports.fetchQuickLinks = fetchQuickLinks;
const fetchAvailableQuickLinks = () => __awaiter(void 0, void 0, void 0, function* () {
    yield (0, exports.simulateNetworkDelay)(200);
    // In a real app, this might be filtered based on user permissions
    return ALL_AVAILABLE_QUICK_LINKS;
});
exports.fetchAvailableQuickLinks = fetchAvailableQuickLinks;
const fetchRecentDocuments = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (limit = 5) {
    yield (0, exports.simulateNetworkDelay)(600);
    // TODO: Implement userId filtering later (implicit)
    return MOCK_RECENT_DOCS.slice(0, limit);
});
exports.fetchRecentDocuments = fetchRecentDocuments;
const fetchUpcomingEvents = (...args_1) => __awaiter(void 0, [...args_1], void 0, function* (limit = 3, companyId) {
    yield (0, exports.simulateNetworkDelay)(400);
    // TODO: Implement startDate filtering later
    const filtered = companyId
        ? MOCK_UPCOMING_EVENTS.filter(event => event.scope === companyId || event.scope === 'Group-wide')
        : MOCK_UPCOMING_EVENTS;
    return filtered.slice(0, limit);
});
exports.fetchUpcomingEvents = fetchUpcomingEvents;
// Update the user's selected/ordered links
const updateQuickLinks = (newLinks) => __awaiter(void 0, void 0, void 0, function* () {
    yield (0, exports.simulateNetworkDelay)(450); // Simulate save delay
    console.log("API Mock: Updating links to:", newLinks);
    // Update the mock array in memory
    MOCK_QUICK_LINKS = newLinks;
    // Simulate potential save error
    // if (Math.random() > 0.85) {
    //  throw new Error("Failed to save quick links configuration.");
    // }
    return Promise.resolve(); // Indicate success
});
exports.updateQuickLinks = updateQuickLinks;
//# sourceMappingURL=dashboardApi.js.map