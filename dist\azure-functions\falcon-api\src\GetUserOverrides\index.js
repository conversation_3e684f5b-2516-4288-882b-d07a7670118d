"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserOverrides = getUserOverrides;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = require("mssql");
function getUserOverrides(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        const entraId = request.params.entraId;
        context.log(`Http function processed request for GetUserOverrides: ${entraId}`);
        if (!entraId) {
            return {
                status: 400,
                jsonBody: { message: "EntraID parameter is required." }
            };
        }
        try {
            // 1. Get UserID from EntraID
            const userQuery = `SELECT UserID, IsActive FROM Users WHERE EntraID = @EntraID`;
            const userParams = [
                { name: 'EntraID', type: sql.NVarChar, value: entraId }
            ];
            const userResult = yield (0, db_1.executeQuery)(userQuery, userParams);
            if (!userResult.recordset || userResult.recordset.length === 0) {
                logger_1.logger.warn(`GetUserOverrides: User not found for EntraID: ${entraId}`);
                return {
                    status: 404,
                    jsonBody: {
                        message: "User not found in the portal database."
                    }
                };
            }
            const userId = userResult.recordset[0].UserID;
            const isActive = userResult.recordset[0].IsActive;
            // 2. Get user's active roles
            const rolesQuery = `
            SELECT r.RoleID, r.RoleName
            FROM Roles r
            JOIN UserRoles ur ON r.RoleID = ur.RoleID 
            WHERE ur.UserID = @UserID AND ur.IsActive = 1 AND r.IsActive = 1
        `;
            const rolesParams = [
                { name: 'UserID', type: sql.Int, value: userId }
            ];
            const rolesResult = yield (0, db_1.executeQuery)(rolesQuery, rolesParams);
            const assignedRoles = rolesResult.recordset.map(row => ({ id: row.RoleID, name: row.RoleName }));
            logger_1.logger.info(`Retrieved status and roles for EntraID ${entraId} (UserID: ${userId})`);
            return {
                status: 200,
                jsonBody: {
                    isActive: isActive,
                    roles: assignedRoles
                }
            };
        }
        catch (error) {
            context.error(`Error fetching user overrides: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error fetching user overrides.",
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('GetUserOverrides', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'users/overrides/{entraId}',
    handler: getUserOverrides
});
//# sourceMappingURL=index.js.map