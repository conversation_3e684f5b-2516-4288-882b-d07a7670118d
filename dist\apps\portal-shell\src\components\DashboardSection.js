"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const FeatherIcons = require("feather-icons-react");
const react_router_dom_1 = require("react-router-dom"); // Import useNavigate
const dashboardApi_1 = require("../services/dashboardApi"); // Correct path back
const date_fns_1 = require("date-fns"); // For date formatting
const QuickLinkCustomizeModal_1 = require("./modals/QuickLinkCustomizeModal"); // Import the modal
const react_hot_toast_1 = require("react-hot-toast"); // Import toast
// Helper to get Feather Icon component by name
const DynamicFeatherIcon = (_a) => {
    var { name } = _a, props = __rest(_a, ["name"]);
    // Use the FeatherIconName type, but still need assertion for dynamic access
    const IconComponent = FeatherIcons[name] || FeatherIcons.AlertCircle;
    return <IconComponent {...props}/>;
};
const DashboardSection = () => {
    // State for each dashboard section
    const [announcements, setAnnouncements] = (0, react_1.useState)({ data: [], loading: true, error: null });
    const [pendingActions, setPendingActions] = (0, react_1.useState)({ data: [], loading: true, error: null });
    const [quickLinks, setQuickLinks] = (0, react_1.useState)({ data: [], loading: true, error: null });
    const [recentDocs, setRecentDocs] = (0, react_1.useState)({ data: [], loading: true, error: null });
    const [upcomingEvents, setUpcomingEvents] = (0, react_1.useState)({ data: [], loading: true, error: null });
    const [isCustomizeModalOpen, setIsCustomizeModalOpen] = (0, react_1.useState)(false); // State for modal visibility
    const navigate = (0, react_router_dom_1.useNavigate)(); // Initialize useNavigate hook
    // --- Mock User Context (Replace with actual context later) ---
    const MOCK_USER_COMPANY_ID = 'SASMOS HET';
    // Fetch data on component mount
    (0, react_1.useEffect)(() => {
        const loadData = () => __awaiter(void 0, void 0, void 0, function* () {
            // Fetch Announcements
            try {
                // Pass mock company ID
                const data = yield (0, dashboardApi_1.fetchAnnouncements)(5, MOCK_USER_COMPANY_ID);
                setAnnouncements({ data, loading: false, error: null });
            }
            catch (_err) { // Prefix unused var with _
                setAnnouncements({ data: [], loading: false, error: 'Failed to load announcements.' });
            }
            // Fetch Pending Actions
            try {
                const data = yield (0, dashboardApi_1.fetchPendingActions)();
                setPendingActions({ data, loading: false, error: null });
            }
            catch (_err) { // Prefix unused var with _
                setPendingActions({ data: [], loading: false, error: 'Failed to load pending actions.' });
            }
            // Fetch Quick Links
            try {
                const data = yield (0, dashboardApi_1.fetchQuickLinks)();
                setQuickLinks({ data, loading: false, error: null });
            }
            catch (_err) { // Prefix unused var with _
                setQuickLinks({ data: [], loading: false, error: 'Failed to load quick links.' });
            }
            // Fetch Recent Documents
            try {
                const data = yield (0, dashboardApi_1.fetchRecentDocuments)();
                setRecentDocs({ data, loading: false, error: null });
            }
            catch (_err) { // Prefix unused var with _
                setRecentDocs({ data: [], loading: false, error: 'Failed to load recent documents.' });
            }
            // Fetch Upcoming Events
            try {
                // Pass mock company ID
                const data = yield (0, dashboardApi_1.fetchUpcomingEvents)(3, MOCK_USER_COMPANY_ID);
                setUpcomingEvents({ data, loading: false, error: null });
            }
            catch (_err) { // Prefix unused var with _
                setUpcomingEvents({ data: [], loading: false, error: 'Failed to load upcoming events.' });
            }
        });
        loadData();
        // Add MOCK_USER_COMPANY_ID to dependency array if it were dynamic
    }, []); // eslint-disable-line react-hooks/exhaustive-deps 
    // --- Helper Functions for Rendering ---
    const getSeverityBorder = (severity) => {
        switch (severity) {
            case 'high': return 'border-red-500';
            case 'medium': return 'border-yellow-500';
            case 'low': return 'border-gray-400'; // Use gray for low
            default: return 'border-gray-500';
        }
    };
    const getActionIcon = (type) => {
        switch (type) {
            case 'Approval': return { component: FeatherIcons.CheckSquare, color: 'text-orange-500' };
            case 'Review': return { component: FeatherIcons.Edit, color: 'text-blue-500' };
            case 'Acknowledgment': return { component: FeatherIcons.ThumbsUp, color: 'text-green-500' };
            case 'Task': return { component: FeatherIcons.Clipboard, color: 'text-purple-500' };
            default: return { component: FeatherIcons.AlertCircle, color: 'text-gray-500' };
        }
    };
    const formatRelativeTime = (isoDate) => {
        try {
            return (0, date_fns_1.formatDistanceToNow)(new Date(isoDate), { addSuffix: true });
        }
        catch (_e) { // Prefix unused var with _
            return 'Invalid date';
        }
    };
    const formatEventDateTime = (startIso, endIso) => {
        try {
            const startDate = new Date(startIso);
            const endDate = endIso ? new Date(endIso) : null;
            const datePart = (0, date_fns_1.format)(startDate, 'MMM d, yyyy');
            const startTimePart = (0, date_fns_1.format)(startDate, 'h:mm a');
            if (endDate && (0, date_fns_1.format)(startDate, 'yyyy-MM-dd') === (0, date_fns_1.format)(endDate, 'yyyy-MM-dd')) {
                // Same day event
                const endTimePart = (0, date_fns_1.format)(endDate, 'h:mm a');
                return `${datePart} • ${startTimePart} - ${endTimePart}`;
            }
            else {
                // Single time or multi-day (just show start time for now)
                return `${datePart} • ${startTimePart}`;
            }
        }
        catch (_e) { // Prefix unused var with _
            return 'Invalid date/time';
        }
    };
    // --- Navigation Handlers (Updated) ---
    const handleViewAll = (section) => {
        // Map section name to a route path (adjust paths as needed)
        let path = '/dashboard'; // Default
        switch (section.toLowerCase()) {
            case 'announcements':
                path = '/communication/announcements';
                break;
            case 'pending actions':
                path = '/actions';
                break; // Or a dedicated actions page
            case 'recent documents':
                path = '/knowledge/documents';
                break;
            case 'upcoming events':
                path = '/communication/events';
                break;
        }
        console.log(`Navigating to View All: ${section} at ${path}`);
        navigate(path);
    };
    const handleCustomize = (section) => {
        if (section === 'Quick Links') {
            setIsCustomizeModalOpen(true); // Open the modal
        }
        else {
            console.log(`Customize action not implemented for: ${section}`);
        }
    };
    // Placeholder for handling save from modal
    const handleSaveQuickLinks = (0, react_1.useCallback)((newLinks) => __awaiter(void 0, void 0, void 0, function* () {
        const toastId = react_hot_toast_1.default.loading('Saving Quick Links...');
        try {
            yield (0, dashboardApi_1.updateQuickLinks)(newLinks);
            setQuickLinks(prevState => (Object.assign(Object.assign({}, prevState), { data: newLinks, loading: false, error: null })));
            setIsCustomizeModalOpen(false);
            react_hot_toast_1.default.success('Quick Links updated!', { id: toastId });
        }
        catch (error) {
            console.error("Failed to save quick links:", error);
            let message = 'Failed to save Quick Links.';
            if (error instanceof Error)
                message = error.message;
            react_hot_toast_1.default.error(message, { id: toastId });
            setQuickLinks(prevState => (Object.assign(Object.assign({}, prevState), { loading: false, error: 'Failed to save links.' })));
        }
    }), []);
    // --- Render Section Content --- 
    const renderSection = (state, renderItem, emptyMessage = "Nothing to display.") => {
        if (state.loading) {
            return <div className="text-center text-gray-500 py-4">Loading...</div>;
        }
        if (state.error) {
            return <div className="text-center text-red-600 py-4">Error: {state.error}</div>;
        }
        if (state.data.length === 0) {
            return <div className="text-center text-gray-500 py-4">{emptyMessage}</div>;
        }
        return <div className="space-y-3">{state.data.map(renderItem)}</div>;
    };
    return (<div className="space-y-6">
      <h1 className="text-2xl font-bold mb-6 mt-4">Welcome to Falcon Hub</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Critical Announcements */}
        <div className="bg-white p-4 rounded shadow col-span-1 md:col-span-3">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Critical Announcements</h2>
            <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => handleViewAll('Announcements')}>
              View All
            </span> 
          </div>
          {renderSection(announcements, (ann) => (<div key={ann.id} className={`border-l-4 ${getSeverityBorder(ann.severity)} pl-3 py-2`}>
                <div className="font-medium">{ann.title}</div>
                <div className="text-sm text-gray-600">{ann.description}</div>
                <div className="text-xs text-gray-500 mt-1 flex items-center flex-wrap">
                  {ann.scope !== 'Group-wide' && (<span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20 mr-2">
                          {ann.scope}
                      </span>)}
                  <span className="mr-2">{formatRelativeTime(ann.publishedAt)}</span>
                  {ann.link && <a href={ann.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Details</a>}
                </div>
              </div>), "No critical announcements right now.")}
        </div>
        
        {/* Pending Actions */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Pending Actions</h2>
            <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => handleViewAll('Pending Actions')}>
              View All
            </span>
          </div>
          {renderSection(pendingActions, (action) => {
            const { component: Icon, color } = getActionIcon(action.type);
            const handleActionClick = (e) => {
                e.preventDefault(); // Prevent default link behavior
                console.log(`Navigating to action: ${action.title} at ${action.link}`);
                navigate(action.link); // Use navigate for internal links
            };
            return (<a href={action.link} key={action.id} onClick={handleActionClick} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <Icon size={16} className={`${color} mr-2 flex-shrink-0`}/>
                  <div>
                    <div className="text-sm font-medium">{action.title}</div>
                    <div className="text-xs text-gray-500">
                      {action.source && `${action.source} • `}
                      {action.details && `${action.details} • `}
                      {action.dueDate ? `Due ${formatRelativeTime(action.dueDate)}` : 'No due date'}
                    </div>
                  </div>
                </a>);
        }, "No pending actions.")}
        </div>
        
        {/* Quick Links */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Quick Links</h2>
            <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => handleCustomize('Quick Links')}>
              Customize
            </span>
          </div>
          {quickLinks.loading && <div className="text-center text-gray-500 py-4">Loading...</div>}
          {quickLinks.error && <div className="text-center text-red-600 py-4">Error: {quickLinks.error}</div>}
          {!quickLinks.loading && !quickLinks.error && (<div className="grid grid-cols-2 gap-2">
              {quickLinks.data.length === 0 && <div className="col-span-2 text-center text-gray-500 py-4">No quick links configured.</div>}
              {quickLinks.data.map(link => {
                const isExternal = link.targetHub === 'external';
                const LinkComponent = isExternal ? 'a' : 'span'; // Use span for internal links handled by onClick
                const handleQuickLinkClick = (e) => {
                    if (!isExternal) {
                        e.preventDefault();
                        console.log(`Navigating to quick link: ${link.title} at ${link.url}`);
                        navigate(link.url);
                    }
                    // External links will behave like normal <a> tags
                };
                return (<LinkComponent href={isExternal ? link.url : undefined} key={link.id} onClick={handleQuickLinkClick} className="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer text-center" target={isExternal ? "_blank" : undefined} rel={isExternal ? "noopener noreferrer" : undefined}>
                        <DynamicFeatherIcon name={link.icon} size={24} className="text-blue-500 mb-1"/>
                        <span className="text-xs">{link.title}</span>
                      </LinkComponent>);
            })}
            </div>)}
        </div>
        
        {/* Recent Documents */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Recent Documents</h2>
             <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => handleViewAll('Recent Documents')}>
               View All
            </span>
          </div>
          {renderSection(recentDocs, (doc) => {
            const handleDocClick = (e) => {
                e.preventDefault();
                console.log(`Navigating to document: ${doc.name} at ${doc.link}`);
                navigate(doc.link);
            };
            return (<a href={doc.link} key={doc.id} onClick={handleDocClick} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                    <FeatherIcons.FileText size={16} className="text-gray-500 mr-2 flex-shrink-0"/>
                    <div>
                      <div className="text-sm font-medium truncate" title={doc.name}>{doc.name}</div>
                      <div className="text-xs text-gray-500">Opened {formatRelativeTime(doc.lastOpenedAt)}</div>
                    </div>
                  </a>);
        }, "No recent documents found.")}
        </div>

        {/* Upcoming Events Section */}
        <div className="bg-white p-4 rounded shadow col-span-1 md:col-span-3">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Upcoming Events</h2>
            <span className="text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => handleViewAll('Upcoming Events')}>
              View All
            </span>
          </div>
          {upcomingEvents.loading && <div className="text-center text-gray-500 py-4">Loading...</div>}
          {upcomingEvents.error && <div className="text-center text-red-600 py-4">Error: {upcomingEvents.error}</div>}
          {!upcomingEvents.loading && !upcomingEvents.error && (<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {upcomingEvents.data.length === 0 && <div className="col-span-1 md:col-span-3 text-center text-gray-500 py-4">No upcoming events.</div>}
              {upcomingEvents.data.map(event => {
                const handleEventClick = (e) => {
                    if (event.link) {
                        e.preventDefault();
                        console.log(`Navigating to event: ${event.title} at ${event.link}`);
                        navigate(event.link);
                    }
                    // If no link, do nothing on click
                };
                return (<a href={event.link || '#'} key={event.id} onClick={handleEventClick} className={`border rounded p-3 hover:shadow block ${event.link ? 'cursor-pointer' : 'cursor-default'}`}>
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm text-gray-600">{formatEventDateTime(event.startDateTime, event.endDateTime)}</div>
                      <div className="text-xs text-gray-500 mt-1 flex items-center">
                        <span className="mr-2">{event.location}</span>•
                        {event.scope !== 'Group-wide' ? (<span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20 ml-2">
                              {event.scope}
                            </span>) : (<span className="inline-flex items-center rounded-md bg-gray-50 px-2 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10 ml-2">
                              Group-wide
                            </span>)}
                      </div>
                    </a>);
            })}
            </div>)}
        </div>
      </div>

      {/* Modal Render */}
      <QuickLinkCustomizeModal_1.default isOpen={isCustomizeModalOpen} onClose={() => setIsCustomizeModalOpen(false)} currentLinks={quickLinks.data} // Pass current links to modal
     onSave={handleSaveQuickLinks} // Pass save handler
    />
    </div>);
};
exports.default = DashboardSection;
//# sourceMappingURL=DashboardSection.js.map