"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.graphConfig = exports.loginRequest = exports.msalConfig = void 0;
const msal_browser_1 = require("@azure/msal-browser");
// MSAL configuration
exports.msalConfig = {
    auth: {
        clientId: "3c911972-81a1-4195-8eee-68f534806b08", // Client ID from Azure App Registration
        authority: "https://login.microsoftonline.com/ecb4a448-4a99-443b-aaff-063150b6c9ea", // Tenant ID
        redirectUri: "/", // Must match Redirect URI configured in Azure App Registration
        postLogoutRedirectUri: "/", // Optional: Where to redirect after logout
    },
    cache: {
        cacheLocation: "sessionStorage", // "localStorage" or "sessionStorage"
        storeAuthStateInCookie: false, // Set to true for IE11 or Edge
    },
    system: {
        loggerOptions: {
            loggerCallback: (level, message, containsPii) => {
                if (containsPii) {
                    return;
                }
                switch (level) {
                    case msal_browser_1.LogLevel.Error:
                        console.error(message);
                        return;
                    case msal_browser_1.LogLevel.Info:
                        // console.info(message); // Avoid excessive logging
                        return;
                    case msal_browser_1.LogLevel.Verbose:
                        // console.debug(message); // Avoid excessive logging
                        return;
                    case msal_browser_1.LogLevel.Warning:
                        console.warn(message);
                        return;
                    default:
                        return;
                }
            },
            logLevel: msal_browser_1.LogLevel.Warning, // Adjust log level as needed (Error, Warning, Info, Verbose)
        },
    },
};
// Add scopes here for ID token to be used at Microsoft identity platform endpoints.
exports.loginRequest = {
    scopes: ["User.Read"] // Basic scope for reading user profile
};
// Add scopes here for access token to be used at Microsoft Graph endpoints.
exports.graphConfig = {
    graphMeEndpoint: "https://graph.microsoft.com/v1.0/me"
};
//# sourceMappingURL=authConfig.js.map