"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDeploymentDate = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
async function UpdateDeploymentDate(request, context) {
    try {
        context.log('UpdateDeploymentDate function triggered');
        // Get database connection
        const pool = await (0, db_1.getPool)();
        // Update specific change requests with deployment dates
        const updateQuery = `
            UPDATE ChangeRequests 
            SET DeploymentDate = CASE 
                WHEN RequestID = 32 THEN '2024-06-15'  -- Database Server Upgrade
                WHEN RequestID = 33 THEN '2024-06-20'  -- Security Patch Deployment  
                WHEN RequestID = 34 THEN '2024-06-10'  -- Network Infrastructure Update
                WHEN RequestID = 35 THEN '2024-06-25'  -- Application Performance Optimization
                WHEN RequestID = 36 THEN '2024-06-28'  -- Backup System Enhancement
                ELSE DeploymentDate
            END
            WHERE RequestID IN (32, 33, 34, 35, 36);
        `;
        const updateResult = await pool.request().query(updateQuery);
        context.log(`Updated ${updateResult.rowsAffected[0]} change requests with deployment dates`);
        // Get the updated results
        const selectQuery = `
            SELECT 
                RequestID, 
                Title, 
                Status, 
                Priority, 
                DeploymentDate,
                RequestedCompletionDate
            FROM ChangeRequests 
            WHERE RequestID IN (32, 33, 34, 35, 36)
            ORDER BY DeploymentDate;
        `;
        const selectResult = await pool.request().query(selectQuery);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: `Updated ${updateResult.rowsAffected[0]} change requests with June deployment dates`,
                updatedCount: updateResult.rowsAffected[0],
                changeRequests: selectResult.recordset
            }
        };
    }
    catch (error) {
        context.log('Error in UpdateDeploymentDate:', error);
        return {
            status: 500,
            jsonBody: {
                error: 'Failed to update deployment dates',
                details: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}
exports.UpdateDeploymentDate = UpdateDeploymentDate;
// Register the function
functions_1.app.http('UpdateDeploymentDate', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'update-deployment-dates',
    handler: UpdateDeploymentDate
});
//# sourceMappingURL=index.js.map