"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteChangeRequestDraft = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const cors_1 = require("../shared/cors");
async function DeleteChangeRequestDraft(request, context) {
    context.log('DeleteChangeRequestDraft function processed a request.');
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: ''
        });
    }
    try {
        // Get draft ID from query parameters
        const draftId = request.query.get('draftId');
        const userId = request.query.get('userId');
        if (!draftId || !userId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'Draft ID and User ID are required'
                }
            });
        }
        // Get database connection
        const pool = await (0, db_1.getPool)();
        // Delete the draft (only if owned by the user)
        const deleteQuery = `
      DELETE FROM ChangeRequestDrafts 
      WHERE DraftID = @draftId AND RequestedBy = @userId
    `;
        const result = await pool.request()
            .input('draftId', draftId)
            .input('userId', parseInt(userId))
            .query(deleteQuery);
        if (result.rowsAffected[0] === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                jsonBody: {
                    success: false,
                    message: 'Draft not found or you do not have permission to delete it'
                }
            });
        }
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            jsonBody: {
                success: true,
                message: 'Draft deleted successfully'
            }
        });
    }
    catch (error) {
        context.log('Error deleting draft:', error);
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            jsonBody: {
                success: false,
                message: 'Internal server error while deleting draft',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
}
exports.DeleteChangeRequestDraft = DeleteChangeRequestDraft;
functions_1.app.http('DeleteChangeRequestDraft', {
    methods: ['DELETE', 'OPTIONS'],
    authLevel: 'function',
    handler: DeleteChangeRequestDraft
});
//# sourceMappingURL=index.js.map