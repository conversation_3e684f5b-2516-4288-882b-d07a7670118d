import { useState, useEffect, useRef } from 'react';

function ToolIssueCreation() {
  const [locationdrpdwn_data, setlocationdata] = useState([]);
  const [categorydata, setcatdata] = useState('');
  const [location_seleted, setSelectedLocation] = useState('');
  const [cat_selected, setcatval] = useState('');
  const [tool_no_creation, settoolno] = useState('');
  const [error, setError] = useState('');
  const [tool_description, setToolDescription] = useState('');
  const [tool_model_no, setToolModelNo] = useState('');
  const [tool_validation_date, setToolValidationDate] = useState('');
  const [tool_validation_months, setToolValidationMonths] = useState('');
  const [fg_part_number, setSelectedFgPartNumber] = useState('');
  const [receiver_employee_id, setReceiverEmployeeId] = useState('');
  const [issuer_employee_id, setIssuerEmployeeId] = useState('');
  const [issue_date, setIssueDate] = useState('');
  const [tool_status, setToolStatus] = useState('');
  const [tool_remarks, setToolRemarks] = useState('');
  const [project_fg_masters, setprojectfgmaster] = useState([]);
  const [employee_data, setempdetails] = useState([]);
  const [getexistingdata, setexistingdetails] = useState([]);
  const [selectedToolId, setSelectedToolId] = useState('');
  const [searchToolNo, setSearchToolNo] = useState('');
  const formRef = useRef(null);

  const status_array = ["IN-USE", "NOT IN-USE", "SCRAP"];
  const validate_months = ["3", "6", "9", "12", "24", "NA"];

  useEffect(() => {
    fetch('http://localhost:3000/api/tool-locations')
      .then(response => response.json())
      .then(data => setlocationdata(data))
      .catch(error => console.error('Error fetching tool locations:', error));
  }, []);

  const location_drpdwn = locationdrpdwn_data["SASMOS"] || {};

  useEffect(() => {
    fetch('http://localhost:3000/api/tool-categories')
      .then(response => response.json())
      .then(data => setcatdata(data))
      .catch(error => console.error('Error fetching tool categories:', error));
  }, []);

  useEffect(() => {
    if (location_seleted && cat_selected) {
      fetch(`http://localhost:3000/api/tool_no?tool_location_code=${location_seleted}&tool_category_code=${cat_selected}`)
        .then(response => response.json())
        .then(data => {
          settoolno(data.tool_no || '');
          setError('');
        })
        .catch(error => {
          console.error('Error fetching tool number:', error);
          setError('Failed to fetch tool number');
        });
    }
  }, [location_seleted, cat_selected]);

  useEffect(() => {
    fetch(`http://localhost:3000/api/project_fg_master`)
      .then(response => response.json())
      .then(data => setprojectfgmaster(data.project_fg_master_table_array))
      .catch(error => console.error("Error fetching project FG master:", error));
  }, []);

  useEffect(() => {
    fetch(`http://localhost:3000/api/get_active_employees`)
      .then(response => response.json())
      .then(data => setempdetails(data))
      .catch(error => console.error("Error fetching employees:", error));
  }, []);

  useEffect(() => {
    fetch(`http://localhost:3000/api/get_existing_tt_detail`)
      .then(response => response.json())
      .then(data => setexistingdetails(data))
      .catch(error => console.error("Error fetching existing tool details:", error));
  }, []);

  const Getlocationselect = (e) => {
    setSelectedLocation(e.target.value);
  };

  const Getcatselected = (e) => {
    setcatval(e.target.value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      tool_location_code: location_seleted,
      tool_category: cat_selected,
      tool_no: tool_no_creation,
      tool_description,
      tool_model_no,
      tool_validation_date,
      tool_validation_months,
      fg_part_number,
      receiver_employee_id,
      issuer_employee_id,
      issue_date,
      tool_status,
      tool_remarks,
      project_number: fg_part_number ? fg_part_number.split('-')[0] : '',
      tool_id_update: selectedToolId
    };

    fetch('http://localhost:3000/api/submit_tool', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })
      .then(response => {
        if (!response.ok) throw new Error('Failed to submit tool');
        return response.json();
      })
      .then(() => {
        alert('Tool submitted successfully');
        setSelectedLocation('');
        setcatval('');
        settoolno('');
        setToolDescription('');
        setToolModelNo('');
        setToolValidationDate('');
        setToolValidationMonths('');
        setSelectedFgPartNumber('');
        setReceiverEmployeeId('');
        setIssuerEmployeeId('');
        setIssueDate('');
        setToolStatus('');
        setToolRemarks('');
        setSelectedToolId('');
        fetch(`http://localhost:3000/api/get_existing_tt_detail`)
          .then(response => response.json())
          .then(data => setexistingdetails(data));
      })
      .catch(error => {
        console.error('Error submitting tool:', error);
        setError('Error submitting tool');
      });
  };

  const handleToolNoClick = (toolId) => {
    fetch(`http://localhost:3000/api/get_tool_details?tool_id=${toolId}`)
      .then(response => {
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        return response.json();
      })
      .then(data => {
        if (data.error) throw new Error(data.error);
        setSelectedToolId(toolId);
        setSelectedLocation(data.tool_location_code || '');
        setcatval(data.tool_category_code || '');
        settoolno(data.tool_no || '');
        setToolDescription(data.tool_description || '');
        setToolModelNo(data.tool_model_no || '');
        setToolValidationDate(data.tool_validation_date || '');
        setToolValidationMonths(data.tool_validation_months || '');
        setSelectedFgPartNumber(data.fg_part_number || '');
        setReceiverEmployeeId(data.receiver_employee_id || '');
        setIssuerEmployeeId(data.issuer_employee_id || '');
        setIssueDate(data.issue_date || '');
        setToolStatus(data.tool_status || '');
        setToolRemarks(data.tool_remarks || '');
        setError('');
        formRef.current?.scrollIntoView({ behavior: 'smooth' });
      })
      .catch(error => {
        console.error('Error fetching tool details:', error.message);
        setError(`Failed to fetch tool details: ${error.message}`);
      });
  };

  const filteredTools = getexistingdata.filter(tool =>
    tool.tool_no.toLowerCase().includes(searchToolNo.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold text-center text-blue-800 mb-8">🛠️ TOOL CREATION</h1>

      <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
        {error && <div className="text-red-500 mb-4">{error}</div>}

        <form onSubmit={handleSubmit} ref={formRef}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Location</label>
              <select className="w-full border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" onChange={Getlocationselect} value={location_seleted}>
                <option value="">Select Location</option>
                {Object.entries(location_drpdwn).map(([key, values]) => (
                  <option key={key} value={key}>{key} - {String(values)}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Category</label>
              <select className="w-full border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" onChange={Getcatselected} value={cat_selected}>
                <option value="">Select Category</option>
                {Object.entries(categorydata).map(([key, values]) => (
                  <option key={key} value={key}>{key} - {values}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool No</label>
              <input type="text" value={tool_no_creation} readOnly className="w-full border rounded-lg px-4 py-2 bg-gray-100" />
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Description</label>
              <textarea className="w-full border rounded-lg px-4 py-2 h-24" value={tool_description} onChange={(e) => setToolDescription(e.target.value)}></textarea>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Model No</label>
              <input type="text" className="w-full border rounded-lg px-4 py-2" value={tool_model_no} onChange={(e) => setToolModelNo(e.target.value)} />
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Validation Date</label>
              <input type="date" className="w-full border rounded-lg px-4 py-2" value={tool_validation_date} onChange={(e) => setToolValidationDate(e.target.value)} />
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Tool Validity Months</label>
              <select className="w-full border rounded-lg px-4 py-2" value={tool_validation_months} onChange={(e) => setToolValidationMonths(e.target.value)}>
                <option value="">Select Months</option>
                {validate_months.map((item, index) => (
                  <option key={index} value={item}>{item + " - months"}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">FG Part Issued To</label>
              <select
                required
                name="fg_part_number"
                className="w-full border rounded-lg px-4 py-2"
                value={fg_part_number}
                onChange={(e) => setSelectedFgPartNumber(e.target.value)}
              >
                <option value=""></option>
                <option value="NA">NA</option>
                {Object.entries(project_fg_masters).map(([project_number, part_numbers]) => (
                  <optgroup key={project_number} label={project_number}>
                    {part_numbers.map((part, idx) => (
                      <option key={idx} value={part}>{part}</option>
                    ))}
                  </optgroup>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Issue To</label>
              <select className="w-full border rounded-lg px-4 py-2" value={receiver_employee_id} onChange={(e) => setReceiverEmployeeId(e.target.value)}>
                <option></option>
                {employee_data.map((emp, index) => (
                  <option key={index} value={emp.employee_id}>{emp.employee_name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Issued Date</label>
              <input type="date" className="w-full border rounded-lg px-4 py-2" value={issue_date} onChange={(e) => setIssueDate(e.target.value)} />
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Issued By</label>
              <select className="w-full border rounded-lg px-4 py-2" value={issuer_employee_id} onChange={(e) => setIssuerEmployeeId(e.target.value)}>
                <option></option>
                {employee_data.map((emp, index) => (
                  <option key={index} value={emp.employee_id}>{emp.employee_name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-semibold mb-1 text-gray-700">Status</label>
              <select className="w-full border rounded-lg px-4 py-2" value={tool_status} onChange={(e) => setToolStatus(e.target.value)}>
                <option></option>
                {status_array.map((status, index) => (
                  <option key={index} value={status}>{status}</option>
                ))}
              </select>
            </div>

            <div className="col-span-1 md:col-span-2 lg:col-span-3">
              <label className="block text-sm font-semibold mb-1 text-gray-700">Remarks</label>
              <textarea className="w-full border rounded-lg px-4 py-2 h-24" value={tool_remarks} onChange={(e) => setToolRemarks(e.target.value)} />
            </div>
          </div>

          <div className="mt-6">
            <button type="submit" className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
              {selectedToolId ? 'Update Tool' : 'Submit Tool'}
            </button>
          </div>
        </form>

        <details className="mt-10 bg-gray-100 p-4 rounded-lg shadow">
          <summary className="text-blue-600 font-semibold cursor-pointer mb-4">🔽 Show/Hide Existing Tool Details</summary>

          <div className="mb-4">
            <input
              type="text"
              placeholder="🔍 Search Tool No"
              className="w-full md:w-1/3 border px-4 py-2 rounded-lg shadow"
              value={searchToolNo}
              onChange={(e) => setSearchToolNo(e.target.value)}
            />
          </div>

          <div className="max-h-[500px] overflow-auto border rounded-lg">
            <table className="min-w-[1200px] text-sm border-collapse w-full">
              <thead className="sticky top-0 bg-gray-200 z-10 text-gray-700 shadow">
                <tr>
                  <th className="px-4 py-2">SL #</th>
                  <th className="px-4 py-2">Tool CATEGORY</th>
                  <th className="px-4 py-2">Tool Location</th>
                  <th className="px-4 py-2">FG PARTNUMBER</th>
                  <th className="px-4 py-2">Tool No</th>
                  <th className="px-4 py-2">Tool Description</th>
                  <th className="px-4 py-2">Tool Model No</th>
                  <th className="px-4 py-2">Issue To</th>
                  <th className="px-4 py-2">Issued Date</th>
                  <th className="px-4 py-2">Issued By</th>
                  <th className="px-4 py-2">VALIDATION DATE</th>
                  <th className="px-4 py-2">RE-VALIDATION DATE</th>
                  <th className="px-4 py-2">VALIDATION MONTHS</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2">Remarks</th>
                </tr>
              </thead>
              <tbody>
                {filteredTools.map((item, index) => (
                  <tr key={index} className="odd:bg-white even:bg-gray-50">
                    <td className="px-4 py-2">{index + 1}</td>
                    <td className="px-4 py-2">{item.tool_category}</td>
                    <td className="px-4 py-2">{item.tool_location}</td>
                    <td className="px-4 py-2">{item.fg_part_number}</td>
                    <td className="px-4 py-2">
                      <button className="text-blue-500 hover:underline" onClick={() => handleToolNoClick(item.tool_id)}>
                        {item.tool_no}
                      </button>
                    </td>
                    <td className="px-4 py-2">{item.tool_description}</td>
                    <td className="px-4 py-2">{item.tool_model_no}</td>
                    <td className="px-4 py-2">{item.receiver_employee_name}</td>
                    <td className="px-4 py-2">{item.issue_date}</td>
                    <td className="px-4 py-2">{item.issuer_employee_name}</td>
                    <td className="px-4 py-2">{item.tool_validation_date}</td>
                    <td className="px-4 py-2">{item.tool_revalidation_date}</td>
                    <td className="px-4 py-2">{item.tool_validation_months}</td>
                    <td className="px-4 py-2">{item.tool_status}</td>
                    <td className="px-4 py-2">{item.tool_remarks}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </details>
      </div>
    </div>
  );
}

export default ToolIssueCreation;
