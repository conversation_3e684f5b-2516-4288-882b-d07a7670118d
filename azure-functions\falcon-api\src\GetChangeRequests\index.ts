import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";

export async function getChangeRequests(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('GetChangeRequests function invoked.');
    
    try {
        // Parse query parameters
        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get('page') || '1');
        const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
        const offset = (page - 1) * pageSize;
        
        // Filter parameters
        const status = url.searchParams.get('status');
        const priority = url.searchParams.get('priority');
        const search = url.searchParams.get('search');

        // Build WHERE conditions
        const whereConditions: string[] = [];
        
        if (status) {
            whereConditions.push(`cr.Status = '${status.replace(/'/g, "''")}'`);
        }
        
        if (priority) {
            whereConditions.push(`cr.Priority = '${priority.replace(/'/g, "''")}'`);
        }
        
        if (search) {
            const searchTerm = search.replace(/'/g, "''");
            whereConditions.push(`(
                cr.Title LIKE '%${searchTerm}%' OR 
                cr.Description LIKE '%${searchTerm}%' OR 
                cr.RequestNumber LIKE '%${searchTerm}%' OR
                CONCAT(requester.FirstName, ' ', requester.LastName) LIKE '%${searchTerm}%'
            )`);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        // Query to get change requests with proper column names
        const query = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.ExpectedBenefit as expectedBenefit,
                cr.RequestedCompletionDate as requestedCompletionDate,
                ISNULL(cr.DevelopmentProgress, 0) as developmentProgress,
                cr.PlannedStartDate as plannedStartDate,
                cr.PlannedCompletionDate as plannedCompletionDate,
                cr.ActualStartDate as actualStartDate,
                cr.ActualCompletionDate as actualCompletionDate,
                cr.CreatedDate as createdDate,
                cr.ApprovedDate as approvedDate,
                crt.TypeName as typeName,
                crt.Description as typeDescription,
                ISNULL(crt.EstimatedDays, 0) as estimatedDays,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                d.DepartmentName as departmentName,
                CONCAT(approver.FirstName, ' ', approver.LastName) as approverName,
                CONCAT(developer.FirstName, ' ', developer.LastName) as developerName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
                LEFT JOIN Departments d ON cr.DepartmentID = d.DepartmentID
                LEFT JOIN Users approver ON cr.ApprovedBy = approver.UserID
                LEFT JOIN Users developer ON cr.AssignedToDevID = developer.UserID
            ${whereClause}
            ORDER BY cr.CreatedDate DESC
            OFFSET ${offset} ROWS
            FETCH NEXT ${pageSize} ROWS ONLY
        `;

        // Count query for pagination
        const countQuery = `
            SELECT COUNT(*) as totalCount
            FROM ChangeRequests cr
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
            ${whereClause}
        `;

        // Execute both queries
        const [result, countResult] = await Promise.all([
            executeQuery(query),
            executeQuery(countQuery)
        ]);

        const totalCount = countResult.recordset[0].totalCount;
        const totalPages = Math.ceil(totalCount / pageSize);

        const response = {
            data: result.recordset,
            pagination: {
                page: page,
                pageSize: pageSize,
                totalCount: totalCount,
                totalPages: totalPages,
                hasNext: page < totalPages,
                hasPrevious: page > 1
            }
        };

        context.log(`Retrieved ${result.recordset.length} change requests (page ${page} of ${totalPages})`);

        return {
            status: 200,
            jsonBody: response
        };

    } catch (error) {
        context.error('Error in GetChangeRequests:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while fetching change requests'
                }
            }
        };
    }
}

app.http('GetChangeRequests', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'change-requests',
    handler: getChangeRequests
}); 