{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../azure-functions/falcon-api/CreateRole/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAIA,gCA0EC;AA9ED,gDAAyF;AACzF,qCAA4C;AAG5C,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,OAAO,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAExE,IAAI,IAAS,CAAC;QACd,IAAI,CAAC;YACD,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACzD,CAAC;QACN,CAAC;QAED,6FAA6F;QAC7F,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;QAEzD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;YAChC,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;aAClD,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,qCAAqC;YACrC,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,kBAAkB,GAAG,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,EAAE,KAAI,IAAI,CAAC;YACvD,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,cAAc;YAEnC,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,8EAA8E,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC,CAAC;YACtJ,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,eAAe,mBAAmB,EAAE;iBAC/E,CAAC;YACN,CAAC;YAED,iEAAiE;YACjE,MAAM,KAAK,GAAG;;;;SAIb,CAAC;YACF,MAAM,MAAM,GAAG,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,kBAAkB,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;YAExG,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAEjD,iEAAiE;YACjE,MAAM,OAAO,GAAG;gBACZ,EAAE,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACzC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ;gBAClC,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW;aAC/C,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAE1D,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,OAAO;aACpB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YACxF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;iBAC/E;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}