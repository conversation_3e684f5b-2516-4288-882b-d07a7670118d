import { useState } from "react";

function CreateKaizen() {
  const [enabled, setEnabled] = useState(false);

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-7xl mx-auto bg-white rounded-xl shadow-md p-6">
        <h1 className="text-xl font-bold text-center text-blue-800 mb-6">
          NEW KAIZEN
        </h1>

        <div className="flex flex-wrap items-center gap-4 text-sm font-medium">
          <div className="flex items-center gap-2">
            <span className="bg-blue-100 px-3 py-1 rounded">Document Class:</span>
            <span className="bg-white px-3 py-1 border rounded">RESTRICTED</span>
          </div>

          <div className="flex items-center gap-2">
            <span className="bg-blue-100 px-3 py-1 rounded">Export Control:</span>
            <button
              onClick={() => setEnabled(!enabled)}
              className={`w-12 h-6 flex items-center rounded-full p-1 transition-colors duration-300 ${
                enabled ? "bg-red-500" : "bg-green-400"
              }`}
            >
              <div
                className={`bg-white w-4 h-4 rounded-full shadow-md transform transition-transform duration-300 ${
                  enabled ? "translate-x-6" : "translate-x-0"
                }`}
              />
            </button>
            <span>{enabled ? "YES" : "NO"}</span>
          </div>

          <div className="flex items-center gap-2">
            <span className="bg-blue-100 px-3 py-1 rounded">Distribution List:</span>
            <span className="bg-white px-3 py-1 border rounded">SASMOS / FE-SIL EMPLOYEES</span>
          </div>

          <div className="flex items-center gap-2">
            <span className="bg-blue-100 px-3 py-1 rounded">Format No:</span>
            <span className="bg-white px-3 py-1 border rounded">
              001-029 FM Rev F Dtd 19-04-2022
            </span>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-4 text-sm font-medium">
            <div className="flex items-center gap-2">
                <span className="bg-white border rounded text-xs text-gray-600 p-3 block leading-relaxed">
                    <strong>*DISCLAIMER*</strong> This document is for the sole use of the intended recipient(s) and may contain confidential and privileged information.
                    If you are not the intended recipient(s), please return to the issuer and destroy all copies of the original document.
                    Any unauthorized review, use, disclosure, dissemination, forwarding, printing or copying of this document,
                    and/or any action taken in reliance on the contents of this document is strictly prohibited and may be unlawful.
                    SASMOS accepts no liability for any damage caused by use of this document.
                </span>
            </div>
        </div>
        <div>
            <div className="flex flex-wrap items-center gap-4 text-sm font-medium">
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">PLATFORM</span>
                    <span>
                        <select name="platform_drpdwn" id="platform_drpdwn" className="rounded px-6 py-2">
                            <option value="">-select-</option>
                        </select>
                    </span>
                </div>
                 <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">TECHNOLOGY</span>
                    <span>
                        <select name="technology_drpdwn" id="technology_drpdwn" className="rounded px-6 py-2">
                            <option value="">-select-</option>
                        </select>
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">BUSINESS UNIT</span>
                    <span>
                        <select name="bu_drpdwn" id="bu_drpdwn" className="rounded px-6 py-2">
                            <option value="">-select-</option>
                        </select>
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">IPT</span>
                    <span>
                        <select name="ipt_drpdwn" id="ipt_drpdwn" className="rounded px-6 py-2">
                            <option value="">-select-</option>
                        </select>
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">Kaizen Theme / Subject</span>
                    <span>
                        <textarea name="kaizen_txtarea" id="" className=""></textarea>
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">Present Status/ Problem Description</span>
                    <span>
                        <textarea name="present_status" id="present_status" className=""></textarea>
                    </span>
                </div>
                 <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded">Root Cause / Analysis Details</span>
                    <span>
                        <textarea name="root_cause" id="root_cause" className=""></textarea>
                    </span>
                </div> <div className="flex items-center gap-2">
                    <span className="px-3 py-1 rounded"> Action / Change Description</span>
                    <span>
                        <textarea name="change" id="present_status" className=""></textarea>
                    </span>
                </div>

            </div>
        </div>
      </div>
    </div>
  );
}

export default CreateKaizen;
