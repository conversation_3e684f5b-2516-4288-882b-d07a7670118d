"use client"

import { useState, useEffect } from "react"
// import * as FeatherIcons from 'feather-icons-react'; // Unused import

import { PlusCircle, Edit, RefreshCcw, CheckCircle,View,User } from 'lucide-react'

function Sidebar_Va({ isOpen, isMobile: _isMobile }) {
  const [activeItem, setActiveItem] = useState("")

  useEffect(() => {
    const hash = window.location.hash.replace("#/", "")
    setActiveItem(`#/${hash}`)
  }, [])

  const nav_items = [
    { name: "CREATE", icon: <PlusCircle size={20} />, href: "#/create" },
    { name: "EDIT", icon: <Edit size={20} />, href: "#/edit" },
    { name: "UPDATE", icon: <RefreshCcw size={20} />, href: "#/update" },
    { name: "APPROVAL", icon: <CheckCircle size={20} />, href: "#/approval" },
    {name:"MIS", icon:<View size={20}/>,href:"#/mis"},
    {name:"USER RIGHTS" , icon:<User size={20}/>,href:"#/userrights"},

  ]

  return (
    <div className={`flex flex-col h-full py-6 bg-gradient-to-b from-gray-800 to-gray-900 text-white transition-all duration-300 ease-in-out`}>
      {/* Title */}
      <div className={`px-6 py-4 text-xl font-bold mb-8 border-b border-gray-700 pb-4 ${!isOpen ? "text-center text-base px-0" : ""}`}>
        {isOpen ? "Visual Alert" : "VA"}
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 px-2">
        <ul className="space-y-2">
          {nav_items.map((item, index) => {
            const isActive = activeItem === item.href
            return (
              <li key={index}>
                <a
                  href={item.href}
                  onClick={(e) => {
                    e.preventDefault()
                    window.location.hash = item.href.replace("#", "")
                    setActiveItem(item.href)
                  }}
                  className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-200 ${
                    isActive ? "bg-blue-700 shadow-lg" : "hover:bg-blue-600 hover:shadow-md"
                  }`}
                >
                  <span className="text-white">{item.icon}</span>
                  {isOpen && <span className="font-medium truncate">{item.name}</span>}
                </a>
              </li>
            )
          })}
        </ul>
      </nav>
    </div>
  )
}

export default Sidebar_Va
