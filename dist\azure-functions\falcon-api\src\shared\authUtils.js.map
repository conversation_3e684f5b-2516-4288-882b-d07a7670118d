{"version": 3, "file": "authUtils.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/shared/authUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;AAsBA,gDAoBC;AAUD,0CAoBC;AAQD,0BAEC;AAUD,wDA6CC;AAxID,2CAAwC;AACxC,6BAA6B;AAC7B,6BAAoD;AACpD,4EAAuE;AAYvE;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,OAAoB;IACnD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,eAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAChE,MAAM,SAAS,GAAoB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvD,mBAAmB;QACnB,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACzD,eAAM,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;YACpG,OAAO,IAAI,CAAC;QACjB,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,eAAM,CAAC,KAAK,CAAC,iEAAiE,EAAE,KAAK,CAAC,CAAC;QACvF,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,SAAiC,EAAE,aAAuB;IACtF,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,OAAO,KAAK,CAAC,CAAC,oBAAoB;IACtC,CAAC;IACD,mEAAmE;IACnE,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QACjE,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,uEAAuE;IACvE,uEAAuE;IACvE,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC9B,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC;YACjD,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CACpC,EAAE,CAAC;YACA,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,SAAiC;IACrD,OAAO,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;GAOG;AACH,SAAsB,sBAAsB,CAAC,SAAiC,EAAE,OAA0B;;;QACtG,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,uCAAuC;QACvC,MAAM,QAAQ,GAAG,MAAA,SAAS,CAAC,MAAM,0CAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,+DAA+D,CAAC,CAAC;QAChI,MAAM,OAAO,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,CAAC;QAE9B,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;YACzF,eAAM,CAAC,IAAI,CAAC,2EAA2E,EAAE,SAAS,CAAC,CAAC;YACpG,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC;YACD,MAAM,KAAK,GAAG;;;SAGb,CAAC;YACF,MAAM,MAAM,GAAqB;gBAC7B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;aAC1D,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAEjD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrD,eAAM,CAAC,IAAI,CAAC,kDAAkD,OAAO,yBAAyB,CAAC,CAAC;gBAChG,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAE1C,4EAA4E;YAC5E,IAAA,2CAAmB,EAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACpC,eAAM,CAAC,KAAK,CAAC,qEAAqE,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;gBAClG,qDAAqD;YACzD,CAAC,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,6DAA6D,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,eAAM,CAAC,KAAK,CAAC,6DAA6D,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7F,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;CAAA"}