  if (filters.search) {
    whereClause[Op.or] = [
      { Title: { [Op.like]: `%${filters.search}%` } },
      { Description: { [Op.like]: `%${filters.search}%` } },
      { FileName: { [Op.like]: `%${filters.search}%` } }
    ];
  }
  
  try {
    // Get total count for pagination
    const totalCount = await Document.count({ where: whereClause });
    
    // Fetch documents with associations
    const documents = await Document.findAll({
      where: whereClause,
      include: [
        { model: DocumentCategory, attributes: ['CategoryID', 'CategoryName'] },
        { model: Company, attributes: ['CompanyID', 'CompanyName'] },
        { model: Department, attributes: ['DepartmentID', 'DepartmentName'] },
        { model: User, as: 'Creator', attributes: ['UserID', 'FirstName', 'LastName'] }
      ],
      limit,
      offset,
      order: [[sortField, sortOrder]]
    });
    
    return {
      documents,
      totalCount
    };
  } catch (error) {
    logger.error('Error in listDocuments service', error);
    throw error;
  }
};

export const getDocumentById = async (documentId: number) => {
  try {
    const document = await Document.findOne({
      where: {
        DocumentID: documentId,
        IsActive: true
      },
      include: [
        { model: DocumentCategory, attributes: ['CategoryID', 'CategoryName'] },
        { model: Company, attributes: ['CompanyID', 'CompanyName'] },
        { model: Department, attributes: ['DepartmentID', 'DepartmentName'] },
        { model: User, as: 'Creator', attributes: ['UserID', 'FirstName', 'LastName'] },
        { model: User, as: 'Modifier', attributes: ['UserID', 'FirstName', 'LastName'] }
      ]
    });
    
    return document;
  } catch (error) {
    logger.error(`Error in getDocumentById service for ID ${documentId}`, error);
    throw error;
  }
};

export const createDocument = async (documentData: any, userId: number) => {
  try {
    // Create document record
    const document = await Document.create({
      ...documentData,
      CreatedBy: userId,
      CreatedDate: new Date()
    });
    
    return document;
  } catch (error) {
    logger.error('Error in createDocument service', error);
    throw error;
  }
};

export const updateDocument = async (documentId: number, documentData: any, userId: number) => {
  try {
    const document = await Document.findByPk(documentId);
    
    if (!document) {
      throw new Error('Document not found');
    }
    
    // Update document record
    await document.update({
      ...documentData,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    });
    
    return await getDocumentById(documentId);
  } catch (error) {
    logger.error(`Error in updateDocument service for ID ${documentId}`, error);
    throw error;
  }
};

export const deleteDocument = async (documentId: number, userId: number) => {
  try {
    const document = await Document.findByPk(documentId);
    
    if (!document) {
      throw new Error('Document not found');
    }
    
    // Soft delete by marking as inactive
    await document.update({
      IsActive: false,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    });
    
    return true;
  } catch (error) {
    logger.error(`Error in deleteDocument service for ID ${documentId}`, error);
    throw error;
  }
};
```

## 8. Route Implementation

```typescript
// src/routes/documentRoutes.ts
import { Router } from 'express';
import { getDocuments, getDocument, createDocument, updateDocument, deleteDocument, downloadDocument } from '../controllers/documentController';
import { createDocumentValidator, updateDocumentValidator, getDocumentsValidator } from '../validators/documentValidator';
import { validateRequest } from '../middlewares/validation';
import { authenticateJwt } from '../middlewares/auth';
import { requireRole } from '../middlewares/roleCheck';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateJwt);

// GET /v1/documents
router.get(
  '/',
  getDocumentsValidator,
  validateRequest,
  getDocuments
);

// GET /v1/documents/:documentId
router.get(
  '/:documentId',
  validateRequest,
  getDocument
);

// GET /v1/documents/:documentId/content
router.get(
  '/:documentId/content',
  validateRequest,
  downloadDocument
);

// POST /v1/documents
router.post(
  '/',
  requireRole(['ContentCreator', 'Administrator']),
  createDocumentValidator,
  validateRequest,
  createDocument
);

// PUT /v1/documents/:documentId
router.put(
  '/:documentId',
  requireRole(['ContentCreator', 'Administrator']),
  updateDocumentValidator,
  validateRequest,
  updateDocument
);

// DELETE /v1/documents/:documentId
router.delete(
  '/:documentId',
  requireRole(['Administrator']),
  validateRequest,
  deleteDocument
);

export default router;
```

## 9. Error Handling and Validation

### 9.1 Validation Middleware

```typescript
// src/middlewares/validation.ts
import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

export const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(422).json({
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Invalid request data',
        details: errors.array()
      }
    });
  }
  
  next();
};
```

### 9.2 Error Handling Middleware

```typescript
// src/middlewares/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export class ApiError extends Error {
  statusCode: number;
  code: string;
  details?: any;

  constructor(statusCode: number, code: string, message: string, details?: any) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

export const errorHandler = (
  err: Error | ApiError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Generate a unique request ID for tracking
  const requestId = req.headers['x-request-id'] || `req-${Date.now()}`;
  
  // Format the error response
  if (err instanceof ApiError) {
    logger.error(`[${requestId}] ${err.code}: ${err.message}`, err.details);
    
    return res.status(err.statusCode).json({
      error: {
        code: err.code,
        message: err.message,
        details: err.details,
        timestamp: new Date().toISOString(),
        requestId
      }
    });
  }
  
  // For unexpected errors
  logger.error(`[${requestId}] Unexpected error: ${err.message}`, err);
  
  return res.status(500).json({
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      requestId
    }
  });
};
```

## 10. Integration Services Implementation

### 10.1 FreshService Integration Example

```typescript
// src/services/freshServiceIntegration.ts
import axios from 'axios';
import { logger } from '../utils/logger';
import config from '../config/freshService';

// Create FreshService API client
const freshServiceClient = axios.create({
  baseURL: config.apiUrl,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Basic ${Buffer.from(`${config.apiKey}:X`).toString('base64')}`
  }
});

// Log requests and responses
freshServiceClient.interceptors.request.use(request => {
  logger.debug('FreshService API request', { 
    method: request.method, 
    url: request.url 
  });
  return request;
});

freshServiceClient.interceptors.response.use(
  response => {
    logger.debug('FreshService API response', { 
      status: response.status,
      statusText: response.statusText
    });
    return response;
  },
  error => {
    logger.error('FreshService API error', { 
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

// Ticket management functions
export const getTickets = async (filters: any = {}) => {
  try {
    const response = await freshServiceClient.get('/tickets', {
      params: filters
    });
    return response.data;
  } catch (error) {
    logger.error('Error fetching tickets from FreshService', error);
    throw new Error('Failed to fetch tickets from FreshService');
  }
};

export const getTicketById = async (ticketId: number) => {
  try {
    const response = await freshServiceClient.get(`/tickets/${ticketId}`);
    return response.data;
  } catch (error) {
    logger.error(`Error fetching ticket ${ticketId} from FreshService`, error);
    throw new Error(`Failed to fetch ticket ${ticketId} from FreshService`);
  }
};

export const createTicket = async (ticketData: any) => {
  try {
    const response = await freshServiceClient.post('/tickets', {
      ticket: {
        subject: ticketData.subject,
        description: ticketData.description,
        email: ticketData.email,
        priority: ticketData.priority || 2,
        status: 2, // Open
        custom_fields: {
          company_id: ticketData.companyId,
          department_id: ticketData.departmentId
        }
      }
    });
    return response.data;
  } catch (error) {
    logger.error('Error creating ticket in FreshService', error);
    throw new Error('Failed to create ticket in FreshService');
  }
};

export const updateTicket = async (ticketId: number, ticketData: any) => {
  try {
    const response = await freshServiceClient.put(`/tickets/${ticketId}`, {
      ticket: ticketData
    });
    return response.data;
  } catch (error) {
    logger.error(`Error updating ticket ${ticketId} in FreshService`, error);
    throw new Error(`Failed to update ticket ${ticketId} in FreshService`);
  }
};

export const addTicketNote = async (ticketId: number, note: string, isPrivate: boolean = false) => {
  try {
    const response = await freshServiceClient.post(`/tickets/${ticketId}/notes`, {
      body: note,
      private: isPrivate
    });
    return response.data;
  } catch (error) {
    logger.error(`Error adding note to ticket ${ticketId} in FreshService`, error);
    throw new Error(`Failed to add note to ticket ${ticketId} in FreshService`);
  }
};
```

### 10.2 Blob Storage Service

```typescript
// src/services/blobStorageService.ts
import { BlobServiceClient, ContainerClient, BlockBlobClient } from '@azure/storage-blob';
import { logger } from '../utils/logger';
import config from '../config/storage';

export class BlobStorageService {
  private blobServiceClient: BlobServiceClient;
  private containerClient: ContainerClient;

  constructor(containerName: string = config.containerName) {
    this.blobServiceClient = BlobServiceClient.fromConnectionString(config.connectionString);
    this.containerClient = this.blobServiceClient.getContainerClient(containerName);
  }

  private getBlobClient(blobName: string): BlockBlobClient {
    return this.containerClient.getBlockBlobClient(blobName);
  }

  /**
   * Upload a file to Azure Blob Storage
   * @param blobName Name to give the blob (file path)
   * @param content File content to upload
   * @param contentType MIME type of the file
   * @returns URL of the uploaded blob
   */
  public async uploadFile(blobName: string, content: Buffer, contentType: string): Promise<string> {
    try {
      const blobClient = this.getBlobClient(blobName);
      
      await blobClient.uploadData(content, {
        blobHTTPHeaders: {
          blobContentType: contentType
        }
      });
      
      logger.info(`File uploaded successfully as ${blobName}`);
      return blobClient.url;
    } catch (error) {
      logger.error(`Error uploading file to blob storage: ${blobName}`, error);
      throw error;
    }
  }

  /**
   * Download a file from Azure Blob Storage
   * @param blobName Path of the file to download
   * @returns Buffer containing the file content
   */
  public async downloadFile(blobName: string): Promise<Buffer> {
    try {
      const blobClient = this.getBlobClient(blobName);
      const downloadResponse = await blobClient.download(0);
      
      // Convert stream to buffer
      const chunks: Buffer[] = [];
      for await (const chunk of downloadResponse.readableStreamBody!) {
        chunks.push(Buffer.from(chunk));
      }
      
      logger.info(`File downloaded successfully: ${blobName}`);
      return Buffer.concat(chunks);
    } catch (error) {
      logger.error(`Error downloading file from blob storage: ${blobName}`, error);
      throw error;
    }
  }

  /**
   * Delete a file from Azure Blob Storage
   * @param blobName Path of the file to delete
   * @returns True if deletion was successful
   */
  public async deleteFile(blobName: string): Promise<boolean> {
    try {
      const blobClient = this.getBlobClient(blobName);
      const response = await blobClient.delete();
      
      logger.info(`File deleted successfully: ${blobName}`);
      return true;
    } catch (error) {
      logger.error(`Error deleting file from blob storage: ${blobName}`, error);
      throw error;
    }
  }

  /**
   * Check if a file exists in Azure Blob Storage
   * @param blobName Path of the file to check
   * @returns True if the file exists
   */
  public async fileExists(blobName: string): Promise<boolean> {
    try {
      const blobClient = this.getBlobClient(blobName);
      return await blobClient.exists();
    } catch (error) {
      logger.error(`Error checking if file exists in blob storage: ${blobName}`, error);
      throw error;
    }
  }

  /**
   * Generate a shared access signature (SAS) URL for temporary access to a blob
   * @param blobName Path of the file
   * @param expiryMinutes Minutes until the SAS token expires
   * @returns SAS URL for the blob
   */
  public async generateSasUrl(blobName: string, expiryMinutes: number = 60): Promise<string> {
    try {
      const blobClient = this.getBlobClient(blobName);
      
      // Get SAS token that expires in specified minutes
      const expiryTime = new Date();
      expiryTime.setMinutes(expiryTime.getMinutes() + expiryMinutes);
      
      // TODO: Complete SAS URL generation logic using Azure SDK
      
      return blobClient.url;
    } catch (error) {
      logger.error(`Error generating SAS URL for blob: ${blobName}`, error);
      throw error;
    }
  }
}
```

## 11. Logging and Monitoring

### 11.1 Logger Setup

```typescript
// src/utils/logger.ts
import winston from 'winston';
import config from '../config/app';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log level based on environment
const level = () => {
  return config.environment === 'development' ? 'debug' : 'info';
};

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Define transports
const transports = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize({ all: true }),
      winston.format.printf(
        (info) => `${info.timestamp} ${info.level}: ${info.message}`
      )
    ),
  }),
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
  }),
  new winston.transports.File({
    filename: 'logs/all.log',
  }),
];

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
});

// Export a stream object for Morgan HTTP request logging
export const stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};
```

### 11.2 Middleware for HTTP Request Logging

```typescript
// src/middlewares/httpLogger.ts
import morgan from 'morgan';
import { stream } from '../utils/logger';

// Create custom token for request body
morgan.token('body', (req: any) => {
  // Avoid logging sensitive information
  const body = { ...req.body };
  
  // Mask sensitive fields
  if (body.password) body.password = '********';
  if (body.token) body.token = '********';
  
  return JSON.stringify(body);
});

// Create Morgan middleware
export const httpLogger = morgan(
  ':method :url :status :response-time ms - :res[content-length] :body',
  { stream }
);
```

## 12. Express.js App Setup

```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import passport from 'passport';
import { httpLogger } from './middlewares/httpLogger';
import { errorHandler } from './middlewares/errorHandler';
import userRoutes from './routes/userRoutes';
import documentRoutes from './routes/documentRoutes';
import itTicketRoutes from './routes/itTicketRoutes';
import hrPolicyRoutes from './routes/hrPolicyRoutes';
import { logger } from './utils/logger';

const app = express();

// Apply middlewares
app.use(helmet()); // Security headers
app.use(compression()); // Compress responses
app.use(cors()); // CORS handling
app.use(express.json({ limit: '10mb' })); // JSON body parser
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // URL-encoded body parser
app.use(httpLogger); // HTTP request logging
app.use(passport.initialize()); // Passport initialization

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API routes
app.use('/v1/users', userRoutes);
app.use('/v1/documents', documentRoutes);
app.use('/v1/it-tickets', itTicketRoutes);
app.use('/v1/hr-policies', hrPolicyRoutes);
// ... add more routes as needed

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: {
      code: 'NOT_FOUND',
      message: 'The requested resource does not exist'
    }
  });
});

export default app;
```

## 13. Server Entry Point

```typescript
// src/server.ts
import app from './app';
import { logger } from './utils/logger';
import config from './config/app';
import sequelize from './config/database';

// Database connection
const connectToDatabase = async () => {
  try {
    await sequelize.authenticate();
    logger.info('Database connection established successfully');
    
    // Sync models with database (only in development)
    if (config.environment === 'development') {
      // await sequelize.sync({ alter: true });
      logger.info('Database models synchronized');
    }
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};

// Start server
const startServer = async () => {
  try {
    await connectToDatabase();
    
    const server = app.listen(config.port, () => {
      logger.info(`Server is running on port ${config.port}`);
      logger.info(`Environment: ${config.environment}`);
    });
    
    // Handle shutdown gracefully
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received. Shutting down gracefully');
      server.close(() => {
        logger.info('Server closed');
        sequelize.close().then(() => {
          logger.info('Database connection closed');
          process.exit(0);
        });
      });
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
```

## 14. Testing Strategies

### 14.1 Unit Testing Example

```typescript
// tests/unit/services/documentService.test.ts
import { Document } from '../../../src/models';
import { listDocuments, getDocumentById } from '../../../src/services/documentService';

// Mock the Document model
jest.mock('../../../src/models', () => ({
  Document: {
    findAll: jest.fn(),
    findOne: jest.fn(),
    count: jest.fn()
  },
  DocumentCategory: {},
  Company: {},
  Department: {},
  User: {}
}));

describe('Document Service Tests', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('listDocuments', () => {
    it('should return documents and total count', async () => {
      // Mock data
      const mockDocuments = [
        { DocumentID: 1, Title: 'Test Document 1' },
        { DocumentID: 2, Title: 'Test Document 2' }
      ];
      
      // Setup mocks
      (Document.count as jest.Mock).mockResolvedValue(2);
      (Document.findAll as jest.Mock).mockResolvedValue(mockDocuments);
      
      // Test function
      const result = await listDocuments(
        { companyId: null, categoryId: null, isPublished: false, search: '' },
        1,
        10,
        'CreatedDate',
        'desc'
      );
      
      // Assertions
      expect(Document.count).toHaveBeenCalled();
      expect(Document.findAll).toHaveBeenCalled();
      expect(result.documents).toEqual(mockDocuments);
      expect(result.totalCount).toBe(2);
    });
  });
  
  describe('getDocumentById', () => {
    it('should return a document by ID', async () => {
      // Mock data
      const mockDocument = { DocumentID: 1, Title: 'Test Document' };
      
      // Setup mocks
      (Document.findOne as jest.Mock).mockResolvedValue(mockDocument);
      
      // Test function
      const result = await getDocumentById(1);
      
      // Assertions
      expect(Document.findOne).toHaveBeenCalledWith(expect.objectContaining({
        where: { DocumentID: 1, IsActive: true }
      }));
      expect(result).toEqual(mockDocument);
    });
    
    it('should return null if document not found', async () => {
      // Setup mocks
      (Document.findOne as jest.Mock).mockResolvedValue(null);
      
      // Test function
      const result = await getDocumentById(999);
      
      // Assertions
      expect(Document.findOne).toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });
});
```

### 14.2 Integration Testing Example

```typescript
// tests/integration/routes/documents.test.ts
import request from 'supertest';
import app from '../../../src/app';
import sequelize from '../../../src/config/database';
import { Document } from '../../../src/models';
import { mockJwtToken } from '../../helpers/auth';

// Setup before tests
beforeAll(async () => {
  // Connect to test database
  await sequelize.authenticate();
});

// Cleanup after tests
afterAll(async () => {
  // Close database connection
  await sequelize.close();
});

describe('Document API Routes', () => {
  describe('GET /v1/documents', () => {
    it('should return a list of documents', async () => {
      const response = await request(app)
        .get('/v1/documents')
        .set('Authorization', `Bearer ${mockJwtToken()}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('items');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.items)).toBe(true);
    });
    
    it('should filter documents by company ID', async () => {
      const response = await request(app)
        .get('/v1/documents?companyId=1')
        .set('Authorization', `Bearer ${mockJwtToken()}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      // All returned documents should have CompanyID: 1
      response.body.items.forEach((doc: any) => {
        expect(doc.Company.CompanyID).toBe(1);
      });
    });
  });
  
  describe('GET /v1/documents/:documentId', () => {
    let testDocument: any;
    
    // Create test document before tests
    beforeAll(async () => {
      testDocument = await Document.create({
        Title: 'Test Document',
        FileName: 'test.pdf',
        StoragePath: 'test/test.pdf',
        CreatedBy: 1,
        CreatedDate: new Date()
      });
    });
    
    // Delete test document after tests
    afterAll(async () => {
      await Document.destroy({
        where: { DocumentID: testDocument.DocumentID }
      });
    });
    
    it('should return a document by ID', async () => {
      const response = await request(app)
        .get(`/v1/documents/${testDocument.DocumentID}`)
        .set('Authorization', `Bearer ${mockJwtToken()}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('DocumentID', testDocument.DocumentID);
      expect(response.body).toHaveProperty('Title', 'Test Document');
    });
    
    it('should return 404 for non-existent document', async () => {
      const response = await request(app)
        .get('/v1/documents/99999')
        .set('Authorization', `Bearer ${mockJwtToken()}`)
        .expect('Content-Type', /json/)
        .expect(404);
      
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'RESOURCE_NOT_FOUND');
    });
  });
});
```

## 15. API Documentation with Swagger

```typescript
// src/utils/swagger.ts
import swaggerJsdoc from 'swagger-jsdoc';
import { Express } from 'express';
import swaggerUi from 'swagger-ui-express';
import config from '../config/app';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Falcon Portal API',
      version: '1.0.0',
      description: 'API documentation for the Falcon Portal',
      contact: {
        name: 'SASMOS IT Team',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `http://localhost:${config.port}/`,
        description: 'Development server'
      },
      {
        url: 'https://api.falcon.sasmos.com/',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['src/routes/*.ts', 'src/models/*.ts'] // Path to the API docs
};

const specs = swaggerJsdoc(options);

export const setupSwagger = (app: Express) => {
  // Serve Swagger docs at /api-docs
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
  
  // Serve Swagger JSON at /swagger.json
  app.get('/swagger.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
};
```

## 16. Deployment Configuration

### 16.1 Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Build the app
RUN npm run build

# Production image
FROM node:18-alpine

WORKDIR /app

# Copy package files and install production dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy built app from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

# Environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Expose port
EXPOSE 3000

# Start the app
CMD ["node", "dist/server.js"]
```

### 16.2 Azure Deployment YAML

```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
      - main
      - develop

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: falcon-api-variables
  - name: dockerImageName
    value: 'falcon-api'
  - name: dockerRegistry
    value: 'falconregistry.azurecr.io'
  - name: dockerImageTag
    value: '$(Build.BuildId)'

stages:
  - stage: Build
    jobs:
      - job: BuildAndTest
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '18.x'
            displayName: 'Install Node.js'
          
          - script: |
              npm ci
            displayName: 'Install dependencies'
          
          - script: |
              npm run lint
            displayName: 'Run linting'
          
          - script: |
              npm test
            displayName: 'Run tests'
          
          - script: |
              npm run build
            displayName: 'Build application'
          
          - task: Docker@2
            inputs:
              containerRegistry: 'FalconAzureContainerRegistry'
              repository: '$(dockerImageName)'
              command: 'buildAndPush'
              Dockerfile: '**/Dockerfile'
              tags: |
                $(dockerImageTag)
                latest
            displayName: 'Build and push Docker image'

  - stage: Deploy
    dependsOn: Build
    jobs:
      - job: DeployToStaging
        condition: eq(variables['Build.SourceBranch'], 'refs/heads/develop')
        steps:
          - task: AzureRmWebAppDeployment@4
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: 'FalconAzureSubscription'
              appType: 'webAppContainer'
              WebAppName: 'falcon-api-staging'
              DockerNamespace: '$(dockerRegistry)'
              DockerRepository: '$(dockerImageName)'
              DockerImageTag: '$(dockerImageTag)'
              AppSettings: |
                -NODE_ENV staging
                -PORT 80
            displayName: 'Deploy to Staging'

      - job: DeployToProduction
        condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
        steps:
          - task: AzureRmWebAppDeployment@4
            inputs:
              ConnectionType: 'AzureRM'
              azureSubscription: 'FalconAzureSubscription'
              appType: 'webAppContainer'
              WebAppName: 'falcon-api-production'
              DockerNamespace: '$(dockerRegistry)'
              DockerRepository: '$(dockerImageName)'
              DockerImageTag: '$(dockerImageTag)'
              AppSettings: |
                -NODE_ENV production
                -PORT 80
            displayName: 'Deploy to Production'
```

## 17. Performance Optimization

### 17.1 Caching Implementation

```typescript
// src/services/cacheService.ts
import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';
import config from '../config/cache';

class CacheService {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient({
      url: config.redisUrl,
      password: config.redisPassword
    });

    this.client.on('error', (err) => {
      logger.error('Redis client error', err);
      this.isConnected = false;
    });

    this.client.on('connect', () => {
      logger.info('Connected to Redis');
      this.isConnected = true;
    });

    this.connect();
  }

  private async connect() {
    try {
      await this.client.connect();
    } catch (error) {
      logger.error('Failed to connect to Redis', error);
    }
  }

  /**
   * Set a value in the cache
   * @param key Cache key
   * @param value Value to cache
   * @param expirySeconds Time to live in seconds (default: 1 hour)
   * @returns Promise<boolean> Success status
   */
  public async set(key: string, value: any, expirySeconds: number = 3600): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      // Convert non-string values to JSON
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      await this.client.set(key, stringValue, { EX: expirySeconds });
      return true;
    } catch (error) {
      logger.error(`Cache set error for key ${key}`, error);
      return false;
    }
  }

  /**
   * Get a value from the cache
   * @param key Cache key
   * @returns Promise<any> Cached value or null if not found
   */
  public async get(key: string): Promise<any> {
    if (!this.isConnected) return null;

    try {
      const value = await this.client.get(key);
      
      if (!value) return null;
      
      // Try to parse as JSON, return as string if parsing fails
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    } catch (error) {
      logger.error(`Cache get error for key ${key}`, error);
      return null;
    }
  }

  /**
   * Delete a value from the cache
   * @param key Cache key
   * @returns Promise<boolean> Success status
   */
  public async delete(key: string): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      logger.error(`Cache delete error for key ${key}`, error);
      return false;
    }
  }

  /**
   * Delete all values with a specific prefix
   * @param prefix Key prefix
   * @returns Promise<boolean> Success status
   */
  public async deleteByPrefix(prefix: string): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      const keys = await this.client.keys(`${prefix}*`);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      logger.error(`Cache deleteByPrefix error for prefix ${prefix}`, error);
      return false;
    }
  }

  /**
   * Check if a key exists in the cache
   * @param key Cache key
   * @returns Promise<boolean> True if key exists
   */
  public async exists(key: string): Promise<boolean> {
    if (!this.isConnected) return false;

    try {
      return (await this.client.exists(key)) === 1;
    } catch (error) {
      logger.error(`Cache exists error for key ${key}`, error);
      return false;
    }
  }

  /**
   * Get or set cache value with a function
   * @param key Cache key
   * @param fn Function to call if cache misses
   * @param expirySeconds TTL in seconds
   * @returns Promise<any> Cached or computed value
   */
  public async getOrSet(key: string, fn: () => Promise<any>, expirySeconds: number = 3600): Promise<any> {
    const cachedValue = await this.get(key);
    
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    const value = await fn();
    await this.set(key, value, expirySeconds);
    return value;
  }
}

// Export singleton instance
export const cacheService = new CacheService();
```

### 17.2 Using Cache in Services

```typescript
// src/services/documentService.ts (updated with caching)
import { Op } from 'sequelize';
import { Document, DocumentCategory, Company, Department, User } from '../models';
import { cacheService } from './cacheService';
import { logger } from '../utils/logger';

// ... existing code ...

export const getDocumentById = async (documentId: number) => {
  try {
    // Try to get from cache first
    const cacheKey = `document:${documentId}`;
    
    return await cacheService.getOrSet(cacheKey, async () => {
      // Cache miss, fetch from database
      const document = await Document.findOne({
        where: {
          DocumentID: documentId,
          IsActive: true
        },
        include: [
          { model: DocumentCategory, attributes: ['CategoryID', 'CategoryName'] },
          { model: Company, attributes: ['CompanyID', 'CompanyName'] },
          { model: Department, attributes: ['DepartmentID', 'DepartmentName'] },
          { model: User, as: 'Creator', attributes: ['UserID', 'FirstName', 'LastName'] },
          { model: User, as: 'Modifier', attributes: ['UserID', 'FirstName', 'LastName'] }
        ]
      });
      
      return document;
    }, 3600); // Cache for 1 hour
  } catch (error) {
    logger.error(`Error in getDocumentById service for ID ${documentId}`, error);
    throw error;
  }
};

export const updateDocument = async (documentId: number, documentData: any, userId: number) => {
  try {
    const document = await Document.findByPk(documentId);
    
    if (!document) {
      throw new Error('Document not found');
    }
    
    // Update document record
    await document.update({
      ...documentData,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    });
    
    // Invalidate cache
    await cacheService.delete(`document:${documentId}`);
    
    return await getDocumentById(documentId);
  } catch (error) {
    logger.error(`Error in updateDocument service for ID ${documentId}`, error);
    throw error;
  }
};

export const deleteDocument = async (documentId: number, userId: number) => {
  try {
    const document = await Document.findByPk(documentId);
    
    if (!document) {
      throw new Error('Document not found');
    }
    
    // Soft delete by marking as inactive
    await document.update({
      IsActive: false,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    });
    
    // Invalidate cache
    await cacheService.delete(`document:${documentId}`);
    
    return true;
  } catch (error) {
    logger.error(`Error in deleteDocument service for ID ${documentId}`, error);
    throw error;
  }
};
```

## 18. Search Implementation

### 18.1 Azure Cognitive Search Integration

```typescript
// src/services/searchService.ts
import { SearchClient, AzureKeyCredential } from '@azure/search-documents';
import { logger } from '../utils/logger';
import config from '../config/search';

class SearchService {
  private client: SearchClient;
  
  constructor() {
    this.client = new SearchClient(
      config.endpoint,
      config.indexName,
      new AzureKeyCredential(config.apiKey)
    );
  }
  
  /**
   * Index a document in Azure Cognitive Search
   * @param document Document to index
   * @returns Promise<boolean> Success status
   */
  public async indexDocument(document: any): Promise<boolean> {
    try {
      // Format document for indexing
      const searchDocument = {
        id: `doc-${document.DocumentID}`,
        type: 'document',
        title: document.Title,
        content: document.Description || '',
        fileName: document.FileName,
        categoryId: document.CategoryID,
        categoryName: document.Category?.CategoryName || '',
        companyId: document.CompanyID,
        companyName: document.Company?.CompanyName || '',
        departmentId: document.DepartmentID,
        departmentName: document.Department?.DepartmentName || '',
        isPublished: document.IsPublished,
        createdDate: document.CreatedDate.toISOString(),
        createdBy: document.CreatedBy,
        creatorName: document.Creator ? `${document.Creator.FirstName} ${document.Creator.LastName}` : '',
        modifiedDate: document.ModifiedDate ? document.ModifiedDate.toISOString() : null
      };
      
      await this.client.uploadDocuments([searchDocument]);
      logger.info(`Document indexed successfully: ${document.DocumentID}`);
      return true;
    } catch (error) {
      logger.error(`Error indexing document ${document.DocumentID}`, error);
      return false;
    }
  }
  
  /**
   * Delete a document from the search index
   * @param documentId Document ID
   * @returns Promise<boolean> Success status
   */
  public async deleteDocument(documentId: number): Promise<boolean> {
    try {
      await this.client.deleteDocuments([{ id: `doc-${documentId}` }]);
      logger.info(`Document deleted from index: ${documentId}`);
      return true;
    } catch (error) {
      logger.error(`Error deleting document ${documentId} from index`, error);
      return false;
    }
  }
  
  /**
   * Search for documents
   * @param query Search query
   * @param filters Search filters
   * @param page Page number
   * @param limit Items per page
   * @returns Promise<SearchResults> Search results
   */
  public async searchDocuments(
    query: string,
    filters: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<any> {
    try {
      // Build filter expression
      let filterExpressions: string[] = [];
      
      if (filters.companyId) {
        filterExpressions.push(`companyId eq ${filters.companyId}`);
      }
      
      if (filters.categoryId) {
        filterExpressions.push(`categoryId eq ${filters.categoryId}`);
      }
      
      if (filters.isPublished !== undefined) {
        filterExpressions.push(`isPublished eq ${filters.isPublished}`);
      }
      
      if (filters.types && Array.isArray(filters.types) && filters.types.length > 0) {
        const typeFilters = filters.types.map((type: string) => `type eq '${type}'`);
        filterExpressions.push(`(${typeFilters.join(' or ')})`);
      }
      
      const filterExpression = filterExpressions.length > 0 
        ? filterExpressions.join(' and ')
        : undefined;
      
      // Calculate skip for pagination
      const skip = (page - 1) * limit;
      
      // Perform search
      const searchResults = await this.client.search(query, {
        filter: filterExpression,
        skip,
        top: limit,
        includeTotalCount: true,
        highlightFields: 'content,title',
        orderBy: ['isPublished desc', 'createdDate desc']
      });
      
      // Format results
      const results: any[] = [];
      for await (const result of searchResults.results) {
        const document = result.document;
        
        // Extract highlights
        const highlights: string[] = [];
        if (result.highlights?.content) {
          highlights.push(...result.highlights.content);
        }
        if (result.highlights?.title) {
          highlights.push(...result.highlights.title);
        }
        
        results.push({
          id: document.id,
          type: document.type,
          title: document.title,
          description: document.content,
          fileName: document.fileName,
          companyId: document.companyId,
          companyName: document.companyName,
          categoryId: document.categoryId,
          categoryName: document.categoryName,
          createdDate: document.createdDate,
          createdBy: document.createdBy,
          creatorName: document.creatorName,
          url: `/documents/${document.id.replace('doc-', '')}`,
          highlights
        });
      }
      
      return {
        results,
        totalCount: searchResults.count || 0
      };
    } catch (error) {
      logger.error(`Error searching for "${query}"`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const searchService = new SearchService();
```

### 18.2 Search Controller Implementation

```typescript
// src/controllers/searchController.ts
import { Request, Response } from 'express';
import { searchService } from '../services/searchService';
import { logger } from '../utils/logger';
import { paginationParams } from '../utils/pagination';

export const search = async (req: Request, res: Response) => {
  try {
    const { page, limit } = paginationParams(req);
    const query = req.query.q as string || '';
    
    // Parse filters
    const filters: any = {};
    
    if (req.query.companyId) {
      filters.companyId = Number(req.query.companyId);
    }
    
    if (req.query.categoryId) {
      filters.categoryId = Number(req.query.categoryId);
    }
    
    if (req.query.types) {
      filters.types = (req.query.types as string).split(',');
    }
    
    if (req.query.published !== undefined) {
      filters.isPublished = req.query.published === 'true';
    }
    
    // Perform search
    const result = await searchService.searchDocuments(query, filters, page, limit);
    
    return res.status(200).json({
      results: result.results,
      totalResults: result.totalCount,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(result.totalCount / limit),
        hasNext: page * limit < result.totalCount,
        hasPrevious: page > 1
      }
    });
  } catch (error) {
    logger.error(`Error in search controller: ${error}`, error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while processing the search request'
      }
    });
  }
};

export const searchSuggestions = async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string || '';
    const limit = Number(req.query.limit) || 10;
    
    // Implement suggestions logic
    // For now, return a simple search with limit=5
    const result = await searchService.searchDocuments(query, {}, 1, limit);
    
    // Format suggestions
    const suggestions = result.results.map((item: any) => ({
      text: item.title,
      id: item.id,
      type: item.type,
      url: item.url
    }));
    
    return res.status(200).json(suggestions);
  } catch (error) {
    logger.error(`Error in searchSuggestions controller: ${error}`, error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while processing the suggestions request'
      }
    });
  }
};
```

## 19. Conclusion

This implementation guide provides a comprehensive approach to building the Falcon Portal API layer. By following these patterns and best practices, you'll create a scalable, maintainable, and secure API that meets the requirements outlined in the Falcon Portal requirements document.

Key takeaways from this guide:

1. **Modular Architecture**: Organize code into controllers, services, and models for better maintainability
2. **Middleware Pattern**: Use middleware for cross-cutting concerns like authentication and validation
3. **Consistent Error Handling**: Standardize error responses across all endpoints
4. **Performance Optimization**: Implement caching strategies to improve response times
5. **Security Best Practices**: Apply proper authentication, authorization, and input validation
6. **Testing Approach**: Include both unit and integration tests for reliability
7. **Deployment Strategy**: Use containerization and CI/CD for consistent deployments

Following this guide will ensure that the Falcon Portal API is built to high standards and provides a solid foundation for the frontend application to deliver a personalized experience to SASMOS Group employees.# Falcon Portal API Implementation Guide

## 1. Introduction

This guide provides detailed implementation instructions for developing the Falcon Portal API layer. It includes best practices, coding standards, and implementation examples to ensure consistency across the various microservices that make up the Falcon Portal backend.

## 2. Technology Stack

### 2.1 Core Technologies

- **Runtime**: Node.js 18+ LTS
- **API Framework**: Express.js 4.18+
- **TypeScript**: For type safety and better developer experience
- **ORM**: Sequelize 6.x for SQL database interactions
- **Authentication**: Passport.js with Azure AD strategy
- **Documentation**: Swagger/OpenAPI 3.0
- **Testing**: Jest with Supertest

### 2.2 Development Environment Setup

```bash
# Create new API service
mkdir falcon-api-service
cd falcon-api-service

# Initialize package.json
npm init -y

# Install core dependencies
npm install express express-validator cors helmet compression dotenv
npm install sequelize tedious
npm install passport passport-azure-ad jsonwebtoken
npm install winston morgan

# Install development dependencies
npm install --save-dev typescript ts-node nodemon @types/express @types/node
npm install --save-dev jest supertest @types/jest @types/supertest
npm install --save-dev eslint prettier
```

### 2.3 Project Structure

Each microservice should follow this consistent structure:

```
project-root/
├── src/
│   ├── config/                # Configuration files
│   ├── controllers/           # Route controllers
│   ├── middlewares/           # Custom middlewares
│   ├── models/                # Data models
│   ├── routes/                # Route definitions
│   ├── services/              # Business logic
│   ├── utils/                 # Utility functions
│   ├── validators/            # Request validation schemas
│   ├── app.ts                 # Express app setup
│   └── server.ts              # Server entry point
├── tests/
│   ├── unit/                  # Unit tests
│   ├── integration/           # Integration tests
│   └── fixtures/              # Test fixtures
├── .env.example               # Environment variables template
├── .eslintrc.js               # ESLint configuration
├── .prettierrc                # Prettier configuration
├── jest.config.js             # Jest configuration
├── package.json               # Dependencies and scripts
├── tsconfig.json              # TypeScript configuration
└── README.md                  # Service documentation
```

## 3. API Development Standards

### 3.1 API Design Principles

- **RESTful**: Follow REST principles for resource-based API design
- **Consistent**: Use consistent naming conventions across all endpoints
- **Versioned**: Include API version in the URL path
- **Documented**: Every endpoint must have OpenAPI documentation
- **Validated**: All requests must be validated before processing
- **Secured**: Apply appropriate authentication and authorization

### 3.2 URL Structure

```
https://api.falcon.sasmos.com/v1/{resource}[/{resource-id}][/{sub-resource}]
```

Examples:
- `/v1/documents` - List all documents
- `/v1/documents/123` - Get document with ID 123
- `/v1/documents/123/versions` - List all versions of document 123

### 3.3 HTTP Methods

- **GET**: Retrieve resources
- **POST**: Create new resources
- **PUT**: Update existing resources completely
- **PATCH**: Update existing resources partially
- **DELETE**: Remove resources

### 3.4 Status Codes

- **200 OK**: Success for GET, PUT, PATCH operations
- **201 Created**: Success for POST operations that create new resources
- **204 No Content**: Success for DELETE operations
- **400 Bad Request**: Invalid input
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Authenticated but not authorized
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

## 4. Authentication Implementation

### 4.1 Entra ID Integration

Configure Passport.js with Microsoft Entra ID strategy:

```typescript
// src/middlewares/auth.ts
import passport from 'passport';
import { BearerStrategy } from 'passport-azure-ad';
import config from '../config/auth';

const options = {
  identityMetadata: `https://login.microsoftonline.com/${config.tenantId}/v2.0/.well-known/openid-configuration`,
  clientID: config.clientId,
  audience: config.audience,
  issuer: config.issuer,
  validateIssuer: true,
  passReqToCallback: false,
  loggingLevel: 'info'
};

const bearerStrategy = new BearerStrategy(options, (token: any, done: any) => {
  // Extract user information from token
  const user = {
    id: token.oid,
    email: token.preferred_username,
    name: token.name,
    roles: token.roles || [],
    companyId: token.extension_CompanyId
  };
  
  return done(null, user, token);
});

passport.use(bearerStrategy);

export const authenticateJwt = passport.authenticate('oauth-bearer', { session: false });
```

### 4.2 Authorization Middleware

Create middleware for role-based access control:

```typescript
// src/middlewares/roleCheck.ts
export const requireRole = (allowedRoles: string[]) => {
  return (req: any, res: any, next: any) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      });
    }
    
    const userRoles = user.roles || [];
    const hasPermission = allowedRoles.some(role => userRoles.includes(role));
    
    if (!hasPermission) {
      return res.status(403).json({
        error: {
          code: 'FORBIDDEN',
          message: 'You do not have permission to access this resource'
        }
      });
    }
    
    next();
  };
};

// Example for company-specific access
export const requireCompanyAccess = (req: any, res: any, next: any) => {
  const user = req.user;
  const requestedCompanyId = req.params.companyId || req.body.companyId;
  
  // Allow admins to access any company
  if (user.roles.includes('Administrator')) {
    return next();
  }
  
  // Check if user belongs to the requested company
  if (user.companyId !== requestedCompanyId) {
    return res.status(403).json({
      error: {
        code: 'FORBIDDEN',
        message: 'You do not have permission to access resources for this company'
      }
    });
  }
  
  next();
};
```

## 5. Model Implementation

### 5.1 Sequelize Model Example

```typescript
// src/models/Document.ts
import { Model, DataTypes, Optional } from 'sequelize';
import sequelize from '../config/database';

interface DocumentAttributes {
  DocumentID: number;
  Title: string;
  Description: string | null;
  FileName: string;
  FileExtension: string | null;
  FileSizeKB: number | null;
  StoragePath: string;
  CategoryID: number | null;
  CompanyID: number | null;
  DepartmentID: number | null;
  IsPublished: boolean;
  Version: string;
  DownloadCount: number;
  ViewCount: number;
  IsActive: boolean;
  CreatedBy: number;
  CreatedDate: Date;
  ModifiedBy: number | null;
  ModifiedDate: Date | null;
}

interface DocumentCreationAttributes extends Optional<DocumentAttributes, 'DocumentID'> {}

class Document extends Model<DocumentAttributes, DocumentCreationAttributes> implements DocumentAttributes {
  public DocumentID!: number;
  public Title!: string;
  public Description!: string | null;
  public FileName!: string;
  public FileExtension!: string | null;
  public FileSizeKB!: number | null;
  public StoragePath!: string;
  public CategoryID!: number | null;
  public CompanyID!: number | null;
  public DepartmentID!: number | null;
  public IsPublished!: boolean;
  public Version!: string;
  public DownloadCount!: number;
  public ViewCount!: number;
  public IsActive!: boolean;
  public CreatedBy!: number;
  public CreatedDate!: Date;
  public ModifiedBy!: number | null;
  public ModifiedDate!: Date | null;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Document.init(
  {
    DocumentID: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    Title: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    Description: {
      type: DataTypes.STRING(1000),
      allowNull: true,
    },
    FileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    FileExtension: {
      type: DataTypes.STRING(10),
      allowNull: true,
    },
    FileSizeKB: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    StoragePath: {
      type: DataTypes.STRING(1000),
      allowNull: false,
    },
    CategoryID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    CompanyID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    DepartmentID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    IsPublished: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    Version: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: '1.0',
    },
    DownloadCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    ViewCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    CreatedBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    CreatedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    ModifiedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ModifiedDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'Documents',
    timestamps: false,
  }
);

export default Document;
```

### 5.2 Model Associations

Define relationships between models:

```typescript
// src/models/index.ts
import Document from './Document';
import DocumentCategory from './DocumentCategory';
import Company from './Company';
import Department from './Department';
import User from './User';

// Document associations
Document.belongsTo(DocumentCategory, { foreignKey: 'CategoryID' });
Document.belongsTo(Company, { foreignKey: 'CompanyID' });
Document.belongsTo(Department, { foreignKey: 'DepartmentID' });
Document.belongsTo(User, { foreignKey: 'CreatedBy', as: 'Creator' });
Document.belongsTo(User, { foreignKey: 'ModifiedBy', as: 'Modifier' });

// Export models
export {
  Document,
  DocumentCategory,
  Company,
  Department,
  User
};
```

## 6. Controller Implementation

### 6.1 Controller Structure

```typescript
// src/controllers/documentController.ts
import { Request, Response } from 'express';
import { Document } from '../models';
import { createDocument, getDocumentById, listDocuments, updateDocument, deleteDocument } from '../services/documentService';
import { paginationParams } from '../utils/pagination';
import { logger } from '../utils/logger';

export const getDocuments = async (req: Request, res: Response) => {
  try {
    const { page, limit, sortField, sortOrder } = paginationParams(req);
    const filters = {
      companyId: req.query.companyId ? Number(req.query.companyId) : null,
      categoryId: req.query.categoryId ? Number(req.query.categoryId) : null,
      isPublished: req.query.published === 'true',
      search: req.query.search as string || '',
    };
    
    const result = await listDocuments(filters, page, limit, sortField, sortOrder);
    
    return res.status(200).json({
      items: result.documents,
      pagination: {
        page,
        limit,
        totalItems: result.totalCount,
        totalPages: Math.ceil(result.totalCount / limit),
        hasNext: page * limit < result.totalCount,
        hasPrevious: page > 1
      }
    });
  } catch (error) {
    logger.error('Error fetching documents', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while fetching documents'
      }
    });
  }
};

export const getDocument = async (req: Request, res: Response) => {
  try {
    const documentId = Number(req.params.documentId);
    const document = await getDocumentById(documentId);
    
    if (!document) {
      return res.status(404).json({
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'Document not found'
        }
      });
    }
    
    // Update view count
    await Document.increment('ViewCount', { by: 1, where: { DocumentID: documentId } });
    
    return res.status(200).json(document);
  } catch (error) {
    logger.error(`Error fetching document with ID ${req.params.documentId}`, error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while fetching the document'
      }
    });
  }
};

// Implement other controller methods...
```

### 6.2 Request Validation

```typescript
// src/validators/documentValidator.ts
import { body, param, query } from 'express-validator';

export const createDocumentValidator = [
  body('title').notEmpty().withMessage('Title is required').isString().withMessage('Title must be a string'),
  body('fileName').notEmpty().withMessage('File name is required'),
  body('storagePath').notEmpty().withMessage('Storage path is required'),
  body('companyId').optional().isInt().withMessage('Company ID must be an integer'),
  body('categoryId').optional().isInt().withMessage('Category ID must be an integer'),
  body('departmentId').optional().isInt().withMessage('Department ID must be an integer'),
  body('isPublished').optional().isBoolean().withMessage('Is published must be a boolean')
];

export const updateDocumentValidator = [
  param('documentId').isInt().withMessage('Document ID must be an integer'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('description').optional().isString().withMessage('Description must be a string'),
  body('categoryId').optional().isInt().withMessage('Category ID must be an integer'),
  body('isPublished').optional().isBoolean().withMessage('Is published must be a boolean')
];

export const getDocumentsValidator = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('companyId').optional().isInt().withMessage('Company ID must be an integer'),
  query('categoryId').optional().isInt().withMessage('Category ID must be an integer'),
  query('published').optional().isBoolean().withMessage('Published must be a boolean')
];
```

## 7. Service Layer Implementation

```typescript
// src/services/documentService.ts
import { Op } from 'sequelize';
import { Document, DocumentCategory, Company, Department, User } from '../models';
import { BlobStorageService } from './blobStorageService';
import { logger } from '../utils/logger';

interface DocumentFilters {
  companyId: number | null;
  categoryId: number | null;
  isPublished: boolean;
  search: string;
}

export const listDocuments = async (
  filters: DocumentFilters,
  page: number,
  limit: number,
  sortField: string = 'CreatedDate',
  sortOrder: 'asc' | 'desc' = 'desc'
) => {
  const offset = (page - 1) * limit;
  
  // Build where clause based on filters
  const whereClause: any = { IsActive: true };
  
  if (filters.companyId) {
    whereClause.CompanyID = filters.companyId;
  }
  
  if (filters.categoryId) {
    whereClause.CategoryID = filters.categoryId;
  }
  
  if (filters.isPublished) {
    whereClause.IsPublished = true;
  }
  
  if (filters.search) {
    whereClause[Op.or] = [
      { Title: { [Op.like]: `%${filters