"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDocuments = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function getDocuments(request, context) {
    context.log(`GetDocuments function invoked.`);
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }
        // Parse query parameters
        const url = new URL(request.url);
        const search = url.searchParams.get('search') || '';
        const categoryId = url.searchParams.get('categoryId');
        const companyId = url.searchParams.get('companyId');
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '20');
        const offset = (page - 1) * limit;
        // Build query with parameters
        let whereConditions = ['d.IsActive = 1', 'd.IsPublished = 1'];
        const queryParams = [
            { name: 'offset', type: sql.Int, value: offset },
            { name: 'limit', type: sql.Int, value: limit }
        ];
        // Add search filter
        if (search) {
            whereConditions.push(`(d.Title LIKE @search OR d.Description LIKE @search OR EXISTS (
                SELECT 1 FROM DocumentTags_Map dtm 
                JOIN DocumentTags dt ON dtm.TagID = dt.TagID 
                WHERE dtm.DocumentID = d.DocumentID AND dt.TagName LIKE @search
            ))`);
            queryParams.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }
        // Add category filter
        if (categoryId && categoryId !== 'all') {
            whereConditions.push('d.CategoryID = @categoryId');
            queryParams.push({ name: 'categoryId', type: sql.Int, value: parseInt(categoryId) });
        }
        // Add company filter
        if (companyId && companyId !== 'all') {
            whereConditions.push('d.CompanyID = @companyId');
            queryParams.push({ name: 'companyId', type: sql.Int, value: parseInt(companyId) });
        }
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        const query = `
            WITH DocumentsWithTags AS (
                SELECT 
                    d.DocumentID,
                    d.Title,
                    d.Description,
                    d.FileName,
                    d.FileExtension,
                    d.FileSizeKB,
                    d.CategoryID,
                    dc.CategoryName,
                    d.CompanyID,
                    c.CompanyName,
                    d.IsPublished,
                    d.Version,
                    d.DownloadCount,
                    d.ViewCount,
                    d.CreatedBy,
                    u.FirstName + ' ' + u.LastName as CreatedByName,
                    u.Email as CreatedByEmail,
                    d.CreatedDate,
                    d.ModifiedDate,
                    STRING_AGG(dt.TagName, ',') WITHIN GROUP (ORDER BY dt.TagName) as Tags
                FROM Documents d
                LEFT JOIN DocumentCategories dc ON d.CategoryID = dc.CategoryID
                LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
                LEFT JOIN Users u ON d.CreatedBy = u.UserID
                LEFT JOIN DocumentTags_Map dtm ON d.DocumentID = dtm.DocumentID
                LEFT JOIN DocumentTags dt ON dtm.TagID = dt.TagID
                ${whereClause}
                GROUP BY 
                    d.DocumentID, d.Title, d.Description, d.FileName, d.FileExtension, 
                    d.FileSizeKB, d.CategoryID, dc.CategoryName, d.CompanyID, c.CompanyName,
                    d.IsPublished, d.Version, d.DownloadCount, d.ViewCount, d.CreatedBy,
                    u.FirstName, u.LastName, u.Email, d.CreatedDate, d.ModifiedDate
            )
            SELECT * FROM DocumentsWithTags
            ORDER BY CreatedDate DESC
            OFFSET @offset ROWS
            FETCH NEXT @limit ROWS ONLY;

            -- Get total count for pagination
            SELECT COUNT(*) as TotalCount
            FROM Documents d
            LEFT JOIN DocumentCategories dc ON d.CategoryID = dc.CategoryID
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            ${whereClause};
        `;
        const result = await (0, db_1.executeQuery)(query, queryParams);
        // Access recordsets properly
        const documentsData = Array.isArray(result.recordsets) ? result.recordsets[0] : result.recordset;
        const countData = Array.isArray(result.recordsets) ? result.recordsets[1] : null;
        const documents = documentsData.map((row) => ({
            id: row.DocumentID.toString(),
            title: row.Title,
            description: row.Description,
            fileName: row.FileName,
            fileExtension: row.FileExtension || '',
            fileSizeKB: row.FileSizeKB || 0,
            category: {
                id: row.CategoryID?.toString() || '',
                name: row.CategoryName || 'Uncategorized'
            },
            company: row.CompanyName || 'Unknown',
            isPublished: row.IsPublished,
            version: row.Version,
            downloadCount: row.DownloadCount,
            viewCount: row.ViewCount,
            createdBy: {
                name: row.CreatedByName || 'Unknown',
                email: row.CreatedByEmail || ''
            },
            createdDate: row.CreatedDate,
            lastModified: row.ModifiedDate,
            tags: row.Tags ? row.Tags.split(',') : []
        }));
        const totalCount = countData?.[0]?.TotalCount || 0;
        const totalPages = Math.ceil(totalCount / limit);
        logger_1.logger.info(`GetDocuments: Retrieved ${documents.length} documents for user ${userId || 'dev-user'}`);
        return {
            status: 200,
            jsonBody: {
                documents,
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetDocuments: Error retrieving documents:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error retrieving documents.",
                error: errorMessage
            }
        };
    }
}
exports.getDocuments = getDocuments;
// Register the function
functions_1.app.http('GetDocuments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents',
    handler: getDocuments
});
//# sourceMappingURL=GetDocuments.js.map