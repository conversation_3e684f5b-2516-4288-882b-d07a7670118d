-- Remove Unauthorized Administrator Roles Script
-- This script removes Administrator role assignments for all users except authorized admins
-- Authorized admins: chetan.pal@aviratadefsys.<NAME_EMAIL>

-- First, let's see what we're working with
PRINT 'Current Administrator role assignments:'
SELECT ur.User<PERSON>ole<PERSON>, u.Email, u.<PERSON>ame, u.<PERSON>ame, r.<PERSON>, ur.AssignedDate
FROM UserRoles ur
INNER JOIN Users u ON ur.UserID = u.UserID
INNER JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
ORDER BY u.Email;

-- Identify unauthorized Administrator role assignments
PRINT 'Unauthorized Administrator role assignments to be removed:'
SELECT ur.User<PERSON>ole<PERSON>, u.Email, u.FirstName, u.LastName, r.RoleName
FROM UserRoles ur
INNER JOIN Users u ON ur.UserID = u.UserID
INNER JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
  AND u.Email NOT IN ('<EMAIL>', '<EMAIL>');

-- Remove unauthorized Administrator role assignments
BEGIN TRANSACTION;

DECLARE @RemovedCount INT = 0;

DELETE ur
FROM UserRoles ur
INNER JOIN Users u ON ur.UserID = u.UserID
INNER JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
  AND u.Email NOT IN ('<EMAIL>', '<EMAIL>');

SET @RemovedCount = @@ROWCOUNT;
PRINT 'Removed ' + CAST(@RemovedCount AS VARCHAR(10)) + ' unauthorized Administrator role assignments.';

-- Ensure all affected users have at least Employee role
PRINT 'Ensuring affected users have Employee role...';

-- Find users who don't have Employee role but should have it
INSERT INTO UserRoles (UserID, RoleID, AssignedBy, AssignedDate)
SELECT DISTINCT u.UserID, er.RoleID, 1, GETDATE()
FROM Users u
CROSS JOIN Roles er
WHERE er.RoleName = 'Employee'
  AND u.IsActive = 1
  AND NOT EXISTS (
    SELECT 1 
    FROM UserRoles ur2 
    INNER JOIN Roles r2 ON ur2.RoleID = r2.RoleID 
    WHERE ur2.UserID = u.UserID AND r2.RoleName = 'Employee'
  );

DECLARE @AddedEmployeeCount INT = @@ROWCOUNT;
PRINT 'Added Employee role to ' + CAST(@AddedEmployeeCount AS VARCHAR(10)) + ' users who needed it.';

-- Verify results
PRINT 'Remaining Administrator role assignments (should only be authorized users):';
SELECT u.Email, u.FirstName, u.LastName, r.RoleName, ur.AssignedDate
FROM UserRoles ur
INNER JOIN Users u ON ur.UserID = u.UserID
INNER JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
ORDER BY u.Email;

-- Check <EMAIL> specifically
PRINT '<NAME_EMAIL> after cleanup:';
SELECT u.Email, r.RoleName, ur.AssignedDate
FROM UserRoles ur
INNER JOIN Users u ON ur.UserID = u.UserID
INNER JOIN Roles r ON ur.RoleID = r.RoleID
WHERE u.Email = '<EMAIL>'
ORDER BY r.RoleName;

-- COMMIT the transaction if everything looks good
-- UNCOMMENT the next line when ready to execute:
-- COMMIT TRANSACTION;

-- For safety, ROLLBACK by default during testing
ROLLBACK TRANSACTION;
PRINT 'Transaction rolled back for safety. Review results and uncomment COMMIT when ready to execute.'; 