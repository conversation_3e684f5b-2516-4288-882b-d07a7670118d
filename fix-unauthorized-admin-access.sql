-- Fix Unauthorized Admin Access
-- This script removes Administrator role from users who shouldn't have it

-- First, let's see who currently has Administrator role
SELECT 
    u.UserID,
    u.Email,
    u.FirstName,
    u.LastName,
    ur.UserRoleID,
    ur.<PERSON>Date,
    ur.CreatedBy
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
ORDER BY u.Email;

-- Remove Administrator <NAME_EMAIL> (and other unauthorized users)
-- Keep only authorized admin users: <EMAIL>, <EMAIL>

UPDATE UserRoles 
SET IsActive = 0, 
    ModifiedBy = 1, 
    ModifiedDate = GETUTCDATE()
WHERE UserRoleID IN (
    SELECT ur.UserRoleID
    FROM Users u
    JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
    JOIN Roles r ON ur.RoleID = r.RoleID
    WHERE r.RoleName = 'Administrator'
    AND u.Email NOT IN (
        '<EMAIL>',
        '<EMAIL>'
    )
);

-- Verify the fix - should only show authorized admins
SELECT 
    u.Email,
    u.FirstName,
    u.LastName,
    'Administrator' as RoleName
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
JOIN Roles r ON ur.RoleID = r.RoleID
WHERE r.RoleName = 'Administrator'
ORDER BY u.Email;

-- Also check that grievance user still has Employee role
SELECT 
    u.Email,
    u.FirstName,
    u.LastName,
    r.RoleName
FROM Users u
JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
JOIN Roles r ON ur.RoleID = r.RoleID
WHERE u.Email = '<EMAIL>'
ORDER BY r.RoleName; 