"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const Header_1 = require("./Header");
const Sidebar_1 = require("./Sidebar");
const MainContent_1 = require("./MainContent");
const Layout = () => {
    const [activeSection, setActiveSection] = (0, react_1.useState)('dashboard');
    const [isSidebarExpanded, setIsSidebarExpanded] = (0, react_1.useState)(true);
    const handleNavigate = (sectionId) => {
        setActiveSection(sectionId);
        // Potentially add logic here to close sidebar on mobile after navigation
    };
    const handleToggleSidebar = () => {
        setIsSidebarExpanded(!isSidebarExpanded);
    };
    return (<div className="flex h-screen overflow-hidden bg-gray-100">
      <Sidebar_1.default activeSection={activeSection} isExpanded={isSidebarExpanded} onNavigate={handleNavigate} onToggle={handleToggleSidebar}/>
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header_1.default />
        <MainContent_1.default activeSection={activeSection}/>
      </div>
    </div>);
};
exports.default = Layout;
//# sourceMappingURL=Layout.js.map