import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal } from "../shared/authUtils";
import { graphService } from "../shared/services/graphService";
import * as sql from 'mssql';

// Interface for the expected request body
interface SyncUserRequest {
    entraId?: string; // Specific user to sync
    bulkSync?: boolean; // Sync all users in database
}

// Define required role(s) for authorization
const REQUIRED_ROLE = 'Administrator'; // Users who can sync user data

export async function syncUserFromEntra(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function SyncUserFromEntra processed request for url "${request.url}"`);
    logger.info('SyncUserFromEntra function invoked.');

    // 1. Authentication & Authorization
    let authenticatedUserId: number;
    
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        logger.warn('SyncUserFromEntra: Bypassing authentication check in development/test mode.');
        authenticatedUserId = 1; // Use default user ID for development
    } else {
        const principal = getClientPrincipal(request);
        if (!principal) {
            logger.warn("SyncUserFromEntra: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }
        
        if (!hasRequiredRole(principal, [REQUIRED_ROLE])) {
            logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted to sync users without required role '${REQUIRED_ROLE}'.`);
            return { status: 403, jsonBody: { error: "Forbidden. User does not have the required permissions." } };
        }
        
        const authUserId = await getUserIdFromPrincipal(principal, context);
        if (!authUserId) {
            logger.error(`SyncUserFromEntra: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
            return { status: 403, jsonBody: { error: "Forbidden. Authenticated user not found or inactive in the portal database." } };
        }
        authenticatedUserId = authUserId;
    }

    logger.info(`SyncUserFromEntra invoked by UserID: ${authenticatedUserId}`);

    // 2. Parse and validate request body
    let parsedBody: SyncUserRequest = {};
    
    if (request.method === 'POST') {
        try {
            parsedBody = await request.json() as SyncUserRequest;
        } catch (error) {
            logger.error('SyncUserFromEntra: Invalid JSON in request body.', error);
            return { status: 400, jsonBody: { error: "Invalid JSON in request body." } };
        }
    }

    // 3. Get Entra ID from either request body or route parameter
    const entraId = parsedBody.entraId || request.params.entraId;
    const bulkSync = parsedBody.bulkSync === true;

    if (!entraId && !bulkSync) {
        return { status: 400, jsonBody: { error: "Either 'entraId' or 'bulkSync: true' must be provided." } };
    }

    try {
        if (bulkSync) {
            // Sync all users in the database
            return await syncAllUsers(authenticatedUserId);
        } else {
            // Sync specific user
            return await syncSingleUser(entraId!, authenticatedUserId);
        }

    } catch (error) {
        logger.error(`Error in SyncUserFromEntra function:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                error: "An unexpected error occurred while syncing user data.",
                details: errorMessage
            }
        };
    }
}

// Sync a single user
async function syncSingleUser(entraId: string, modifiedByUserId: number): Promise<HttpResponseInit> {
    logger.info(`Starting sync for single user: ${entraId}`);

    // 1. Check if user exists in our database
    const checkUserQuery = `
        SELECT UserID, EntraID, FirstName, LastName, Email, ContactNumber 
        FROM Users 
        WHERE EntraID = @EntraID;
    `;
    const checkUserParams: QueryParameter[] = [
        { name: 'EntraID', type: sql.NVarChar, value: entraId }
    ];

    const userResult = await executeQuery(checkUserQuery, checkUserParams);

    if (!userResult.recordset || userResult.recordset.length === 0) {
        logger.warn(`User with Entra ID ${entraId} not found in database.`);
        return { 
            status: 404, 
            jsonBody: { 
                error: `User with Entra ID ${entraId} not found in database. Use AddUser function to create new users.` 
            } 
        };
    }

    const dbUser = userResult.recordset[0];
    logger.info(`Found user in database: UserID ${dbUser.UserID}, current name: ${dbUser.FirstName} ${dbUser.LastName}`);

    // 2. Get fresh data from Entra ID
    const entraData = await graphService.syncUserDataFromEntra(entraId);
    if (!entraData) {
        return { 
            status: 404, 
            jsonBody: { 
                error: `User with Entra ID ${entraId} not found in Entra ID directory.` 
            } 
        };
    }

    // 3. Update user data in database
    const updateQuery = `
        UPDATE Users SET 
            FirstName = @FirstName,
            LastName = @LastName,
            Email = @Email,
            ContactNumber = @ContactNumber,
            ModifiedBy = @ModifiedBy,
            ModifiedDate = GETUTCDATE()
        WHERE UserID = @UserID;
    `;

    const updateParams: QueryParameter[] = [
        { name: 'UserID', type: sql.Int, value: dbUser.UserID },
        { name: 'FirstName', type: sql.NVarChar, value: entraData.firstName },
        { name: 'LastName', type: sql.NVarChar, value: entraData.lastName },
        { name: 'Email', type: sql.NVarChar, value: entraData.email },
        { name: 'ContactNumber', type: sql.NVarChar, value: entraData.contactNumber },
        { name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId }
    ];

    await executeQuery(updateQuery, updateParams);

    logger.info(`Successfully synced user data for UserID ${dbUser.UserID} from Entra ID`);

    return {
        status: 200,
        jsonBody: {
            message: "User data synced successfully from Entra ID.",
            userId: dbUser.UserID,
            updatedFields: {
                firstName: entraData.firstName,
                lastName: entraData.lastName,
                email: entraData.email,
                contactNumber: entraData.contactNumber
            }
        }
    };
}

// Sync all users who have Entra IDs
async function syncAllUsers(modifiedByUserId: number): Promise<HttpResponseInit> {
    logger.info(`Starting bulk sync for all users with Entra IDs`);

    // 1. Get all users with Entra IDs
    const getAllUsersQuery = `
        SELECT UserID, EntraID, FirstName, LastName, Email, ContactNumber 
        FROM Users 
        WHERE EntraID IS NOT NULL AND EntraID != '' AND IsActive = 1;
    `;

    const usersResult = await executeQuery(getAllUsersQuery, []);

    if (!usersResult.recordset || usersResult.recordset.length === 0) {
        return { 
            status: 200, 
            jsonBody: { 
                message: "No users with Entra IDs found to sync.",
                syncedCount: 0
            } 
        };
    }

    const totalUsers = usersResult.recordset.length;
    logger.info(`Found ${totalUsers} users to sync`);

    let syncedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // 2. Sync each user
    for (const dbUser of usersResult.recordset) {
        try {
            logger.info(`Syncing user ${dbUser.UserID} (Entra ID: ${dbUser.EntraID})`);

            // Get fresh data from Entra ID
            const entraData = await graphService.syncUserDataFromEntra(dbUser.EntraID);
            if (!entraData) {
                logger.warn(`User ${dbUser.EntraID} not found in Entra ID, skipping sync`);
                errors.push(`User ${dbUser.EntraID} not found in Entra ID directory`);
                errorCount++;
                continue;
            }

            // Update user data in database
            const updateQuery = `
                UPDATE Users SET 
                    FirstName = @FirstName,
                    LastName = @LastName,
                    Email = @Email,
                    ContactNumber = @ContactNumber,
                    ModifiedBy = @ModifiedBy,
                    ModifiedDate = GETUTCDATE()
                WHERE UserID = @UserID;
            `;

            const updateParams: QueryParameter[] = [
                { name: 'UserID', type: sql.Int, value: dbUser.UserID },
                { name: 'FirstName', type: sql.NVarChar, value: entraData.firstName },
                { name: 'LastName', type: sql.NVarChar, value: entraData.lastName },
                { name: 'Email', type: sql.NVarChar, value: entraData.email },
                { name: 'ContactNumber', type: sql.NVarChar, value: entraData.contactNumber },
                { name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId }
            ];

            await executeQuery(updateQuery, updateParams);
            syncedCount++;

            logger.info(`Successfully synced UserID ${dbUser.UserID}`);

        } catch (error) {
            logger.error(`Error syncing user ${dbUser.UserID} (${dbUser.EntraID}):`, error);
            errors.push(`Failed to sync user ${dbUser.EntraID}: ${error instanceof Error ? error.message : String(error)}`);
            errorCount++;
        }
    }

    logger.info(`Bulk sync completed. Synced: ${syncedCount}, Errors: ${errorCount}, Total: ${totalUsers}`);

    return {
        status: 200,
        jsonBody: {
            message: `Bulk sync completed. ${syncedCount} users synced successfully.`,
            totalUsers: totalUsers,
            syncedCount: syncedCount,
            errorCount: errorCount,
            errors: errors.length > 0 ? errors : undefined
        }
    };
}

// Register the function
app.http('syncUserFromEntra', {
    methods: ['POST', 'PUT'],
    route: 'sync-user-from-entra/{entraId?}',
    authLevel: 'anonymous',
    handler: syncUserFromEntra
}); 