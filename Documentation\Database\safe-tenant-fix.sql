-- Safe Tenant ID Fix Script
-- This script safely adds TenantID columns to both tables before attempting updates

-- ===========================================
-- STEP 1: CHECK AND ADD TENANTID COLUMNS
-- ===========================================

PRINT 'Step 1: Checking table structures...';

-- Check if TenantID exists in Companies table
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'TenantID')
BEGIN
    PRINT 'Adding TenantID column to Companies table...';
    ALTER TABLE Companies ADD TenantID NVARCHAR(50) NULL;
    PRINT 'TenantID column added to Companies table';
END
ELSE
BEGIN
    PRINT 'TenantID column already exists in Companies table';
END

-- Check if TenantID exists in Users table  
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
BEGIN
    PRINT 'Adding TenantID column to Users table...';
    ALTER TABLE Users ADD TenantID NVARCHAR(50) NULL;
    PRINT 'TenantID column added to Users table';
END
ELSE
BEGIN
    PRINT 'TenantID column already exists in Users table';
END

-- ===========================================
-- STEP 2: UPDATE COMPANIES TABLE
-- ===========================================

PRINT '';
PRINT 'Step 2: Updating Companies table with tenant IDs...';

-- Show current companies first
PRINT 'Current companies:';
SELECT CompanyID, CompanyName, CompanyCode FROM Companies ORDER BY CompanyName;

-- Update each company with its correct tenant ID
UPDATE Companies SET TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' WHERE CompanyName = 'Avirata Defence Systems';
UPDATE Companies SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyName = 'SASMOS HET';
UPDATE Companies SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' WHERE CompanyName = 'SASMOS Group';
UPDATE Companies SET TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' WHERE CompanyName = 'FE-SIL';
UPDATE Companies SET TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' WHERE CompanyName = 'Glodesi';
UPDATE Companies SET TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' WHERE CompanyName = 'Hanuka';
UPDATE Companies SET TenantID = 'PLACEHOLDER_WWH_TENANT_ID' WHERE CompanyName = 'West Wire Harnessing';

PRINT 'Companies updated with tenant IDs';

-- Show updated companies
PRINT '';
PRINT 'Companies with tenant IDs:';
SELECT CompanyID, CompanyName, CompanyCode, TenantID FROM Companies ORDER BY CompanyName;

-- ===========================================
-- STEP 3: UPDATE USERS TABLE
-- ===========================================

PRINT '';
PRINT 'Step 3: Updating Users table with tenant IDs...';

-- Show current users first
PRINT 'Current users before update:';
SELECT UserID, Email, CompanyID, TenantID FROM Users ORDER BY Email;

-- Update users based on their CompanyID
UPDATE Users 
SET TenantID = 
    CASE 
        WHEN CompanyID = 1 THEN 'ecb4a448-4a99-443b-aaff-063150b6c9ea'  -- Avirata Defence Systems
        WHEN CompanyID = 2 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS HET
        WHEN CompanyID = 3 THEN '334d188b-2ac3-43a9-8bad-590957b087c2'  -- SASMOS Group (same as HET)
        WHEN CompanyID = 4 THEN 'd6a5d909-b6c5-4724-a46d-2641d73acff1'  -- FE-SIL
        WHEN CompanyID = 5 THEN '7732add2-c45c-472b-8da8-4e2b4699bbb0'  -- Glodesi
        WHEN CompanyID = 6 THEN 'a8dcc1ff-5cc0-4432-827b-9da18737a775'  -- Hanuka
        WHEN CompanyID = 7 THEN 'PLACEHOLDER_WWH_TENANT_ID'              -- West Wire Harnessing (placeholder)
        ELSE TenantID -- Keep current if unknown company
    END,
    ModifiedDate = GETUTCDATE(),
    ModifiedBy = 1  -- System update
WHERE CompanyID IN (1, 2, 3, 4, 5, 6, 7);

PRINT 'Users updated with tenant IDs';

-- ===========================================
-- STEP 4: VERIFICATION
-- ===========================================

PRINT '';
PRINT 'Step 4: Final verification...';

-- Show users after update
PRINT 'Users after update:';
SELECT UserID, Email, CompanyID, TenantID FROM Users ORDER BY Email;

-- Show tenant distribution
PRINT '';
PRINT 'User distribution by tenant:';
SELECT 
    TenantID,
    COUNT(*) as UserCount,
    STRING_AGG(Email, ', ') as UserEmails
FROM Users
GROUP BY TenantID
ORDER BY TenantID;

-- ===========================================
-- COMPLETION MESSAGE
-- ===========================================

PRINT '';
PRINT '============================================';
PRINT 'TENANT ID FIX COMPLETED SUCCESSFULLY!';
PRINT '============================================';
PRINT '';
PRINT 'What was done:';
PRINT '1. Added TenantID columns to both Companies and Users tables (if missing)';
PRINT '2. Updated Companies table with correct Azure tenant IDs';
PRINT '3. Updated Users table with correct tenant IDs based on company';
PRINT '4. Verified all updates completed successfully';
PRINT '';
PRINT 'Expected results:';
PRINT '- <EMAIL> → Avirata tenant (ecb4a448-4a99-443b-aaff-063150b6c9ea)';
PRINT '- <EMAIL> → SASMOS HET tenant (334d188b-2ac3-43a9-8bad-590957b087c2)';
PRINT '- Other users → Their respective company tenant IDs';
PRINT '';
PRINT 'IMPORTANT: WestWire Harnessing users have placeholder tenant ID';
PRINT 'Update WWH tenant ID once you have the actual value from Azure'; 