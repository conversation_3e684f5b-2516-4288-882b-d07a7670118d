"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const feather_icons_react_1 = require("feather-icons-react");
const CURRENT_VERSION = "v0.0.3";
const Sidebar = () => {
    const [openMenu, setOpenMenu] = (0, react_1.useState)(null);
    const location = (0, react_router_dom_1.useLocation)();
    // Re-introduce nested structure for Admin Hub
    const navLinks = [
        { path: '/dashboard', label: 'Dashboard', icon: feather_icons_react_1.Home },
        { path: '/knowledge', label: 'Knowledge Hub', icon: feather_icons_react_1.Briefcase },
        { path: '/it', label: 'IT Hub', icon: feather_icons_react_1.Settings },
        { path: '/hr', label: 'HR Hub', icon: feather_icons_react_1.Heart },
        { path: '/admin-hub', label: 'Admin Hub', icon: feather_icons_react_1.Globe },
        {
            path: '/portal-admin',
            label: 'Portal Admin',
            icon: feather_icons_react_1.Shield,
            children: [
                { path: '/portal-admin/user-management', label: 'User Management', icon: feather_icons_react_1.Users },
                { path: '/portal-admin/role-management', label: 'Role Management', icon: feather_icons_react_1.UserCheck },
            ]
        },
        { path: '/communication', label: 'Communication Hub', icon: feather_icons_react_1.MessageSquare },
    ];
    const baseClasses = "flex items-center px-4 py-2 text-gray-300 rounded-md hover:bg-gray-700 hover:text-white";
    const activeClasses = "bg-gray-600 text-white font-semibold";
    const childBaseClasses = "flex items-center pl-11 pr-4 py-2 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white text-sm";
    const childActiveClasses = "bg-gray-600 text-white font-semibold";
    const handleMenuToggle = (label) => {
        setOpenMenu(openMenu === label ? null : label);
    };
    const isParentActive = (parentPath) => {
        return location.pathname.startsWith(parentPath) && (parentPath === '/portal-admin' ? location.pathname !== '/portal-admin' : true);
    };
    // Determine initial open state based on current path
    react_1.default.useEffect(() => {
        const activeParent = navLinks.find(link => link.children && isParentActive(link.path));
        if (activeParent) {
            setOpenMenu(activeParent.label);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (<aside className="w-64 bg-black h-screen p-4 fixed top-0 left-0 overflow-y-auto flex flex-col">
      <div className="text-xl mb-6 text-white">
        <span className="font-bold">Falcon</span><span className="font-light">Hub</span>
      </div>
      <nav className="flex-grow">
        <ul>
          {navLinks.map(link => (<li key={link.path} className="mb-1">
              {link.children ? (<>
                  <button onClick={() => handleMenuToggle(link.label)} className={`${baseClasses} w-full justify-between ${(isParentActive(link.path) && openMenu !== link.label) || location.pathname === link.path ? activeClasses : ''} ${openMenu === link.label ? 'bg-gray-700' : ''}`}>
                    <div className="flex items-center">
                      <link.icon size={18} className="mr-3 flex-shrink-0"/>
                      {link.label}
                    </div>
                    {openMenu === link.label ? <feather_icons_react_1.ChevronDown size={16}/> : <feather_icons_react_1.ChevronRight size={16}/>}
                  </button>
                  {openMenu === link.label && (<ul className="mt-1 space-y-1">
                      {link.children.map(child => (<li key={child.path}>
                           <react_router_dom_1.NavLink to={child.path} className={({ isActive }) => `${childBaseClasses} ${isActive ? childActiveClasses : ''}`}>
                            <child.icon size={16} className="mr-3 flex-shrink-0"/> 
                            {child.label}
                           </react_router_dom_1.NavLink>
                        </li>))}
                    </ul>)}
                </>) : (<react_router_dom_1.NavLink to={link.path} className={({ isActive }) => `${baseClasses} ${isActive ? activeClasses : ''}`}>
                  <link.icon size={18} className="mr-3 flex-shrink-0"/>
                  {link.label}
                </react_router_dom_1.NavLink>)}
            </li>))}
        </ul>
      </nav>
      <div className="mt-auto pt-4 border-t border-gray-700 text-center text-xs text-gray-500">
        Version {CURRENT_VERSION}
      </div>
    </aside>);
};
exports.default = Sidebar;
//# sourceMappingURL=Sidebar.js.map