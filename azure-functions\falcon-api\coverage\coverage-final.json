{"C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\DeleteRole\\index.ts": {"path": "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\DeleteRole\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 89}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 44}}, "2": {"start": {"line": 5, "column": 19}, "end": {"line": 5, "column": 48}}, "3": {"start": {"line": 6, "column": 4}, "end": {"line": 6, "column": 77}}, "4": {"start": {"line": 8, "column": 4}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 9, "column": 8}, "end": {"line": 12, "column": 10}}, "6": {"start": {"line": 15, "column": 29}, "end": {"line": 15, "column": 34}}, "7": {"start": {"line": 16, "column": 4}, "end": {"line": 100, "column": null}}, "8": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 133}}, "9": {"start": {"line": 18, "column": 8}, "end": {"line": 23, "column": null}}, "10": {"start": {"line": 19, "column": 12}, "end": {"line": 22, "column": 14}}, "11": {"start": {"line": 25, "column": 25}, "end": {"line": 25, "column": 58}}, "12": {"start": {"line": 26, "column": 8}, "end": {"line": 31, "column": null}}, "13": {"start": {"line": 27, "column": 12}, "end": {"line": 30, "column": 14}}, "14": {"start": {"line": 33, "column": 31}, "end": {"line": 33, "column": 118}}, "15": {"start": {"line": 34, "column": 34}, "end": {"line": 34, "column": 113}}, "16": {"start": {"line": 35, "column": 8}, "end": {"line": 37, "column": null}}, "17": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 86}}, "18": {"start": {"line": 39, "column": 43}, "end": {"line": 46, "column": 10}}, "19": {"start": {"line": 47, "column": 36}, "end": {"line": 47, "column": 102}}, "20": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 80}}, "21": {"start": {"line": 48, "column": 71}, "end": {"line": 48, "column": 79}}, "22": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 48}}, "23": {"start": {"line": 51, "column": 8}, "end": {"line": 51, "column": 34}}, "24": {"start": {"line": 53, "column": 8}, "end": {"line": 53, "column": 66}}, "25": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 95}}, "26": {"start": {"line": 56, "column": 8}, "end": {"line": 66, "column": null}}, "27": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 118}}, "28": {"start": {"line": 58, "column": 12}, "end": {"line": 65, "column": null}}, "29": {"start": {"line": 59, "column": 35}, "end": {"line": 60, "column": 66}}, "30": {"start": {"line": 61, "column": 16}, "end": {"line": 64, "column": null}}, "31": {"start": {"line": 62, "column": 20}, "end": {"line": 63, "column": 71}}, "32": {"start": {"line": 68, "column": 8}, "end": {"line": 68, "column": 70}}, "33": {"start": {"line": 69, "column": 29}, "end": {"line": 69, "column": 111}}, "34": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 49}}, "35": {"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 35}}, "36": {"start": {"line": 74, "column": 8}, "end": {"line": 80, "column": null}}, "37": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 100}}, "38": {"start": {"line": 76, "column": 12}, "end": {"line": 79, "column": 14}}, "39": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 62}}, "40": {"start": {"line": 83, "column": 8}, "end": {"line": 85, "column": 10}}, "41": {"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": null}}, "42": {"start": {"line": 89, "column": 12}, "end": {"line": 89, "column": 92}}, "43": {"start": {"line": 90, "column": 12}, "end": {"line": 90, "column": 55}}, "44": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 106}}, "45": {"start": {"line": 93, "column": 8}, "end": {"line": 99, "column": 10}}, "46": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 7}}, "47": {"start": {"line": 103, "column": 0}, "end": {"line": 108, "column": 3}}}, "fnMap": {"0": {"name": "deleteRole", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 32}}, "loc": {"start": {"line": 4, "column": 81}, "end": {"line": 101, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 66}, "end": {"line": 48, "column": 67}}, "loc": {"start": {"line": 48, "column": 71}, "end": {"line": 48, "column": 79}}}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 4}, "end": {"line": 13, "column": null}}, "type": "if", "locations": [{"start": {"line": 8, "column": 4}, "end": {"line": 13, "column": null}}]}, "1": {"loc": {"start": {"line": 18, "column": 8}, "end": {"line": 23, "column": null}}, "type": "if", "locations": [{"start": {"line": 18, "column": 8}, "end": {"line": 23, "column": null}}]}, "2": {"loc": {"start": {"line": 26, "column": 8}, "end": {"line": 31, "column": null}}, "type": "if", "locations": [{"start": {"line": 26, "column": 8}, "end": {"line": 31, "column": null}}]}, "3": {"loc": {"start": {"line": 26, "column": 12}, "end": {"line": 26, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 12}, "end": {"line": 26, "column": 31}}, {"start": {"line": 26, "column": 35}, "end": {"line": 26, "column": 62}}]}, "4": {"loc": {"start": {"line": 34, "column": 34}, "end": {"line": 34, "column": 113}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 72}, "end": {"line": 34, "column": 106}}, {"start": {"line": 34, "column": 109}, "end": {"line": 34, "column": 113}}]}, "5": {"loc": {"start": {"line": 35, "column": 8}, "end": {"line": 37, "column": null}}, "type": "if", "locations": [{"start": {"line": 35, "column": 8}, "end": {"line": 37, "column": null}}]}, "6": {"loc": {"start": {"line": 56, "column": 8}, "end": {"line": 66, "column": null}}, "type": "if", "locations": [{"start": {"line": 56, "column": 8}, "end": {"line": 66, "column": null}}]}, "7": {"loc": {"start": {"line": 61, "column": 16}, "end": {"line": 64, "column": null}}, "type": "if", "locations": [{"start": {"line": 61, "column": 16}, "end": {"line": 64, "column": null}}]}, "8": {"loc": {"start": {"line": 74, "column": 8}, "end": {"line": 80, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 8}, "end": {"line": 80, "column": null}}]}, "9": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": null}}, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": null}}]}, "10": {"loc": {"start": {"line": 92, "column": 56}, "end": {"line": 92, "column": 102}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 81}, "end": {"line": 92, "column": 94}}, {"start": {"line": 92, "column": 97}, "end": {"line": 92, "column": 102}}]}, "11": {"loc": {"start": {"line": 97, "column": 23}, "end": {"line": 97, "column": 92}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 48}, "end": {"line": 97, "column": 61}}, {"start": {"line": 97, "column": 64}, "end": {"line": 97, "column": 92}}]}}, "s": {"0": 1, "1": 1, "2": 5, "3": 5, "4": 5, "5": 1, "6": 4, "7": 4, "8": 4, "9": 4, "10": 1, "11": 3, "12": 3, "13": 1, "14": 2, "15": 2, "16": 2, "17": 0, "18": 2, "19": 2, "20": 2, "21": 3, "22": 2, "23": 2, "24": 2, "25": 2, "26": 1, "27": 1, "28": 1, "29": 2, "30": 2, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 0, "38": 0, "39": 1, "40": 1, "41": 1, "42": 1, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1}, "f": {"0": 5, "1": 3}, "b": {"0": [1], "1": [1], "2": [1], "3": [3, 3], "4": [2, 0], "5": [0], "6": [1], "7": [1], "8": [0], "9": [1], "10": [1, 0], "11": [1, 0]}}, "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\shared\\db.ts": {"path": "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\shared\\db.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 29}}, "1": {"start": {"line": 3, "column": 27}, "end": {"line": 17, "column": 2}}, "2": {"start": {"line": 20, "column": 38}, "end": {"line": 20, "column": 42}}, "3": {"start": {"line": 21, "column": 61}, "end": {"line": 21, "column": 65}}, "4": {"start": {"line": 23, "column": 16}, "end": {"line": 51, "column": 1}}, "5": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": null}}, "6": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 20}}, "7": {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": null}}, "8": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 34}}, "9": {"start": {"line": 33, "column": 4}, "end": {"line": 48, "column": 7}}, "10": {"start": {"line": 34, "column": 8}, "end": {"line": 47, "column": null}}, "11": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": 110}}, "12": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 50}}, "13": {"start": {"line": 37, "column": 12}, "end": {"line": 37, "column": 33}}, "14": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 59}}, "15": {"start": {"line": 40, "column": 12}, "end": {"line": 40, "column": 38}}, "16": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": 26}}, "17": {"start": {"line": 43, "column": 12}, "end": {"line": 43, "column": 62}}, "18": {"start": {"line": 44, "column": 12}, "end": {"line": 44, "column": 24}}, "19": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 38}}, "20": {"start": {"line": 46, "column": 12}, "end": {"line": 46, "column": 24}}, "21": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 30}}, "22": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 39}}, "23": {"start": {"line": 55, "column": 20}, "end": {"line": 55, "column": 41}}, "24": {"start": {"line": 57, "column": 4}, "end": {"line": 80, "column": null}}, "25": {"start": {"line": 58, "column": 8}, "end": {"line": 79, "column": null}}, "26": {"start": {"line": 60, "column": 27}, "end": {"line": 60, "column": 38}}, "27": {"start": {"line": 62, "column": 13}, "end": {"line": 77, "column": null}}, "28": {"start": {"line": 65, "column": 18}, "end": {"line": 65, "column": 41}}, "29": {"start": {"line": 66, "column": 20}, "end": {"line": 77, "column": null}}, "30": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 41}}, "31": {"start": {"line": 68, "column": 20}, "end": {"line": 77, "column": null}}, "32": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 36}}, "33": {"start": {"line": 71, "column": 20}, "end": {"line": 77, "column": null}}, "34": {"start": {"line": 72, "column": 18}, "end": {"line": 72, "column": 36}}, "35": {"start": {"line": 73, "column": 20}, "end": {"line": 77, "column": null}}, "36": {"start": {"line": 74, "column": 18}, "end": {"line": 74, "column": 42}}, "37": {"start": {"line": 76, "column": 18}, "end": {"line": 76, "column": 41}}, "38": {"start": {"line": 78, "column": 12}, "end": {"line": 78, "column": 47}}, "39": {"start": {"line": 81, "column": 4}, "end": {"line": 87, "column": null}}, "40": {"start": {"line": 82, "column": 24}, "end": {"line": 82, "column": 50}}, "41": {"start": {"line": 83, "column": 9}, "end": {"line": 83, "column": 23}}, "42": {"start": {"line": 85, "column": 9}, "end": {"line": 85, "column": 94}}, "43": {"start": {"line": 86, "column": 9}, "end": {"line": 86, "column": 19}}, "44": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 7}}, "45": {"start": {"line": 91, "column": 0}, "end": {"line": 97, "column": 3}}, "46": {"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}, "47": {"start": {"line": 93, "column": 9}, "end": {"line": 93, "column": 54}}, "48": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 27}}, "49": {"start": {"line": 95, "column": 9}, "end": {"line": 95, "column": 51}}}, "fnMap": {"0": {"name": "(anonymous_6)", "decl": {"start": {"line": 23, "column": 16}, "end": {"line": 23, "column": 21}}, "loc": {"start": {"line": 23, "column": 56}, "end": {"line": 51, "column": 1}}}, "1": {"name": "(anonymous_7)", "decl": {"start": {"line": 33, "column": 37}, "end": {"line": 33, "column": 42}}, "loc": {"start": {"line": 33, "column": 63}, "end": {"line": 48, "column": 5}}}, "2": {"name": "execute<PERSON>uery", "decl": {"start": {"line": 53, "column": 22}, "end": {"line": 53, "column": 34}}, "loc": {"start": {"line": 53, "column": 81}, "end": {"line": 88, "column": 1}}}, "3": {"name": "(anonymous_9)", "decl": {"start": {"line": 91, "column": 19}, "end": {"line": 91, "column": 24}}, "loc": {"start": {"line": 91, "column": 30}, "end": {"line": 97, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 33}}, {"start": {"line": 6, "column": 37}, "end": {"line": 6, "column": 39}}]}, "1": {"loc": {"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 14}, "end": {"line": 7, "column": 37}}, {"start": {"line": 7, "column": 41}, "end": {"line": 7, "column": 43}}]}, "2": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": null}}, "type": "if", "locations": [{"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": null}}]}, "3": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 12}}, {"start": {"line": 24, "column": 16}, "end": {"line": 24, "column": 30}}]}, "4": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": null}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": null}}]}, "5": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 80, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 80, "column": null}}]}, "6": {"loc": {"start": {"line": 62, "column": 13}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 62, "column": 13}, "end": {"line": 77, "column": null}}, {"start": {"line": 66, "column": 20}, "end": {"line": 77, "column": null}}]}, "7": {"loc": {"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 62, "column": 17}, "end": {"line": 62, "column": 31}}, {"start": {"line": 62, "column": 35}, "end": {"line": 62, "column": 54}}]}, "8": {"loc": {"start": {"line": 66, "column": 20}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 66, "column": 20}, "end": {"line": 77, "column": null}}, {"start": {"line": 68, "column": 20}, "end": {"line": 77, "column": null}}]}, "9": {"loc": {"start": {"line": 68, "column": 20}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 68, "column": 20}, "end": {"line": 77, "column": null}}, {"start": {"line": 71, "column": 20}, "end": {"line": 77, "column": null}}]}, "10": {"loc": {"start": {"line": 71, "column": 20}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 71, "column": 20}, "end": {"line": 77, "column": null}}, {"start": {"line": 73, "column": 20}, "end": {"line": 77, "column": null}}]}, "11": {"loc": {"start": {"line": 73, "column": 20}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 73, "column": 20}, "end": {"line": 77, "column": null}}, {"start": {"line": 75, "column": 20}, "end": {"line": 77, "column": null}}]}, "12": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 1, "45": 1, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [1, 1], "1": [1, 1], "2": [0], "3": [0, 0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0]}}, "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\authUtils.ts": {"path": "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\authUtils.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 71}}, "3": {"start": {"line": 23, "column": 19}, "end": {"line": 23, "column": 63}}, "4": {"start": {"line": 24, "column": 4}, "end": {"line": 27, "column": null}}, "5": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 81}}, "6": {"start": {"line": 26, "column": 8}, "end": {"line": 26, "column": 20}}, "7": {"start": {"line": 29, "column": 4}, "end": {"line": 41, "column": null}}, "8": {"start": {"line": 30, "column": 24}, "end": {"line": 30, "column": 71}}, "9": {"start": {"line": 31, "column": 43}, "end": {"line": 31, "column": 62}}, "10": {"start": {"line": 33, "column": 8}, "end": {"line": 36, "column": null}}, "11": {"start": {"line": 34, "column": 13}, "end": {"line": 34, "column": 113}}, "12": {"start": {"line": 35, "column": 13}, "end": {"line": 35, "column": 25}}, "13": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 25}}, "14": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 95}}, "15": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 20}}, "16": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 16}}, "17": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": null}}, "18": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 21}}, "19": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}, "20": {"start": {"line": 57, "column": 41}, "end": {"line": 57, "column": 69}}, "21": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 20}}, "22": {"start": {"line": 62, "column": 4}, "end": {"line": 69, "column": null}}, "23": {"start": {"line": 63, "column": 8}, "end": {"line": 68, "column": null}}, "24": {"start": {"line": 64, "column": 12}, "end": {"line": 65, "column": 45}}, "25": {"start": {"line": 67, "column": 12}, "end": {"line": 67, "column": 24}}, "26": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 17}}, "27": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 16}}, "28": {"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": null}}, "29": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 92}}, "30": {"start": {"line": 85, "column": 8}, "end": {"line": 85, "column": 20}}, "31": {"start": {"line": 89, "column": 21}, "end": {"line": 89, "column": 131}}, "32": {"start": {"line": 89, "column": 53}, "end": {"line": 89, "column": 130}}, "33": {"start": {"line": 90, "column": 20}, "end": {"line": 90, "column": 33}}, "34": {"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}, "35": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 97}}, "36": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 108}}, "37": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 20}}, "38": {"start": {"line": 98, "column": 4}, "end": {"line": 121, "column": null}}, "39": {"start": {"line": 99, "column": 22}, "end": {"line": 99, "column": 91}}, "40": {"start": {"line": 100, "column": 23}, "end": {"line": 100, "column": 70}}, "41": {"start": {"line": 102, "column": 8}, "end": {"line": 116, "column": null}}, "42": {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 53}}, "43": {"start": {"line": 106, "column": 12}, "end": {"line": 109, "column": 15}}, "44": {"start": {"line": 107, "column": 16}, "end": {"line": 107, "column": 114}}, "45": {"start": {"line": 111, "column": 12}, "end": {"line": 111, "column": 26}}, "46": {"start": {"line": 113, "column": 12}, "end": {"line": 113, "column": 102}}, "47": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 102}}, "48": {"start": {"line": 115, "column": 12}, "end": {"line": 115, "column": 24}}, "49": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 100}}, "50": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 101}}, "51": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 20}}, "52": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 7}}}, "fnMap": {"0": {"name": "getClientPrincipal", "decl": {"start": {"line": 22, "column": 16}, "end": {"line": 22, "column": 34}}, "loc": {"start": {"line": 22, "column": 55}, "end": {"line": 42, "column": 1}}}, "1": {"name": "hasRequiredRole", "decl": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 31}}, "loc": {"start": {"line": 52, "column": 90}, "end": {"line": 72, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 57, "column": 33}, "end": {"line": 57, "column": 37}}, "loc": {"start": {"line": 57, "column": 41}, "end": {"line": 57, "column": 69}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 63, "column": 34}, "end": {"line": 63, "column": 39}}, "loc": {"start": {"line": 64, "column": 12}, "end": {"line": 65, "column": 45}}}, "4": {"name": "getUserIdFromPrincipal", "decl": {"start": {"line": 82, "column": 22}, "end": {"line": 82, "column": 44}}, "loc": {"start": {"line": 82, "column": 106}, "end": {"line": 122, "column": 1}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 89, "column": 44}, "end": {"line": 89, "column": 49}}, "loc": {"start": {"line": 89, "column": 53}, "end": {"line": 89, "column": 130}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 106, "column": 46}, "end": {"line": 106, "column": 49}}, "loc": {"start": {"line": 106, "column": 52}, "end": {"line": 109, "column": 13}}}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 27, "column": null}}, "type": "if", "locations": [{"start": {"line": 24, "column": 4}, "end": {"line": 27, "column": null}}]}, "1": {"loc": {"start": {"line": 33, "column": 8}, "end": {"line": 36, "column": null}}, "type": "if", "locations": [{"start": {"line": 33, "column": 8}, "end": {"line": 36, "column": null}}]}, "2": {"loc": {"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 12}, "end": {"line": 33, "column": 22}}, {"start": {"line": 33, "column": 26}, "end": {"line": 33, "column": 43}}, {"start": {"line": 33, "column": 47}, "end": {"line": 33, "column": 67}}]}, "3": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": null}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 55, "column": null}}]}, "4": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}]}, "5": {"loc": {"start": {"line": 62, "column": 4}, "end": {"line": 69, "column": null}}, "type": "if", "locations": [{"start": {"line": 62, "column": 4}, "end": {"line": 69, "column": null}}]}, "6": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 68, "column": null}}, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 68, "column": null}}]}, "7": {"loc": {"start": {"line": 64, "column": 12}, "end": {"line": 65, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 34}}, {"start": {"line": 64, "column": 38}, "end": {"line": 64, "column": 60}}, {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 45}}]}, "8": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": null}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 86, "column": null}}]}, "9": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}]}, "10": {"loc": {"start": {"line": 102, "column": 8}, "end": {"line": 116, "column": null}}, "type": "if", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 116, "column": null}}, {"start": {"line": 112, "column": 15}, "end": {"line": 116, "column": null}}]}, "11": {"loc": {"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 12}, "end": {"line": 102, "column": 28}}, {"start": {"line": 102, "column": 32}, "end": {"line": 102, "column": 59}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 4, "4": 4, "5": 1, "6": 1, "7": 3, "8": 3, "9": 3, "10": 2, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 6, "18": 1, "19": 5, "20": 8, "21": 2, "22": 3, "23": 2, "24": 2, "25": 2, "26": 1, "27": 1, "28": 6, "29": 1, "30": 1, "31": 5, "32": 5, "33": 5, "34": 5, "35": 1, "36": 1, "37": 1, "38": 4, "39": 4, "40": 4, "41": 3, "42": 2, "43": 2, "44": 1, "45": 2, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1}, "f": {"0": 4, "1": 6, "2": 8, "3": 2, "4": 6, "5": 5, "6": 1}, "b": {"0": [1], "1": [1], "2": [2, 2, 1], "3": [1], "4": [2], "5": [2], "6": [2], "7": [2, 1, 2], "8": [1], "9": [1], "10": [2, 1], "11": [3, 3]}}, "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\validationSchemas.ts": {"path": "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\validationSchemas.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "1": {"start": {"line": 5, "column": 23}, "end": {"line": 7, "column": 1}}, "2": {"start": {"line": 6, "column": 4}, "end": {"line": 6, "column": 81}}, "3": {"start": {"line": 6, "column": 33}, "end": {"line": 6, "column": 68}}, "4": {"start": {"line": 12, "column": 13}, "end": {"line": 20, "column": 3}}, "5": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 97}}, "6": {"start": {"line": 23, "column": 13}, "end": {"line": 40, "column": 3}}, "7": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 97}}, "8": {"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 97}}, "9": {"start": {"line": 43, "column": 13}, "end": {"line": 45, "column": 3}}, "10": {"start": {"line": 48, "column": 13}, "end": {"line": 57, "column": 3}}, "11": {"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 69}}, "12": {"start": {"line": 52, "column": 26}, "end": {"line": 52, "column": 48}}, "13": {"start": {"line": 55, "column": 29}, "end": {"line": 55, "column": 46}}, "14": {"start": {"line": 61, "column": 13}, "end": {"line": 78, "column": 3}}, "15": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": 69}}, "16": {"start": {"line": 65, "column": 26}, "end": {"line": 65, "column": 48}}, "17": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": 46}}, "18": {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 69}}, "19": {"start": {"line": 73, "column": 26}, "end": {"line": 73, "column": 48}}, "20": {"start": {"line": 76, "column": 29}, "end": {"line": 76, "column": 46}}, "21": {"start": {"line": 83, "column": 13}, "end": {"line": 85, "column": 3}}, "22": {"start": {"line": 88, "column": 13}, "end": {"line": 90, "column": 3}}, "23": {"start": {"line": 93, "column": 13}, "end": {"line": 97, "column": 3}}, "24": {"start": {"line": 110, "column": 4}, "end": {"line": 113, "column": null}}, "25": {"start": {"line": 111, "column": 8}, "end": {"line": 111, "column": 97}}, "26": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 20}}, "27": {"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": 41}}, "28": {"start": {"line": 117, "column": 4}, "end": {"line": 128, "column": null}}, "29": {"start": {"line": 118, "column": 29}, "end": {"line": 118, "column": 57}}, "30": {"start": {"line": 119, "column": 8}, "end": {"line": 119, "column": 80}}, "31": {"start": {"line": 121, "column": 8}, "end": {"line": 127, "column": 10}}, "32": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 58}}, "33": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 16}}, "34": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 16}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 23}, "end": {"line": 5, "column": 24}}, "loc": {"start": {"line": 5, "column": 53}, "end": {"line": 7, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 6, "column": 28}, "end": {"line": 6, "column": 29}}, "loc": {"start": {"line": 6, "column": 33}, "end": {"line": 6, "column": 68}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 9}}, "loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 97}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 9}}, "loc": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 97}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 9}}, "loc": {"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 97}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 9}}, "loc": {"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 69}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 52, "column": 17}, "end": {"line": 52, "column": 18}}, "loc": {"start": {"line": 52, "column": 26}, "end": {"line": 52, "column": 48}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 55, "column": 20}, "end": {"line": 55, "column": 21}}, "loc": {"start": {"line": 55, "column": 29}, "end": {"line": 55, "column": 46}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 9}}, "loc": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": 69}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 65, "column": 17}, "end": {"line": 65, "column": 18}}, "loc": {"start": {"line": 65, "column": 26}, "end": {"line": 65, "column": 48}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": 21}}, "loc": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": 46}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 9}}, "loc": {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 69}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 73, "column": 17}, "end": {"line": 73, "column": 18}}, "loc": {"start": {"line": 73, "column": 26}, "end": {"line": 73, "column": 48}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 76, "column": 20}, "end": {"line": 76, "column": 21}}, "loc": {"start": {"line": 76, "column": 29}, "end": {"line": 76, "column": 46}}}, "14": {"name": "validateRequest", "decl": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 31}}, "loc": {"start": {"line": 109, "column": 113}, "end": {"line": 133, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 15, "column": 74}, "end": {"line": 15, "column": 91}}, {"start": {"line": 15, "column": 94}, "end": {"line": 15, "column": 97}}]}, "1": {"loc": {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 50}}, {"start": {"line": 15, "column": 54}, "end": {"line": 15, "column": 71}}]}, "2": {"loc": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 29, "column": 74}, "end": {"line": 29, "column": 91}}, {"start": {"line": 29, "column": 94}, "end": {"line": 29, "column": 97}}]}, "3": {"loc": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 50}}, {"start": {"line": 29, "column": 54}, "end": {"line": 29, "column": 71}}]}, "4": {"loc": {"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 97}}, "type": "cond-expr", "locations": [{"start": {"line": 35, "column": 74}, "end": {"line": 35, "column": 91}}, {"start": {"line": 35, "column": 94}, "end": {"line": 35, "column": 97}}]}, "5": {"loc": {"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 27}, "end": {"line": 35, "column": 50}}, {"start": {"line": 35, "column": 54}, "end": {"line": 35, "column": 71}}]}, "6": {"loc": {"start": {"line": 50, "column": 27}, "end": {"line": 50, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 50, "column": 53}, "end": {"line": 50, "column": 63}}, {"start": {"line": 50, "column": 66}, "end": {"line": 50, "column": 69}}]}, "7": {"loc": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 53}, "end": {"line": 63, "column": 63}}, {"start": {"line": 63, "column": 66}, "end": {"line": 63, "column": 69}}]}, "8": {"loc": {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 71, "column": 53}, "end": {"line": 71, "column": 63}}, {"start": {"line": 71, "column": 66}, "end": {"line": 71, "column": 69}}]}, "9": {"loc": {"start": {"line": 110, "column": 4}, "end": {"line": 113, "column": null}}, "type": "if", "locations": [{"start": {"line": 110, "column": 4}, "end": {"line": 113, "column": null}}]}, "10": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 128, "column": null}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 128, "column": null}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 6, "6": 1, "7": 6, "8": 6, "9": 1, "10": 1, "11": 4, "12": 4, "13": 1, "14": 1, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 1, "22": 1, "23": 1, "24": 3, "25": 1, "26": 1, "27": 2, "28": 2, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1}, "f": {"0": 1, "1": 1, "2": 6, "3": 6, "4": 6, "5": 4, "6": 4, "7": 1, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 3}, "b": {"0": [3, 3], "1": [6, 3], "2": [2, 4], "3": [6, 2], "4": [2, 4], "5": [6, 2], "6": [4, 0], "7": [0, 0], "8": [0, 0], "9": [1], "10": [1]}}, "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\services\\userManagementService.ts": {"path": "C:\\GitHub\\FalconHub\\FalconHub\\azure-functions\\falcon-api\\src\\shared\\services\\userManagementService.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "1": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 41}}, "2": {"start": {"line": 57, "column": 32}, "end": {"line": 203, "column": 1}}, "3": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 73}}, "4": {"start": {"line": 60, "column": 27}, "end": {"line": 64, "column": 6}}, "5": {"start": {"line": 66, "column": 4}, "end": {"line": 202, "column": null}}, "6": {"start": {"line": 67, "column": 35}, "end": {"line": 67, "column": 96}}, "7": {"start": {"line": 69, "column": 8}, "end": {"line": 195, "column": null}}, "8": {"start": {"line": 70, "column": 41}, "end": {"line": 70, "column": 72}}, "9": {"start": {"line": 71, "column": 12}, "end": {"line": 71, "column": 99}}, "10": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 32}}, "11": {"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": 92}}, "12": {"start": {"line": 80, "column": 43}, "end": {"line": 80, "column": 47}}, "13": {"start": {"line": 81, "column": 12}, "end": {"line": 105, "column": null}}, "14": {"start": {"line": 82, "column": 37}, "end": {"line": 82, "column": 121}}, "15": {"start": {"line": 83, "column": 16}, "end": {"line": 95, "column": null}}, "16": {"start": {"line": 84, "column": 42}, "end": {"line": 84, "column": 114}}, "17": {"start": {"line": 85, "column": 20}, "end": {"line": 91, "column": null}}, "18": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 73}}, "19": {"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 163}}, "20": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 137}}, "21": {"start": {"line": 93, "column": 21}, "end": {"line": 93, "column": 129}}, "22": {"start": {"line": 94, "column": 21}, "end": {"line": 94, "column": 103}}, "23": {"start": {"line": 101, "column": 17}, "end": {"line": 101, "column": 31}}, "24": {"start": {"line": 102, "column": 17}, "end": {"line": 102, "column": 139}}, "25": {"start": {"line": 108, "column": 46}, "end": {"line": 108, "column": 50}}, "26": {"start": {"line": 109, "column": 12}, "end": {"line": 123, "column": null}}, "27": {"start": {"line": 110, "column": 35}, "end": {"line": 110, "column": 157}}, "28": {"start": {"line": 111, "column": 17}, "end": {"line": 122, "column": null}}, "29": {"start": {"line": 112, "column": 40}, "end": {"line": 112, "column": 133}}, "30": {"start": {"line": 113, "column": 21}, "end": {"line": 118, "column": null}}, "31": {"start": {"line": 114, "column": 25}, "end": {"line": 114, "column": 77}}, "32": {"start": {"line": 117, "column": 25}, "end": {"line": 117, "column": 181}}, "33": {"start": {"line": 121, "column": 21}, "end": {"line": 121, "column": 128}}, "34": {"start": {"line": 126, "column": 36}, "end": {"line": 136, "column": 14}}, "35": {"start": {"line": 137, "column": 34}, "end": {"line": 147, "column": 14}}, "36": {"start": {"line": 149, "column": 34}, "end": {"line": 149, "column": 84}}, "37": {"start": {"line": 151, "column": 12}, "end": {"line": 154, "column": null}}, "38": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 110}}, "39": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 70}}, "40": {"start": {"line": 156, "column": 40}, "end": {"line": 156, "column": 66}}, "41": {"start": {"line": 157, "column": 12}, "end": {"line": 157, "column": 104}}, "42": {"start": {"line": 160, "column": 36}, "end": {"line": 160, "column": 79}}, "43": {"start": {"line": 161, "column": 12}, "end": {"line": 161, "column": 81}}, "44": {"start": {"line": 162, "column": 47}, "end": {"line": 162, "column": 51}}, "45": {"start": {"line": 163, "column": 12}, "end": {"line": 173, "column": null}}, "46": {"start": {"line": 164, "column": 35}, "end": {"line": 164, "column": 106}}, "47": {"start": {"line": 165, "column": 36}, "end": {"line": 165, "column": 96}}, "48": {"start": {"line": 166, "column": 17}, "end": {"line": 170, "column": null}}, "49": {"start": {"line": 167, "column": 21}, "end": {"line": 167, "column": 68}}, "50": {"start": {"line": 169, "column": 22}, "end": {"line": 169, "column": 110}}, "51": {"start": {"line": 172, "column": 17}, "end": {"line": 172, "column": 96}}, "52": {"start": {"line": 175, "column": 12}, "end": {"line": 192, "column": null}}, "53": {"start": {"line": 176, "column": 44}, "end": {"line": 179, "column": 18}}, "54": {"start": {"line": 180, "column": 16}, "end": {"line": 191, "column": null}}, "55": {"start": {"line": 181, "column": 21}, "end": {"line": 186, "column": 24}}, "56": {"start": {"line": 187, "column": 21}, "end": {"line": 187, "column": 132}}, "57": {"start": {"line": 190, "column": 21}, "end": {"line": 190, "column": 113}}, "58": {"start": {"line": 194, "column": 12}, "end": {"line": 194, "column": 31}}, "59": {"start": {"line": 198, "column": 8}, "end": {"line": 198, "column": 95}}, "60": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 20}}, "61": {"start": {"line": 57, "column": 13}, "end": {"line": 57, "column": 32}}, "62": {"start": {"line": 215, "column": 32}, "end": {"line": 264, "column": 1}}, "63": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 84}}, "64": {"start": {"line": 218, "column": 23}, "end": {"line": 222, "column": 6}}, "65": {"start": {"line": 224, "column": 4}, "end": {"line": 263, "column": null}}, "66": {"start": {"line": 225, "column": 28}, "end": {"line": 225, "column": 94}}, "67": {"start": {"line": 227, "column": 8}, "end": {"line": 259, "column": null}}, "68": {"start": {"line": 229, "column": 39}, "end": {"line": 229, "column": 63}}, "69": {"start": {"line": 230, "column": 12}, "end": {"line": 244, "column": null}}, "70": {"start": {"line": 232, "column": 16}, "end": {"line": 232, "column": 157}}, "71": {"start": {"line": 233, "column": 36}, "end": {"line": 237, "column": 18}}, "72": {"start": {"line": 238, "column": 16}, "end": {"line": 238, "column": 125}}, "73": {"start": {"line": 239, "column": 16}, "end": {"line": 239, "column": 28}}, "74": {"start": {"line": 242, "column": 16}, "end": {"line": 242, "column": 112}}, "75": {"start": {"line": 243, "column": 16}, "end": {"line": 243, "column": 28}}, "76": {"start": {"line": 247, "column": 12}, "end": {"line": 247, "column": 97}}, "77": {"start": {"line": 248, "column": 32}, "end": {"line": 251, "column": 14}}, "78": {"start": {"line": 252, "column": 12}, "end": {"line": 257, "column": 15}}, "79": {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 24}}, "80": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 81}}, "81": {"start": {"line": 262, "column": 8}, "end": {"line": 262, "column": 21}}, "82": {"start": {"line": 215, "column": 13}, "end": {"line": 215, "column": 32}}, "83": {"start": {"line": 274, "column": 34}, "end": {"line": 304, "column": 1}}, "84": {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 86}}, "85": {"start": {"line": 277, "column": 24}, "end": {"line": 281, "column": 6}}, "86": {"start": {"line": 283, "column": 4}, "end": {"line": 303, "column": null}}, "87": {"start": {"line": 284, "column": 23}, "end": {"line": 288, "column": 10}}, "88": {"start": {"line": 291, "column": 8}, "end": {"line": 299, "column": null}}, "89": {"start": {"line": 292, "column": 12}, "end": {"line": 292, "column": 109}}, "90": {"start": {"line": 293, "column": 12}, "end": {"line": 293, "column": 24}}, "91": {"start": {"line": 295, "column": 12}, "end": {"line": 295, "column": 115}}, "92": {"start": {"line": 298, "column": 12}, "end": {"line": 298, "column": 24}}, "93": {"start": {"line": 301, "column": 8}, "end": {"line": 301, "column": 82}}, "94": {"start": {"line": 302, "column": 8}, "end": {"line": 302, "column": 21}}, "95": {"start": {"line": 274, "column": 13}, "end": {"line": 274, "column": 34}}, "96": {"start": {"line": 312, "column": 35}, "end": {"line": 322, "column": 1}}, "97": {"start": {"line": 313, "column": 4}, "end": {"line": 313, "column": 64}}, "98": {"start": {"line": 314, "column": 18}, "end": {"line": 314, "column": 86}}, "99": {"start": {"line": 315, "column": 4}, "end": {"line": 321, "column": null}}, "100": {"start": {"line": 316, "column": 8}, "end": {"line": 316, "column": 54}}, "101": {"start": {"line": 317, "column": 8}, "end": {"line": 317, "column": 20}}, "102": {"start": {"line": 319, "column": 8}, "end": {"line": 319, "column": 82}}, "103": {"start": {"line": 320, "column": 8}, "end": {"line": 320, "column": 21}}, "104": {"start": {"line": 312, "column": 13}, "end": {"line": 312, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 57, "column": 32}, "end": {"line": 57, "column": 37}}, "loc": {"start": {"line": 57, "column": 120}, "end": {"line": 203, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 215, "column": 32}, "end": {"line": 215, "column": 37}}, "loc": {"start": {"line": 215, "column": 117}, "end": {"line": 264, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 274, "column": 34}, "end": {"line": 274, "column": 39}}, "loc": {"start": {"line": 274, "column": 118}, "end": {"line": 304, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 312, "column": 35}, "end": {"line": 312, "column": 40}}, "loc": {"start": {"line": 312, "column": 78}, "end": {"line": 322, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 57, "column": 65}, "end": {"line": 57, "column": 92}}, "type": "default-arg", "locations": [{"start": {"line": 57, "column": 91}, "end": {"line": 57, "column": 92}}]}, "1": {"loc": {"start": {"line": 69, "column": 8}, "end": {"line": 195, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 8}, "end": {"line": 195, "column": null}}, {"start": {"line": 76, "column": 15}, "end": {"line": 195, "column": null}}]}, "2": {"loc": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 40}}, {"start": {"line": 69, "column": 44}, "end": {"line": 69, "column": 83}}]}, "3": {"loc": {"start": {"line": 81, "column": 12}, "end": {"line": 105, "column": null}}, "type": "if", "locations": [{"start": {"line": 81, "column": 12}, "end": {"line": 105, "column": null}}, {"start": {"line": 96, "column": 19}, "end": {"line": 105, "column": null}}]}, "4": {"loc": {"start": {"line": 85, "column": 20}, "end": {"line": 91, "column": null}}, "type": "if", "locations": [{"start": {"line": 85, "column": 20}, "end": {"line": 91, "column": null}}, {"start": {"line": 87, "column": 27}, "end": {"line": 91, "column": null}}]}, "5": {"loc": {"start": {"line": 85, "column": 24}, "end": {"line": 85, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 24}, "end": {"line": 85, "column": 47}}, {"start": {"line": 85, "column": 51}, "end": {"line": 85, "column": 85}}]}, "6": {"loc": {"start": {"line": 109, "column": 12}, "end": {"line": 123, "column": null}}, "type": "if", "locations": [{"start": {"line": 109, "column": 12}, "end": {"line": 123, "column": null}}]}, "7": {"loc": {"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 109, "column": 16}, "end": {"line": 109, "column": 36}}, {"start": {"line": 109, "column": 40}, "end": {"line": 109, "column": 49}}]}, "8": {"loc": {"start": {"line": 113, "column": 21}, "end": {"line": 118, "column": null}}, "type": "if", "locations": [{"start": {"line": 113, "column": 21}, "end": {"line": 118, "column": null}}, {"start": {"line": 115, "column": 28}, "end": {"line": 118, "column": null}}]}, "9": {"loc": {"start": {"line": 113, "column": 25}, "end": {"line": 113, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 113, "column": 25}, "end": {"line": 113, "column": 45}}, {"start": {"line": 113, "column": 49}, "end": {"line": 113, "column": 80}}]}, "10": {"loc": {"start": {"line": 140, "column": 23}, "end": {"line": 140, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 23}, "end": {"line": 140, "column": 37}}, {"start": {"line": 140, "column": 41}, "end": {"line": 140, "column": 68}}]}, "11": {"loc": {"start": {"line": 141, "column": 27}, "end": {"line": 141, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 27}, "end": {"line": 141, "column": 46}}, {"start": {"line": 141, "column": 50}, "end": {"line": 141, "column": 52}}]}, "12": {"loc": {"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": 43}}, {"start": {"line": 142, "column": 47}, "end": {"line": 142, "column": 53}}]}, "13": {"loc": {"start": {"line": 151, "column": 12}, "end": {"line": 154, "column": null}}, "type": "if", "locations": [{"start": {"line": 151, "column": 12}, "end": {"line": 154, "column": null}}]}, "14": {"loc": {"start": {"line": 151, "column": 16}, "end": {"line": 151, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 16}, "end": {"line": 151, "column": 40}}, {"start": {"line": 151, "column": 44}, "end": {"line": 151, "column": 80}}]}, "15": {"loc": {"start": {"line": 160, "column": 36}, "end": {"line": 160, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 160, "column": 36}, "end": {"line": 160, "column": 65}}, {"start": {"line": 160, "column": 69}, "end": {"line": 160, "column": 79}}]}, "16": {"loc": {"start": {"line": 166, "column": 17}, "end": {"line": 170, "column": null}}, "type": "if", "locations": [{"start": {"line": 166, "column": 17}, "end": {"line": 170, "column": null}}, {"start": {"line": 168, "column": 24}, "end": {"line": 170, "column": null}}]}, "17": {"loc": {"start": {"line": 166, "column": 21}, "end": {"line": 166, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 166, "column": 21}, "end": {"line": 166, "column": 41}}, {"start": {"line": 166, "column": 45}, "end": {"line": 166, "column": 76}}]}, "18": {"loc": {"start": {"line": 175, "column": 12}, "end": {"line": 192, "column": null}}, "type": "if", "locations": [{"start": {"line": 175, "column": 12}, "end": {"line": 192, "column": null}}]}, "19": {"loc": {"start": {"line": 227, "column": 8}, "end": {"line": 259, "column": null}}, "type": "if", "locations": [{"start": {"line": 227, "column": 8}, "end": {"line": 259, "column": null}}, {"start": {"line": 245, "column": 15}, "end": {"line": 259, "column": null}}]}, "20": {"loc": {"start": {"line": 227, "column": 12}, "end": {"line": 227, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 227, "column": 12}, "end": {"line": 227, "column": 33}}, {"start": {"line": 227, "column": 37}, "end": {"line": 227, "column": 69}}]}, "21": {"loc": {"start": {"line": 230, "column": 12}, "end": {"line": 244, "column": null}}, "type": "if", "locations": [{"start": {"line": 230, "column": 12}, "end": {"line": 244, "column": null}}, {"start": {"line": 240, "column": 19}, "end": {"line": 244, "column": null}}]}, "22": {"loc": {"start": {"line": 291, "column": 8}, "end": {"line": 299, "column": null}}, "type": "if", "locations": [{"start": {"line": 291, "column": 8}, "end": {"line": 299, "column": null}}, {"start": {"line": 294, "column": 15}, "end": {"line": 299, "column": null}}]}, "23": {"loc": {"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 291, "column": 12}, "end": {"line": 291, "column": 31}}, {"start": {"line": 291, "column": 35}, "end": {"line": 291, "column": 63}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 6, "4": 6, "5": 6, "6": 6, "7": 5, "8": 1, "9": 1, "10": 1, "11": 4, "12": 4, "13": 4, "14": 3, "15": 3, "16": 3, "17": 3, "18": 2, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 3, "26": 3, "27": 3, "28": 3, "29": 3, "30": 3, "31": 2, "32": 1, "33": 0, "34": 3, "35": 3, "36": 3, "37": 3, "38": 0, "39": 0, "40": 3, "41": 3, "42": 3, "43": 3, "44": 3, "45": 3, "46": 3, "47": 3, "48": 3, "49": 2, "50": 1, "51": 0, "52": 3, "53": 2, "54": 2, "55": 2, "56": 2, "57": 0, "58": 3, "59": 2, "60": 2, "61": 1, "62": 1, "63": 4, "64": 4, "65": 4, "66": 4, "67": 3, "68": 2, "69": 2, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 1, "84": 3, "85": 3, "86": 3, "87": 3, "88": 2, "89": 1, "90": 1, "91": 1, "92": 1, "93": 1, "94": 1, "95": 1, "96": 1, "97": 2, "98": 2, "99": 2, "100": 2, "101": 1, "102": 1, "103": 1, "104": 1}, "f": {"0": 6, "1": 4, "2": 3, "3": 2}, "b": {"0": [2], "1": [1, 4], "2": [5, 5], "3": [3, 1], "4": [2, 1], "5": [3, 3], "6": [3], "7": [3, 3], "8": [2, 1], "9": [3, 3], "10": [3, 0], "11": [3, 0], "12": [3, 0], "13": [0], "14": [3, 3], "15": [3, 0], "16": [2, 1], "17": [3, 3], "18": [2], "19": [2, 1], "20": [3, 3], "21": [1, 1], "22": [1, 1], "23": [2, 2]}}}