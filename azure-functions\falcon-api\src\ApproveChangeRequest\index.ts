import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';
import { EmailService, EmailNotificationData } from "../shared/services/emailService";

export async function approveChangeRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('ApproveChangeRequest function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json() as any;
        const { comments = '', userId } = body;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required for approval'
                    }
                }
            };
        }

        // First, check if the request exists and can be approved
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const currentRequest = checkResult.recordset[0];
        
        // Check if request can be approved
        if (!['Under Review'].includes(currentRequest.Status)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be approved in current status: ${currentRequest.Status}`
                    }
                }
            };
        }

        // Update the request to approved status
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Approved',
                ApprovedBy = @userId,
                ApprovedDate = GETDATE(),
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Approved', @userId, GETDATE(), @comments
            )
        `;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: comments || 'Change request approved' }
        ];

        await executeQuery(historyQuery, historyParams);

        // Add approval comment if provided
        if (comments.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, 'ApprovalNote', 0, @userId, GETDATE()
                )
            `;

            const commentParams: QueryParameter[] = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: comments },
                { name: 'userId', type: sql.Int, value: parseInt(userId) }
            ];

            await executeQuery(commentQuery, commentParams);
        }

        // Get comprehensive request details for email notification
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Status as status,
                cr.Priority as priority,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                cr.ApprovedDate as approvedDate,
                CONCAT(approver.FirstName, ' ', approver.LastName) as approverName,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                CONCAT(assignee.FirstName, ' ', assignee.LastName) as assigneeName,
                assignee.Email as assigneeEmail
            FROM ChangeRequests cr
                LEFT JOIN Users approver ON cr.ApprovedBy = approver.UserID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON requester.CompanyID = c.CompanyID
                LEFT JOIN Users assignee ON cr.AssignedTo = assignee.UserID
            WHERE cr.RequestID = @requestId
        `;

        const detailsResult = await executeQuery(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];

        // Send email notification asynchronously
        try {
            const emailData: EmailNotificationData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                assigneeName: requestDetails.assigneeName,
                assigneeEmail: requestDetails.assigneeEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                comments: comments,
                approverName: requestDetails.approverName,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.dueDate ? new Date(requestDetails.dueDate) : undefined
            };

            // Send approval notification (don't await to avoid blocking the response)
            EmailService.getInstance().sendChangeRequestApproved(emailData).catch((error: any) => {
                context.error('Failed to send approval email notification:', error);
            });

            context.log(`Email notification queued for approved change request ${requestId}`);
        } catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the approval if email fails
        }

        context.log(`Successfully approved change request ${requestId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request approved successfully',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    approvedDate: requestDetails.approvedDate,
                    approverName: requestDetails.approverName,
                    requesterName: requestDetails.requesterName
                }
            }
        };

    } catch (error: any) {
        context.error('Error in ApproveChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while approving the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('ApproveChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/approve',
    handler: approveChangeRequest
}); 