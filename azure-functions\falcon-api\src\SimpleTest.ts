import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";

export async function simpleTest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('SimpleTest function executed');
    
    return {
        status: 200,
        jsonBody: {
            message: "Hello from SimpleTest function!",
            timestamp: new Date().toISOString()
        }
    };
}

app.http('SimpleTest', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'simple-test',
    handler: simpleTest
}); 