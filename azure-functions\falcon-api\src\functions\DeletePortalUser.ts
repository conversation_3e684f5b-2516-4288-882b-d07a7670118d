import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { userManagementService } from "../shared/services/userManagementService";
import { getClientPrincipal, isAdmin } from "../shared/authUtils"; 
import { logger } from "../shared/utils/logger";

export async function DeletePortalUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log = logger.info; // Use logger for context.log
    logger.info(`Http function processed request for url "${request.url}" to delete user.`);

    // Authentication bypass for development mode
    if (process.env.NODE_ENV === 'development') {
        logger.warn("DeletePortalUser: Bypassing authentication in development mode.");
    } else {
        const principal = getClientPrincipal(request);
        if (!principal) {
            logger.warn("DeletePortalUser: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }

        // Ensure the user is an administrator
        if (!isAdmin(principal)) {
            logger.warn(`DeletePortalUser: Forbidden. User ${principal.userDetails} (ID: ${principal.userId}) is not an admin.`);
            return { status: 403, jsonBody: { error: "Forbidden. Administrator role required." } };
        }
    }

    const entraId = request.params.entraId;
    if (!entraId) {
        logger.warn("DeletePortalUser: Entra ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
    }

    try {
        // We need the internal UserID of the authenticated admin to log who made the change.
        // For local development, if admin doesn't exist in DB, use default system user ID
        let adminInternalUserId: number;
        
        if (process.env.NODE_ENV === 'development') {
            // In development mode, use default system user ID
            adminInternalUserId = 1;
            logger.warn("DeletePortalUser: Using default system user ID (1) for audit in development mode.");
        } else {
            const principal = getClientPrincipal(request);
            if (!principal) {
                logger.warn("DeletePortalUser: Principal should exist at this point.");
                return { status: 500, jsonBody: { error: "Internal error: Principal missing." } };
            }
            
            adminInternalUserId = await userManagementService.getUserIdByEntraId(principal.userId) || 1;
            if (adminInternalUserId === 1) {
                logger.warn(`DeletePortalUser: Admin user ${principal.userDetails} (ID: ${principal.userId}) not found in database. Using default system user ID for audit.`);
            }
        }

        const success = await userManagementService.deactivatePortalUserByEntraId(entraId, adminInternalUserId);

        if (success) {
            logger.info(`DeletePortalUser: Successfully deactivated user with Entra ID ${entraId}.`);
            return { status: 204 }; // No content, successful deletion (soft delete)
        } else {
            logger.warn(`DeletePortalUser: User with Entra ID ${entraId} not found or already inactive.`);
            return { status: 404, jsonBody: { error: "User not found or already inactive." } };
        }
    } catch (err) {
        logger.error(`Error in DeletePortalUser for Entra ID ${entraId}:`, err);
        const error = err as Error;
        return { status: 500, jsonBody: { error: error.message || "Failed to delete user due to an internal error." } };
    }
}

app.http('DeletePortalUser', {
    methods: ['DELETE'],
    authLevel: 'anonymous', // Keep as anonymous for now, relying on in-code isAdmin check
    route: 'portal-users/{entraId}',
    handler: DeletePortalUser // Corrected handler name to match the exported function
}); 