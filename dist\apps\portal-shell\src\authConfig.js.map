{"version": 3, "file": "authConfig.js", "sourceRoot": "", "sources": ["../../../../apps/portal-shell/src/authConfig.ts"], "names": [], "mappings": ";;;AAAA,sDAA8D;AAE9D,qBAAqB;AACR,QAAA,UAAU,GAAkB;IACrC,IAAI,EAAE;QACF,QAAQ,EAAE,sCAAsC,EAAE,wCAAwC;QAC1F,SAAS,EAAE,wEAAwE,EAAE,YAAY;QACjG,WAAW,EAAE,GAAG,EAAE,+DAA+D;QACjF,qBAAqB,EAAE,GAAG,EAAE,2CAA2C;KAC1E;IACD,KAAK,EAAE;QACH,aAAa,EAAE,gBAAgB,EAAE,qCAAqC;QACtE,sBAAsB,EAAE,KAAK,EAAE,+BAA+B;KACjE;IACD,MAAM,EAAE;QACJ,aAAa,EAAE;YACX,cAAc,EAAE,CAAC,KAAe,EAAE,OAAe,EAAE,WAAoB,EAAE,EAAE;gBACvE,IAAI,WAAW,EAAE,CAAC;oBACd,OAAO;gBACX,CAAC;gBACD,QAAQ,KAAK,EAAE,CAAC;oBACZ,KAAK,uBAAQ,CAAC,KAAK;wBACf,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;wBACvB,OAAO;oBACX,KAAK,uBAAQ,CAAC,IAAI;wBACd,oDAAoD;wBACpD,OAAO;oBACX,KAAK,uBAAQ,CAAC,OAAO;wBACjB,qDAAqD;wBACrD,OAAO;oBACX,KAAK,uBAAQ,CAAC,OAAO;wBACjB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACtB,OAAO;oBACX;wBACI,OAAO;gBACf,CAAC;YACL,CAAC;YACD,QAAQ,EAAE,uBAAQ,CAAC,OAAO,EAAE,6DAA6D;SAC5F;KACJ;CACJ,CAAC;AAEF,oFAAoF;AACvE,QAAA,YAAY,GAAG;IACxB,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,uCAAuC;CAChE,CAAC;AAEF,4EAA4E;AAC/D,QAAA,WAAW,GAAG;IACvB,eAAe,EAAE,qCAAqC;CACzD,CAAC"}