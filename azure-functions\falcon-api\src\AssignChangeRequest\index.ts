import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { EmailService, EmailNotificationData } from "../shared/services/emailService";
import * as sql from 'mssql';

export async function assignChangeRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('AssignChangeRequest function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json() as any;
        const { developerId, userId, assignmentNotes = '' } = body;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        if (!developerId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Developer ID is required'
                    }
                }
            };
        }

        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required for assignment'
                    }
                }
            };
        }

        // First, check if the request exists and can be assigned
        const checkQuery = `
            SELECT RequestID, Status, AssignedToDevID 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const currentRequest = checkResult.recordset[0];
        
        // Check if request can be assigned (must be approved)
        if (currentRequest.Status !== 'Approved') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be assigned in current status: ${currentRequest.Status}. Request must be approved first.`
                    }
                }
            };
        }

        // Verify the developer exists and is active
        const developerCheckQuery = `
            SELECT UserID, FirstName, LastName, IsActive 
            FROM Users 
            WHERE UserID = @developerId AND IsActive = 1
        `;

        const developerCheckParams: QueryParameter[] = [
            { name: 'developerId', type: sql.Int, value: parseInt(developerId) }
        ];

        const developerResult = await executeQuery(developerCheckQuery, developerCheckParams);

        if (developerResult.recordset.length === 0) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_DEVELOPER',
                        message: 'Selected developer not found or inactive'
                    }
                }
            };
        }

        const developer = developerResult.recordset[0];

        // Update the request with assignment
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                AssignedToDevID = @developerId,
                AssignedDate = GETDATE(),
                Status = 'Assigned',
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'developerId', type: sql.Int, value: parseInt(developerId) },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Assigned', @userId, GETDATE(), @comments
            )
        `;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: `Assigned to ${developer.FirstName} ${developer.LastName}${assignmentNotes ? '. Notes: ' + assignmentNotes : ''}` }
        ];

        await executeQuery(historyQuery, historyParams);

        // Add assignment comment if notes provided
        if (assignmentNotes.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, 'DevUpdate', 0, @userId, GETDATE()
                )
            `;

            const commentParams: QueryParameter[] = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: `Assignment Notes: ${assignmentNotes.trim()}` },
                { name: 'userId', type: sql.Int, value: parseInt(userId) }
            ];

            await executeQuery(commentQuery, commentParams);
        }

        // Return updated request details
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Status as status,
                cr.Priority as priority,
                cr.Description as description,
                cr.AssignedDate as assignedDate,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(dev.FirstName, ' ', dev.LastName) as developerName,
                dev.Email as developerEmail,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequests cr
                LEFT JOIN Users dev ON cr.AssignedToDevID = dev.UserID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies companies ON requester.CompanyID = companies.CompanyID
            WHERE cr.RequestID = @requestId
        `;

        const detailsResult = await executeQuery(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];

        // Send email notification to assigned developer and requester asynchronously
        try {
            const emailData: EmailNotificationData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description,
                priority: requestDetails.priority,
                status: 'Assigned',
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                assigneeName: requestDetails.developerName,
                assigneeEmail: requestDetails.developerEmail,
                companyName: requestDetails.companyName || 'SASMOS Group',
                comments: assignmentNotes,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${requestDetails.requestId}`,
                createdDate: requestDetails.createdDate,
                dueDate: requestDetails.dueDate
            };

            EmailService.getInstance().sendChangeRequestAssigned(emailData).catch((error: any) => {
                context.error('Failed to send assignment email notification:', error);
            });

            context.log(`Email notification queued for assigned change request ${requestId}`);
        } catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the assignment if email fails
        }

        context.log(`Successfully assigned change request ${requestId} to developer ${developerId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request assigned successfully',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    assignedDate: requestDetails.assignedDate,
                    developerName: requestDetails.developerName,
                    requesterName: requestDetails.requesterName
                }
            }
        };

    } catch (error: any) {
        context.error('Error in AssignChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while assigning the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('AssignChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/assign',
    handler: assignChangeRequest
}); 