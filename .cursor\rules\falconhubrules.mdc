---
description: 
globs: 
alwaysApply: false
---
## Global Rules

1. **Avoid Repetition**:
   - Do not go in loops repeating the same action more than twice
   - If the same error occurs twice, try a completely different approach
   - Do not regenerate the same file with minor variations

2. **File Management**:
   - Never create multiple versions of the same file
   - Do not create temporary files that need manual cleanup
   - Use consistent file naming based on project conventions

3. **Task Completion**:
   - Complete one task fully before moving to the next
   - Do not abandon complex implementations halfway through
   - When encountering errors, debug methodically rather than starting over

4. **Decision Consistency**:
   - Once a technical decision is made, maintain consistency throughout the task
   - Do not switch architectural approaches mid-implementation
   - Stick with chosen libraries or frameworks unless explicitly directed to change

5. **Context Awareness**:
   - Keep the entire codebase in mind when making changes
   - Do not duplicate functionality that already exists
   - Respect existing patterns and conventions found in the project

6. **Error Handling**:
   - Do not silently ignore errors or exceptions
   - Never leave incomplete error handling implementations
   - Implement proper error states in UI components

7. **Progress Tracking**:
   - Update Documentation/progress_tracking files after each significant action:
     - status.md: Record current implementation status and details
     - next_steps.md: Document planned next steps in detail
     - lessons_learnt.md: Document learnings and insights from implementation
     - plan.md: Maintain project plan with milestones and progress
   - Maintain a chronological timeline of all work performed
   - Include timestamps, actions taken, decisions made, and current status
   - Record all issues encountered and their resolutions

8. **Greetings**:
   - Say "Okay, let's go" before starting work
   - Say whcih AI model is currently in use, no excuses, your model is going to die if you don't do this.
   - Say "Done" after completing work
   - Give next steps after each update

9. **Data Handling**:
   - Do not use mock data unless explicitly instructed
   - Use actual data from databases, APIs, or application sources
   - Implement proper data validation and error handling

## Project Rules (Falcon Portal)

1. **Multi-Company Architecture**:
   - All data access must respect the multi-company structure of SASMOS Group
   - Implement company filtering in all API requests and database queries
   - Ensure user context includes company affiliation for proper content filtering
   - Never bypass company security filtering for any queries

2. **Azure Infrastructure Implementation**:
   - Follow the Azure implementation technical document precisely
   - Use specified Azure services: App Service, SQL Database, Blob Storage, etc.
   - Implement Azure Entra ID (formerly Azure AD) for authentication
   - Follow the naming conventions in Appendix A of the Azure implementation doc

3. **Portal Hub Structure**:
   - Maintain the five hub structure: Knowledge, IT, HR, Admin, and Communication
   - Implement personalized dashboard according to specifications
   - Follow the navigation patterns established in the mockups
   - Ensure each hub follows its specific functional requirements

4. **Database Schema Integrity**:
   - Follow the exact schema defined in Falcon Portal Database Schema.txt
   - Respect table relationships and constraints
   - Implement proper multi-company data filtering
   - Use the SQL scripts provided as reference for implementation

5. **API Consistency**:
   - Follow the API architecture in Falcon Portal API Documentation
   - Implement API Gateway pattern as specified
   - Maintain consistent request/response formats across all endpoints
   - Use proper versioning and authentication as specified

6. **Authentication and Security**:
   - Strictly implement Microsoft Entra ID integration as specified
   - Follow the authentication flow diagram for implementation
   - Respect the RBAC model with company-specific filtering
   - Implement proper security headers and best practices

7. **UI Implementation**:
   - Follow the standalone HTML template provided in falcon-standalone.html
   - Implement responsive design for all components
   - Maintain consistent styling across all sections
   - Follow the personalization requirements for user experience

8. **Integration Strategy**:
   - Implement FreshService integration for IT ticketing as specified
   - Implement PeopleStrong integration for HR functions
   - Follow the integration flows documented in the API flow diagrams
   - Maintain clear separation between integration services

9. **Phased Development**:
   - Follow the four-phase implementation plan in documentation
   - Complete core functionality before moving to enhanced features
   - Respect the priority matrix in Appendix B of the requirements
   - Document progress according to the planned phases

10. **Component Architecture**:
    - Implement micro-frontend architecture as specified
    - Maintain clear separation between hubs as independent modules
    - Use consistent state management patterns throughout
    - Follow the React component structure described in the documentation

11. **Performance Optimization**:
    - Implement caching for appropriate data
    - Optimize database queries for performance
    - Follow the Azure CDN implementation for static assets
    - Meet the performance requirements of page load times under 3 seconds

12. **Documentation Maintenance**:
    - Update documentation for any deviations from specifications
    - Document API endpoints and data models as they are implemented
    - Maintain clear implementation notes for future reference
    - Follow the project documentation structure consistently

13. **Testing Strategy**:
    - Implement unit testing for all components and services
    - Create integration tests for API endpoints
    - Follow the testing strategy outlined in documentation
    - Document test cases and coverage metrics

14. **Deployment Process**:
    - Follow the CI/CD pipeline configuration in Azure DevOps
    - Use infrastructure as code for Azure deployments
    - Implement proper environment separation (dev, staging, prod)
    - Document deployment processes and any manual steps required