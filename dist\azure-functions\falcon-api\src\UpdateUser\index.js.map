{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/UpdateUser/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAoBA,gCAyLC;AA7MD,gDAAyF;AACzF,qCAA4C;AAC5C,mDAAgD;AAChD,oFAAgG;AAChG,mDAAkG;AAClG,mEAA6G;AAC7G,6BAA6B;AAU7B,0BAA0B;AAC1B,mHAAmH;AACnH,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,qBAAqB;AAE3D,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,OAAO,CAAC,GAAG,CAAC,uDAAuD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QACnF,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE5C,0CAA0C;QAC1C,mDAAmD;QACnD,eAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;QAE3D,sEAAsE;QACtE,IAAI,mBAAmB,GAAW,CAAC,CAAC,CAAC,+BAA+B;QAEpE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;YAC7F,CAAC;YACD,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;gBAC9C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,6CAA6C,aAAa,IAAI,CAAC,CAAC;gBAClI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yDAAyD,EAAE,EAAE,CAAC;YAC9G,CAAC;YACD,MAAM,mBAAmB,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC7E,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,eAAM,CAAC,KAAK,CAAC,6EAA6E,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;gBACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,6EAA6E,EAAE,EAAE,CAAC;YACjI,CAAC;YACD,mBAAmB,GAAG,mBAAmB,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,iCAAiC,mBAAmB,EAAE,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACJ,eAAM,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QACnF,CAAC;QACD,oBAAoB;QAEpB,4BAA4B;QAC5B,4BAA4B;QAC5B,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxD,IAAI,eAAe,GAAG,IAAA,mCAAe,EAAC,2CAAuB,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACzG,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAC5C,wCAAwC;QACxC,MAAM,oBAAoB,GAAG,2CAAuB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACxE,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC,mBAAmB;QAEjE,wBAAwB;QACxB,IAAI,UAAe,CAAC;QACpB,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;QACnF,CAAC;QAED,eAAe,GAAG,IAAA,mCAAe,EAAC,wCAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC7F,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAE5C,wCAAwC;QACxC,MAAM,aAAa,GAAG,wCAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC7D,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,aAAa,CAAC;QACjF,0BAA0B;QAE1B,IAAI,CAAC;YACD,2DAA2D;YAC3D,MAAM,gBAAgB,GAAG,mBAAmB,CAAC;YAE7C,0EAA0E;YAC1E,MAAM,gBAAgB,GAAG;;;;;;;;SAQxB,CAAC;YACF,MAAM,cAAc,GAAqB;gBACrC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;aAC1D,CAAC;YACF,MAAM,cAAc,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YAE5E,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrE,eAAM,CAAC,IAAI,CAAC,wCAAwC,OAAO,aAAa,CAAC,CAAC;gBAC1E,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,qBAAqB,OAAO,aAAa,EAAE;iBACnE,CAAC;YACN,CAAC;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAClD,MAAM,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC7D,MAAM,gBAAgB,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;YAEtI,eAAM,CAAC,IAAI,CAAC,oBAAoB,MAAM,qBAAqB,eAAe,qBAAqB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC9H,eAAM,CAAC,IAAI,CAAC,qBAAqB,iBAAiB,uBAAuB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAE1G,+CAA+C;YAC/C,IAAI,OAAO,iBAAiB,KAAK,SAAS,IAAI,iBAAiB,KAAK,eAAe,EAAE,CAAC;gBAClF,eAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,OAAO,iBAAiB,EAAE,CAAC,CAAC;gBACrF,MAAM,iBAAiB,GAAG,sHAAsH,CAAC;gBACjJ,MAAM,YAAY,GAAqB;oBACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;oBAChD,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,iBAAiB,EAAE;oBAC7D,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;iBACjE,CAAC;gBACF,MAAM,IAAA,iBAAY,EAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;YACxD,CAAC;YAED,qCAAqC;YACrC,IAAI,kBAAkB,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC1D,eAAM,CAAC,IAAI,CAAC,sCAAsC,MAAM,EAAE,CAAC,CAAC;gBAC5D,8CAA8C;gBAC9C,MAAM,YAAY,GAAgB,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC/F,MAAM,YAAY,GAAgB,IAAI,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEzH,8EAA8E;gBAC9E,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7E,MAAM,aAAa,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEhF,iDAAiD;gBACjD,MAAM,cAAc,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;gBACrD,2CAA2C;gBAC3C,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClF,MAAM,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;gBAExF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC9D,MAAM,gBAAgB,GAAG,CAAC,GAAG,eAAe,EAAE,GAAG,kBAAkB,CAAC,CAAC;oBACrE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC9B,MAAM,eAAe,GAAG,yDAAyD,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;wBAC/I,MAAM,YAAY,GAAqB,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;4BACtE,IAAI,EAAE,WAAW,CAAC,EAAE;4BACpB,IAAI,EAAE,GAAG,CAAC,QAAQ;4BAClB,KAAK,EAAE,IAAc,CAAC,yBAAyB;yBAClD,CAAC,CAAC,CAAC;wBAEJ,MAAM,aAAa,GAAG,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;wBACxE,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;wBAC5C,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;4BAClC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,QAAkB,EAAE,GAAG,CAAC,MAAgB,CAAC,CAAC;wBAChE,CAAC,CAAC,CAAC;wBAEH,mBAAmB;wBACnB,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;4BACxC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAkB,CAAC,CAAC;4BACjD,IAAI,MAAM,EAAE,CAAC;gCACT,eAAM,CAAC,KAAK,CAAC,kBAAkB,QAAQ,UAAU,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;gCAClF,6CAA6C;gCAC7C,MAAM,IAAA,0CAAkB,EAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;4BAC/D,CAAC;iCAAM,CAAC;gCACJ,eAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,mDAAmD,CAAC,CAAC;4BAC3F,CAAC;wBACL,CAAC;wBAED,oBAAoB;wBACpB,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;4BACrC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAkB,CAAC,CAAC;4BACjD,IAAI,MAAM,EAAE,CAAC;gCACT,eAAM,CAAC,KAAK,CAAC,gBAAgB,QAAQ,UAAU,MAAM,eAAe,MAAM,EAAE,CAAC,CAAC;gCAC9E,6CAA6C;gCAC7C,MAAM,IAAA,wCAAgB,EAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;4BAC7D,CAAC;iCAAM,CAAC;gCACJ,eAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,oDAAoD,CAAC,CAAC;4BAC5F,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,oDAAoD,MAAM,GAAG,CAAC,CAAC;YAC3E,wDAAwD;YACxD,4EAA4E;YAC5E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;aACtD,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,4CAA4C,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACN,OAAO,EAAE,uDAAuD;oBAChE,KAAK,EAAE,YAAY;iBACtB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW,EAAE,oCAAoC;IAC5D,KAAK,EAAE,wBAAwB,EAAE,sBAAsB;IACvD,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}