<svg viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8f9fa" />
  
  <!-- Title -->
  <text x="500" y="40" font-family="Arial" font-size="24" font-weight="bold" text-anchor="middle">Falcon Portal API Flow Diagram</text>
  
  <!-- Client Component -->
  <rect x="50" y="100" width="200" height="80" rx="5" fill="#b3e0ff" stroke="#0066cc" stroke-width="2" />
  <text x="150" y="145" font-family="Arial" font-size="16" text-anchor="middle">React Frontend</text>
  <text x="150" y="170" font-family="Arial" font-size="12" text-anchor="middle">(Web/Mobile)</text>
  
  <!-- API Gateway -->
  <rect x="350" y="100" width="200" height="80" rx="5" fill="#ffcc99" stroke="#ff8000" stroke-width="2" />
  <text x="450" y="145" font-family="Arial" font-size="16" text-anchor="middle">Azure API Management</text>
  <text x="450" y="170" font-family="Arial" font-size="12" text-anchor="middle">(API Gateway)</text>
  
  <!-- Authentication Service -->
  <rect x="650" y="100" width="200" height="80" rx="5" fill="#c2f0c2" stroke="#339933" stroke-width="2" />
  <text x="750" y="145" font-family="Arial" font-size="16" text-anchor="middle">Microsoft Entra ID</text>
  <text x="750" y="170" font-family="Arial" font-size="12" text-anchor="middle">(Authentication)</text>
  
  <!-- Microservices -->
  <!-- Core Services -->
  <rect x="200" y="250" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="290" y="285" font-family="Arial" font-size="16" text-anchor="middle">Core API Services</text>
  <text x="290" y="305" font-family="Arial" font-size="12" text-anchor="middle">(User, Dashboard, etc)</text>
  
  <!-- Knowledge Hub -->
  <rect x="50" y="350" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="140" y="385" font-family="Arial" font-size="16" text-anchor="middle">Knowledge Hub API</text>
  <text x="140" y="405" font-family="Arial" font-size="12" text-anchor="middle">(Documents, Search)</text>
  
  <!-- IT Hub -->
  <rect x="50" y="450" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="140" y="485" font-family="Arial" font-size="16" text-anchor="middle">IT Hub API</text>
  <text x="140" y="505" font-family="Arial" font-size="12" text-anchor="middle">(Tickets, Software)</text>
  
  <!-- HR Hub -->
  <rect x="50" y="550" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="140" y="585" font-family="Arial" font-size="16" text-anchor="middle">HR Hub API</text>
  <text x="140" y="605" font-family="Arial" font-size="12" text-anchor="middle">(Policies, Requests)</text>
  
  <!-- Admin Hub -->
  <rect x="350" y="350" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="440" y="385" font-family="Arial" font-size="16" text-anchor="middle">Admin Hub API</text>
  <text x="440" y="405" font-family="Arial" font-size="12" text-anchor="middle">(Travel, Facilities)</text>
  
  <!-- Communication Hub -->
  <rect x="350" y="450" width="180" height="70" rx="5" fill="#d9d9f3" stroke="#4d4dff" stroke-width="2" />
  <text x="440" y="485" font-family="Arial" font-size="16" text-anchor="middle">Communication API</text>
  <text x="440" y="505" font-family="Arial" font-size="12" text-anchor="middle">(Announcements, Events)</text>
  
  <!-- Integration Services -->
  <rect x="350" y="550" width="180" height="70" rx="5" fill="#ffd6cc" stroke="#ff6666" stroke-width="2" />
  <text x="440" y="585" font-family="Arial" font-size="16" text-anchor="middle">Integration Services</text>
  <text x="440" y="605" font-family="Arial" font-size="12" text-anchor="middle">(FreshService, PeopleStrong)</text>
  
  <!-- External Systems -->
  <rect x="650" y="350" width="180" height="70" rx="5" fill="#ffe6cc" stroke="#ff9933" stroke-width="2" />
  <text x="740" y="385" font-family="Arial" font-size="16" text-anchor="middle">FreshService</text>
  <text x="740" y="405" font-family="Arial" font-size="12" text-anchor="middle">(IT Service Management)</text>
  
  <rect x="650" y="450" width="180" height="70" rx="5" fill="#ffe6cc" stroke="#ff9933" stroke-width="2" />
  <text x="740" y="485" font-family="Arial" font-size="16" text-anchor="middle">PeopleStrong</text>
  <text x="740" y="505" font-family="Arial" font-size="12" text-anchor="middle">(HR System)</text>
  
  <!-- Backend Services -->
  <rect x="650" y="550" width="180" height="70" rx="5" fill="#d9ead3" stroke="#6aa84f" stroke-width="2" />
  <text x="740" y="585" font-family="Arial" font-size="16" text-anchor="middle">Azure Services</text>
  <text x="740" y="605" font-family="Arial" font-size="12" text-anchor="middle">(Storage, Search, etc)</text>
  
  <!-- Database -->
  <path d="M 610 650 a 70 25 0 1 0 140 0 a 70 25 0 1 0 -140 0" fill="#f2d7d5" stroke="#c0392b" stroke-width="2" />
  <path d="M 610 650 a 70 25 0 0 0 140 0" fill="none" stroke="#c0392b" stroke-width="2" />
  <text x="680" y="655" font-family="Arial" font-size="16" text-anchor="middle">Azure SQL</text>
  
  <!-- Connections -->
  <!-- Frontend to API Gateway -->
  <line x1="250" y1="140" x2="350" y2="140" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- API Gateway to Authentication -->
  <line x1="550" y1="140" x2="650" y2="140" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- API Gateway to Core Services -->
  <line x1="450" y1="180" x2="290" y2="250" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Core to Microservices -->
  <line x1="290" y1="320" x2="290" y2="350" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="290" y1="320" x2="140" y2="350" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="290" y1="320" x2="140" y2="450" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="290" y1="320" x2="140" y2="550" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="290" y1="320" x2="440" y2="350" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="290" y1="320" x2="440" y2="450" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Integration Services to External Systems -->
  <line x1="530" y1="585" x2="650" y2="585" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="530" y1="565" x2="650" y2="485" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="530" y1="545" x2="650" y2="385" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Services to Integration Service -->
  <line x1="140" y1="520" x2="350" y2="565" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="140" y1="620" x2="350" y2="585" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Services to Database -->
  <line x1="230" y1="585" x2="610" y2="650" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="230" y1="485" x2="610" y2="640" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="230" y1="385" x2="610" y2="630" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="440" y1="620" x2="610" y2="650" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="440" y1="520" x2="610" y2="640" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="440" y1="420" x2="610" y2="630" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Services to Azure Services -->
  <line x1="230" y1="380" x2="650" y2="560" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  <line x1="530" y1="380" x2="650" y2="550" stroke="#666666" stroke-width="2" marker-end="url(#arrow)" />
  
  <!-- Arrows definition -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#666666" />
    </marker>
  </defs>
  
  <!-- Legend -->
  <rect x="780" y="620" width="15" height="15" fill="#b3e0ff" stroke="#0066cc" stroke-width="1" />
  <text x="800" y="632" font-family="Arial" font-size="12" text-anchor="start">Client Application</text>
  
  <rect x="780" y="645" width="15" height="15" fill="#ffcc99" stroke="#ff8000" stroke-width="1" />
  <text x="800" y="657" font-family="Arial" font-size="12" text-anchor="start">API Gateway</text>
  
  <rect x="780" y="670" width="15" height="15" fill="#d9d9f3" stroke="#4d4dff" stroke-width="1" />
  <text x="800" y="682" font-family="Arial" font-size="12" text-anchor="start">Microservices</text>
  
  <rect x="900" y="620" width="15" height="15" fill="#ffd6cc" stroke="#ff6666" stroke-width="1" />
  <text x="920" y="632" font-family="Arial" font-size="12" text-anchor="start">Integration Services</text>
  
  <rect x="900" y="645" width="15" height="15" fill="#ffe6cc" stroke="#ff9933" stroke-width="1" />
  <text x="920" y="657" font-family="Arial" font-size="12" text-anchor="start">External Systems</text>
  
  <rect x="900" y="670" width="15" height="15" fill="#d9ead3" stroke="#6aa84f" stroke-width="1" />
  <text x="920" y="682" font-family="Arial" font-size="12" text-anchor="start">Azure Services</text>
</svg>