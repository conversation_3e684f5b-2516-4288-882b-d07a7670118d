"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestSimple = void 0;
const functions_1 = require("@azure/functions");
async function TestSimple(request, context) {
    context.log(`Http function processed request for url "${request.url}"`);
    return {
        status: 200,
        body: 'Hello from simple test function!'
    };
}
exports.TestSimple = TestSimple;
functions_1.app.http('TestSimple', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'test-simple',
    handler: TestSimple
});
//# sourceMappingURL=index.js.map