import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

interface DocumentCategory {
    id: string;
    name: string;
    parentId?: string;
    documentCount: number;
}

export async function getDocumentCategories(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`GetDocumentCategories function invoked.`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = await getUserIdFromPrincipal(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }

        const query = `
            SELECT 
                dc.CategoryID,
                dc.CategoryName,
                dc.ParentCategoryID,
                COUNT(d.DocumentID) as DocumentCount
            FROM DocumentCategories dc
            LEFT JOIN Documents d ON dc.CategoryID = d.CategoryID 
                AND d.IsActive = 1 
                AND d.IsPublished = 1
            WHERE dc.IsActive = 1
            GROUP BY dc.CategoryID, dc.CategoryName, dc.ParentCategoryID
            ORDER BY dc.CategoryName
        `;

        const result = await executeQuery(query, []);
        
        const categories: DocumentCategory[] = result.recordset.map((row: any) => ({
            id: row.CategoryID.toString(),
            name: row.CategoryName,
            parentId: row.ParentCategoryID?.toString(),
            documentCount: row.DocumentCount || 0
        }));

        logger.info(`GetDocumentCategories: Retrieved ${categories.length} categories for user ${userId || 'dev-user'}`);

        return {
            status: 200,
            jsonBody: categories
        };

    } catch (error) {
        logger.error("GetDocumentCategories: Error retrieving categories:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error retrieving document categories.",
                error: errorMessage
            }
        };
    }
}

// Register the function
app.http('GetDocumentCategories', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents/categories',
    handler: getDocumentCategories
}); 