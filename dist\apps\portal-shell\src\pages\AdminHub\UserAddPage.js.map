{"version": 3, "file": "UserAddPage.js", "sourceRoot": "", "sources": ["../../../../../../apps/portal-shell/src/pages/AdminHub/UserAddPage.tsx"], "names": [], "mappings": ";;;;;;;;;;;AAAA,iCAAmD;AACnD,uDAA+C;AAC/C,sDAIiC;AAEjC,MAAM,WAAW,GAAa,GAAG,EAAE;IAC/B,MAAM,QAAQ,GAAG,IAAA,8BAAW,GAAE,CAAC;IAC/B,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IAEhE,wBAAwB;IACxB,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC;IACvC,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,2BAA2B;IACvG,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAwB,QAAQ,CAAC,CAAC;IAEtF,kCAAkC;IAClC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAW,EAAE,CAAC,CAAC;IAEnE,2CAA2C;IAC3C,MAAM,gBAAgB,GAAG,CAAC,IAAY,EAAE,SAAkB,EAAE,EAAE;QAC1D,gBAAgB,CAAC,SAAS,CAAC,EAAE;YACzB,IAAI,SAAS,EAAE,CAAC;gBACZ,kCAAkC;gBAClC,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACJ,cAAc;gBACd,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,gCAAgC;IAChC,IAAA,iBAAS,EAAC,GAAG,EAAE;QACX,MAAM,UAAU,GAAG,GAAS,EAAE;YAC1B,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,MAAM,IAAA,+BAAoB,GAAE,CAAC;gBAC3C,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;gBAC5C,uCAAuC;gBACvC,iBAAiB,CAAC,CAAC,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC;YACtH,CAAC;QACL,CAAC,CAAA,CAAC;QACF,UAAU,EAAE,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,yBAAyB;IACzB,MAAM,UAAU,GAAG,GAAS,EAAE;QAC1B,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;YAC1B,YAAY,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,yBAAyB;QACzB,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;YACjC,YAAY,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO;QACX,CAAC;QAED,uCAAuC;QACvC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,YAAY,CAAC,mCAAmC,CAAC,CAAC;YAClD,OAAO;QACX,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnB,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG;gBACb,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;gBACnB,KAAK,EAAE,aAAa;gBACpB,MAAM,EAAE,cAAc;aACzB,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;YAE7C,6BAA6B;YAC7B,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAgB,EAAC,QAAQ,CAAC,CAAC;YAErD,IAAI,WAAW,EAAE,CAAC;gBACd,4CAA4C;gBAC5C,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACJ,YAAY,CAAC,yCAAyC,CAAC,CAAC;YAC5D,CAAC;QAEL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;YAC3C,MAAM,OAAO,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,0CAA0C,CAAC;YAChG,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC;gBAAS,CAAC;YACP,YAAY,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;IACL,CAAC,CAAA,CAAC;IAEF,OAAO,CACH,CAAC,GAAG,CAAC,SAAS,CAAC,6BAA6B,CACxC;YAAA,CAAC,EAAE,CAAC,SAAS,CAAC,2CAA2C,CAAC,YAAY,EAAE,EAAE,CAE1E;;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,kDAAkD,CAC7D;gBAAA,CAAC,iBAAiB,CAClB;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CACjB;oBAAA,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,8CAA8C,CAC3E;sCAAc,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CACxD;oBAAA,EAAE,KAAK,CACP;oBAAA,CAAC,KAAK,CACF,IAAI,CAAC,OAAO,CACZ,EAAE,CAAC,OAAO,CACV,KAAK,CAAC,CAAC,KAAK,CAAC,CACb,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAC1C,WAAW,CAAC,kBAAkB,CAC9B,SAAS,CAAC,qJAAqJ,CAC/J,QAAQ,EAEZ;oBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CACrC;;oBACJ,EAAE,CAAC,CACP;gBAAA,EAAE,GAAG,CAEL;;gBAAA,CAAC,kBAAkB,CACnB;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CACjB;oBAAA,CAAC,KAAK,CAAC,SAAS,CAAC,8CAA8C,CAC3D;qCAAa,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CACvD;oBAAA,EAAE,KAAK,CACP;oBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAC3B;wBAAA,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CACzB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAC1C;gCAAA,CAAC,KAAK,CACF,EAAE,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CACnB,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,UAAU,CACf,KAAK,CAAC,CAAC,IAAI,CAAC,CACZ,OAAO,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CACtC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAC1D,SAAS,CAAC,uEAAuE,EAErF;gCAAA,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,kCAAkC,CACxE;oCAAA,CAAC,IAAI,CACT;gCAAA,EAAE,KAAK,CACX;4BAAA,EAAE,GAAG,CAAC,CACT,CAAC,CACN;oBAAA,EAAE,GAAG,CACT;gBAAA,EAAE,GAAG,CAEL;;gBAAA,CAAC,gBAAgB,CACjB;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CACjB;oBAAA,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,8CAA8C,CAClF;uCAAe,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CACzD;oBAAA,EAAE,KAAK,CACN;qBAAA,CAAC,MAAM,CACJ,EAAE,CAAC,cAAc,CACjB,KAAK,CAAC,CAAC,cAAc,CAAC,CACtB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,KAA8B,CAAC,CAAC,CAC5E,SAAS,CAAC,8JAA8J,CAExK;wBAAA,CAAC,0BAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAC3B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CACxD,CAAC,CACN;oBAAA,EAAE,MAAM,CACZ;gBAAA,EAAE,GAAG,CAEL;;gBAAA,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAEtE;;gBAAA,CAAC,oBAAoB,CACrB;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CACvC;oBAAA,CAAC,MAAM,CACH,IAAI,CAAC,QAAQ,CACb,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAClD,SAAS,CAAC,mLAAmL,CAE7L;;oBACJ,EAAE,MAAM,CACR;oBAAA,CAAC,MAAM,CACH,IAAI,CAAC,QAAQ,CACb,OAAO,CAAC,CAAC,UAAU,CAAC,CACpB,QAAQ,CAAC,CAAC,SAAS,CAAC,CACpB,SAAS,CAAC,2OAA2O,CAErP;wBAAA,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAC9C;oBAAA,EAAE,MAAM,CACZ;gBAAA,EAAE,GAAG,CACT;YAAA,EAAE,GAAG,CACT;QAAA,EAAE,GAAG,CAAC,CACT,CAAC;AACN,CAAC,CAAC;AAEF,kBAAe,WAAW,CAAC"}