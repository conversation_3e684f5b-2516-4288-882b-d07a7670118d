"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const RoleForm = ({ initialData, onSubmit, onCancel, isSaving, submitError, renderFooter // Destructure render prop
 }) => {
    const [name, setName] = (0, react_1.useState)('');
    const [description, setDescription] = (0, react_1.useState)('');
    const [isValid, setIsValid] = (0, react_1.useState)(false); // Track basic validity
    const [formError, setFormError] = (0, react_1.useState)(null);
    // Update validity when name changes
    (0, react_1.useEffect)(() => {
        setIsValid(name.trim().length > 0);
    }, [name]);
    (0, react_1.useEffect)(() => {
        var _a;
        if (initialData) {
            setName(initialData.name || '');
            setDescription(initialData.description || '');
            setIsValid(((_a = initialData.name) === null || _a === void 0 ? void 0 : _a.trim().length) > 0); // Initial validity
        }
        else {
            // Reset for add form
            setName('');
            setDescription('');
            setIsValid(false); // Invalid initially for add
        }
        setFormError(null); // Clear errors when initial data changes (or form opens)
    }, [initialData]);
    const handleSubmit = (e) => __awaiter(void 0, void 0, void 0, function* () {
        e.preventDefault();
        setFormError(null);
        if (!isValid) { // Use state validity
            setFormError('Role name cannot be empty.');
            return;
        }
        // Prevent editing name of 'User' role if needed (though button should be disabled)
        if ((initialData === null || initialData === void 0 ? void 0 : initialData.name) === 'User' && name !== 'User') {
            setFormError("Cannot rename the default 'User' role.");
            return;
        }
        const formData = { name: name.trim(), description: description.trim() };
        yield onSubmit(formData);
    });
    return (<form onSubmit={handleSubmit} id="role-form">
            <div className="mb-4">
                <label htmlFor="roleName" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name <span className="text-red-500">*</span>
                </label>
                <input type="text" id="roleName" value={name} onChange={(e) => setName(e.target.value)} placeholder="e.g., Content Approver" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100" required disabled={(initialData === null || initialData === void 0 ? void 0 : initialData.name) === 'User'} // Disable editing name for 'User' role
    />
            </div>
            <div className="mb-4">
                <label htmlFor="roleDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                </label>
                <textarea id="roleDescription" value={description} onChange={(e) => setDescription(e.target.value)} rows={3} placeholder="Briefly describe the role's purpose..." className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
            </div>

            {/* Display validation errors */}
            {formError && <p className="text-sm text-red-600 mb-3">{formError}</p>}
            {/* Display submission errors from parent */}
             {submitError && <p className="text-sm text-red-600 mb-3">Error: {submitError}</p>}

            {/* Render the footer buttons passed from parent */}
            <div className="flex justify-end pt-4 border-t border-gray-200 space-x-2">
                {renderFooter(isValid)} 
            </div>
        </form>);
};
exports.default = RoleForm;
//# sourceMappingURL=RoleForm.js.map