"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const DashboardSection_1 = require("./DashboardSection");
// Placeholder components for different sections
// const DashboardSection: React.FC = () => <div>Dashboard Content Placeholder</div>; // Remove old placeholder
const KnowledgeSection = () => <div>Knowledge Hub Content Placeholder</div>;
const ITSection = () => <div>IT Hub Content Placeholder</div>;
const HRSection = () => <div>HR Hub Content Placeholder</div>;
const AdminSection = () => <div>Admin Hub Content Placeholder</div>;
const CommunicationSection = () => <div>Communication Hub Content Placeholder</div>;
const MainContent = ({ activeSection }) => {
    const renderSection = () => {
        switch (activeSection) {
            case 'dashboard':
                return <DashboardSection_1.default />;
            case 'knowledge':
                return <KnowledgeSection />;
            case 'it':
                return <ITSection />;
            case 'hr':
                return <HRSection />;
            case 'admin':
                return <AdminSection />;
            case 'communication':
                return <CommunicationSection />;
            default:
                return <DashboardSection_1.default />; // Default to dashboard
        }
    };
    return (<main className="flex-1 overflow-y-auto p-6 bg-gray-100">
      {/* Render active section component here based on state/routing */}
      {renderSection()}
    </main>);
};
exports.default = MainContent;
//# sourceMappingURL=MainContent.js.map