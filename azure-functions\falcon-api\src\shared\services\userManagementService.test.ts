import {
    findOrCreateUser,
    assignRoleToUser,
    removeRoleFromUser,
    updateUserLastLogin,
    EntraUserData,
    DbUser
} from './userManagementService';
import { executeQuery } from '../db';
import { logger } from '../utils/logger';

// Mock dependencies
jest.mock('../db');
jest.mock('../utils/logger', () => ({
    logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn(),
    }
}));

// Define mock data
const mockEntraUser: EntraUserData = {
    id: 'entra-guid-123',
    userPrincipalName: '<EMAIL>',
    mail: '<EMAIL>',
    givenName: 'Test',
    surname: 'User',
    companyName: 'Test Company',
    department: 'Test Department'
};

const mockExistingDbUser: DbUser = {
    UserID: 1,
    EntraID: 'entra-guid-123',
    Username: '<EMAIL>',
    Email: '<EMAIL>',
    FirstName: 'Test',
    LastName: 'User',
    CompanyID: 10,
    LocationID: null,
    DepartmentID: 20,
    EmployeeID: null,
    ContactNumber: null,
    ProfileImageURL: null,
    ManagerID: null,
    DateOfJoining: null,
    LastLoginDate: new Date(),
    IsActive: true,
    CreatedBy: 1,
    CreatedDate: new Date(),
    ModifiedBy: null,
    ModifiedDate: null,
    TenantID: 'test-tenant-id',
    CompanyName: 'Test Company',
    DepartmentName: 'Test Department'
};

const mockCreatedDbUser: DbUser = {
    ...mockExistingDbUser,
    UserID: 2, // Different ID for creation
    CreatedDate: new Date(), // Reset dates potentially
    LastLoginDate: null
};

describe('User Management Service', () => {

    // Hold original process.env
    const originalEnv = process.env;

    beforeEach(() => {
        jest.clearAllMocks();
        // Restore env before each test
        process.env = { ...originalEnv };
    });

    afterAll(() => {
        // Restore original env after all tests
        process.env = originalEnv;
    });

    // --- findOrCreateUser Tests ---
    describe('findOrCreateUser', () => {
        it('should return existing user if found', async () => {
            (executeQuery as jest.Mock).mockResolvedValueOnce({ recordset: [mockExistingDbUser] });

            const user = await findOrCreateUser(mockEntraUser);
            expect(user).toEqual(mockExistingDbUser);
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), { EntraID: mockEntraUser.id });
            expect(executeQuery).toHaveBeenCalledTimes(1);
            expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Found existing user'));
        });

        it('should create a new user if not found (with company and department)', async () => {
            process.env.DEFAULT_USER_ROLE = 'Employee'; // Set env var for test
            const mockCompanyResult = { recordset: [{ CompanyID: 10 }] };
            const mockDeptResult = { recordset: [{ DepartmentID: 20 }] };
            const mockRoleResult = { recordset: [{ RoleID: 5 }] }; // Default role 'Employee' ID
            const mockInsertUserResult = { recordset: [mockCreatedDbUser] };
            const mockInsertUserRoleResult = { rowsAffected: [1] }; // Simulate role assignment success

            (executeQuery as jest.Mock)
                .mockResolvedValueOnce({ recordset: [] }) // 1. User check - not found
                .mockResolvedValueOnce(mockCompanyResult) // 2. Company lookup - found
                .mockResolvedValueOnce(mockDeptResult)    // 3. Department lookup - found
                .mockResolvedValueOnce(mockInsertUserResult) // 4. User insert - success
                .mockResolvedValueOnce(mockRoleResult) // 5. Default role lookup - found
                .mockResolvedValueOnce(mockInsertUserRoleResult); // 6. UserRole insert - success

            const user = await findOrCreateUser(mockEntraUser, 99); // createdByUserId = 99

            expect(user).toEqual(mockCreatedDbUser);
            expect(executeQuery).toHaveBeenCalledTimes(6);
            // Check user insert call
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), {
                EntraID: mockEntraUser.id,
                Username: mockEntraUser.userPrincipalName,
                Email: mockEntraUser.mail,
                FirstName: mockEntraUser.givenName,
                LastName: mockEntraUser.surname,
                CompanyID: 10,
                DepartmentID: 20,
                IsActive: true,
                CreatedBy: 99
            });
            // Check role assignment call
             expect(executeQuery).toHaveBeenCalledWith(expect.any(String), {
                UserID: mockCreatedDbUser.UserID,
                RoleID: 5,
                IsActive: true,
                CreatedBy: 99
            });
            expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Creating new user'));
            expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Successfully created user'));
             expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Assigned default role'));
        });

        it('should create user with default company if companyName is missing', async () => {
            // Mock the implementation of executeQuery for this specific test
            (executeQuery as jest.Mock).mockImplementation((query: string, params?: any) => {
                // Check for user query
                if (query.includes('SELECT * FROM Users')) {
                    return { recordset: [] }; // User not found
                }

                // Check for user insert query
                if (query.includes('INSERT INTO Users')) {
                    return {
                        recordset: [{
                            UserID: 3,
                            EntraID: 'entra-guid-123',
                            Username: '<EMAIL>',
                            Email: '<EMAIL>',
                            FirstName: 'Test',
                            LastName: 'User',
                            CompanyID: 1,
                            DepartmentID: null,
                            IsActive: true,
                            CreatedBy: 99,
                            CreatedDate: new Date(),
                            TenantID: 'test-tenant-id'
                        }]
                    };
                }

                // Check for role query
                if (query.includes('SELECT RoleID FROM Roles')) {
                    return { recordset: [{ RoleID: 5 }] };
                }

                // Check for role assignment query
                if (query.includes('INSERT INTO UserRoles')) {
                    return { rowsAffected: [1] };
                }

                return { recordset: [] };
            });

            process.env.DEFAULT_USER_ROLE = 'Employee';
            const entraUserNoCompany = { ...mockEntraUser, companyName: undefined };

            const user = await findOrCreateUser(entraUserNoCompany, 99);

            // Verify the user object has the expected properties
            expect(user).toBeTruthy();
            expect(user?.UserID).toBe(3);
            expect(user?.CompanyID).toBe(1);
            expect(user?.DepartmentID).toBeNull();

            // Verify the warning about missing company name
            expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('has no companyName. Assigning default CompanyID: 1'));
        });

        it('should throw error if companyName provided but not found in DB', async () => {
            const mockCompanyResult = { recordset: [] };

            (executeQuery as jest.Mock)
                .mockResolvedValueOnce({ recordset: [] })
                .mockResolvedValueOnce(mockCompanyResult);

            // FIX: Expect the error re-thrown from the catch block in the service
            await expect(findOrCreateUser(mockEntraUser, 99)).rejects.toThrow(`Database error during company lookup for user ${mockEntraUser.id}.`);
            expect(executeQuery).toHaveBeenCalledTimes(2);
            // The specific error IS logged before being re-thrown
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('provided by Entra ID not found or inactive'));
        });

         it('should create user but not assign role if default role not found', async () => {
            process.env.DEFAULT_USER_ROLE = 'NonExistentRole';
            const mockCompanyResult = { recordset: [{ CompanyID: 10 }] };
            const mockDeptResult = { recordset: [{ DepartmentID: 20 }] };
            const mockRoleResult = { recordset: [] }; // Default role NOT found
            const mockInsertUserResult = { recordset: [mockCreatedDbUser] };

            (executeQuery as jest.Mock)
                .mockResolvedValueOnce({ recordset: [] }) // User check
                .mockResolvedValueOnce(mockCompanyResult) // Company lookup
                .mockResolvedValueOnce(mockDeptResult)    // Department lookup
                .mockResolvedValueOnce(mockInsertUserResult) // User insert
                .mockResolvedValueOnce(mockRoleResult); // Role lookup (fails)
                // No role insert call expected

            const user = await findOrCreateUser(mockEntraUser, 99);
            expect(user).toEqual(mockCreatedDbUser);
            expect(executeQuery).toHaveBeenCalledTimes(5);
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining("Default role 'NonExistentRole' not found"));
        });

         it('should handle DB error during user check', async () => {
            const dbError = new Error('Check failed');
            (executeQuery as jest.Mock).mockRejectedValueOnce(dbError);
            await expect(findOrCreateUser(mockEntraUser)).rejects.toThrow('Check failed');
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error in findOrCreateUser process'), dbError);
         });
    });

    // --- assignRoleToUser Tests ---
    describe('assignRoleToUser', () => {
         const userId = 1;
         const roleId = 5;
         const assignerId = 99;

        it('should insert new assignment if none exists', async () => {
            (executeQuery as jest.Mock)
                .mockResolvedValueOnce({ recordset: [] })
                .mockResolvedValueOnce({ rowsAffected: [1] });

            const result = await assignRoleToUser(userId, roleId, assignerId);
            expect(result).toBe(true);
            expect(executeQuery).toHaveBeenCalledTimes(2);
            expect(executeQuery).toHaveBeenNthCalledWith(1, expect.any(String), { UserID: userId, RoleID: roleId });
            expect(executeQuery).toHaveBeenNthCalledWith(2, expect.any(String), { UserID: userId, RoleID: roleId, IsActive: true, CreatedBy: assignerId });
            expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Creating new role assignment'));
        });

        it('should update (reactivate) assignment if it exists and is inactive', async () => {
            const existingInactive = { UserRoleID: 50, IsActive: false };
             (executeQuery as jest.Mock)
                .mockResolvedValueOnce({ recordset: [existingInactive] })
                .mockResolvedValueOnce({ rowsAffected: [1] });

             const result = await assignRoleToUser(userId, roleId, assignerId);
            expect(result).toBe(true);
            expect(executeQuery).toHaveBeenCalledTimes(2);
            expect(executeQuery).toHaveBeenNthCalledWith(2, expect.any(String), { UserRoleID: existingInactive.UserRoleID, ModifiedBy: assignerId });
             expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Reactivating existing role assignment'));
        });

        it('should do nothing and return true if assignment exists and is active', async () => {
             const existingActive = { UserRoleID: 50, IsActive: true };
             (executeQuery as jest.Mock).mockResolvedValueOnce({ recordset: [existingActive] }); // Check query finds active

             const result = await assignRoleToUser(userId, roleId, assignerId);
             expect(result).toBe(true);
             expect(executeQuery).toHaveBeenCalledTimes(1); // Only the check query
             expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('already actively assigned'));
        });

         it('should return false if DB error occurs', async () => {
             const dbError = new Error('Assign failed');
             (executeQuery as jest.Mock).mockRejectedValue(dbError);

             const result = await assignRoleToUser(userId, roleId, assignerId);
             expect(result).toBe(false);
             expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error assigning role'), dbError);
        });
    });

    // --- removeRoleFromUser Tests ---
    describe('removeRoleFromUser', () => {
         const userId = 1;
         const roleId = 5;
         const removerId = 98;

        it('should update assignment to inactive if it exists and is active', async () => {
            (executeQuery as jest.Mock).mockResolvedValueOnce({ rowsAffected: [1] });

            const result = await removeRoleFromUser(userId, roleId, removerId);
            expect(result).toBe(true);
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), { UserID: userId, RoleID: roleId, ModifiedBy: removerId });
             expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Successfully deactivated role assignment'));
        });

        it('should return true (noop) if assignment is already inactive or doesnt exist', async () => {
             (executeQuery as jest.Mock).mockResolvedValueOnce({ rowsAffected: [0] });

            const result = await removeRoleFromUser(userId, roleId, removerId);
            expect(result).toBe(true);
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), { UserID: userId, RoleID: roleId, ModifiedBy: removerId });
            expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('No active role assignment found'));
        });

         it('should return false if DB error occurs', async () => {
             const dbError = new Error('Remove failed');
             (executeQuery as jest.Mock).mockRejectedValue(dbError);

             const result = await removeRoleFromUser(userId, roleId, removerId);
             expect(result).toBe(false);
             expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error removing role'), dbError);
        });
    });

    // --- updateUserLastLogin Tests ---
    describe('updateUserLastLogin', () => {
        const userId = 123;

        it('should execute update query successfully', async () => {
             (executeQuery as jest.Mock).mockResolvedValueOnce({ rowsAffected: [1] });
             const result = await updateUserLastLogin(userId);
             expect(result).toBe(true);
             expect(executeQuery).toHaveBeenCalledWith(expect.stringContaining('UPDATE Users SET LastLoginDate'), { UserID: userId });
             expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Updating LastLoginDate'));
        });

         it('should return false if DB error occurs', async () => {
             const dbError = new Error('Update failed');
             (executeQuery as jest.Mock).mockRejectedValue(dbError);

             const result = await updateUserLastLogin(userId);
             expect(result).toBe(false);
             expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error updating LastLoginDate'), dbError);
        });
    });
});