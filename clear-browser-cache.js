/**
 * Browser Cache Clear Script for Falcon Portal
 * This script helps users clear cached authentication data and force fresh role retrieval
 * 
 * Instructions for users:
 * 1. Open browser Developer Tools (F12)
 * 2. Go to Console tab
 * 3. Copy and paste this entire script
 * 4. Press Enter to execute
 * 5. Refresh the page (F5) to get fresh authentication data
 */

(function() {
    console.log('🔧 Falcon Portal Cache Clear Script Started');
    
    // Clear all localStorage data
    if (typeof(Storage) !== "undefined" && localStorage) {
        console.log('📦 Clearing localStorage...');
        const localStorageKeys = Object.keys(localStorage);
        console.log(`Found ${localStorageKeys.length} localStorage items:`, localStorageKeys);
        localStorage.clear();
        console.log('✅ localStorage cleared');
    }
    
    // Clear all sessionStorage data
    if (typeof(Storage) !== "undefined" && sessionStorage) {
        console.log('📦 Clearing sessionStorage...');
        const sessionStorageKeys = Object.keys(sessionStorage);
        console.log(`Found ${sessionStorageKeys.length} sessionStorage items:`, sessionStorageKeys);
        sessionStorage.clear();
        console.log('✅ sessionStorage cleared');
    }
    
    // Clear IndexedDB if available
    if ('indexedDB' in window) {
        console.log('🗄️ Clearing IndexedDB...');
        indexedDB.databases().then(databases => {
            databases.forEach(db => {
                console.log(`Deleting IndexedDB: ${db.name}`);
                indexedDB.deleteDatabase(db.name);
            });
            console.log('✅ IndexedDB cleared');
        }).catch(err => {
            console.log('⚠️ Could not clear IndexedDB:', err);
        });
    }
    
    // Clear cookies for current domain
    console.log('🍪 Clearing cookies...');
    const cookies = document.cookie.split(";");
    console.log(`Found ${cookies.length} cookies`);
    
    for (let cookie of cookies) {
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        if (name) {
            // Clear for current path
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
            // Clear for root path
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;`;
            // Clear for current domain
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;domain=${window.location.hostname};path=/`;
            // Clear for parent domain (in case of subdomains)
            const parentDomain = window.location.hostname.split('.').slice(-2).join('.');
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;domain=.${parentDomain};path=/`;
        }
    }
    console.log('✅ Cookies cleared');
    
    // Clear any Microsoft Authentication Library (MSAL) cache
    if (window.msal || window.PublicClientApplication) {
        console.log('🔐 Clearing MSAL cache...');
        try {
            // Try to access MSAL instance if available
            if (window.msalInstance) {
                window.msalInstance.clearCache();
                console.log('✅ MSAL cache cleared via instance');
            } else {
                // Manually clear MSAL storage keys
                const msalKeys = Object.keys(localStorage).filter(key => 
                    key.includes('msal') || 
                    key.includes('authority') || 
                    key.includes('login_hint') ||
                    key.includes('state') ||
                    key.includes('nonce')
                );
                msalKeys.forEach(key => localStorage.removeItem(key));
                console.log(`✅ Cleared ${msalKeys.length} MSAL-related keys`);
            }
        } catch (err) {
            console.log('⚠️ Could not clear MSAL cache:', err);
        }
    }
    
    // Clear any Azure AD B2C related cache
    console.log('🔐 Clearing Azure AD B2C cache...');
    const azureKeys = Object.keys(localStorage).filter(key => 
        key.includes('b2c') || 
        key.includes('azure') || 
        key.includes('oauth') ||
        key.includes('token') ||
        key.includes('auth')
    );
    azureKeys.forEach(key => localStorage.removeItem(key));
    console.log(`✅ Cleared ${azureKeys.length} Azure AD related keys`);
    
    // Clear any application-specific cache
    console.log('🚀 Clearing Falcon Portal specific cache...');
    const falconKeys = Object.keys(localStorage).filter(key => 
        key.includes('falcon') || 
        key.includes('portal') || 
        key.includes('user') ||
        key.includes('role')
    );
    falconKeys.forEach(key => localStorage.removeItem(key));
    console.log(`✅ Cleared ${falconKeys.length} Falcon Portal related keys`);
    
    console.log('🎉 Cache clear completed!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Close this browser tab completely');
    console.log('2. Close all browser windows');
    console.log('3. Reopen browser and navigate to Falcon Portal');
    console.log('4. Log in again to get fresh authentication data');
    console.log('');
    console.log('⚠️ If you still see admin access where you shouldn\'t:');
    console.log('1. Try using an incognito/private browsing window');
    console.log('2. Try a different browser');
    console.log('3. Contact IT support if the issue persists');
    
})();

console.log('Copy the above script to your browser console and press Enter to clear all cached data.'); 