const sql = require('mssql');

const config = {
  server: 'falcon-portal-sql-server.database.windows.net',
  database: 'FalconPortalDB',
  authentication: {
    type: 'default'
  },
  options: {
    encrypt: true,
    trustServerCertificate: false
  }
};

async function checkData() {
  try {
    await sql.connect(config);
    
    // Check change requests for company 4
    const result = await sql.query`
      SELECT 
        RequestID, 
        Title, 
        Status, 
        CompanyID,
        DeploymentDate,
        RequestedCompletionDate
      FROM ChangeRequests 
      WHERE CompanyID = 4
      ORDER BY RequestID
    `;
    
    console.log('Change Requests for Company 4 (Avirata Defence Systems):');
    console.log(JSON.stringify(result.recordset, null, 2));
    
    // Also check all companies to see what data exists
    const allResult = await sql.query`
      SELECT 
        RequestID, 
        Title, 
        Status, 
        CompanyID,
        DeploymentDate,
        RequestedCompletionDate
      FROM ChangeRequests 
      ORDER BY CompanyID, RequestID
    `;
    
    console.log('\nAll Change Requests:');
    console.log(JSON.stringify(allResult.recordset, null, 2));
    
    await sql.close();
  } catch (err) {
    console.error('Error:', err.message);
  }
}

checkData(); 