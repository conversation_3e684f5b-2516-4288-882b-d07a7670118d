import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { removeRoleFromUser } from "../shared/services/userManagementService";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal } from "../shared/authUtils";
import { validateRequest, userRoleRouteParamsSchema } from "../shared/validationSchemas";

// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Administrator'; // Match the actual role name in database

export async function removeRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function RemoveRole processed request for url "${request.url}"`);
    logger.info('RemoveRole function invoked.');

    // --- Authentication & Authorization --- 
    const principal = getClientPrincipal(request);
    if (!principal) {
        return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
    }
    if (!hasRequiredRole(principal, [REQUIRED_ROLE])) {
         logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
         return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
    }
    const authenticatedUserId = await getUserIdFromPrincipal(principal, context);
    if (!authenticatedUserId) {
        logger.error(`RemoveRole: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
        return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
    }
    // NOTE: Using retrieved authenticatedUserId below for 'removedByUserId' field.
    // Ensure getUserIdFromPrincipal correctly retrieves the UserID from your DB 
    // based on the oid claim provided by the configured authentication provider.
    logger.info(`RemoveRole invoked by UserID: ${authenticatedUserId}`);
    // --- End Auth --- 

    // --- Input Validation --- 
    const routeParams = { 
        userId: request.params.userId, 
        roleId: request.params.roleId 
    }; // Extract for validation
    
    const validationError = validateRequest(userRoleRouteParamsSchema, routeParams, context, "route parameters");
    if (validationError) return validationError;
    
    // Validation passed, use validated data
    const validatedRouteParams = userRoleRouteParamsSchema.parse(routeParams); // Use parse to get typed data
    const userId = validatedRouteParams.userId; // Already a number
    const roleId = validatedRouteParams.roleId; // Already a number
    // --- End Validation --- 

    try {
        const success = await removeRoleFromUser(userId, roleId, authenticatedUserId);

        if (success) {
            // removeRoleFromUser logs success/warning internally
            // Return 204 No Content for successful deletion/update
            return {
                status: 204, 
            };
        } else {
            // removeRoleFromUser handles logging the specific error
            // Return a generic server error if it failed
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: "Failed to remove role." }
            };
        }

    } catch (error) {
        // This catch block is mainly for unexpected errors during the process
        logger.error(`Unexpected error in RemoveRole function for User ${userId}, Role ${roleId} by UserID ${authenticatedUserId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "An unexpected error occurred while removing the role.",
                error: errorMessage
            }
        };
    }
}

app.http('RemoveRole', {
    methods: ['DELETE'],
    authLevel: 'anonymous', // Handled by provider + code checks
    route: 'users/{userId}/roles/{roleId}', // Match function.json
    handler: removeRole
}); 