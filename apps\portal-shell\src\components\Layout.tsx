import React, { useState } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import MainContent from './MainContent';

const Layout: React.FC = () => {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(true);

  const handleNavigate = (sectionId: string) => {
    setActiveSection(sectionId);
    // Potentially add logic here to close sidebar on mobile after navigation
  };

  const handleToggleSidebar = () => {
    setIsSidebarExpanded(!isSidebarExpanded);
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      <Sidebar 
        activeSection={activeSection} 
        isExpanded={isSidebarExpanded}
        onNavigate={handleNavigate}
        onToggle={handleToggleSidebar}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        <MainContent activeSection={activeSection} />
      </div>
    </div>
  );
};

export default Layout; 