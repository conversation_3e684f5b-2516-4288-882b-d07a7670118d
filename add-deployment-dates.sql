-- Add deployment dates to existing change requests for real-time calendar data
-- Run this script against your Azure SQL database

UPDATE ChangeRequests 
SET DeploymentDate = CASE 
    WHEN RequestID = 32 THEN '2024-06-15'  -- Database Server Upgrade - June Deployment
    WHEN RequestID = 33 THEN '2024-06-20'  -- Security Patch Deployment  
    WHEN RequestID = 34 THEN '2024-06-10'  -- Network Infrastructure Update
    WHEN RequestID = 35 THEN '2024-06-25'  -- Application Performance Optimization
    WHEN RequestID = 36 THEN '2024-06-28'  -- Backup System Enhancement
    ELSE DeploymentDate
END
WHERE RequestID IN (32, 33, 34, 35, 36);

-- Verify the update
SELECT 
    RequestID, 
    RequestNumber,
    Title, 
    Status, 
    Priority, 
    DeploymentDate,
    RequestedCompletionDate
FROM ChangeRequests 
WHERE RequestID IN (32, 33, 34, 35, 36)
ORDER BY DeploymentDate; 