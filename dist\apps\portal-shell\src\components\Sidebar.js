"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const feather_icons_react_1 = require("feather-icons-react"); // Import necessary icons
// Define navigation items structure
const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: feather_icons_react_1.Home },
    { id: 'knowledge', label: 'Knowledge Hub', icon: feather_icons_react_1.Book },
    { id: 'it', label: 'IT Hub', icon: feather_icons_react_1.Monitor },
    { id: 'hr', label: 'HR Hub', icon: feather_icons_react_1.Users },
    { id: 'admin', label: 'Admin Hub', icon: feather_icons_react_1.Briefcase },
    { id: 'communication', label: 'Communication Hub', icon: feather_icons_react_1.MessageSquare },
];
// Define company options
const companyOptions = [
    'SASMOS HET',
    'FESIL',
    'WestWire Harnesses',
    'Avirata Defence Systems',
    'LiDER Technologies',
    'GloDesi Technologies',
    'Hanuka',
];
const Sidebar = ({ activeSection, isExpanded, onNavigate, onToggle }) => {
    // Use onNavigate prop directly
    const handleNavClick = (sectionId) => {
        onNavigate(sectionId);
    };
    // Use onToggle prop directly
    const handleToggle = () => {
        onToggle();
    };
    return (<div id="sidebar" className={`bg-gray-800 text-white ${isExpanded ? 'w-64' : 'w-20'} transition-all duration-300 flex flex-col flex-shrink-0`}>
      {/* Brand and Toggle */}
      <div className="p-4 flex items-center justify-between border-b border-gray-700">
        <div id="brand-title" className={`font-bold text-xl whitespace-nowrap overflow-hidden ${!isExpanded && 'hidden'}`}>Falcon Portal</div>
        <button id="toggle-sidebar" onClick={handleToggle} className="text-gray-300 hover:text-white">
          <feather_icons_react_1.Menu size={20}/> 
        </button>
      </div>

      {/* Company Selector */}
      <div className={`p-4 ${!isExpanded && 'hidden'}`}>
        <select id="company-select" className="bg-gray-700 text-white p-2 rounded w-full text-sm">
          {companyOptions.map(company => (<option key={company} value={company}>{company}</option>))}
        </select>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 mt-4 overflow-y-auto">
        {navItems.map(item => (<button key={item.id} data-section={item.id} onClick={() => handleNavClick(item.id)} className={`nav-link flex items-center p-4 w-full hover:bg-gray-700 ${activeSection === item.id ? 'bg-blue-600' : ''} ${!isExpanded ? 'justify-center' : ''}`} title={item.label}>
            <item.icon size={20}/>
            <span className={`ml-4 whitespace-nowrap ${!isExpanded && 'hidden'}`}>{item.label}</span>
          </button>))}
      </nav>
      
      {/* User Profile Section */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center">
          <div className="bg-gray-500 rounded-full h-8 w-8 flex items-center justify-center mr-2 flex-shrink-0">
            <feather_icons_react_1.User size={16} className="text-white"/>
          </div>
          <div className={`overflow-hidden ${!isExpanded && 'hidden'}`}>
            {/* TODO: Get actual user name/title from MSAL/API */}
            <div className="text-sm font-medium truncate">Rohit Kumar</div>
            <div className="text-xs text-gray-400 truncate">Project Manager</div>
          </div>
        </div>
      </div>
    </div>);
};
exports.default = Sidebar;
//# sourceMappingURL=Sidebar.js.map