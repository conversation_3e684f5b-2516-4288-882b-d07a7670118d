"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthTips = void 0;
exports.healthTips = [
    "Stay hydrated! Drink plenty of water throughout the day.",
    "Take short breaks to stretch if you sit for long periods.",
    "Aim for 7-9 hours of quality sleep each night.",
    "Incorporate fruits and vegetables into your daily meals.",
    "Practice mindfulness or meditation for a few minutes daily to reduce stress.",
    "Take the stairs instead of the elevator when possible.",
    "Schedule regular check-ups with your doctor.",
    "Limit processed foods and sugary drinks.",
    "Get some fresh air and sunshine, even a short walk helps.",
    "Practice good posture while sitting and standing.",
    "Find a physical activity you enjoy and stick with it.",
    "Wash your hands regularly to prevent the spread of germs.",
    "Connect with friends or family regularly for social well-being.",
    "Take time for hobbies and activities you enjoy.",
    "Listen to your body and rest when you need to.",
    "Consider portion sizes when eating meals.",
    "Protect your skin from the sun with sunscreen.",
    "Limit screen time, especially before bed.",
    "Practice deep breathing exercises to calm your mind.",
    "Keep your workspace tidy and organized.",
    "Start your day with a healthy breakfast.",
    "Read a book or listen to music to unwind.",
    "Set realistic goals for yourself, both big and small.",
    "Learn something new to keep your mind active.",
    "Express gratitude daily for the positive things in your life."
];
//# sourceMappingURL=healthTips.js.map