"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPool = void 0;
exports.executeQuery = executeQuery;
const sql = require("mssql");
const config = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_SERVER || '',
    database: process.env.DB_DATABASE || '',
    options: {
        encrypt: true,
        trustServerCertificate: process.env.NODE_ENV === 'development' // Set NODE_ENV=development for local run if needed
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};
// Store the pool promise globally
let pool = null;
let poolConnectPromise = null;
const getPool = () => __awaiter(void 0, void 0, void 0, function* () {
    if (pool && pool.connected) {
        return pool;
    }
    // If connection is in progress, wait for it
    if (poolConnectPromise) {
        return poolConnectPromise;
    }
    // Create a new connection promise
    poolConnectPromise = new Promise((resolve, reject) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            console.log(`Attempting to connect to DB: ${config.server}/${config.database} as ${config.user}`);
            pool = new sql.ConnectionPool(config);
            yield pool.connect();
            console.log("DB Connection Pool Established.");
            // Clear the promise once connected
            poolConnectPromise = null;
            resolve(pool);
        }
        catch (err) {
            console.error('Database Connection Failed!', err);
            pool = null; // Reset pool on error
            poolConnectPromise = null; // Reset promise on error
            reject(err);
        }
    }));
    return poolConnectPromise;
});
exports.getPool = getPool;
function executeQuery(query, params) {
    return __awaiter(this, void 0, void 0, function* () {
        const currentPool = yield (0, exports.getPool)(); // Ensure pool is connected
        const request = currentPool.request(); // Get a request object from the pool
        if (params) {
            for (const key in params) {
                let sqltype;
                const value = params[key];
                // Basic type inference (add more as needed)
                if (value === null || value === undefined) {
                    // Default to NVarChar for null/undefined unless explicitly typed
                    // Or handle based on expected column type if possible
                    sqltype = sql.NVarChar;
                }
                else if (typeof value === 'string') {
                    sqltype = sql.NVarChar;
                }
                else if (typeof value === 'number') {
                    // Check if integer or float? For now, assume Int
                    sqltype = sql.Int;
                }
                else if (typeof value === 'boolean') {
                    sqltype = sql.Bit;
                }
                else if (value instanceof Date) {
                    sqltype = sql.DateTime2;
                }
                else {
                    sqltype = sql.NVarChar; // Default fallback
                }
                request.input(key, sqltype, value);
            }
        }
        try {
            const result = yield request.query(query);
            return result;
        }
        catch (err) {
            console.error('SQL Error executing query:', query, 'Params:', params, 'Error:', err);
            throw err; // Re-throw to be handled by the calling Function
        }
    });
}
// Optional: Close pool on process exit (useful for local dev)
process.on('exit', () => __awaiter(void 0, void 0, void 0, function* () {
    if (pool) {
        console.log("Closing DB connection pool...");
        yield pool.close();
        console.log("DB connection pool closed.");
    }
}));
//# sourceMappingURL=db.js.map