"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadImage = void 0;
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const cors_1 = require("../shared/cors");
// Configuration
const STORAGE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'falconhubstorage';
const STORAGE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const CONTAINER_NAME = 'change-request-images';
// Allowed image types and max file size (5MB)
const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
async function UploadImage(request, context) {
    context.log('UploadImage function processed a request.');
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: ''
        });
    }
    try {
        // Parse the multipart form data
        const formData = await request.formData();
        const fileEntry = formData.get('image');
        const userId = formData.get('userId');
        const requestId = formData.get('requestId'); // Optional: for associating with specific requests
        // Type guard for File
        if (!fileEntry || typeof fileEntry === 'string') {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'No image file provided'
                }
            });
        }
        // Use the undici File type (which is what we get from FormData)
        const file = fileEntry; // Using any to avoid type conflicts between DOM File and undici File
        if (!userId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'User ID is required'
                }
            });
        }
        // Validate file type
        if (!ALLOWED_MIME_TYPES.includes(file.type)) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: `Invalid file type. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}`
                }
            });
        }
        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`
                }
            });
        }
        // Convert File to ArrayBuffer for both storage and data URL
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Create data URL for immediate preview (works without storage account public access)
        const base64Data = buffer.toString('base64');
        const dataUrl = `data:${file.type};base64,${base64Data}`;
        // Generate unique blob name for storage
        const fileExtension = file.name.split('.').pop() || 'jpg';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const uniqueId = (0, uuid_1.v4)().substring(0, 8);
        const blobName = `${userId}/${timestamp}-${uniqueId}.${fileExtension}`;
        let imageUrl = dataUrl; // Default to data URL for preview
        let uploadSuccess = true;
        // Try to upload to blob storage (but don't fail if storage is not configured)
        if (STORAGE_ACCOUNT_KEY) {
            try {
                // Create storage shared key credential
                const sharedKeyCredential = new storage_blob_1.StorageSharedKeyCredential(STORAGE_ACCOUNT_NAME, STORAGE_ACCOUNT_KEY);
                // Create blob service client
                const blobServiceClient = new storage_blob_1.BlobServiceClient(`https://${STORAGE_ACCOUNT_NAME}.blob.core.windows.net`, sharedKeyCredential);
                // Get container client
                const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
                // Ensure container exists
                try {
                    await containerClient.createIfNotExists();
                }
                catch (error) {
                    context.log('Container creation/check failed:', error);
                    // Continue if container already exists
                }
                // Upload file to blob storage
                const blockBlobClient = containerClient.getBlockBlobClient(blobName);
                // Set blob metadata
                const metadata = {
                    userId: userId,
                    originalName: file.name,
                    uploadDate: new Date().toISOString(),
                    requestId: requestId || '',
                    fileSize: file.size.toString(),
                    mimeType: file.type
                };
                // Upload with metadata and content type
                await blockBlobClient.upload(buffer, buffer.length, {
                    blobHTTPHeaders: {
                        blobContentType: file.type,
                        blobContentDisposition: `inline; filename="${file.name}"`
                    },
                    metadata: metadata,
                    tags: {
                        userId: userId,
                        category: 'change-request',
                        uploadDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format for tags
                    }
                });
                // Use blob URL if upload successful (but keep data URL as fallback)
                const blobUrl = blockBlobClient.url;
                context.log(`Image uploaded to blob storage: ${blobUrl}`);
            }
            catch (error) {
                context.log('Blob storage upload failed, using data URL:', error);
                uploadSuccess = false;
            }
        }
        else {
            context.log('Azure Storage not configured, using data URL for preview');
            uploadSuccess = false;
        }
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            jsonBody: {
                success: true,
                message: uploadSuccess ? 'Image uploaded successfully' : 'Image processed for preview (storage not available)',
                imageUrl: imageUrl,
                dataUrl: dataUrl,
                blobName: uploadSuccess ? blobName : undefined
            }
        });
    }
    catch (error) {
        context.log('Error uploading image:', error);
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            jsonBody: {
                success: false,
                message: 'Internal server error while uploading image',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
}
exports.UploadImage = UploadImage;
functions_1.app.http('UploadImage', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    handler: UploadImage
});
//# sourceMappingURL=index.js.map