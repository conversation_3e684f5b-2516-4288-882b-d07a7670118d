import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';
import { EmailService, EmailNotificationData } from "../shared/services/emailService";

export async function submitChangeRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('SubmitChangeRequest function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        // First, check if the request exists and can be submitted
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const currentRequest = checkResult.recordset[0];
        
        // Check if request can be submitted
        if (currentRequest.Status !== 'Draft') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be submitted in current status: ${currentRequest.Status}`
                    }
                }
            };
        }

        // Update the request to submitted status
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Under Review',
                ModifiedBy = @requestedBy,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'requestedBy', type: sql.Int, value: currentRequest.RequestedBy }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Under Review', @requestedBy, GETDATE(), @comments
            )
        `;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'requestedBy', type: sql.Int, value: currentRequest.RequestedBy },
            { name: 'comments', type: sql.NVarChar, value: 'Change request submitted for review' }
        ];

        await executeQuery(historyQuery, historyParams);

        // Get comprehensive request details for email notification
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.ExpectedBenefit as expectedBenefit,
                cr.RequestedCompletionDate as requestedCompletionDate,
                ISNULL(cr.DevelopmentProgress, 0) as developmentProgress,
                cr.CreatedDate as createdDate,
                crt.TypeName as typeName,
                crt.Description as typeDescription,
                ISNULL(crt.EstimatedDays, 0) as estimatedDays,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
            WHERE cr.RequestID = @requestId
        `;

        const detailsResult = await executeQuery(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];

        // Send email notification to Change Managers asynchronously
        try {
            const emailData: EmailNotificationData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.requestedCompletionDate ? new Date(requestDetails.requestedCompletionDate) : undefined
            };

            // Send submission notification to Change Managers (don't await to avoid blocking the response)
            EmailService.getInstance().sendChangeRequestSubmitted(emailData).catch((error: any) => {
                context.error('Failed to send submission email notification:', error);
            });

            context.log(`Email notification queued for submitted change request ${requestId}`);
        } catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the submission if email fails
        }

        context.log(`Successfully submitted change request ${requestId} for approval`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request submitted for approval successfully. Change Managers have been notified.',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    description: requestDetails.description,
                    priority: requestDetails.priority,
                    status: requestDetails.status,
                    businessJustification: requestDetails.businessJustification,
                    expectedBenefit: requestDetails.expectedBenefit,
                    requestedCompletionDate: requestDetails.requestedCompletionDate,
                    developmentProgress: requestDetails.developmentProgress,
                    createdDate: requestDetails.createdDate,
                    typeName: requestDetails.typeName,
                    typeDescription: requestDetails.typeDescription,
                    estimatedDays: requestDetails.estimatedDays,
                    requesterName: requestDetails.requesterName,
                    requesterEmail: requestDetails.requesterEmail,
                    companyName: requestDetails.companyName
                }
            }
        };

    } catch (error: any) {
        context.error('Error in SubmitChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while submitting the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('SubmitChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/submit',
    handler: submitChangeRequest
}); 