"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const adminApi_1 = require("../../services/adminApi");
const UserManagementPage = () => {
    // --- State for User List ---
    const [userList, setUserList] = (0, react_1.useState)([]);
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    const [totalUsers, setTotalUsers] = (0, react_1.useState)(0);
    const [currentPage, setCurrentPage] = (0, react_1.useState)(1);
    const [pageSize] = (0, react_1.useState)(10);
    // --- State for Filtering User List ---
    const [searchTerm, setSearchTerm] = (0, react_1.useState)('');
    const [companyFilter, setCompanyFilter] = (0, react_1.useState)('All');
    const [roleFilter, setRoleFilter] = (0, react_1.useState)('All');
    const [statusFilter, setStatusFilter] = (0, react_1.useState)('All');
    // --- State for Dynamic Roles ---
    const [availableRoles, setAvailableRoles] = (0, react_1.useState)([]);
    // Hardcoded company list (replace with dynamic fetch if needed)
    const MOCK_COMPANIES = ['SASMOS HET', 'SASMOS CMT', 'All']; // Add 'All'
    // --- Fetch Available Roles ---
    const fetchRoles = (0, react_1.useCallback)(() => __awaiter(void 0, void 0, void 0, function* () {
        try {
            const roles = yield (0, adminApi_1.fetchPortalRoleNames)();
            setAvailableRoles(roles);
        }
        catch (err) {
            console.error("Error fetching roles:", err);
            // Fallback to basic roles if API fails
            setAvailableRoles(['Administrator', 'Employee', 'Manager', 'Executive', 'IT Admin', 'HR Admin', 'Content Admin']);
        }
    }), []);
    // --- Handlers for Filter Changes (reset page) ---
    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };
    const handleCompanyFilterChange = (e) => {
        setCompanyFilter(e.target.value);
        setCurrentPage(1);
    };
    const handleRoleFilterChange = (e) => {
        setRoleFilter(e.target.value);
        setCurrentPage(1);
    };
    const handleStatusFilterChange = (e) => {
        setStatusFilter(e.target.value);
        setCurrentPage(1);
    };
    // --- Fetching User List (Combined Directory/Managed) ---
    const fetchUsers = (0, react_1.useCallback)(() => __awaiter(void 0, void 0, void 0, function* () {
        setIsLoading(true);
        setError(null);
        try {
            const response = yield (0, adminApi_1.fetchPortalUsers)(searchTerm, companyFilter, roleFilter, statusFilter, currentPage, pageSize);
            setUserList(response.users);
            setTotalUsers(response.totalCount);
        }
        catch (err) {
            console.error("Error fetching users:", err);
            setError("Failed to load users. Please try again.");
        }
        finally {
            setIsLoading(false);
        }
    }), [searchTerm, companyFilter, roleFilter, statusFilter, currentPage, pageSize]);
    (0, react_1.useEffect)(() => {
        fetchRoles();
    }, [fetchRoles]);
    (0, react_1.useEffect)(() => {
        fetchUsers();
    }, [fetchUsers]);
    // --- Pagination Calculation ---
    const totalPages = Math.ceil(totalUsers / pageSize);
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };
    // --- Render Logic ---
    return (<div className="p-6 bg-white min-h-screen">
            <h1 className="text-2xl font-semibold text-gray-800 mb-6">User Management</h1>

            {/* Updated Section Title with Add User Button */}
            <div className="p-4 bg-white shadow rounded-lg">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-medium text-gray-700">Manage User Roles & Status</h2>
                    <react_router_dom_1.Link to="/portal-admin/add-user" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd"/>
                        </svg>
                        Add User
                    </react_router_dom_1.Link>
                </div>
                
                {/* Filter Controls */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    {/* Search Filter (updated state variable and handler) */}
                    <div>
                       <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 mb-1">
                           Search Users:
                        </label>
                       <input type="search" id="userSearch" value={searchTerm} onChange={handleSearchChange} placeholder="Name or Email..." className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"/>
                    </div>
                   {/* Company Filter */}
                    <div>
                       <label htmlFor="companyFilter" className="block text-sm font-medium text-gray-700 mb-1">
                            Company:
                       </label>
                       <select id="companyFilter" value={companyFilter} onChange={handleCompanyFilterChange} className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                           {MOCK_COMPANIES.map(company => (<option key={company} value={company}>{company}</option>))}
                       </select>
                    </div>
                    {/* Role Filter */}
                    <div>
                       <label htmlFor="roleFilter" className="block text-sm font-medium text-gray-700 mb-1">
                            Role:
                       </label>
                       <select id="roleFilter" value={roleFilter} onChange={handleRoleFilterChange} className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                           <option value="All">All Roles</option>
                           {availableRoles.map(role => (<option key={role} value={role}>{role}</option>))}
                       </select>
                    </div>
                    {/* Status Filter */}
                    <div>
                       <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
                            Status:
                       </label>
                       <select id="statusFilter" value={statusFilter} onChange={handleStatusFilterChange} className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                           <option value="All">All Statuses</option>
                           {adminApi_1.PORTAL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
                       </select>
                    </div>
                </div>

                {/* User Table */}
                {isLoading && <p className="text-sm text-gray-500">Loading users...</p>}
                {error && <p className="text-sm text-red-600">{error}</p>}
                {!isLoading && !error && (<div>
                       <p className="mb-4">Displaying {userList.length} of {totalUsers} users.</p>
                       <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                               <thead className="bg-gray-50">
                               <tr>
                                   <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                       <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                       <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                       <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                       <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                       <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                   </tr>
                               </thead>
                               <tbody className="bg-white divide-y divide-gray-200">
                               {userList.map((user) => (<tr key={user.id}>
                                       <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                                       <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                       <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.company}</td>
                                       <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.roles.join(', ')}</td>
                                       <td className="px-6 py-4 whitespace-nowrap">
                                           <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                               {user.status}
                                           </span>
                                       </td>
                                       <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                           <react_router_dom_1.Link to={`/portal-admin/manage-user/${user.id}`} className="text-indigo-600 hover:text-indigo-900">
                                               Edit
                                           </react_router_dom_1.Link>
                                       </td>
                                   </tr>))}
                               </tbody>
                           </table>
                       </div>
                        {/* Pagination Controls (use renamed state variable) */}
                        {totalPages > 1 && (<div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
                               <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                   Previous
                               </button>
                               <span className="text-sm text-gray-700">
                                   Page {currentPage} of {totalPages}
                               </span>
                               <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                   Next
                               </button>
                           </div>)}
                   </div>)}
            </div>
        </div>);
};
exports.default = UserManagementPage;
//# sourceMappingURL=UserManagementPage.js.map