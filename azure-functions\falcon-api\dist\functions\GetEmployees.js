"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEmployees = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function getEmployees(req, context) {
    logger_1.logger.info("GetEmployees: Processing request");
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = principal?.userId || 'dev-user';
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        if (!isDevelopment && principal) {
            const internalUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await (0, db_1.executeQuery)(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        logger_1.logger.info(`GetEmployees: Processing request for user: ${userId}, company: ${userCompanyId}`);
        // Parse query parameters
        const url = new URL(req.url);
        const searchParams = {
            search: url.searchParams.get('search') || undefined,
            department: url.searchParams.get('department') || undefined,
            location: url.searchParams.get('location') || undefined,
            company: url.searchParams.get('company') || undefined,
            status: url.searchParams.get('status') || 'active',
            limit: parseInt(url.searchParams.get('limit') || '50'),
            offset: parseInt(url.searchParams.get('offset') || '0')
        };
        // Build dynamic query
        let whereClause = 'WHERE u.CompanyID = @CompanyID';
        const parameters = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];
        // Add status filter
        if (searchParams.status === 'active') {
            whereClause += ' AND u.IsActive = 1';
        }
        else if (searchParams.status === 'inactive') {
            whereClause += ' AND u.IsActive = 0';
        }
        // Add search filter
        if (searchParams.search) {
            whereClause += ` AND (
                u.FirstName LIKE @Search 
                OR u.LastName LIKE @Search 
                OR u.Email LIKE @Search 
                OR u.EmployeeID LIKE @Search
                OR (u.FirstName + ' ' + u.LastName) LIKE @Search
            )`;
            parameters.push({
                name: 'Search',
                type: sql.NVarChar,
                value: `%${searchParams.search}%`
            });
        }
        // Add department filter
        if (searchParams.department) {
            whereClause += ' AND d.DepartmentName = @Department';
            parameters.push({
                name: 'Department',
                type: sql.NVarChar,
                value: searchParams.department
            });
        }
        // Add location filter
        if (searchParams.location) {
            whereClause += ' AND l.LocationName = @Location';
            parameters.push({
                name: 'Location',
                type: sql.NVarChar,
                value: searchParams.location
            });
        }
        // Main query to get employees with their details
        const query = `
            WITH EmployeeData AS (
                SELECT 
                    u.UserID,
                    u.EntraID,
                    u.FirstName,
                    u.LastName,
                    (u.FirstName + ' ' + u.LastName) as FullName,
                    u.Email,
                    u.EmployeeID,
                    u.ContactNumber,
                    u.ProfileImageURL,
                    u.ManagerID,
                    u.DateOfJoining,
                    u.LastLoginDate,
                    u.IsActive,
                    u.CompanyID,
                    c.CompanyName,
                    d.DepartmentName,
                    l.LocationName,
                    m.FirstName + ' ' + m.LastName as ManagerName
                FROM Users u
                LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
                LEFT JOIN Departments d ON u.DepartmentID = d.DepartmentID
                LEFT JOIN Locations l ON u.LocationID = l.LocationID
                LEFT JOIN Users m ON u.ManagerID = m.UserID
                ${whereClause}
            ),
            TotalCount AS (
                SELECT COUNT(*) as Total FROM EmployeeData
            )
            SELECT 
                e.*,
                t.Total as TotalCount
            FROM EmployeeData e
            CROSS JOIN TotalCount t
            ORDER BY e.FullName
            OFFSET @Offset ROWS
            FETCH NEXT @Limit ROWS ONLY;
        `;
        parameters.push({ name: 'Offset', type: sql.Int, value: searchParams.offset || 0 }, { name: 'Limit', type: sql.Int, value: Math.min(searchParams.limit || 50, 100) } // Max 100 records
        );
        logger_1.logger.info(`GetEmployees: Executing query with parameters:`, { searchParams, whereClause });
        const result = await (0, db_1.executeQuery)(query, parameters);
        if (!result.recordset || result.recordset.length === 0) {
            logger_1.logger.info(`GetEmployees: No employees found`);
            return {
                status: 200,
                jsonBody: {
                    employees: [],
                    totalCount: 0,
                    hasMore: false,
                    searchParams
                }
            };
        }
        const totalCount = result.recordset[0]?.TotalCount || 0;
        const hasMore = (searchParams.offset || 0) + result.recordset.length < totalCount;
        // Map results to Employee interface
        const employees = result.recordset.map((row) => ({
            id: row.UserID,
            entraId: row.EntraID,
            name: row.FullName,
            email: row.Email,
            firstName: row.FirstName,
            lastName: row.LastName,
            employeeId: row.EmployeeID,
            department: row.DepartmentName,
            location: row.LocationName,
            company: row.CompanyName,
            companyId: row.CompanyID,
            contactNumber: row.ContactNumber,
            profileImageURL: row.ProfileImageURL,
            managerId: row.ManagerID,
            managerName: row.ManagerName,
            dateOfJoining: row.DateOfJoining ? new Date(row.DateOfJoining).toISOString().split('T')[0] : null,
            lastLoginDate: row.LastLoginDate ? new Date(row.LastLoginDate).toISOString() : null,
            isActive: row.IsActive
        }));
        logger_1.logger.info(`GetEmployees: Found ${employees.length} employees (${totalCount} total)`);
        return {
            status: 200,
            jsonBody: {
                employees,
                totalCount,
                hasMore,
                searchParams
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetEmployees: Error processing request:", error);
        return {
            status: 500,
            jsonBody: {
                error: "Internal server error",
                details: error instanceof Error ? error.message : String(error)
            }
        };
    }
}
exports.getEmployees = getEmployees;
// Register the function
functions_1.app.http('GetEmployees', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'employees',
    handler: getEmployees
});
//# sourceMappingURL=GetEmployees.js.map