import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { getClientPrincipal } from "../shared/authUtils";
import { OAuthTokenService } from "../services/OAuthTokenService";
import { logger } from "../shared/utils/logger";

// Extend global namespace for token storage
declare global {
    var zohoTokens: { [userId: string]: any } | undefined;
}

// Unified Zoho OAuth Configuration for both Desk and AssetExplorer
interface ZohoOAuthConfig {
    clientId: string;
    clientSecret: string;
    redirectUri: string;
    baseUrl: string;
    scopes: string[];
}

interface ZohoTokens {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
}

// Get unified Zoho OAuth configuration from environment variables
function getZohoOAuthConfig(): ZohoOAuthConfig {
    // Determine the correct Zoho region
    const region = process.env.ZOHO_REGION || 'com'; // Default to .com
    const baseUrl = `https://accounts.zoho.${region}/oauth/v2`;
    
    return {
        // Use existing Zoho Desk OAuth app credentials
        clientId: process.env.ZOHO_DESK_CLIENT_ID || '',
        clientSecret: process.env.ZOHO_DESK_CLIENT_SECRET || '',
        redirectUri: process.env.ZOHO_DESK_REDIRECT_URI || 'http://localhost:7075/api/auth/zoho/callback',
        baseUrl: baseUrl,
        scopes: [
            // Zoho Desk scopes (existing)
            'Desk.tickets.READ',
            'Desk.tickets.WRITE',
            'Desk.contacts.READ',
            'Desk.contacts.WRITE',
            'Desk.basic.READ',
            // ManageEngine AssetExplorer scopes (new)
            'SDPOnDemand.assets.ALL',
            'SDPOnDemand.cmdb.ALL',
            'SDPOnDemand.setup.READ',
            'SDPOnDemand.general.READ'
        ]
    };
}

// Function to initiate OAuth authorization for all Zoho services
async function authorizeZoho(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoOAuth: Starting unified OAuth authorization flow`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            logger.warn("ZohoOAuth: Unauthorized access attempt");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const config = getZohoOAuthConfig();
        
        if (!config.clientId || !config.clientSecret) {
            logger.error("ZohoOAuth: Missing Zoho OAuth configuration");
            return { status: 500, jsonBody: { error: "OAuth configuration not found" } };
        }

        // Generate OAuth authorization URL
        const authUrl = new URL(`${config.baseUrl}/auth`);
        authUrl.searchParams.append('response_type', 'code');
        authUrl.searchParams.append('client_id', config.clientId);
        authUrl.searchParams.append('redirect_uri', config.redirectUri);
        authUrl.searchParams.append('scope', config.scopes.join(','));
        authUrl.searchParams.append('access_type', 'offline');
        authUrl.searchParams.append('prompt', 'consent');

        // Add state parameter for security
        const state = Buffer.from(JSON.stringify({
            userId: principal?.userId || 'dev-user',
            timestamp: Date.now()
        })).toString('base64');
        authUrl.searchParams.append('state', state);

        logger.info(`ZohoOAuth: Generated authorization URL for user ${principal?.userId || 'dev-user'}`);
        
        return {
            status: 200,
            jsonBody: {
                authUrl: authUrl.toString(),
                message: 'Visit the authorization URL to grant access to Zoho services (Desk + AssetExplorer)',
                scopes: config.scopes
            }
        };
        
    } catch (error) {
        logger.error("ZohoOAuth: Error generating authorization URL:", error);
        return { status: 500, jsonBody: { error: "Failed to generate authorization URL" } };
    }
}

// Function to handle OAuth callback and exchange code for tokens
async function zohoCallback(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoOAuth: Processing OAuth callback`);
    
    try {
        const url = new URL(req.url);
        const code = url.searchParams.get('code');
        const state = url.searchParams.get('state');
        const error = url.searchParams.get('error');

        if (error) {
            logger.error(`ZohoOAuth: OAuth error: ${error}`);
            return { status: 400, jsonBody: { error: `OAuth authorization failed: ${error}` } };
        }

        if (!code) {
            logger.error("ZohoOAuth: No authorization code received");
            return { status: 400, jsonBody: { error: "Authorization code not found" } };
        }

        const config = getZohoOAuthConfig();

        // Exchange authorization code for access token
        const tokenRequestBody = new URLSearchParams({
            grant_type: 'authorization_code',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            redirect_uri: config.redirectUri,
            code: code
        });

        const tokenResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenRequestBody
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            logger.error(`ZohoOAuth: Token exchange failed: ${errorText}`);
            return { status: 400, jsonBody: { error: "Failed to exchange authorization code for tokens" } };
        }

        const tokens: ZohoTokens = await tokenResponse.json();
        
        // Parse state to get user information
        let userId = 'unknown';
        if (state) {
            try {
                const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
                userId = stateData.userId;
            } catch (err) {
                logger.warn("ZohoOAuth: Failed to parse state parameter");
            }
        }

        // Store tokens securely
        await storeZohoTokens(userId, tokens);

        logger.info(`ZohoOAuth: Successfully obtained tokens for user ${userId} with scopes: ${tokens.scope}`);
        
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Zoho authorization completed successfully for all services',
                expiresIn: tokens.expires_in,
                scope: tokens.scope,
                services: ['Zoho Desk', 'ManageEngine AssetExplorer']
            }
        };
        
    } catch (error) {
        logger.error("ZohoOAuth: Error processing callback:", error);
        return { status: 500, jsonBody: { error: "Failed to process OAuth callback" } };
    }
}

// Function to get current access token (with refresh if needed)
async function getZohoToken(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoOAuth: Getting access token`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Get stored tokens
        const tokens = await getStoredZohoTokens(userId);
        
        if (!tokens) {
            logger.warn(`ZohoOAuth: No tokens found for user ${userId}`);
            return { status: 404, jsonBody: { error: "No Zoho authorization found. Please authorize first." } };
        }

        // Check if token needs refresh (if expires within next 5 minutes)
        const expiresIn = tokens.expires_in || 3600; // Default to 1 hour if undefined
        const expiryTime = tokens.timestamp + (expiresIn * 1000);
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;

        if (expiryTime - now < fiveMinutes) {
            // Refresh token
            const refreshedTokens = await refreshZohoToken(tokens.refresh_token);
            if (refreshedTokens) {
                await storeZohoTokens(userId, refreshedTokens);
                return { status: 200, jsonBody: { accessToken: refreshedTokens.access_token } };
            } else {
                return { status: 401, jsonBody: { error: "Failed to refresh token. Please reauthorize." } };
            }
        }

        return { status: 200, jsonBody: { accessToken: tokens.access_token } };
        
    } catch (error) {
        logger.error("ZohoOAuth: Error getting token:", error);
        return { status: 500, jsonBody: { error: "Failed to get access token" } };
    }
}

// Helper function to refresh access token
async function refreshZohoToken(refreshToken: string): Promise<ZohoTokens | null> {
    try {
        const config = getZohoOAuthConfig();
        
        const refreshRequestBody = new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: refreshToken
        });

        const refreshResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: refreshRequestBody
        });

        if (!refreshResponse.ok) {
            logger.error(`ZohoOAuth: Token refresh failed: ${await refreshResponse.text()}`);
            return null;
        }

        const newTokens: ZohoTokens = await refreshResponse.json();
        logger.info("ZohoOAuth: Successfully refreshed access token");
        
        return newTokens;
        
    } catch (error) {
        logger.error("ZohoOAuth: Error refreshing token:", error);
        return null;
    }
}

// Helper function to store tokens using database persistence
async function storeZohoTokens(userId: string, tokens: ZohoTokens): Promise<void> {
    try {
        const tokenId = await OAuthTokenService.saveToken(
            userId,
            tokens.access_token,
            tokens.refresh_token,
            tokens.expires_in || 3600,
            tokens.scope,
            'zoho',
            'desk',
            tokens.token_type || 'Bearer'
        );
        
        if (tokenId) {
            logger.info(`ZohoOAuth: Stored tokens for user ${userId} with database ID ${tokenId}`);
        } else {
            throw new Error('Failed to save token to database');
        }
        
    } catch (error) {
        logger.error("ZohoOAuth: Error storing tokens:", error);
        throw error;
    }
}

// Helper function to get stored tokens from database
async function getStoredZohoTokens(userId: string): Promise<any> {
    try {
        const token = await OAuthTokenService.getActiveToken(userId, 'zoho', 'desk');
        
        if (!token) {
            return null;
        }
        
        // Convert database token format to legacy format for backward compatibility
        return {
            access_token: token.accessToken,
            refresh_token: token.refreshToken,
            expires_in: Math.floor((token.expiresAt.getTime() - Date.now()) / 1000),
            token_type: token.tokenType,
            scope: token.scope,
            timestamp: token.createdAt?.getTime() || Date.now()
        };
        
    } catch (error) {
        logger.error("ZohoOAuth: Error getting stored tokens:", error);
        return null;
    }
}

// Register Azure Functions
app.http('zoho-oauth-authorize', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'auth/zoho/authorize',
    handler: authorizeZoho
});

app.http('zoho-oauth-callback', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'auth/zoho/callback',
    handler: zohoCallback
});

app.http('zoho-oauth-token', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'auth/zoho/token',
    handler: getZohoToken
}); 