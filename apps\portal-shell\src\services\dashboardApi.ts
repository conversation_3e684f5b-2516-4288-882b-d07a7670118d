// --- Types (based on docs/api/dashboard_endpoints.md) ---

export interface Announcement {
  id: string;
  title: string;
  description: string;
  scope: string;
  publishedAt: string; // ISO timestamp string
  severity: 'high' | 'medium' | 'low';
  link?: string | null;
}

export interface PendingAction {
  id: string;
  type: 'Approval' | 'Review' | 'Acknowledgment' | 'Task';
  title: string;
  source: string;
  dueDate?: string | null; // ISO timestamp string
  details?: string | null;
  link: string;
}

export interface QuickLink {
  id: string;
  title: string;
  icon: string; // Feather icon name
  targetHub: 'it' | 'admin' | 'hr' | 'knowledge' | 'communication' | 'external';
  url: string;
}

export interface RecentDocument {
  id: string;
  name: string;
  lastOpenedAt: string; // ISO timestamp string
  link: string;
}

export interface UpcomingEvent {
  id: string;
  title: string;
  startDateTime: string; // ISO timestamp string
  endDateTime?: string | null; // ISO timestamp string
  location: string;
  scope: string;
  link?: string | null;
}

// --- Mock Data (Derived from DashboardSection.tsx placeholders) ---

const MOCK_ANNOUNCEMENTS: Announcement[] = [
  {
    id: 'ann-1',
    title: 'System Maintenance Notice',
    description: 'Scheduled maintenance on May 5th from 10 PM to 2 AM',
    scope: 'Group-wide',
    publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    severity: 'high'
  },
  {
    id: 'ann-2',
    title: 'New Travel Policy Update',
    description: 'Important changes to domestic travel approval workflow',
    scope: 'SASMOS HET', // Example company-specific
    publishedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    severity: 'medium'
  },
  {
    id: 'ann-3',
    title: 'Q2 Town Hall Recording Available',
    description: 'The recording for the Q2 All-Hands meeting is now available on the Knowledge Hub.',
    scope: 'Group-wide',
    publishedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), 
    severity: 'low',
    link: '/knowledge/recordings/q2-townhall'
  },
  {
    id: 'ann-4',
    title: 'Office Closure - Holiday',
    description: 'All SASMOS HET offices will be closed next Monday for the regional holiday.',
    scope: 'SASMOS HET', 
    publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), 
    severity: 'medium'
  },
];

const MOCK_PENDING_ACTIONS: PendingAction[] = [
  { id: 'act-1', type: 'Approval', title: 'Travel Request Approval', source: 'Rahul S.', details: 'Pending manager review', link: '/admin/travel/requests/123' },
  { id: 'act-2', type: 'Review', title: 'Document Review', source: 'Falcon Project', dueDate: new Date().toISOString(), link: '/knowledge/docs/falcon/review/456' },
  { id: 'act-3', type: 'Acknowledgment', title: 'HR Policy Acknowledgment', source: 'HRMS', details: 'Code of Conduct 2025', dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), link: '/hr/policies/coc2025/ack' },
];

let MOCK_QUICK_LINKS: QuickLink[] = [
  { id: 'ql-1', title: 'Submit IT Ticket', icon: 'FileText', targetHub: 'it', url: '/it/tickets/new' },
  { id: 'ql-2', title: 'Book Meeting Room', icon: 'Calendar', targetHub: 'admin', url: '/admin/facilities/rooms' },
  { id: 'ql-3', title: 'Request Travel', icon: 'Home', targetHub: 'admin', url: '/admin/travel/new' },
  { id: 'ql-4', title: 'Procurement', icon: 'ShoppingCart', targetHub: 'admin', url: '/admin/procurement/new' },
];

const MOCK_RECENT_DOCS: RecentDocument[] = [
  { id: 'doc-1', name: 'Q1 Project Report.docx', lastOpenedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), link: '/knowledge/docs/projects/q1report' },
  { id: 'doc-2', name: 'Travel Expense Policy.pdf', lastOpenedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), link: '/hr/policies/travel-expense' },
  { id: 'doc-3', name: 'Meeting Minutes.docx', lastOpenedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), link: '/knowledge/docs/meetings/minutes-xyz' },
];

const MOCK_UPCOMING_EVENTS: UpcomingEvent[] = [
  { id: 'evt-1', title: 'Company Town Hall', startDateTime: '2025-04-25T10:00:00Z', endDateTime: '2025-04-25T11:30:00Z', location: 'Virtual', scope: 'SASMOS HET', link: '/communication/events/townhall-q2' },
  { id: 'evt-2', title: 'IT Security Training', startDateTime: '2025-04-28T14:00:00Z', endDateTime: '2025-04-28T16:00:00Z', location: 'Training Room B', scope: 'Group-wide' },
  { id: 'evt-3', title: 'New Product Launch', startDateTime: '2025-05-05T09:00:00Z', endDateTime: '2025-05-05T12:00:00Z', location: 'Main Conference Room', scope: 'SASMOS HET' },
];

// Define all potentially available quick links
const ALL_AVAILABLE_QUICK_LINKS: QuickLink[] = [
  { id: 'ql-1', title: 'Submit IT Ticket', icon: 'FileText', targetHub: 'it', url: '/it/tickets/new' },
  { id: 'ql-2', title: 'Book Meeting Room', icon: 'Calendar', targetHub: 'admin', url: '/admin/facilities/rooms' },
  { id: 'ql-3', title: 'Request Travel', icon: 'Home', targetHub: 'admin', url: '/admin/travel/new' },
  { id: 'ql-4', title: 'Procurement', icon: 'ShoppingCart', targetHub: 'admin', url: '/admin/procurement/new' },
  { id: 'ql-5', title: 'View Payslip', icon: 'DollarSign', targetHub: 'hr', url: '/hr/payslips' },
  { id: 'ql-6', title: 'Apply for Leave', icon: 'Coffee', targetHub: 'hr', url: '/hr/leave/new' },
  { id: 'ql-7', title: 'Knowledge Base', icon: 'BookOpen', targetHub: 'knowledge', url: '/knowledge' },
  { id: 'ql-8', title: 'Company Directory', icon: 'Users', targetHub: 'communication', url: '/communication/directory' },
];

// --- Mock API Functions ---

// Export this helper
export const simulateNetworkDelay = (delayMs = 500) => new Promise(resolve => setTimeout(resolve, delayMs));

export const fetchAnnouncements = async (limit = 5, companyId?: string): Promise<Announcement[]> => {
  await simulateNetworkDelay();
  // Simulate potential error
  // if (Math.random() > 0.8) {
  //   throw new Error("Failed to fetch announcements");
  // }
  const filtered = companyId 
    ? MOCK_ANNOUNCEMENTS.filter(ann => ann.scope === companyId || ann.scope === 'Group-wide')
    : MOCK_ANNOUNCEMENTS;

  return filtered.slice(0, limit);
};

export const fetchAllAnnouncements = async (companyId?: string): Promise<Announcement[]> => {
  await simulateNetworkDelay(600); // Slightly longer delay for more data
  // Simulate filtering based on company context
  const filtered = companyId
    ? MOCK_ANNOUNCEMENTS.filter(ann => ann.scope === companyId || ann.scope === 'Group-wide')
    : MOCK_ANNOUNCEMENTS;
  // TODO: Add pagination later
  return filtered;
};

export const fetchPendingActions = async (limit = 5): Promise<PendingAction[]> => {
  await simulateNetworkDelay(700); // Slightly different delay
  // TODO: Implement userId filtering later (implicit)
  return MOCK_PENDING_ACTIONS.slice(0, limit);
};

export const fetchQuickLinks = async (): Promise<QuickLink[]> => {
  await simulateNetworkDelay(300);
  return MOCK_QUICK_LINKS;
};

export const fetchAvailableQuickLinks = async (): Promise<QuickLink[]> => {
  await simulateNetworkDelay(200);
  // In a real app, this might be filtered based on user permissions
  return ALL_AVAILABLE_QUICK_LINKS;
};

export const fetchRecentDocuments = async (limit = 5): Promise<RecentDocument[]> => {
  await simulateNetworkDelay(600);
  // TODO: Implement userId filtering later (implicit)
  return MOCK_RECENT_DOCS.slice(0, limit);
};

export const fetchUpcomingEvents = async (limit = 3, companyId?: string): Promise<UpcomingEvent[]> => {
  await simulateNetworkDelay(400);
  // TODO: Implement startDate filtering later
  const filtered = companyId
    ? MOCK_UPCOMING_EVENTS.filter(event => event.scope === companyId || event.scope === 'Group-wide')
    : MOCK_UPCOMING_EVENTS;
  
  return filtered.slice(0, limit);
};

// Update the user's selected/ordered links
export const updateQuickLinks = async (newLinks: QuickLink[]): Promise<void> => {
  await simulateNetworkDelay(450); // Simulate save delay
  console.log("API Mock: Updating links to:", newLinks);
  // Update the mock array in memory
  MOCK_QUICK_LINKS = newLinks; 
  
  // Simulate potential save error
  // if (Math.random() > 0.85) {
  //  throw new Error("Failed to save quick links configuration.");
  // }
  
  return Promise.resolve(); // Indicate success
}; 