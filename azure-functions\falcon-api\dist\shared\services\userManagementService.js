"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userManagementService = exports.UserManagementService = exports.updateUserEntraIdAndTenant = exports.getPortalUserByEmail = exports.updateUserEntraId = exports.updateUserLastLogin = exports.removeRoleFromUser = exports.assignRoleToUser = exports.findOrCreateUser = void 0;
const db_1 = require("../db");
const logger_1 = require("../utils/logger");
// Remove GraphUser import if not used
// import { GraphUser } from "./graphService"; 
const mssql_1 = __importDefault(require("mssql")); // Ensure mssql is imported
// Helper to map DB row to PortalUser - respects database role assignments as-is
function mapDbUserToPortalUser(dbUser) {
    // Use actual FirstName and LastName from database if available, otherwise extract from email
    const firstName = dbUser.FirstName || '';
    const lastName = dbUser.LastName || '';
    const fullName = firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName;
    // Parse roles from database - respect all assigned roles
    let userRoles = dbUser.Roles ? dbUser.Roles.split(',').map((role) => role.trim()) : [];
    // Fallback to email extraction if no name in database
    if (!fullName) {
        const emailPart = dbUser.Email.split('@')[0]; // Get part before @
        const nameParts = emailPart.split('.'); // Split by dots
        const extractedFirstName = nameParts[0] ? nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : '';
        const extractedLastName = nameParts[1] ? nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : '';
        const extractedFullName = extractedLastName ? `${extractedFirstName} ${extractedLastName}` : extractedFirstName;
        return {
            id: dbUser.EntraID,
            internalId: dbUser.UserID,
            name: extractedFullName || dbUser.Username,
            email: dbUser.Email,
            company: dbUser.CompanyName,
            companyId: dbUser.CompanyID,
            roles: userRoles,
            status: dbUser.IsActive ? 'Active' : 'Inactive',
            lastLogin: dbUser.LastLogin ? dbUser.LastLogin.toISOString() : undefined,
            tenantId: dbUser.TenantID // Multi-tenant support
        };
    }
    const mappedUser = {
        id: dbUser.EntraID,
        internalId: dbUser.UserID,
        name: fullName,
        email: dbUser.Email,
        company: dbUser.CompanyName,
        companyId: dbUser.CompanyID,
        roles: userRoles,
        status: dbUser.IsActive ? 'Active' : 'Inactive',
        lastLogin: dbUser.LastLogin ? dbUser.LastLogin.toISOString() : undefined,
        tenantId: dbUser.TenantID // Multi-tenant support
    };
    return mappedUser;
}
/**
 * Enhanced findOrCreateUser with security checks
 * Creates a new user in the database with proper role assignment
 */
const findOrCreateUser = async (entraUser, createdByUserId = 1) => {
    logger_1.logger.info(`findOrCreateUser called for Entra ID: ${entraUser.id}`);
    const checkUserQuery = `SELECT * FROM Users WHERE EntraID = @EntraID;`;
    const checkUserParams = [
        { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraUser.id }
    ];
    try {
        const existingUserResult = await (0, db_1.executeQuery)(checkUserQuery, checkUserParams);
        if (existingUserResult.recordset && existingUserResult.recordset.length > 0) {
            const existingUser = existingUserResult.recordset[0];
            logger_1.logger.info(`Found existing user ${existingUser.UserID} for Entra ID ${entraUser.id}`);
            // TODO: Optionally update LastLoginDate or other fields on find?
            // const updateUserLoginQuery = `UPDATE Users SET LastLoginDate = GETDATE() WHERE UserID = @UserID`;
            // await executeQuery(updateUserLoginQuery, { UserID: existingUser.UserID });
            return existingUser;
        }
        else {
            logger_1.logger.info(`User with Entra ID ${entraUser.id} not found. Creating new user.`);
            // --- 1. Resolve CompanyID --- 
            let companyId = null;
            if (entraUser.companyName) {
                const companyQuery = `SELECT CompanyID FROM Companies WHERE CompanyName = @CompanyName AND IsActive = 1;`;
                const companyParams = [
                    { name: 'CompanyName', type: mssql_1.default.NVarChar, value: entraUser.companyName }
                ];
                try {
                    const companyResult = await (0, db_1.executeQuery)(companyQuery, companyParams);
                    if (companyResult.recordset && companyResult.recordset.length > 0) {
                        companyId = companyResult.recordset[0].CompanyID;
                    }
                    else {
                        // Company name provided but not found in DB - this is an error state.
                        logger_1.logger.error(`Company '${entraUser.companyName}' provided by Entra ID not found or inactive in Companies table for user ${entraUser.id}.`);
                        throw new Error(`Company '${entraUser.companyName}' not found or inactive in the database. Cannot create user.`);
                    }
                }
                catch (companyError) {
                    logger_1.logger.error(`Error looking up company '${entraUser.companyName}' for user ${entraUser.id}:`, companyError);
                    throw new Error(`Database error during company lookup for user ${entraUser.id}.`); // Rethrow as critical error
                }
            }
            else {
                // No company name provided by Entra ID. What's the policy?
                // Option A: Assign a default/placeholder company.
                // Option B: Reject user creation.
                // For now, let's use a placeholder (e.g., ID 1) but log a strong warning.
                companyId = 1; // Placeholder for 'Unspecified' or first company
                logger_1.logger.warn(`Entra user ${entraUser.id} has no companyName. Assigning default CompanyID: ${companyId}. Review required.`);
                // Consider throwing an error if a company is strictly required:
                // throw new Error(`Entra user ${entraUser.id} is missing required company information.`);
            }
            // --- 2. Resolve DepartmentID (Optional) --- 
            let departmentId = null;
            if (entraUser.department && companyId) { // Only lookup if department and company are known
                const deptQuery = `SELECT DepartmentID FROM Departments WHERE DepartmentName = @DepartmentName AND CompanyID = @CompanyID AND IsActive = 1;`;
                const deptParams = [
                    { name: 'DepartmentName', type: mssql_1.default.NVarChar, value: entraUser.department },
                    { name: 'CompanyID', type: mssql_1.default.Int, value: companyId }
                ];
                try {
                    const deptResult = await (0, db_1.executeQuery)(deptQuery, deptParams);
                    if (deptResult.recordset && deptResult.recordset.length > 0) {
                        departmentId = deptResult.recordset[0].DepartmentID;
                    }
                    else {
                        // Department not found, but maybe that's okay? Log warning, set to NULL.
                        logger_1.logger.warn(`Department '${entraUser.department}' not found or inactive in CompanyID ${companyId} for user ${entraUser.id}. Setting DepartmentID to NULL.`);
                    }
                }
                catch (deptError) {
                    // Log error but don't necessarily fail user creation
                    logger_1.logger.error(`Error looking up department '${entraUser.department}' for user ${entraUser.id}:`, deptError);
                }
            }
            // --- 3. Create User --- 
            const insertUserQuery = `
                INSERT INTO Users (
                    EntraID, Username, Email, FirstName, LastName, 
                    CompanyID, DepartmentID, IsActive, CreatedBy, CreatedDate, ModifiedDate
                ) 
                OUTPUT INSERTED.* 
                VALUES (
                    @EntraID, @Username, @Email, @FirstName, @LastName, 
                    @CompanyID, @DepartmentID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE()
                );
            `;
            // Convert newUserParams object to QueryParameter array
            const newUserParamsArray = [
                { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraUser.id },
                { name: 'Username', type: mssql_1.default.NVarChar, value: entraUser.userPrincipalName },
                { name: 'Email', type: mssql_1.default.NVarChar, value: entraUser.mail || entraUser.userPrincipalName },
                { name: 'FirstName', type: mssql_1.default.NVarChar, value: entraUser.givenName || '' },
                { name: 'LastName', type: mssql_1.default.NVarChar, value: entraUser.surname || 'User' },
                { name: 'CompanyID', type: mssql_1.default.Int, value: companyId },
                // Handle potential null for DepartmentID - pass sql.Int, value can be null
                { name: 'DepartmentID', type: mssql_1.default.Int, value: departmentId },
                { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                { name: 'CreatedBy', type: mssql_1.default.Int, value: createdByUserId }
            ];
            const newUserResult = await (0, db_1.executeQuery)(insertUserQuery, newUserParamsArray);
            if (!newUserResult.recordset || newUserResult.recordset.length === 0) {
                logger_1.logger.error("Failed to create user or retrieve created record.", { entraId: entraUser.id });
                throw new Error("User creation failed in database.");
            }
            const createdUser = newUserResult.recordset[0];
            logger_1.logger.info(`Successfully created user ${createdUser.UserID} for Entra ID ${entraUser.id}`);
            // --- 4. Assign Default Role --- 
            const defaultRoleName = process.env.DEFAULT_USER_ROLE || 'Employee'; // Use env var or fallback
            logger_1.logger.info(`Attempting to assign default role: ${defaultRoleName}`);
            let defaultRoleId = null;
            try {
                const roleQuery = `SELECT RoleID FROM Roles WHERE RoleName = @RoleName AND IsActive = 1;`;
                const roleParams = [
                    { name: 'RoleName', type: mssql_1.default.NVarChar, value: defaultRoleName }
                ];
                const roleResult = await (0, db_1.executeQuery)(roleQuery, roleParams);
                if (roleResult.recordset && roleResult.recordset.length > 0) {
                    defaultRoleId = roleResult.recordset[0].RoleID;
                }
                else {
                    logger_1.logger.error(`Default role '${defaultRoleName}' not found or inactive in Roles table.`);
                }
            }
            catch (roleError) {
                logger_1.logger.error(`Error looking up default role '${defaultRoleName}':`, roleError);
            }
            if (defaultRoleId) {
                const insertUserRoleQuery = `
                    INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate)
                    VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());
                `;
                const insertUserRoleParams = [
                    { name: 'UserID', type: mssql_1.default.Int, value: createdUser.UserID },
                    { name: 'RoleID', type: mssql_1.default.Int, value: defaultRoleId },
                    { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                    { name: 'CreatedBy', type: mssql_1.default.Int, value: createdByUserId }
                ];
                try {
                    await (0, db_1.executeQuery)(insertUserRoleQuery, insertUserRoleParams);
                    logger_1.logger.info(`Assigned default role '${defaultRoleName}' (ID: ${defaultRoleId}) to user ${createdUser.UserID}`);
                }
                catch (userRoleError) {
                    // Log error but don't fail the whole process
                    logger_1.logger.error(`Failed to assign default role to user ${createdUser.UserID}:`, userRoleError);
                }
            }
            return createdUser;
        }
    }
    catch (error) {
        // Log the specific error from company/dept lookup or user insert
        logger_1.logger.error(`Error in findOrCreateUser process for Entra ID ${entraUser.id}:`, error);
        // Re-throw the error to be caught by the calling function (e.g., AddUser API)
        throw error;
        // return null; // Returning null hides the error type
    }
};
exports.findOrCreateUser = findOrCreateUser;
/**
 * Assigns a specific role to a user.
 * If the assignment already exists but is inactive, it reactivates it.
 * If the assignment doesn't exist, it creates a new one.
 *
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to assign.
 * @param assignedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role was successfully assigned or reactivated, false otherwise.
 */
const assignRoleToUser = async (userId, roleId, assignedByUserId) => {
    logger_1.logger.info(`assignRoleToUser called for UserID: ${userId}, RoleID: ${roleId}`);
    const checkQuery = `SELECT UserRoleID, IsActive FROM UserRoles WHERE UserID = @UserID AND RoleID = @RoleID;`;
    const checkParams = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId },
        { name: 'RoleID', type: mssql_1.default.Int, value: roleId }
    ];
    try {
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset && checkResult.recordset.length > 0) {
            // Assignment exists
            const existingAssignment = checkResult.recordset[0];
            if (!existingAssignment.IsActive) {
                // Reactivate existing inactive assignment
                logger_1.logger.info(`Reactivating existing role assignment (UserRoleID: ${existingAssignment.UserRoleID}) for UserID: ${userId}, RoleID: ${roleId}`);
                const updateQuery = `UPDATE UserRoles SET IsActive = 1, ModifiedBy = @ModifiedBy, ModifiedDate = GETDATE() WHERE UserRoleID = @UserRoleID;`;
                const updateParams = [
                    { name: 'UserRoleID', type: mssql_1.default.Int, value: existingAssignment.UserRoleID },
                    { name: 'ModifiedBy', type: mssql_1.default.Int, value: assignedByUserId }
                ];
                await (0, db_1.executeQuery)(updateQuery, updateParams);
                return true;
            }
            else {
                // Role already active
                logger_1.logger.info(`Role ${roleId} is already actively assigned to user ${userId}. No action needed.`);
                return true; // Consider already assigned as success
            }
        }
        else {
            // Assignment does not exist, create new one
            logger_1.logger.info(`Creating new role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            const insertQuery = `INSERT INTO UserRoles (UserID, RoleID, IsActive, CreatedBy, CreatedDate, ModifiedDate) VALUES (@UserID, @RoleID, @IsActive, @CreatedBy, GETUTCDATE(), GETUTCDATE());`;
            const insertParams = [
                { name: 'UserID', type: mssql_1.default.Int, value: userId },
                { name: 'RoleID', type: mssql_1.default.Int, value: roleId },
                { name: 'IsActive', type: mssql_1.default.Bit, value: true },
                { name: 'CreatedBy', type: mssql_1.default.Int, value: assignedByUserId }
            ];
            await (0, db_1.executeQuery)(insertQuery, insertParams);
            return true;
        }
    }
    catch (error) {
        logger_1.logger.error(`Error assigning role ${roleId} to user ${userId}:`, error);
        return false; // Indicate failure
    }
};
exports.assignRoleToUser = assignRoleToUser;
/**
 * Removes a specific role from a user (by marking the assignment as inactive).
 *
 * @param userId - The internal UserID of the user.
 * @param roleId - The internal RoleID of the role to remove.
 * @param removedByUserId - The UserID performing the action (for audit columns).
 * @returns True if the role assignment was successfully marked as inactive, false otherwise.
 */
const removeRoleFromUser = async (userId, roleId, removedByUserId) => {
    logger_1.logger.info(`removeRoleFromUser called for UserID: ${userId}, RoleID: ${roleId}`);
    const updateQuery = `UPDATE UserRoles SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID AND RoleID = @RoleID AND IsActive = 1;`;
    const updateParams = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId },
        { name: 'RoleID', type: mssql_1.default.Int, value: roleId },
        { name: 'ModifiedBy', type: mssql_1.default.Int, value: removedByUserId }
    ];
    try {
        const result = await (0, db_1.executeQuery)(updateQuery, updateParams);
        // rowsAffected will be 1 if an active record was found and updated, 0 otherwise.
        if (result.rowsAffected && result.rowsAffected[0] === 1) {
            logger_1.logger.info(`Successfully deactivated role assignment for UserID: ${userId}, RoleID: ${roleId}`);
            return true;
        }
        else {
            logger_1.logger.warn(`No active role assignment found for UserID: ${userId}, RoleID: ${roleId} to deactivate.`);
            // Consider it success if the role wasn't active anyway?
            // Returning true might be less confusing for the caller.
            return true; // Indicate success even if no change was needed
        }
    }
    catch (error) {
        logger_1.logger.error(`Error removing role ${roleId} from user ${userId}:`, error);
        return false; // Indicate failure
    }
};
exports.removeRoleFromUser = removeRoleFromUser;
/**
 * Updates the LastLoginDate for a given user in the local database.
 *
 * @param userId - The internal UserID of the user.
 * @returns True if the update was successful, false otherwise.
 */
const updateUserLastLogin = async (userId) => {
    logger_1.logger.info(`Updating LastLoginDate for UserID: ${userId}`);
    const query = `UPDATE Users SET LastLoginDate = GETUTCDATE() WHERE UserID = @UserID;`;
    const params = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId }
    ];
    try {
        await (0, db_1.executeQuery)(query, params);
        return true;
    }
    catch (error) {
        logger_1.logger.error(`Error updating LastLoginDate for UserID ${userId}:`, error);
        return false;
    }
};
exports.updateUserLastLogin = updateUserLastLogin;
/**
 * Updates a user's EntraID to match the actual Entra Object ID.
 * This is used when a user is found by email but has an incorrect EntraID stored.
 * @param userId - The internal UserID of the user to update.
 * @param correctEntraId - The correct Entra Object ID from authentication.
 * @returns True if the update was successful, false otherwise.
 */
const updateUserEntraId = async (userId, correctEntraId) => {
    logger_1.logger.info(`updateUserEntraId: Updating UserID ${userId} with correct EntraID: ${correctEntraId}`);
    const query = `
        UPDATE Users 
        SET EntraID = @EntraID, ModifiedDate = GETUTCDATE() 
        WHERE UserID = @UserID AND IsActive = 1;
    `;
    const params = [
        { name: 'EntraID', type: mssql_1.default.NVarChar, value: correctEntraId },
        { name: 'UserID', type: mssql_1.default.Int, value: userId }
    ];
    try {
        const result = await (0, db_1.executeQuery)(query, params);
        if (result.rowsAffected && result.rowsAffected[0] > 0) {
            logger_1.logger.info(`Successfully updated EntraID for UserID: ${userId}`);
            return true;
        }
        logger_1.logger.warn(`No active user found to update with UserID: ${userId}`);
        return false;
    }
    catch (error) {
        logger_1.logger.error(`Error updating EntraID for UserID ${userId}:`, error);
        throw new Error(`Database error while updating EntraID for UserID ${userId}.`);
    }
};
exports.updateUserEntraId = updateUserEntraId;
/**
 * Gets a portal user by email address.
 * Used as fallback when EntraID lookup fails due to data inconsistency.
 * @param email - The email address of the user.
 * @returns The PortalUser or null if not found.
 */
const getPortalUserByEmail = async (email) => {
    logger_1.logger.info(`getPortalUserByEmail: Starting lookup for email: ${email}`);
    const query = `
        SELECT
            pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
            c.CompanyID, c.CompanyName,
            (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
             FROM UserRoles ur
             JOIN Roles r ON ur.RoleID = r.RoleID
             WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
        FROM Users pu
        LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
        WHERE pu.Email = @Email AND pu.IsActive = 1;
    `;
    const params = [
        { name: 'Email', type: mssql_1.default.NVarChar, value: email }
    ];
    try {
        logger_1.logger.info(`getPortalUserByEmail: Executing SQL query...`);
        logger_1.logger.info(`getPortalUserByEmail: SQL: ${query.replace(/\s+/g, ' ').trim()}`);
        logger_1.logger.info(`getPortalUserByEmail: Params: Email = '${email}'`);
        const result = await (0, db_1.executeQuery)(query, params);
        logger_1.logger.info(`getPortalUserByEmail: Query executed successfully`);
        logger_1.logger.info(`getPortalUserByEmail: Result count: ${result.recordset?.length || 0}`);
        if (result.recordset && result.recordset.length > 0) {
            const dbUser = result.recordset[0];
            logger_1.logger.info(`getPortalUserByEmail: ✅ USER FOUND IN DATABASE`);
            logger_1.logger.info(`getPortalUserByEmail: UserID: ${dbUser.UserID}`);
            logger_1.logger.info(`getPortalUserByEmail: Email: ${dbUser.Email}`);
            logger_1.logger.info(`getPortalUserByEmail: Username: ${dbUser.Username}`);
            logger_1.logger.info(`getPortalUserByEmail: FirstName: ${dbUser.FirstName}`);
            logger_1.logger.info(`getPortalUserByEmail: LastName: ${dbUser.LastName}`);
            logger_1.logger.info(`getPortalUserByEmail: IsActive: ${dbUser.IsActive}`);
            logger_1.logger.info(`getPortalUserByEmail: CompanyName: ${dbUser.CompanyName}`);
            logger_1.logger.info(`getPortalUserByEmail: Roles from DB: '${dbUser.Roles}'`);
            const mappedUser = mapDbUserToPortalUser(dbUser);
            logger_1.logger.info(`getPortalUserByEmail: Mapped user roles: [${mappedUser.roles.join(', ')}]`);
            return mappedUser;
        }
        logger_1.logger.warn(`getPortalUserByEmail: ❌ NO USER FOUND - Email '${email}' not in database or user is inactive`);
        return null;
    }
    catch (error) {
        logger_1.logger.error(`getPortalUserByEmail: 💥 DATABASE ERROR for email ${email}:`, error);
        throw new Error(`Database error while fetching user by email ${email}.`);
    }
};
exports.getPortalUserByEmail = getPortalUserByEmail;
/**
 * Updates a user's EntraID and TenantID.
 * Used when migrating legacy users to multi-tenant identification.
 * @param userId - The internal UserID from the Users table.
 * @param entraId - The new EntraID to set.
 * @param tenantId - The new TenantID to set.
 * @returns Promise that resolves when the update is complete.
 */
const updateUserEntraIdAndTenant = async (userId, entraId, tenantId) => {
    logger_1.logger.info(`updateUserEntraIdAndTenant called for UserID: ${userId}, EntraID: ${entraId}, TenantID: ${tenantId}`);
    const query = `
        UPDATE Users 
        SET EntraID = @EntraID, TenantID = @TenantID, ModifiedDate = GETDATE()
        WHERE UserID = @UserID;
    `;
    const params = [
        { name: 'UserID', type: mssql_1.default.Int, value: userId },
        { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
        { name: 'TenantID', type: mssql_1.default.NVarChar, value: tenantId }
    ];
    try {
        await (0, db_1.executeQuery)(query, params);
        logger_1.logger.info(`Successfully updated user ${userId} with EntraID ${entraId} and TenantID ${tenantId}`);
    }
    catch (error) {
        logger_1.logger.error(`Error updating user ${userId} with EntraID ${entraId} and TenantID ${tenantId}:`, error);
        throw new Error(`Database error while updating user ${userId} with EntraID and TenantID.`);
    }
};
exports.updateUserEntraIdAndTenant = updateUserEntraIdAndTenant;
// TODO: Implement other user management functions from the guide:
// - (Potentially) updateUser(userId: number, updateData: Partial<DbUser>, modifiedBy: number): Promise<DbUser | null>
// - (Potentially) getUserById(userId: number): Promise<DbUser | null> - might need joins
// - (Potentially) getUsersWithRoles(filters, pagination): Promise<{users: DbUser[], totalCount: number}> - complex query needed
class UserManagementService {
    // ... existing methods ...
    async updateUserLastLogin(userId) {
        // ... existing implementation ...
    }
    /**
     * Retrieves a paginated and filtered list of portal users.
     * @param filters - Filtering criteria (searchTerm, companyFilter, roleFilter, status).
     * @param pagination - Pagination options (page, pageSize).
     * @returns A promise resolving to an object containing the list of users and the total count.
     */
    async getPortalUsersPaginated(filters, pagination) {
        logger_1.logger.info(`[DEBUG] Executing getPortalUsersPaginated with filters: ${JSON.stringify(filters)}, pagination: ${JSON.stringify(pagination)}`);
        // Build WHERE conditions and parameters
        const whereClauses = [];
        const parameters = [];
        // Search filter
        if (filters.searchTerm) {
            whereClauses.push(`(pu.Username LIKE @searchTerm OR pu.Email LIKE @searchTerm OR pu.FirstName LIKE @searchTerm OR pu.LastName LIKE @searchTerm)`);
            parameters.push({ name: 'searchTerm', type: mssql_1.default.NVarChar, value: `%${filters.searchTerm}%` });
        }
        // Company filter - filter by company name
        if (filters.companyFilter) {
            whereClauses.push(`c.CompanyName = @companyFilter`);
            parameters.push({ name: 'companyFilter', type: mssql_1.default.NVarChar, value: filters.companyFilter });
        }
        // Status filter - THIS IS THE KEY FIX
        if (filters.status === 'Active') {
            whereClauses.push(`pu.IsActive = 1`);
        }
        else if (filters.status === 'Inactive') {
            whereClauses.push(`pu.IsActive = 0`);
        }
        // If status is undefined (All), don't add any filter
        // Role filter - filter by role name using EXISTS subquery
        if (filters.roleFilter) {
            whereClauses.push(`EXISTS (SELECT 1 FROM UserRoles ur JOIN Roles r ON ur.RoleID = r.RoleID WHERE ur.UserID = pu.UserID AND ur.IsActive = 1 AND r.RoleName = @roleFilter)`);
            parameters.push({ name: 'roleFilter', type: mssql_1.default.NVarChar, value: filters.roleFilter });
        }
        const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        logger_1.logger.info(`[DEBUG] WHERE clause: ${whereClause}`);
        // Simple count query
        const countQuery = `
            SELECT COUNT(*) as totalCount
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            ${whereClause}
        `;
        // Simple data query with pagination
        const dataQuery = `
            SELECT 
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') 
                 FROM UserRoles ur 
                 JOIN Roles r ON ur.RoleID = r.RoleID 
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            ${whereClause}
            ORDER BY pu.UserID
            OFFSET @offset ROWS
            FETCH NEXT @pageSize ROWS ONLY
        `;
        // Add pagination parameters
        const offset = (pagination.page - 1) * pagination.pageSize;
        parameters.push({ name: 'offset', type: mssql_1.default.Int, value: offset });
        parameters.push({ name: 'pageSize', type: mssql_1.default.Int, value: pagination.pageSize });
        try {
            // Execute count query (without pagination parameters)
            const countParams = parameters.filter(p => p.name !== 'offset' && p.name !== 'pageSize');
            const countResult = await (0, db_1.executeQuery)(countQuery, countParams);
            const totalCount = countResult.recordset[0].totalCount;
            // Execute data query (with pagination parameters)
            const dataResult = await (0, db_1.executeQuery)(dataQuery, parameters);
            logger_1.logger.info(`[DEBUG] Raw query returned ${dataResult.recordset.length} users`);
            logger_1.logger.info(`[DEBUG] Total count: ${totalCount}`);
            // Map results
            const users = dataResult.recordset.map(mapDbUserToPortalUser);
            return {
                users,
                totalCount
            };
        }
        catch (error) {
            logger_1.logger.error(`Error in getPortalUsersPaginated: ${error}`);
            throw error;
        }
    }
    async getPortalUserByEntraId(entraId) {
        logger_1.logger.info(`getPortalUserByEntraId called for Entra ID: ${entraId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.EntraID = @EntraID;
        `;
        const params = [
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger_1.logger.debug("Raw DB result for user:", JSON.stringify(result.recordset[0], null, 2));
                // Ensure mapDbUserToPortalUser is accessible, if it's not exported or part of the class, you might need to define it locally or make it accessible.
                // For now, assuming mapDbUserToPortalUser is accessible in this scope.
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger_1.logger.warn(`No user found with EntraID: ${entraId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching user by EntraID ${entraId}:`, error);
            // It's better to throw the error to let the caller (Azure function) handle the HTTP response code
            throw new Error(`Database error while fetching user by EntraID ${entraId}.`);
        }
    }
    /**
     * Gets a portal user by their Entra ID and Tenant ID (multi-tenant support).
     * @param entraId - The Entra ID of the user.
     * @param tenantId - The Tenant ID of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByEntraIdAndTenant(entraId, tenantId) {
        logger_1.logger.info(`getPortalUserByEntraIdAndTenant called for Entra ID: ${entraId}, Tenant ID: ${tenantId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.EntraID = @EntraID AND pu.TenantID = @TenantID;
        `;
        const params = [
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
            { name: 'TenantID', type: mssql_1.default.NVarChar, value: tenantId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger_1.logger.debug("Raw DB result for user by EntraID and TenantID:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger_1.logger.warn(`No user found with EntraID: ${entraId} and TenantID: ${tenantId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching user by EntraID ${entraId} and TenantID ${tenantId}:`, error);
            throw new Error(`Database error while fetching user by EntraID ${entraId} and TenantID ${tenantId}.`);
        }
    }
    /**
     * Gets a portal user by their internal UserID.
     * @param userId - The internal UserID of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByUserId(userId) {
        logger_1.logger.info(`getPortalUserByUserId called for UserID: ${userId}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.UserID = @UserID;
        `;
        const params = [
            { name: 'UserID', type: mssql_1.default.Int, value: userId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger_1.logger.debug("Raw DB result for user by UserID:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger_1.logger.warn(`No user found with UserID: ${userId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching user by UserID ${userId}:`, error);
            throw new Error(`Database error while fetching user by UserID ${userId}.`);
        }
    }
    /**
     * Gets the internal UserID for a user by their Entra ID.
     * @param entraId - The Entra ID of the user.
     * @returns The internal UserID or null if not found.
     */
    async getUserIdByEntraId(entraId) {
        logger_1.logger.info(`getUserIdByEntraId called for Entra ID: ${entraId}`);
        const query = `SELECT UserID FROM Users WHERE EntraID = @EntraID AND IsActive = 1;`;
        const params = [
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.recordset && result.recordset.length > 0) {
                return result.recordset[0].UserID;
            }
            logger_1.logger.warn(`No active user found with EntraID: ${entraId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching UserID by EntraID ${entraId}:`, error);
            throw new Error(`Database error while fetching UserID by EntraID ${entraId}.`);
        }
    }
    /**
     * Deactivates a portal user by their Entra ID.
     * @param entraId - The Entra ID of the user to deactivate.
     * @param modifiedByUserId - The UserID of the user performing the operation.
     * @returns True if the user was successfully deactivated, false otherwise.
     */
    async deactivatePortalUserByEntraId(entraId, modifiedByUserId) {
        logger_1.logger.info(`deactivatePortalUserByEntraId called for Entra ID: ${entraId} by UserID: ${modifiedByUserId}`);
        const query = `
            UPDATE Users 
            SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() 
            WHERE EntraID = @EntraID AND IsActive = 1;
        `;
        const params = [
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
            { name: 'ModifiedBy', type: mssql_1.default.Int, value: modifiedByUserId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.rowsAffected && result.rowsAffected[0] > 0) {
                logger_1.logger.info(`Successfully deactivated user with EntraID: ${entraId}`);
                return true;
            }
            logger_1.logger.warn(`No active user found to deactivate with EntraID: ${entraId}`);
            return false;
        }
        catch (error) {
            logger_1.logger.error(`Error deactivating user by EntraID ${entraId}:`, error);
            throw new Error(`Database error while deactivating user by EntraID ${entraId}.`);
        }
    }
    /**
     * Gets a portal user by email address.
     * Used as fallback when EntraID lookup fails due to data inconsistency.
     * @param email - The email address of the user.
     * @returns The PortalUser or null if not found.
     */
    async getPortalUserByEmail(email) {
        logger_1.logger.info(`getPortalUserByEmail called for email: ${email}`);
        const query = `
            SELECT
                pu.UserID, pu.EntraID, pu.TenantID, pu.Username, pu.Email, pu.FirstName, pu.LastName, pu.IsActive, pu.LastLoginDate AS LastLogin,
                c.CompanyID, c.CompanyName,
                (SELECT STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName)
                 FROM UserRoles ur
                 JOIN Roles r ON ur.RoleID = r.RoleID
                 WHERE ur.UserID = pu.UserID AND ur.IsActive = 1) AS Roles
            FROM Users pu
            LEFT JOIN Companies c ON pu.CompanyID = c.CompanyID
            WHERE pu.Email = @Email AND pu.IsActive = 1;
        `;
        const params = [
            { name: 'Email', type: mssql_1.default.NVarChar, value: email }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.recordset && result.recordset.length > 0) {
                logger_1.logger.debug("Raw DB result for user by email:", JSON.stringify(result.recordset[0], null, 2));
                return mapDbUserToPortalUser(result.recordset[0]);
            }
            logger_1.logger.warn(`No user found with email: ${email}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error fetching user by email ${email}:`, error);
            throw new Error(`Database error while fetching user by email ${email}.`);
        }
    }
    /**
     * Updates a user's EntraID to match the actual Entra Object ID.
     * This is used when a user is found by email but has an incorrect EntraID stored.
     * @param userId - The internal UserID of the user to update.
     * @param correctEntraId - The correct Entra Object ID from authentication.
     * @returns True if the update was successful, false otherwise.
     */
    async updateUserEntraId(userId, correctEntraId) {
        logger_1.logger.info(`updateUserEntraId: Updating UserID ${userId} with correct EntraID: ${correctEntraId}`);
        const query = `
            UPDATE Users 
            SET EntraID = @EntraID, ModifiedDate = GETUTCDATE() 
            WHERE UserID = @UserID AND IsActive = 1;
        `;
        const params = [
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: correctEntraId },
            { name: 'UserID', type: mssql_1.default.Int, value: userId }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, params);
            if (result.rowsAffected && result.rowsAffected[0] > 0) {
                logger_1.logger.info(`Successfully updated EntraID for UserID: ${userId}`);
                return true;
            }
            logger_1.logger.warn(`No active user found to update with UserID: ${userId}`);
            return false;
        }
        catch (error) {
            logger_1.logger.error(`Error updating EntraID for UserID ${userId}:`, error);
            throw new Error(`Database error while updating EntraID for UserID ${userId}.`);
        }
    }
    /**
     * Updates a user's EntraID and TenantID for multi-tenant support.
     * Used when migrating legacy users to multi-tenant identification.
     * @param userId - The internal UserID from the Users table.
     * @param entraId - The new EntraID to set.
     * @param tenantId - The new TenantID to set.
     * @returns Promise that resolves when the update is complete.
     */
    async updateUserEntraIdAndTenant(userId, entraId, tenantId) {
        logger_1.logger.info(`updateUserEntraIdAndTenant called for UserID: ${userId}, EntraID: ${entraId}, TenantID: ${tenantId}`);
        const query = `
            UPDATE Users 
            SET EntraID = @EntraID, TenantID = @TenantID, ModifiedDate = GETUTCDATE()
            WHERE UserID = @UserID;
        `;
        const params = [
            { name: 'UserID', type: mssql_1.default.Int, value: userId },
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
            { name: 'TenantID', type: mssql_1.default.NVarChar, value: tenantId }
        ];
        try {
            await (0, db_1.executeQuery)(query, params);
            logger_1.logger.info(`Successfully updated user ${userId} with EntraID ${entraId} and TenantID ${tenantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Error updating user ${userId} with EntraID ${entraId} and TenantID ${tenantId}:`, error);
            throw new Error(`Database error while updating user ${userId} with EntraID and TenantID.`);
        }
    }
    // ======= PHASE 2: MAPPING TABLE METHODS =======
    /**
     * Upserts a user's EntraID mapping when they login
     * This is the core of Phase 2 - automatic EntraID capture
     */
    async upsertUserEntraIDMapping(email, entraId, tenantId, displayName, companyName, captureSource = 'AutoLogin') {
        logger_1.logger.info(`Upserting EntraID mapping for ${email} with EntraID ${entraId}`);
        const query = `EXEC UpsertUserEntraIDMapping @Email, @EntraID, @TenantID, @DisplayName, @CompanyName, @CaptureSource`;
        const parameters = [
            { name: 'Email', type: mssql_1.default.NVarChar, value: email },
            { name: 'EntraID', type: mssql_1.default.NVarChar, value: entraId },
            { name: 'TenantID', type: mssql_1.default.NVarChar, value: tenantId },
            { name: 'DisplayName', type: mssql_1.default.NVarChar, value: displayName || null },
            { name: 'CompanyName', type: mssql_1.default.NVarChar, value: companyName || null },
            { name: 'CaptureSource', type: mssql_1.default.NVarChar, value: captureSource }
        ];
        await (0, db_1.executeQuery)(query, parameters);
        logger_1.logger.info(`Successfully upserted EntraID mapping for ${email}`);
    }
    /**
     * Gets user data using the new mapping table view
     * This provides the most complete user information with automatic mapping
     */
    async getPortalUserWithMapping(email) {
        logger_1.logger.info(`Getting user with mapping for email: ${email}`);
        const query = `
            SELECT 
                u.UserID,
                COALESCE(m.MappedEntraID, u.EntraID) as EntraID,
                COALESCE(m.MappedTenantID, u.TenantID) as TenantID,
                u.Username,
                u.Email,
                u.FirstName,
                u.LastName,
                u.CompanyID,
                c.CompanyName,
                u.IsActive,
                u.LastLoginDate,
                STRING_AGG(r.RoleName, ',') as Roles,
                m.EntraIDStatus,
                m.LastSeen
            FROM vw_UsersWithEntraIDMappings u
            LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            LEFT JOIN UserEntraIDMappings m ON u.Email = m.Email
            WHERE u.Email = @email AND u.IsActive = 1
            GROUP BY 
                u.UserID, u.EntraID, u.TenantID, u.Username, u.Email, u.FirstName, u.LastName, 
                u.CompanyID, c.CompanyName, u.IsActive, u.LastLoginDate,
                m.MappedEntraID, m.MappedTenantID, m.EntraIDStatus, m.LastSeen
        `;
        const parameters = [
            { name: 'email', type: mssql_1.default.NVarChar, value: email }
        ];
        try {
            const result = await (0, db_1.executeQuery)(query, parameters);
            if (result.recordset && result.recordset.length > 0) {
                const dbUser = result.recordset[0];
                const user = mapDbUserToPortalUser(dbUser);
                logger_1.logger.info(`Found user with mapping: ${email} (Status: ${dbUser.EntraIDStatus})`);
                return user;
            }
            logger_1.logger.info(`No user found with mapping for email: ${email}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Error getting user with mapping for ${email}:`, error);
            throw error;
        }
    }
    /**
     * Gets user by EntraID and TenantID using the mapping table system
     * This is the preferred method for production authentication
     */
    async getPortalUserByEntraIdAndTenantWithMapping(entraId, tenantId) {
        logger_1.logger.info(`Getting user by EntraID ${entraId} and TenantID ${tenantId} with mapping`);
        // First try direct mapping table lookup
        const mappingQuery = `
            SELECT Email FROM UserEntraIDMappings 
            WHERE EntraID = @entraId AND TenantID = @tenantId AND IsActive = 1
        `;
        const mappingParams = [
            { name: 'entraId', type: mssql_1.default.NVarChar, value: entraId },
            { name: 'tenantId', type: mssql_1.default.NVarChar, value: tenantId }
        ];
        try {
            const mappingResult = await (0, db_1.executeQuery)(mappingQuery, mappingParams);
            if (mappingResult.recordset && mappingResult.recordset.length > 0) {
                const email = mappingResult.recordset[0].Email;
                logger_1.logger.info(`Found mapping for EntraID ${entraId}: ${email}`);
                return await this.getPortalUserWithMapping(email);
            }
            // Fallback to original method if no mapping found
            logger_1.logger.info(`No mapping found for EntraID ${entraId}, falling back to direct lookup`);
            return await this.getPortalUserByEntraIdAndTenant(entraId, tenantId);
        }
        catch (error) {
            logger_1.logger.error(`Error getting user by EntraID and Tenant with mapping:`, error);
            throw error;
        }
    }
    /**
     * Captures and stores EntraID mapping automatically during login
     * This is called every time a user successfully authenticates
     */
    async captureEntraIDOnLogin(email, entraId, tenantId, displayName, companyName) {
        logger_1.logger.info(`Capturing EntraID on login for ${email}`);
        try {
            // 1. Upsert the mapping data
            await this.upsertUserEntraIDMapping(email, entraId, tenantId, displayName, companyName, 'AutoLogin');
            // 2. Update the main Users table if we have a user record
            const existingUser = await this.getPortalUserByEmail(email);
            if (existingUser && existingUser.internalId) {
                // Update Users table with captured EntraID and TenantID
                await this.updateUserEntraIdAndTenant(existingUser.internalId, entraId, tenantId);
                logger_1.logger.info(`Updated Users table for ${email} with captured EntraID`);
            }
            // 3. Return the complete user data with mapping
            return await this.getPortalUserWithMapping(email);
        }
        catch (error) {
            logger_1.logger.error(`Error capturing EntraID on login for ${email}:`, error);
            throw error;
        }
    }
    /**
     * Gets users with incomplete mappings for administrative review
     * Useful for identifying users who need manual attention
     */
    async getUsersWithIncompleteMapping() {
        const query = `
            SELECT 
                u.Email,
                u.FirstName,
                u.LastName,
                c.CompanyName,
                u.EntraID as CurrentEntraID,
                m.EntraID as MappedEntraID,
                m.EntraIDStatus,
                m.LastSeen,
                CASE 
                    WHEN m.EntraID IS NULL THEN 'No Mapping'
                    WHEN u.EntraID LIKE '%placeholder%' OR u.EntraID LIKE '%simulated%' THEN 'Placeholder'
                    WHEN u.EntraID != m.EntraID THEN 'Mismatch'
                    ELSE 'Complete'
                END as MappingStatus
            FROM Users u
            LEFT JOIN UserEntraIDMappings m ON u.Email = m.Email
            LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE u.IsActive = 1
            AND (
                m.EntraID IS NULL 
                OR u.EntraID LIKE '%placeholder%' 
                OR u.EntraID LIKE '%simulated%'
                OR u.EntraID LIKE '00000000-%'
                OR u.EntraID != m.EntraID
            )
            ORDER BY c.CompanyName, u.LastName, u.FirstName
        `;
        try {
            const result = await (0, db_1.executeQuery)(query, []);
            logger_1.logger.info(`Found ${result.recordset.length} users with incomplete mappings`);
            return result.recordset;
        }
        catch (error) {
            logger_1.logger.error('Error getting users with incomplete mapping:', error);
            throw error;
        }
    }
}
exports.UserManagementService = UserManagementService;
exports.userManagementService = new UserManagementService();
//# sourceMappingURL=userManagementService.js.map