"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addUser = void 0;
const functions_1 = require("@azure/functions");
const graphService_1 = require("../shared/services/graphService");
const userManagementService_1 = require("../shared/services/userManagementService");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const validationSchemas_1 = require("../shared/validationSchemas");
// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Administrator'; // Match the actual role name in database
async function addUser(request, context) {
    context.log(`Http function AddUser processed request for url "${request.url}"`);
    logger_1.logger.info('AddUser function invoked.');
    // --- Authentication & Authorization --- 
    const principal = (0, authUtils_1.getClientPrincipal)(request);
    if (!principal) {
        return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
    }
    if (!(0, authUtils_1.hasRequiredRole)(principal, [REQUIRED_ROLE])) {
        logger_1.logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
        return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
    }
    const authenticatedUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
    if (!authenticatedUserId) {
        logger_1.logger.error(`AddUser: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
        return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
    }
    logger_1.logger.info(`AddUser invoked by UserID: ${authenticatedUserId}`);
    // --- End Auth --- 
    // --- Input Validation --- 
    let parsedBody;
    try {
        parsedBody = await request.json();
    }
    catch (error) {
        logger_1.logger.error('AddUser: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { message: "Invalid JSON in request body." } };
    }
    const validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.addUserBodySchema, parsedBody, context, "request body");
    if (validationError)
        return validationError;
    // Validation passed, use validated data
    const validatedBody = validationSchemas_1.addUserBodySchema.parse(parsedBody); // Use parse to get typed data
    const { entraId } = validatedBody;
    // --- End Validation --- 
    try {
        // 1. Get user details from Graph API
        logger_1.logger.info(`AddUser: Fetching user details from Graph for Entra ID: ${entraId}`);
        const graphUser = await graphService_1.graphService.getUserById(entraId);
        if (!graphUser) {
            logger_1.logger.warn(`AddUser: User with Entra ID ${entraId} not found in Graph API.`);
            return {
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: `User with Entra ID ${entraId} not found in directory.` }
            };
        }
        // Map Graph user data to the format expected by findOrCreateUser
        const entraUserData = {
            id: graphUser.id,
            userPrincipalName: graphUser.userPrincipalName,
            mail: graphUser.mail,
            givenName: graphUser.givenName,
            surname: graphUser.surname,
            companyName: graphUser.companyName,
            department: graphUser.department,
        };
        // 2. Find or create the user in the local database
        logger_1.logger.info(`AddUser: Calling findOrCreateUser for Entra ID: ${entraId} by UserID: ${authenticatedUserId}`);
        // Pass the authenticated user ID for the CreatedBy field
        const dbUser = await (0, userManagementService_1.findOrCreateUser)(entraUserData, authenticatedUserId);
        if (!dbUser) {
            logger_1.logger.error(`AddUser: findOrCreateUser failed for Entra ID ${entraId}.`);
            // Throwing error here as failure in findOrCreateUser is likely a DB issue
            throw new Error('Failed to add or retrieve user from local database.');
        }
        logger_1.logger.info(`AddUser: Successfully found/created local user ${dbUser.UserID} for Entra ID ${entraId}`);
        // Returning the DB user object
        return {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: dbUser
        };
    }
    catch (error) {
        logger_1.logger.error(`Error in AddUser function for Entra ID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        const status = (errorMessage.includes('Authentication or permission error') || errorMessage.includes('Company ') || errorMessage.includes('Database error during company lookup'))
            ? 400 // Bad request if company validation fails
            : (errorMessage.includes('User creation failed')) ? 500 : 500; // Default 500
        return {
            status: status,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Error adding user.",
                error: errorMessage
            }
        };
    }
}
exports.addUser = addUser;
functions_1.app.http('AddUser', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'users',
    handler: addUser
});
//# sourceMappingURL=index.js.map