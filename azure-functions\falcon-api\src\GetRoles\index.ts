import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { logger } from "../shared/utils/logger";

// Define the structure expected by the frontend (RoleDefinition)
interface RoleDefinition {
    id: number; // RoleID
    name: string; // RoleName
    description?: string | null; // RoleDescription (explicitly allow null)
}

export async function getRoles(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function GetRoles processed request for url "${request.url}"`);
    logger.info('GetRoles function invoked.');

    // TODO: Add authentication check here if needed based on function.json authLevel

    try {
        const query = `
            SELECT 
                RoleID, 
                RoleName, 
                RoleDescription 
            FROM Roles 
            WHERE IsActive = 1 
            ORDER BY RoleName;
        `;

        const result = await executeQuery(query);

        // Map DB result to frontend RoleDefinition interface
        const roles: RoleDefinition[] = result.recordset.map(dbRole => ({
            id: dbRole.RoleID,
            name: dbRole.RoleName,
            description: dbRole.RoleDescription || null // Ensure null if empty/null from DB
        }));

        logger.info(`Successfully retrieved ${roles.length} active roles.`);

        return {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: roles // Use jsonBody in V4 model for automatic serialization
        };

    } catch (error) {
        logger.error('Error in GetRoles function:', error);
        // Type assertion for error handling
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Error retrieving roles.",
                error: errorMessage
            }
        };
    }
}

// Register the function using the V4 model
app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'anonymous', // Match function.json
    route: 'roles', // Match function.json
    handler: getRoles
});
