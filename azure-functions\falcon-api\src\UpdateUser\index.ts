import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { assignRoleToUser, removeRoleFromUser } from "../shared/services/userManagementService";
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal } from "../shared/authUtils";
import { validateRequest, entraIdRouteParamSchema, updateUserBodySchema } from "../shared/validationSchemas";
import * as sql from 'mssql';
import { QueryParameter } from "../shared/db";
import { userManagementService } from "../shared/services/userManagementService";

// Interface for the expected request body, matching adminApi.ts updatePortalUser
interface UpdateUserRequestBody {
    roles: string[]; // Array of role NAMES
    isActive: boolean;
}

// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Administrator'; // Match the actual role name in database

export async function updateUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function UpdateUser processed request for url "${request.url}"`);
    logger.info('UpdateUser function invoked.');

    // --- Authentication & Authorization --- 
    // Log the environment variable value for debugging
    logger.warn(`Current NODE_ENV: [${process.env.NODE_ENV}]`);

    // Bypass auth check for local development where header is not present
    let authenticatedUserId: number = 1; // Default for development mode
    
    if (process.env.NODE_ENV !== 'development') {
        const principal = getClientPrincipal(request);
        if (!principal) {
            return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
        }
        if (!hasRequiredRole(principal, [REQUIRED_ROLE])) {
             logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
             return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
        }
        const userIdFromPrincipal = await getUserIdFromPrincipal(principal, context);
        if (!userIdFromPrincipal) {
            logger.error(`UpdateUser: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
            return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
        }
        authenticatedUserId = userIdFromPrincipal;
        logger.info(`UpdateUser invoked by UserID: ${authenticatedUserId}`);
    } else {
        logger.warn("UpdateUser: Bypassing authentication check in development mode.");
    }
    // --- End Auth --- 

    // --- Input Validation --- 
    // Validate Route Parameters
    const routeParams = { entraId: request.params.entraId };
    let validationError = validateRequest(entraIdRouteParamSchema, routeParams, context, "route parameters");
    if (validationError) return validationError;
    // Validation passed, use validated data
    const validatedRouteParams = entraIdRouteParamSchema.parse(routeParams);
    const entraId = validatedRouteParams.entraId; // Already a string

    // Validate Request Body
    let parsedBody: any;
    try {
        parsedBody = await request.json();
    } catch (error) {
        logger.error('UpdateUser: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { message: "Invalid JSON in request body." } };
    }

    validationError = validateRequest(updateUserBodySchema, parsedBody, context, "request body");
    if (validationError) return validationError;
    
    // Validation passed, use validated data
    const validatedBody = updateUserBodySchema.parse(parsedBody);
    const { roles: requestedRoleNames, isActive: requestedIsActive } = validatedBody;
    // --- End Validation --- 

    try {
        // Placeholder for the ID of the user performing the update
        const modifiedByUserId = authenticatedUserId;

        // 1. Get UserID and current status/roles for the target user (by EntraID)
        const getUserInfoQuery = `
            SELECT u.UserID, u.IsActive,
                   STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) as CurrentRoleNames
            FROM Users u
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE u.EntraID = @EntraID
            GROUP BY u.UserID, u.IsActive;
        `;
        const userInfoParams: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const userInfoResult = await executeQuery(getUserInfoQuery, userInfoParams);

        if (!userInfoResult.recordset || userInfoResult.recordset.length === 0) {
            logger.warn(`UpdateUser: Target user with EntraID ${entraId} not found.`);
            return {
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: `User with EntraID ${entraId} not found.` }
            };
        }

        const userId = userInfoResult.recordset[0].UserID;
        const currentIsActive = userInfoResult.recordset[0].IsActive;
        const currentRoleNames = (userInfoResult.recordset[0].CurrentRoleNames || '').split(',').filter((role: string) => role.trim() !== '');
        
        logger.info(`Updating UserID: ${userId}. Current Status: ${currentIsActive}, Current Roles: [${currentRoleNames.join(',')}]`);
        logger.info(`Requested Status: ${requestedIsActive}, Requested Roles: [${requestedRoleNames.join(',')}]`);

        // 2. Update User Status (IsActive) if provided
        if (typeof requestedIsActive === 'boolean' && requestedIsActive !== currentIsActive) {
            logger.info(`Updating IsActive status for UserID ${userId} to ${requestedIsActive}`);
            const updateStatusQuery = `UPDATE Users SET IsActive = @IsActive, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
            const statusParams: QueryParameter[] = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'IsActive', type: sql.Bit, value: requestedIsActive },
                { name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId }
            ];
            await executeQuery(updateStatusQuery, statusParams);
        }

        // 3. Handle Role Updates if provided
        if (requestedRoleNames && Array.isArray(requestedRoleNames)) {
            logger.info(`Processing role updates for UserID ${userId}`);
            // Create Sets explicitly typed as Set<string>
            const desiredRoles: Set<string> = new Set(requestedRoleNames.map((role: any) => String(role))); 
            const currentRoles: Set<string> = new Set(currentRoleNames.filter((r: string) => r).map((role: string) => String(role)));

            // Find roles to add and roles to remove (filter should now infer string type)
            const rolesToAdd = [...desiredRoles].filter(role => !currentRoles.has(role));
            const rolesToRemove = [...currentRoles].filter(role => !desiredRoles.has(role));

            // Avoid removing/adding protected roles directly
            const protectedRoles = ['Administrator', 'Employee'];
            // Filter should infer string type here too
            const finalRolesToAdd = rolesToAdd.filter(role => !protectedRoles.includes(role)); 
            const finalRolesToRemove = rolesToRemove.filter(role => !protectedRoles.includes(role));

            if (finalRolesToAdd.length > 0 || finalRolesToRemove.length > 0) {
                const roleNamesToQuery = [...finalRolesToAdd, ...finalRolesToRemove];
                if (roleNamesToQuery.length > 0) {
                    const getRoleIdsQuery = `SELECT RoleName, RoleID FROM Roles WHERE RoleName IN (${roleNamesToQuery.map((_, i) => `@RoleName${i}`).join(', ')})`;
                    const roleIdParams: QueryParameter[] = roleNamesToQuery.map((name, i) => ({
                        name: `RoleName${i}`,
                        type: sql.NVarChar,
                        value: name as string // Ensure value is string
                    }));
                    
                    const roleIdsResult = await executeQuery(getRoleIdsQuery, roleIdParams);
                    const roleIdMap = new Map<string, number>();
                    roleIdsResult.recordset.forEach(row => {
                        roleIdMap.set(row.RoleName as string, row.RoleID as number);
                    });

                    // Process removals
                    for (const roleName of finalRolesToRemove) {
                        const roleId = roleIdMap.get(roleName as string);
                        if (roleId) {
                            logger.debug(`Removing role '${roleName}' (ID: ${roleId}) from UserID ${userId}`);
                            // Try calling the exported function directly
                            await removeRoleFromUser(userId, roleId, modifiedByUserId);
                        } else {
                            logger.warn(`Role name '${roleName}' specified for removal not found in Roles table.`);
                        }
                    }

                    // Process additions
                    for (const roleName of finalRolesToAdd) {
                        const roleId = roleIdMap.get(roleName as string);
                        if (roleId) {
                            logger.debug(`Adding role '${roleName}' (ID: ${roleId}) to UserID ${userId}`);
                            // Try calling the exported function directly
                            await assignRoleToUser(userId, roleId, modifiedByUserId);
                        } else {
                            logger.warn(`Role name '${roleName}' specified for addition not found in Roles table.`);
                        }
                    }
                }
            }
        }

        logger.info(`Successfully updated status and roles for UserID ${userId}.`);
        // Return success, maybe return the updated user object?
        // For now, just returning success message. Frontend can re-fetch if needed.
        return {
            status: 200,
            jsonBody: { message: "User updated successfully." }
        };

    } catch (error) {
        logger.error(`Error in UpdateUser function for EntraID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "An unexpected error occurred while updating the user.",
                error: errorMessage
            }
        };
    }
}

// COMMENTED OUT - Using new UpdatePortalUser function instead
// app.http('UpdateUser', {
//     methods: ['PUT'],
//     authLevel: 'anonymous', // Handled by provider + code checks
//     route: 'portal-users/{entraId}', // Match function.json
//     handler: updateUser
// }); 