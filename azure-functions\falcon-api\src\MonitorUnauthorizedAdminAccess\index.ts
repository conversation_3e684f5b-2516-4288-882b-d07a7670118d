import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { executeQuery, QueryParameter } from '../shared/db';
import { logger } from '../shared/utils/logger';
import sql from 'mssql';

// Define authorized admin users - must match userManagementService.ts
const AUTHORIZED_ADMIN_USERS = [
    '<EMAIL>',
    '<EMAIL>'
];

interface UnauthorizedAdminAlert {
    userEmail: string;
    userName: string;
    assignedDate: string;
    assignedBy: number;
    userRoleId: number;
    companyName: string;
}

/**
 * Security monitoring function to detect unauthorized Administrator role assignments
 * Can be called manually or scheduled to run periodically
 */
export async function MonitorUnauthorizedAdminAccess(
    request: HttpRequest, 
    context: InvocationContext
): Promise<HttpResponseInit> {
    context.log('Starting unauthorized admin access monitoring...');
    
    try {
        // Query to find all users with Administrator role who are not authorized
        const query = `
            SELECT 
                ur.UserRoleID,
                u.Email,
                u.FirstName + ' ' + u.LastName as UserName,
                ur.AssignedDate,
                ur.AssignedBy,
                c.CompanyName,
                u.UserID
            FROM UserRoles ur
            INNER JOIN Users u ON ur.UserID = u.UserID
            INNER JOIN Roles r ON ur.RoleID = r.RoleID
            INNER JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE r.RoleName = 'Administrator'
              AND u.Email NOT IN ('<EMAIL>', '<EMAIL>')
              AND u.IsActive = 1
            ORDER BY ur.AssignedDate DESC
        `;

        const result = await executeQuery(query, []);
        
        if (!result.recordset || result.recordset.length === 0) {
            context.log('✅ No unauthorized Administrator role assignments found');
            return {
                status: 200,
                jsonBody: {
                    status: 'success',
                    message: 'No unauthorized admin access detected',
                    unauthorizedCount: 0,
                    timestamp: new Date().toISOString()
                }
            };
        }

        // Found unauthorized admin assignments - this is a security issue!
        const unauthorizedAdmins: UnauthorizedAdminAlert[] = result.recordset.map(row => ({
            userEmail: row.Email,
            userName: row.UserName,
            assignedDate: row.AssignedDate,
            assignedBy: row.AssignedBy,
            userRoleId: row.UserRoleID,
            companyName: row.CompanyName
        }));

        // Log security alert
        logger.warn(`🚨 SECURITY ALERT: ${unauthorizedAdmins.length} unauthorized Administrator role assignments detected!`);
        
        for (const admin of unauthorizedAdmins) {
            logger.warn(`Unauthorized admin: ${admin.userEmail} (${admin.userName}) at ${admin.companyName}, assigned on ${admin.assignedDate}`);
        }

        // Get assignment details for investigation
        const assignmentDetailsQuery = `
            SELECT 
                ur.UserRoleID,
                u.Email,
                assignedByUser.Email as AssignedByEmail,
                assignedByUser.FirstName + ' ' + assignedByUser.LastName as AssignedByName,
                ur.AssignedDate
            FROM UserRoles ur
            INNER JOIN Users u ON ur.UserID = u.UserID
            INNER JOIN Roles r ON ur.RoleID = r.RoleID
            LEFT JOIN Users assignedByUser ON ur.AssignedBy = assignedByUser.UserID
            WHERE ur.UserRoleID IN (${unauthorizedAdmins.map((_, i) => `@UserRoleID${i}`).join(',')})
        `;

        const assignmentParams: QueryParameter[] = unauthorizedAdmins.map((admin, i) => ({
            name: `UserRoleID${i}`,
            type: sql.Int,
            value: admin.userRoleId
        }));

        const assignmentDetails = await executeQuery(assignmentDetailsQuery, assignmentParams);

        // Auto-remediation: Remove unauthorized admin roles if configured
        const autoRemediate = request.query.get('autoRemediate') === 'true';
        
        if (autoRemediate) {
            logger.info('Auto-remediation enabled. Removing unauthorized Administrator roles...');
            
            const removeUnauthorizedQuery = `
                DELETE ur
                FROM UserRoles ur
                INNER JOIN Users u ON ur.UserID = u.UserID
                INNER JOIN Roles r ON ur.RoleID = r.RoleID
                WHERE r.RoleName = 'Administrator'
                  AND u.Email NOT IN ('<EMAIL>', '<EMAIL>')
            `;
            
            const removeResult = await executeQuery(removeUnauthorizedQuery, []);
            const removedCount = removeResult.rowsAffected?.[0] || 0;
            
            logger.info(`Auto-remediation completed: Removed ${removedCount} unauthorized Administrator role assignments`);
            
            // Ensure affected users have Employee role
            const ensureEmployeeRoleQuery = `
                INSERT INTO UserRoles (UserID, RoleID, AssignedBy, AssignedDate)
                SELECT DISTINCT u.UserID, er.RoleID, 1, GETDATE()
                FROM Users u
                CROSS JOIN Roles er
                WHERE er.RoleName = 'Employee'
                  AND u.IsActive = 1
                  AND u.Email IN (${unauthorizedAdmins.map((_, i) => `@Email${i}`).join(',')})
                  AND NOT EXISTS (
                    SELECT 1 
                    FROM UserRoles ur2 
                    INNER JOIN Roles r2 ON ur2.RoleID = r2.RoleID 
                    WHERE ur2.UserID = u.UserID AND r2.RoleName = 'Employee'
                  )
            `;
            
            const employeeParams: QueryParameter[] = unauthorizedAdmins.map((admin, i) => ({
                name: `Email${i}`,
                type: sql.NVarChar,
                value: admin.userEmail
            }));
            
            const employeeResult = await executeQuery(ensureEmployeeRoleQuery, employeeParams);
            const employeeAdded = employeeResult.rowsAffected?.[0] || 0;
            
            logger.info(`Ensured Employee role for ${employeeAdded} users`);
        }

        return {
            status: 200,
            jsonBody: {
                status: autoRemediate ? 'remediated' : 'alert',
                message: `Found ${unauthorizedAdmins.length} unauthorized Administrator role assignments`,
                unauthorizedCount: unauthorizedAdmins.length,
                unauthorizedAdmins: unauthorizedAdmins,
                assignmentDetails: assignmentDetails.recordset,
                autoRemediated: autoRemediate,
                timestamp: new Date().toISOString(),
                actionRequired: autoRemediate ? 'None - auto-remediated' : 'Manual remediation required'
            }
        };

    } catch (error) {
        logger.error('Error in MonitorUnauthorizedAdminAccess:', error);
        return {
            status: 500,
            jsonBody: {
                status: 'error',
                message: 'Failed to monitor unauthorized admin access',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }
        };
    }
}

// Register the function
app.http('MonitorUnauthorizedAdminAccess', {
    methods: ['GET', 'POST'],
    authLevel: 'function',
    handler: MonitorUnauthorizedAdminAccess
}); 