-- Multi-Tenant Identity Management Database Schema Update
-- Purpose: Add TenantID column to Users table for proper multi-tenant user identification
-- Date: January 2025
-- Author: Falcon Portal Development Team
-- Note: Azure Portal Query Editor compatible version (no USE statement)

-- Step 1: Add TenantID column to Users table
ALTER TABLE [dbo].[Users] 
ADD [TenantID] NVARCHAR(100) NULL;
GO

-- Step 2: Update existing users with a default TenantID (Avirata tenant)
-- This ensures existing users continue to work without issues
UPDATE [dbo].[Users] 
SET [TenantID] = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' 
WHERE [TenantID] IS NULL;
GO

-- Step 3: Make TenantID NOT NULL after setting default values
ALTER TABLE [dbo].[Users] 
ALTER COLUMN [TenantID] NVARCHAR(100) NOT NULL;
GO

-- Step 4: Create composite unique index on EntraID + TenantID
-- This ensures that the same user from different tenants are treated as separate entities
CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_EntraID_TenantID] 
ON [dbo].[Users] ([EntraID], [TenantID]) 
WHERE [EntraID] IS NOT NULL;
GO

-- Step 5: Add index on TenantID for better query performance
CREATE NONCLUSTERED INDEX [IX_Users_TenantID] 
ON [dbo].[Users] ([TenantID]) 
INCLUDE ([UserID], [Email], [CompanyID], [IsActive]);
GO

-- Step 6: Update the EntraID unique constraint to be composite
-- First, drop the existing unique constraint if it exists
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    DROP INDEX [IX_Users_EntraID] ON [dbo].[Users];
END
GO

-- The new composite index IX_Users_EntraID_TenantID serves as the unique constraint

-- Step 7: Add comment to document the multi-tenant approach
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Azure Entra ID Tenant ID for multi-tenant user identification. Forms composite key with EntraID.', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'Users', 
    @level2type = N'Column', @level2name = 'TenantID';
GO

-- Step 8: Verify the schema update
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
    AND COLUMN_NAME IN ('EntraID', 'TenantID', 'Email', 'CompanyID')
ORDER BY COLUMN_NAME;
GO

-- Step 9: Display current indexes on Users table
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    STRING_AGG(c.name, ', ') AS Columns
FROM sys.indexes i
JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.Users')
    AND i.name IS NOT NULL
GROUP BY i.name, i.type_desc, i.is_unique
ORDER BY i.name;
GO

PRINT 'Multi-tenant database schema update completed successfully!';
PRINT 'Next steps:';
PRINT '1. Update GetCurrentUser API to use composite key (EntraID, TenantID)';
PRINT '2. Update userManagementService queries to include TenantID';
PRINT '3. Test with users from different tenants'; 