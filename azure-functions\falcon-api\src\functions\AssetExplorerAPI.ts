import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { getClientPrincipal } from "../shared/authUtils";
import { logger } from "../shared/utils/logger";
import { OAuthTokenService } from "../services/OAuthTokenService";

// AssetExplorer API configuration
interface AssetExplorerConfig {
    baseUrl: string;
    apiVersion: string;
}

// Get AssetExplorer configuration
function getAssetExplorerConfig(): AssetExplorerConfig {
    const baseUrl = process.env.ASSETEXPLORER_BASE_URL || 'https://sdpondemand.manageengine.com';
    const orgId = process.env.ASSETEXPLORER_ORG_ID || '';
    
    return {
        baseUrl: `${baseUrl}/app/${orgId}/api`,
        apiVersion: 'v3'
    };
}

// Get Zoho access token for AssetExplorer API calls
async function getZohoAccessToken(userId: string): Promise<string | null> {
    try {
        // Get tokens from unified Zoho OAuth using database-backed service
        const token = await OAuthTokenService.getActiveToken(userId, 'zoho', 'desk');
        
        if (!token) {
            logger.warn(`AssetExplorerAPI: No Zoho tokens found for user ${userId}`);
            return null;
        }
        
        // Check if token needs refresh (OAuthTokenService handles this)
        const needsRefresh = await OAuthTokenService.needsRefresh(userId, 'zoho', 'desk');
        
        if (needsRefresh) {
            logger.warn(`AssetExplorerAPI: Token expired for user ${userId}, needs refresh`);
            return null;
        }

        return token.accessToken;
        
    } catch (error) {
        logger.error("AssetExplorerAPI: Error getting access token:", error);
        return null;
    }
}

// Universal AssetExplorer API proxy
async function assetExplorerAPIProxy(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`AssetExplorerAPI: Processing ${req.method} request to ${req.url}`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            logger.warn("AssetExplorerAPI: Unauthorized access attempt");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Development mode fallback data
        if (isDevelopment) {
            return await getAssetExplorerFallbackData(req);
        }

        // Get access token
        const accessToken = await getZohoAccessToken(userId);
        if (!accessToken) {
            logger.warn(`AssetExplorerAPI: No valid access token for user ${userId}`);
            return { status: 401, jsonBody: { error: "No valid AssetExplorer authorization. Please authorize first." } };
        }

        // Extract the path from the request URL
        const url = new URL(req.url);
        const pathMatch = url.pathname.match(/\/api\/assetexplorer\/(.+)/);
        if (!pathMatch) {
            return { status: 400, jsonBody: { error: "Invalid AssetExplorer API path" } };
        }

        const apiPath = pathMatch[1];
        const config = getAssetExplorerConfig();
        
        // Construct AssetExplorer API URL
        let assetExplorerUrl = `${config.baseUrl}/${config.apiVersion}/${apiPath}`;
        
        // Add query parameters
        if (url.search) {
            assetExplorerUrl += url.search;
        }

        logger.info(`AssetExplorerAPI: Proxying to ${assetExplorerUrl}`);

        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'Accept': 'application/vnd.manageengine.sdp.v3+json',
            'Content-Type': 'application/x-www-form-urlencoded'
        };

        // Prepare request options
        const requestOptions: RequestInit = {
            method: req.method,
            headers: headers
        };

        // Add body for POST/PUT/PATCH requests
        if (req.method !== 'GET' && req.method !== 'DELETE') {
            if (req.body) {
                // Handle different body types
                if (typeof req.body === 'string') {
                    requestOptions.body = req.body;
                } else {
                    requestOptions.body = JSON.stringify(req.body);
                }
            }
        }

        // Make the API call to AssetExplorer
        const response = await fetch(assetExplorerUrl, requestOptions);
        
        if (!response.ok) {
            const errorText = await response.text();
            logger.error(`AssetExplorerAPI: API call failed: ${response.status} - ${errorText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: `AssetExplorer API error: ${response.statusText}`,
                    details: errorText
                } 
            };
        }

        const responseData = await response.json();
        logger.info(`AssetExplorerAPI: Successfully proxied request to ${apiPath}`);
        
        return {
            status: 200,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("AssetExplorerAPI: Error processing request:", error);
        return { status: 500, jsonBody: { error: "Internal server error" } };
    }
}

// Development mode fallback data
async function getAssetExplorerFallbackData(req: HttpRequest): Promise<HttpResponseInit> {
    const url = new URL(req.url);
    const pathMatch = url.pathname.match(/\/api\/assetexplorer\/(.+)/);
    const apiPath = pathMatch ? pathMatch[1] : '';

    logger.info(`AssetExplorerAPI: Development mode - returning fallback data for ${apiPath}`);

    // Route to appropriate fallback data based on path
    if (apiPath.startsWith('assets')) {
        return getAssetsFallbackData(url.searchParams);
    } else if (apiPath.startsWith('asset-types') || apiPath.startsWith('asset_types')) {
        return getAssetTypesFallbackData();
    } else if (apiPath.startsWith('categories')) {
        return getCategoriesFallbackData();
    } else if (apiPath.includes('departments')) {
        return getDepartmentsFallbackData();
    } else {
        return {
            status: 404,
            jsonBody: {
                error: `AssetExplorer API path not implemented in development mode: ${apiPath}`,
                availablePaths: ['assets', 'asset-types', 'categories', 'departments']
            }
        };
    }
}

// Assets fallback data
function getAssetsFallbackData(searchParams: URLSearchParams): Promise<HttpResponseInit> {
    const assets = [
        {
            id: "100000000000048265",
            name: "Dell OptiPlex 7090",
            asset_tag: "SASMOS-DT-001",
            serial_number: "H7PM4XLG",
            state: {
                id: "100000000000002848",
                name: "In Use",
                internal_name: "In Use"
            },
            product: {
                id: "100000000000048243",
                name: "OptiPlex 7090",
                manufacturer: "Dell",
                category: {
                    id: "100000000000002681",
                    name: "IT"
                }
            },
            product_type: {
                id: "100000000000002705",
                name: "Desktop",
                display_name: "Desktop Computer"
            },
            user: {
                id: "100000000000041183",
                name: "John Doe",
                email_id: "<EMAIL>",
                department: {
                    id: "100000000000007191",
                    name: "Engineering"
                }
            },
            department: {
                id: "100000000000007191",
                name: "Engineering",
                site: {
                    id: "100000000000006936",
                    name: "SASMOS HQ"
                }
            },
            location: "Floor 3, Desk 12",
            purchase_cost: "1200.00",
            current_cost: "800.00",
            acquisition_date: {
                display_value: "Jan 15, 2024",
                value: "1705276800000"
            },
            warranty_expiry: {
                display_value: "Jan 15, 2027",
                value: "1736812800000"
            },
            ip_address: "************",
            mac_address: "00:A0:C9:14:C8:29",
            created_time: {
                display_value: "Jan 15, 2024 10:30 AM",
                value: "1705315800000"
            }
        },
        {
            id: "100000000000048266",
            name: "MacBook Pro 16\"",
            asset_tag: "SASMOS-LT-002",
            serial_number: "C02ZR8JWLVDQ",
            state: {
                id: "100000000000002848",
                name: "In Use",
                internal_name: "In Use"
            },
            product: {
                id: "100000000000048244",
                name: "MacBook Pro",
                manufacturer: "Apple",
                category: {
                    id: "100000000000002681",
                    name: "IT"
                }
            },
            product_type: {
                id: "100000000000002706",
                name: "Laptop",
                display_name: "Laptop Computer"
            },
            user: {
                id: "100000000000041184",
                name: "Sarah Wilson",
                email_id: "<EMAIL>",
                department: {
                    id: "100000000000007192",
                    name: "Design"
                }
            },
            department: {
                id: "100000000000007192",
                name: "Design",
                site: {
                    id: "100000000000006936",
                    name: "SASMOS HQ"
                }
            },
            location: "Floor 2, Creative Zone",
            purchase_cost: "2499.00",
            current_cost: "1800.00",
            acquisition_date: {
                display_value: "Mar 10, 2024",
                value: "1710028800000"
            },
            warranty_expiry: {
                display_value: "Mar 10, 2027",
                value: "1741564800000"
            },
            created_time: {
                display_value: "Mar 10, 2024 09:15 AM",
                value: "1710063300000"
            }
        },
        {
            id: "100000000000048267",
            name: "HP LaserJet Pro",
            asset_tag: "SASMOS-PR-003",
            serial_number: "VND8B2L5K7",
            state: {
                id: "100000000000002848",
                name: "In Use",
                internal_name: "In Use"
            },
            product: {
                id: "100000000000048245",
                name: "LaserJet Pro 4025n",
                manufacturer: "HP",
                category: {
                    id: "100000000000002681",
                    name: "IT"
                }
            },
            product_type: {
                id: "100000000000002707",
                name: "Printer",
                display_name: "Network Printer"
            },
            user: null,
            department: {
                id: "100000000000007191",
                name: "Engineering",
                site: {
                    id: "100000000000006936",
                    name: "SASMOS HQ"
                }
            },
            location: "Floor 3, Print Room",
            purchase_cost: "450.00",
            current_cost: "300.00",
            acquisition_date: {
                display_value: "Feb 20, 2024",
                value: "1708387200000"
            },
            warranty_expiry: {
                display_value: "Feb 20, 2026",
                value: "1740182400000"
            },
            ip_address: "*************",
            mac_address: "B4:A9:FC:1E:8A:45",
            created_time: {
                display_value: "Feb 20, 2024 02:45 PM",
                value: "1708434300000"
            }
        }
    ];

    // Apply basic filtering if search parameters exist
    let filteredAssets = assets;
    const searchValue = searchParams.get('searchvalue');
    if (searchValue) {
        filteredAssets = assets.filter(asset => 
            asset.name.toLowerCase().includes(searchValue.toLowerCase()) ||
            asset.asset_tag.toLowerCase().includes(searchValue.toLowerCase()) ||
            asset.user?.name.toLowerCase().includes(searchValue.toLowerCase())
        );
    }

    return Promise.resolve({
        status: 200,
        jsonBody: {
            response_status: {
                status_code: 2000,
                status: "success"
            },
            assets: filteredAssets,
            list_info: {
                has_more_rows: false,
                row_count: filteredAssets.length
            }
        }
    });
}

// Asset types fallback data
function getAssetTypesFallbackData(): Promise<HttpResponseInit> {
    const assetTypes = [
        {
            id: "100000000000002705",
            name: "Desktop",
            display_name: "Desktop Computer",
            category: {
                id: "100000000000002681",
                name: "IT"
            }
        },
        {
            id: "100000000000002706",
            name: "Laptop",
            display_name: "Laptop Computer",
            category: {
                id: "100000000000002681",
                name: "IT"
            }
        },
        {
            id: "100000000000002707",
            name: "Printer",
            display_name: "Network Printer",
            category: {
                id: "100000000000002681",
                name: "IT"
            }
        },
        {
            id: "100000000000002708",
            name: "Server",
            display_name: "Server Hardware",
            category: {
                id: "100000000000002681",
                name: "IT"
            }
        },
        {
            id: "100000000000002709",
            name: "Monitor",
            display_name: "Computer Monitor",
            category: {
                id: "100000000000002681",
                name: "IT"
            }
        }
    ];

    return Promise.resolve({
        status: 200,
        jsonBody: {
            response_status: {
                status_code: 2000,
                status: "success"
            },
            asset_types: assetTypes
        }
    });
}

// Categories fallback data
function getCategoriesFallbackData(): Promise<HttpResponseInit> {
    const categories = [
        {
            id: "100000000000002681",
            name: "IT",
            display_name: "IT Assets",
            description: "Information Technology Assets and Equipment"
        },
        {
            id: "100000000000002682",
            name: "Facilities",
            display_name: "Facilities",
            description: "Office Furniture and Facilities"
        },
        {
            id: "100000000000002683",
            name: "Vehicles",
            display_name: "Vehicles",
            description: "Company Vehicles and Transportation"
        }
    ];

    return Promise.resolve({
        status: 200,
        jsonBody: {
            response_status: {
                status_code: 2000,
                status: "success"
            },
            categories: categories
        }
    });
}

// Departments fallback data
function getDepartmentsFallbackData(): Promise<HttpResponseInit> {
    const departments = [
        {
            id: "100000000000007191",
            name: "Engineering",
            site: {
                id: "100000000000006936",
                name: "SASMOS HQ"
            }
        },
        {
            id: "100000000000007192",
            name: "Design",
            site: {
                id: "100000000000006936",
                name: "SASMOS HQ"
            }
        },
        {
            id: "100000000000007193",
            name: "Administration",
            site: {
                id: "100000000000006936",
                name: "SASMOS HQ"
            }
        },
        {
            id: "100000000000007194",
            name: "Human Resources",
            site: {
                id: "100000000000006936",
                name: "SASMOS HQ"
            }
        }
    ];

    return Promise.resolve({
        status: 200,
        jsonBody: {
            response_status: {
                status_code: 2000,
                status: "success"
            },
            departments: departments
        }
    });
}

// Register Azure Function as universal API proxy
app.http('assetexplorer-api-proxy', {
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    authLevel: 'anonymous',
    route: 'assetexplorer/{*path}',
    handler: assetExplorerAPIProxy
}); 