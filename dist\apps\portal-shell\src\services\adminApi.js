"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createPortalUser = exports.fetchPortalRoleNames = exports.deleteRoleDefinition = exports.updateRoleDefinition = exports.createRoleDefinition = exports.fetchRoleDefinitions = exports.updatePortalUser = exports.fetchPortalUser = exports.fetchPortalUsers = exports.PORTAL_STATUSES = void 0;
exports.PORTAL_STATUSES = ['Active', 'Inactive'];
// Helper function to make API calls
const callApi = (endpoint_1, ...args_1) => __awaiter(void 0, [endpoint_1, ...args_1], void 0, function* (endpoint, options = {}) {
    const response = yield fetch(`/api${endpoint}`, Object.assign({ headers: Object.assign({ 'Content-Type': 'application/json' }, options.headers) }, options));
    if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }
    return response.json();
});
// Convert backend user data to frontend format
const transformBackendUser = (backendUser) => {
    return {
        id: backendUser.id,
        internalId: backendUser.internalId,
        name: backendUser.name,
        email: backendUser.email,
        company: backendUser.company,
        companyId: backendUser.companyId,
        roles: backendUser.roles || ['Employee'],
        status: backendUser.status === 'Active' ? 'Active' : 'Inactive',
        lastLogin: backendUser.lastLogin,
    };
};
// Convert backend role data to frontend format
const transformBackendRole = (backendRole) => {
    return {
        id: backendRole.RoleID.toString(),
        name: backendRole.RoleName,
        description: backendRole.Description,
    };
};
const fetchPortalUsers = (searchTerm_1, companyFilter_1, roleFilter_1, statusFilter_1, ...args_1) => __awaiter(void 0, [searchTerm_1, companyFilter_1, roleFilter_1, statusFilter_1, ...args_1], void 0, function* (searchTerm, companyFilter, roleFilter, statusFilter, page = 1, pageSize = 10) {
    try {
        // Build query parameters
        const params = new URLSearchParams();
        if (searchTerm)
            params.append('search', searchTerm);
        if (companyFilter && companyFilter !== 'All')
            params.append('company', companyFilter);
        if (roleFilter && roleFilter !== 'All')
            params.append('role', roleFilter);
        if (statusFilter && statusFilter !== 'All')
            params.append('status', statusFilter);
        params.append('page', page.toString());
        params.append('pageSize', pageSize.toString());
        const queryString = params.toString();
        const endpoint = `/portal-users${queryString ? `?${queryString}` : ''}`;
        console.log('Fetching users from:', endpoint);
        const response = yield callApi(endpoint);
        console.log('Backend response:', response);
        // Transform backend data to frontend format
        const transformedUsers = response.users.map(transformBackendUser);
        return {
            users: transformedUsers,
            totalCount: response.totalCount || transformedUsers.length
        };
    }
    catch (error) {
        console.error('Error fetching portal users:', error);
        throw error;
    }
});
exports.fetchPortalUsers = fetchPortalUsers;
// Fetch a specific user (check managed first, then directory for defaults)
const fetchPortalUser = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Fetching user:', userId);
        const response = yield callApi(`/portal-users/${userId}`);
        return transformBackendUser(response);
    }
    catch (error) {
        console.error('Error fetching portal user:', error);
        return null;
    }
});
exports.fetchPortalUser = fetchPortalUser;
// Update roles/status - Creates entry if user isn't already managed
const updatePortalUser = (userId, data) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Updating user:', userId, data);
        // Transform frontend data to backend format
        const updateData = {
            roles: data.roles,
            isActive: data.status === 'Active'
        };
        const response = yield callApi(`/portal-users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
        console.log('Update response:', response);
        // If update was successful, fetch the updated user data
        if (response.message === "User updated successfully.") {
            const updatedUser = yield (0, exports.fetchPortalUser)(userId);
            return updatedUser;
        }
        throw new Error(response.message || 'Update failed');
    }
    catch (error) {
        console.error('Error updating portal user:', error);
        throw error;
    }
});
exports.updatePortalUser = updatePortalUser;
// --- Role Definition Management Functions (Now using real API) ---
const fetchRoleDefinitions = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Fetching roles from database...');
        const response = yield callApi('/roles');
        console.log('Roles response:', response);
        // Transform backend roles to frontend format
        const transformedRoles = response.map(transformBackendRole);
        return transformedRoles;
    }
    catch (error) {
        console.error('Error fetching role definitions:', error);
        throw error;
    }
});
exports.fetchRoleDefinitions = fetchRoleDefinitions;
const createRoleDefinition = (roleData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!roleData || !roleData.name || !roleData.name.trim()) {
            throw new Error("Role name cannot be empty.");
        }
        const response = yield callApi('/roles', {
            method: 'POST',
            body: JSON.stringify({
                roleName: roleData.name,
                description: roleData.description || ''
            })
        });
        return transformBackendRole(response);
    }
    catch (error) {
        console.error('Error creating role definition:', error);
        throw error;
    }
});
exports.createRoleDefinition = createRoleDefinition;
const updateRoleDefinition = (roleId, roleData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (roleData.name && roleData.name.trim() === '') {
            throw new Error("Role name cannot be empty.");
        }
        const updateData = {};
        if (roleData.name)
            updateData.roleName = roleData.name;
        if (roleData.description !== undefined)
            updateData.description = roleData.description;
        const response = yield callApi(`/roles/${roleId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
        return transformBackendRole(response);
    }
    catch (error) {
        console.error('Error updating role definition:', error);
        throw error;
    }
});
exports.updateRoleDefinition = updateRoleDefinition;
const deleteRoleDefinition = (roleId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield callApi(`/roles/${roleId}`, {
            method: 'DELETE'
        });
        return true;
    }
    catch (error) {
        console.error('Error deleting role definition:', error);
        throw error;
    }
});
exports.deleteRoleDefinition = deleteRoleDefinition;
// Function to get available portal role *names* (used by User Edit Page)
const fetchPortalRoleNames = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const roles = yield (0, exports.fetchRoleDefinitions)();
        return roles.map(role => role.name);
    }
    catch (error) {
        console.error('Error fetching portal role names:', error);
        // Fallback to basic roles if API fails
        return ['Administrator', 'Employee', 'Manager', 'Executive', 'IT Admin', 'HR Admin', 'Content Admin'];
    }
});
exports.fetchPortalRoleNames = fetchPortalRoleNames;
// Create a new user
const createPortalUser = (userData) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Creating user:', userData);
        // Transform frontend data to backend format
        const createData = {
            email: userData.email,
            roles: userData.roles,
            status: userData.status
        };
        const response = yield callApi('/portal-users', {
            method: 'POST',
            body: JSON.stringify(createData)
        });
        console.log('Create response:', response);
        // If creation was successful, fetch the created user data
        if (response.message && response.message.includes("successfully")) {
            // The backend might return the user ID or email, we'll try to fetch by email
            const createdUser = yield (0, exports.fetchPortalUser)(userData.email);
            return createdUser;
        }
        throw new Error(response.message || 'User creation failed');
    }
    catch (error) {
        console.error('Error creating portal user:', error);
        // Handle specific error cases for better user experience
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('409')) {
            throw new Error(`A user with the email address "${userData.email}" already exists in the system. Please use a different email address or check if the user is already registered.`);
        }
        else if (errorMessage.includes('400')) {
            throw new Error('Invalid user data provided. Please check the email format and selected roles.');
        }
        else if (errorMessage.includes('500')) {
            throw new Error('Server error occurred while creating the user. Please try again later or contact support.');
        }
        else {
            // For other errors, try to extract meaningful message
            throw new Error(errorMessage || 'Failed to create user. Please try again.');
        }
    }
});
exports.createPortalUser = createPortalUser;
// TODO: Add function updateUserRoles(userId, roles) -> This might be part of updateUser or separate
// TODO: Add function fetchUser(userId)
// TODO: Add function updateUser(userId, userData)
// TODO: Add function createUser(userData)
// TODO: Add function fetchRoles() -> return MOCK_ROLES
// TODO: Add function updateUserRoles(userId, roles) 
//# sourceMappingURL=adminApi.js.map