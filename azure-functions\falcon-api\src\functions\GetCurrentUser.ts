import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { userManagementService } from '../shared/services/userManagementService';
import { logger } from '../shared/utils/logger';
import { getClientPrincipal } from "../shared/authUtils";
import { PortalUser } from '../shared/interfaces';

// Helper function to extract name from email if not available
function extractNameFromEmail(email: string): string {
    if (!email) return 'User';
    
    const localPart = email.split('@')[0];
    // Handle common email patterns like firstname.lastname
    if (localPart.includes('.')) {
        const nameParts = localPart.split('.');
        return nameParts
            .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
            .join(' ');
    }
    
    // For other patterns, just capitalize the first letter
    return localPart.charAt(0).toUpperCase() + localPart.slice(1).toLowerCase();
}

async function getCurrentUser(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info("GetCurrentUser: Function invoked");

    // Check if we're in development mode (no Azure App Service authentication)
    const isDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    logger.info(`GetCurrentUser: Development mode check - WEBSITE_SITE_NAME: ${process.env.WEBSITE_SITE_NAME}, isDevelopment: ${isDevelopment}`);
    
    // Check for Azure Easy Auth first (production), then MSAL token (development/local)
    const principal = getClientPrincipal(req);
    const authHeader = req.headers.get('authorization');
    
    if (!principal && !authHeader) {
        logger.warn("GetCurrentUser: No authentication found - neither Easy Auth nor Bearer token");
        return { status: 401, jsonBody: { error: "Unauthorized - Authentication required" } };
    }

    // If we have MSAL token but no Easy Auth principal (development scenario)
    if (!principal && authHeader?.startsWith('Bearer ')) {
        logger.info("GetCurrentUser: Using MSAL token authentication (development mode)");
        
        try {
            // Decode the JWT token to get user information (basic decode, not validation)
            const token = authHeader.replace('Bearer ', '');
            logger.info(`GetCurrentUser: Processing MSAL JWT token`);
            logger.info(`GetCurrentUser: Raw Authorization header: ${authHeader?.substring(0, 50)}...`);
            logger.info(`GetCurrentUser: Received token of length: ${token.length}`);
            
            // Parse JWT token manually (since MSAL tokens might not validate against Azure's keys)
            const parts = token.split('.');
            if (parts.length !== 3) {
                logger.error(`GetCurrentUser: Invalid JWT format - expected 3 parts, got ${parts.length}`);
                return { status: 401, jsonBody: { error: "Invalid JWT token format - must have 3 parts" } };
            }

            const [header, payload, signature] = parts;
            logger.info(`GetCurrentUser: JWT parts - Header length: ${header.length}, Payload length: ${payload.length}, Signature length: ${signature.length}`);
            
            // Decode and parse header
            let decodedHeader: any;
            try {
                const headerBase64 = header.replace(/-/g, '+').replace(/_/g, '/');
                const headerJson = Buffer.from(headerBase64, 'base64').toString();
                decodedHeader = JSON.parse(headerJson);
                logger.info(`GetCurrentUser: Header decoded - Algorithm: ${decodedHeader.alg}, Type: ${decodedHeader.typ}`);
            } catch (headerError) {
                logger.error(`GetCurrentUser: Error decoding JWT header:`, headerError);
                return { status: 401, jsonBody: { error: "Invalid JWT token header" } };
            }

            // Decode and parse payload
            let decodedPayload: any;
            try {
                const base64Url = payload;
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = Buffer.from(base64, 'base64').toString();
                decodedPayload = JSON.parse(jsonPayload);
                logger.info(`GetCurrentUser: Token payload decoded successfully`);
                logger.info(`GetCurrentUser: Payload keys: [${Object.keys(decodedPayload).join(', ')}]`);
                
                // Log key claims for debugging
                logger.info(`GetCurrentUser: Token issuer (iss): ${decodedPayload.iss}`);
                logger.info(`GetCurrentUser: Token audience (aud): ${decodedPayload.aud}`);
                logger.info(`GetCurrentUser: Token version (ver): ${decodedPayload.ver}`);
                logger.info(`GetCurrentUser: Token type in header: ${decodedHeader.typ}`);
                
            } catch (payloadError) {
                logger.error(`GetCurrentUser: Error decoding JWT payload:`, payloadError);
                return { status: 401, jsonBody: { error: "Invalid JWT token payload" } };
            }
            
            // Extract user information with better error handling
            const userEmail = decodedPayload.preferred_username || decodedPayload.email || decodedPayload.upn;
            const userName = decodedPayload.name;
            const entraId = decodedPayload.oid;
            const tenantId = decodedPayload.tid;
            
            logger.info(`GetCurrentUser: MSAL token decoded successfully`);
            logger.info(`GetCurrentUser: Email from token: ${userEmail}`);
            logger.info(`GetCurrentUser: Name from token: ${userName}`);
            logger.info(`GetCurrentUser: EntraID from token: ${entraId}`);
            logger.info(`GetCurrentUser: TenantID from token: ${tenantId}`);
            
            if (!userEmail || !entraId) {
                logger.warn("GetCurrentUser: Invalid MSAL token - missing user information");
                logger.warn(`GetCurrentUser: Available payload:`, JSON.stringify(decodedPayload, null, 2));
                return { status: 401, jsonBody: { error: "Invalid authentication token - missing user information" } };
            }
            
            // Try to get user from database
            logger.info(`GetCurrentUser: Looking up user in database by email: ${userEmail}`);
            let existingUser: PortalUser | null = null;
            
            try {
                existingUser = await userManagementService.getPortalUserByEmail(userEmail);
                logger.info(`GetCurrentUser: Database lookup completed for ${userEmail} - Found: ${existingUser ? 'YES' : 'NO'}`);
            } catch (dbError) {
                logger.error(`GetCurrentUser: Database error during user lookup:`, dbError);
                return { status: 500, jsonBody: { error: "Database error during user lookup" } };
            }
            
            if (existingUser) {
                logger.info(`GetCurrentUser: ✅ DATABASE LOOKUP SUCCESS - Found user ${userEmail}`);
                logger.info(`GetCurrentUser: User ID: ${existingUser.internalId}`);
                logger.info(`GetCurrentUser: User Name: ${existingUser.name}`);
                logger.info(`GetCurrentUser: User Company: ${existingUser.company}`);
                logger.info(`GetCurrentUser: User Roles: [${existingUser.roles.join(', ')}]`);
                logger.info(`GetCurrentUser: User Status: ${existingUser.status}`);
                return { status: 200, jsonBody: existingUser };
            } else {
                // User not in database - return Employee-only user
                logger.warn(`GetCurrentUser: ❌ DATABASE LOOKUP FAILED - User ${userEmail} not found in database`);
                logger.info(`GetCurrentUser: Returning Employee-only fallback user`);
                const guestUser: PortalUser = {
                    id: entraId,
                    internalId: 0,
                    name: userName || extractNameFromEmail(userEmail),
                    email: userEmail,
                    company: getCompanyFromEmailOrTenant(userEmail, tenantId),
                    companyId: getCompanyIdFromName(getCompanyFromEmailOrTenant(userEmail, tenantId)),
                    roles: ['Employee'], // Default Employee role only
                    status: 'Active',
                    lastLogin: new Date().toISOString()
                };
                logger.info(`GetCurrentUser: Fallback user - Email: ${guestUser.email}, Company: ${guestUser.company}, Roles: [${guestUser.roles.join(', ')}]`);
                return { status: 200, jsonBody: guestUser };
            }
        } catch (error) {
            logger.error("GetCurrentUser: Error processing MSAL token:", error);
            return { status: 500, jsonBody: { error: "Internal server error processing authentication token" } };
        }
    }

    // If we reach here, we must have a principal (Easy Auth)
    if (!principal) {
        logger.error("GetCurrentUser: Unexpected null principal in Easy Auth path");
        return { status: 500, jsonBody: { error: "Internal authentication error" } };
    }

    try {
        // Extract user information from Entra ID claims
        const entraId = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.microsoft.com/identity/claims/objectidentifier' || 
            claim.typ === 'oid'
        )?.val || principal.userId;

        const email = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress' ||
            claim.typ === 'email' ||
            claim.typ === 'preferred_username'
        )?.val || principal.userDetails;

        const name = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name' ||
            claim.typ === 'name'
        )?.val || extractNameFromEmail(email || entraId);

        const tenantId = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.microsoft.com/identity/claims/tenantid' ||
            claim.typ === 'tid'
        )?.val;

        // Validate that we have both EntraID and TenantID for proper multi-tenant identification
        if (!entraId || !tenantId) {
            logger.error(`GetCurrentUser: Missing required identity information - EntraID: ${entraId}, TenantID: ${tenantId}`);
            return { status: 400, jsonBody: { error: "Invalid authentication token - missing required identity information" } };
        }

        logger.info(`GetCurrentUser: Processing user - EntraID: ${entraId}, Email: ${email}, Tenant: ${tenantId}`);

        // Try to get existing user from portal database using composite key (EntraID, TenantID)
        let existingUser: PortalUser | null = await userManagementService.getPortalUserByEntraIdAndTenant(entraId, tenantId);
        
        // If not found by composite key and we have email, try email as fallback (for legacy data)
        if (!existingUser && email) {
            logger.info(`GetCurrentUser: Composite key lookup failed, trying email fallback: ${email}`);
            existingUser = await userManagementService.getPortalUserByEmail(email);
            
            // If found by email but EntraID/TenantID doesn't match, update the database with correct values
            if (existingUser && existingUser.internalId) {
                logger.info(`GetCurrentUser: Found user by email, updating EntraID from ${existingUser.id} to ${entraId} and TenantID to ${tenantId}`);
                await userManagementService.updateUserEntraIdAndTenant(existingUser.internalId, entraId, tenantId);
                // Update the user object with correct values
                existingUser.id = entraId;
                existingUser.tenantId = tenantId;
            }
        }
        
        if (existingUser) {
            // Update user information with latest from Entra ID - roles are already included from the database query
            const updatedUser: PortalUser = {
                ...existingUser,
                name: name || existingUser.name,
                email: email || existingUser.email,
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };

            logger.info(`GetCurrentUser: Found existing user ${existingUser.email} with roles: ${updatedUser.roles.join(', ')}`);
            return { status: 200, jsonBody: updatedUser };
        } else {
            // User not found in portal database - create with default Employee role
            logger.info(`GetCurrentUser: User ${entraId} (${email}) from tenant ${tenantId} not found in portal database, creating with default Employee role`);
            
            // Determine company from email domain or tenant
            const company = getCompanyFromEmailOrTenant(email, tenantId);
            const companyId = getCompanyIdFromName(company);
            
            // Create a basic user record with Employee role
            const defaultUser: PortalUser = {
                id: entraId,
                internalId: 0, // Will be set when user is properly created in database
                name: name,
                email: email || entraId,
                company: company,
                companyId: companyId,
                roles: ['Employee'], // Default role for all authenticated users
                status: 'Active',
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };
            
            logger.info(`GetCurrentUser: Created default user for ${email} in company ${company} from tenant ${tenantId}`);
            return { status: 200, jsonBody: defaultUser };
        }
    } catch (error) {
        logger.error(`GetCurrentUser: Error processing user ${principal?.userId || 'unknown'}:`, error);
        return { status: 500, jsonBody: { error: "Failed to fetch current user information" } };
    }
}

// Helper function to determine company from email domain or tenant ID
function getCompanyFromEmailOrTenant(email: string, tenantId?: string): string {
    if (!email) return 'Unknown Company';
    
    const emailDomain = email.split('@')[1]?.toLowerCase();
    
    // Map email domains to companies
    switch (emailDomain) {
        case 'aviratadefsys.com':
        case 'aviratadefencesystems.onmicrosoft.com':
            return 'Avirata Defence Systems';
        case 'sasmos.com':
        case 'sasmoshettech.onmicrosoft.com':
            return 'SASMOS HET';
        case 'sasmosgroup.com':
            return 'SASMOS Group';
        case 'fe-sil.com':
        case 'fesilsystem.onmicrosoft.com':
            return 'FE-SIL';
        case 'glodesi.com':
        case 'glodesitechnologies.onmicrosoft.com':
            return 'Glodesi';
        case 'hanuka.com':
        case 'hanukatechnology.onmicrosoft.com':
            return 'Hanuka';
        case 'westwireharnessing.co.uk':
            return 'West Wire Harnessing';
        default:
            // Try to map by tenant ID if available
            if (tenantId) {
                switch (tenantId) {
                    case 'ecb4a448-4a99-443b-aaff-063150b6c9ea':
                        return 'Avirata Defence Systems';
                    case '334d188b-2ac3-43a9-8bad-590957b087c2':
                        return 'SASMOS HET';
                    case 'd6a5d909-b6c5-4724-a46d-2641d73acff1':
                        return 'FE-SIL';
                    case '7732add2-c45c-472b-8da8-4e2b4699bbb0':
                        return 'Glodesi';
                    case 'a8dcc1ff-5cc0-4432-827b-9da18737a775':
                        return 'Hanuka';
                    default:
                        console.warn(`Unknown tenant ID: ${tenantId}`);
                        return 'Unknown Company';
                }
            }
            console.warn(`Unknown email domain: ${emailDomain}`);
            return 'Unknown Company';
    }
}

// Helper function to get company ID from company name
function getCompanyIdFromName(companyName: string): number {
    switch (companyName) {
        case 'Avirata Defence Systems':
            return 1;
        case 'SASMOS HET':
            return 2;
        case 'SASMOS Group':
            return 3;
        case 'FE-SIL':
            return 4;
        case 'Glodesi':
            return 5;
        case 'Hanuka':
            return 6;
        case 'West Wire Harnessing':
            return 7;
        default:
            return 1; // Default to Avirata if unknown
    }
}

// Register the Azure Function
app.http('GetCurrentUser', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'current-user',
    handler: getCurrentUser
});