# Multi-Tenant Architecture Summary

## Problem Identified
The database currently has ALL users assigned to the same TenantID (`ecb4a448-4a99-443b-aaff-063150b6c9ea` - Avirata's tenant), despite SASMOS Group operating with **multiple separate Azure Entra ID tenants**.

## Actual Tenant Architecture
SASMOS Group uses **multiple Azure Entra ID tenants** - one for each company:

| Company | Azure Tenant | Tenant ID |
|---------|-------------|-----------|
| Avirata Defence Systems | AVIRATADEFENCESYSTEMS.onmicrosoft.com | `ecb4a448-4a99-443b-aaff-063150b6c9ea` |
| SASMOS HET | sasmoshettech.onmicrosoft.com | `334d188b-2ac3-43a9-8bad-590957b087c2` |
| FE-SIL | fesilsystem.onmicrosoft.com | `d6a5d909-b6c5-4724-a46d-2641d73acff1` |
| Glodesi | GLODESITECHNOLOGIES.onmicrosoft.com | `7732add2-c45c-472b-8da8-4e2b4699bbb0` |
| Hanuka | Hanukatechnology.onmicrosoft.com | `a8dcc1ff-5cc0-4432-827b-9da18737a775` |
| West Wire Harnessing | TBD | **PLACEHOLDER_WWH_TENANT_ID** |

## Database Schema Mapping

### Companies Table (Database)
| CompanyID | CompanyName | CompanyCode | Should Map To Tenant |
|-----------|-------------|-------------|----------------------|
| 1 | Avirata Defence Systems | ADS | `ecb4a448-4a99-443b-aaff-063150b6c9ea` |
| 2 | SASMOS HET | SASMOS | `334d188b-2ac3-43a9-8bad-590957b087c2` |
| 3 | SASMOS Group | SG | `334d188b-2ac3-43a9-8bad-590957b087c2` |
| 4 | FE-SIL | FESIL | `d6a5d909-b6c5-4724-a46d-2641d73acff1` |
| 5 | Glodesi | GLO | `7732add2-c45c-472b-8da8-4e2b4699bbb0` |
| 6 | Hanuka | HAN | `a8dcc1ff-5cc0-4432-827b-9da18737a775` |
| 7 | West Wire Harnessing | WWH | **[PENDING]** |

## Files Updated

### ✅ Database Schema
- **Created**: `Documentation/Database/fix-tenant-id-mapping.sql`
  - Adds TenantID column to Companies table
  - Maps correct tenant IDs to companies
  - Updates existing user records to use correct tenant IDs
  - Provides verification queries

### ✅ Backend Functions
- **Updated**: `azure-functions/falcon-api/src/functions/GetCurrentUser.ts`
  - Fixed tenant ID mappings in `getCompanyFromEmailOrTenant()`
  - Updated domain mappings to use correct onmicrosoft.com domains

### ✅ Frontend Services
- **Updated**: `apps/portal-shell/src/services/userContext.ts`
  - Fixed domain mappings in `getCompanyFromEmailDomain()`
  - Updated to use correct onmicrosoft.com domains

## Current Database State (Before Fix)
- **Problem**: All users have TenantID = `ecb4a448-4a99-443b-aaff-063150b6c9ea` (Avirata's tenant)
- **Impact**: Multi-tenant authentication will fail for non-Avirata users
- **Example**: <EMAIL> has Avirata's tenant ID instead of SASMOS HET's tenant ID

## Next Steps

### 1. Execute Database Fix (CRITICAL)
```sql
-- Run this script in Azure Portal Query Editor
-- File: Documentation/Database/fix-tenant-id-mapping.sql
```

### 2. Get WestWire Harnessing Tenant ID
- **Action Required**: User needs to provide WWH tenant ID
- **Current Status**: Placeholder `PLACEHOLDER_WWH_TENANT_ID` in database
- **Update Command**: 
  ```sql
  UPDATE Companies 
  SET TenantID = '[ACTUAL_WWH_TENANT_ID]' 
  WHERE CompanyName = 'West Wire Harnessing';
  ```

### 3. Test Multi-Tenant Authentication
- **Test Users**: <EMAIL>, <EMAIL>
- **Expected Result**: Each user should authenticate with their company's tenant
- **Verification**: Check TenantID in authentication tokens

### 4. Deploy Updated Code
- **Backend**: Deploy updated `GetCurrentUser.ts` function
- **Frontend**: Deploy updated `userContext.ts` service

## Verification Steps

### After Database Fix
1. **Check Company Mappings**:
   ```sql
   SELECT CompanyName, TenantID FROM Companies ORDER BY CompanyName;
   ```

2. **Check User Mappings**:
   ```sql
   SELECT u.Email, u.TenantID, c.CompanyName, c.TenantID as CompanyTenantID
   FROM Users u
   INNER JOIN Companies c ON u.CompanyID = c.CompanyID;
   ```

3. **Verify No Mismatches**:
   ```sql
   SELECT * FROM Users u
   INNER JOIN Companies c ON u.CompanyID = c.CompanyID
   WHERE u.TenantID != c.TenantID;
   ```

## Authentication Flow Impact

### Before Fix (BROKEN)
- User from any company → Avirata's tenant ID
- Authentication fails for non-Avirata users
- Company filtering doesn't work properly

### After Fix (WORKING)
- Avirata user → Avirata tenant ID
- SASMOS user → SASMOS tenant ID
- FE-SIL user → FE-SIL tenant ID
- Proper multi-tenant isolation

## Security Implications

### Current Risk
- **Data Leakage**: Users might see data from other companies
- **Authentication Bypass**: Incorrect tenant mapping could allow unauthorized access
- **Compliance Issues**: Multi-tenant isolation requirements not met

### After Fix
- **Proper Isolation**: Each company's data stays within their tenant
- **Correct Authentication**: Users authenticate with their company's Azure Entra ID
- **Compliance**: Meets multi-tenant security requirements

## Dependencies

### To Complete Multi-Tenant Setup
1. **Database Fix**: Execute `fix-tenant-id-mapping.sql` ✅ Ready
2. **WWH Tenant ID**: Get actual tenant ID from user ⏳ Pending
3. **Code Deployment**: Deploy updated functions ✅ Ready
4. **Azure Communication Services**: Update with correct tenant mappings ⏳ Pending

### Testing Requirements
1. **Authentication Test**: Users from different companies
2. **Data Isolation Test**: Company-specific data filtering
3. **Email Notifications**: Correct tenant context in emails
4. **Role-Based Access**: Company-specific permissions

## Status
- **Architecture**: ✅ Defined
- **Database Script**: ✅ Created
- **Code Updates**: ✅ Complete
- **Database Execution**: ⏳ Pending user action
- **WWH Tenant ID**: ⏳ Pending user input
- **Testing**: ⏳ Pending database fix 