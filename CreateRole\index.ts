import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../azure-functions/falcon-api/shared/db"; // Import from azure-functions shared folder

// Define interface for request body consistency
interface CreateRoleRequestBody {
    name?: string;
    roleName?: string;
    description?: string;
}

// Define the structure for frontend compatibility  
interface RoleDefinition {
    id: string;
    name: string;
    description?: string;
}

export async function createRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}" to create a role.`);

    let body: CreateRoleRequestBody;
    try {
        // Use request.json() for parsing
        body = await request.json() as CreateRoleRequestBody;
    } catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }

    // Handle both roleName (frontend) and name (generic) fields
    const roleName = body?.roleName?.trim() || body?.name?.trim();
    const description = body?.description?.trim() || null; // Use null for empty description

    if (!roleName) {
        return {
            status: 400,
            jsonBody: { message: "Role name is required and cannot be empty." }
        };
    }

    try {
        // 1. Check if role already exists (case-insensitive)
        const checkQuery = 'SELECT COUNT(*) as Count FROM dbo.Roles WHERE LOWER(RoleName) = LOWER(@RoleName)';
        const checkParams = { RoleName: roleName };
        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset[0].Count > 0) {
            context.log(`Conflict: Role with name '${roleName}' already exists.`);
            return {
                status: 409, // Conflict
                jsonBody: { message: `Role with name '${roleName}' already exists.` }
            };
        }

        // 2. Insert the new role
        // Adapt table/column names and add CreatedBy/CreatedDate if needed based on schema
        const insertQuery = `
            INSERT INTO dbo.Roles (RoleName, RoleDescription, IsActive, CreatedDate) 
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription
            VALUES (@RoleNameParam, @DescriptionParam, 1, GETUTCDATE()) 
        `;
        const insertParams = { 
            RoleNameParam: roleName, 
            DescriptionParam: description,
            // Add CreatedByParam: userId // If tracking user who created it
        };

        const result = await executeQuery(insertQuery, insertParams);

        if (result.recordset && result.recordset.length > 0) {
            const outputRow = result.recordset[0];
            const newRole: RoleDefinition = {
                id: outputRow.RoleID.toString(),
                name: outputRow.RoleName,
                description: outputRow.RoleDescription
            };
            context.log(`Successfully created role '${roleName}' with ID: ${newRole.id}`);
            return {
                status: 201, // Created
                jsonBody: newRole
            };
        } else {
             context.error("Role creation query did not return the expected output.");
             return {
                 status: 500,
                 jsonBody: { message: "Failed to create role due to unexpected database response." }
             };
        }

    } catch (err) {
        context.error(`Error creating role '${roleName}':`, err instanceof Error ? err.message : err);
        // Specific error handling (like unique constraint) is now likely handled within executeQuery or needs adjustment
        return {
            status: 500,
            jsonBody: { 
                message: "Failed to create role due to a server error.",
                error: err instanceof Error ? err.message : "Unknown error" // Provide error details cautiously
            }
        };
    }
}

// Register the function (ensure function name matches export and is unique)
app.http('CreateRoleFunction', { // Use a distinct name like CreateRoleFunction
    methods: ['POST'],
    authLevel: 'function', // Or 'anonymous' / 'admin' depending on requirements
    route: 'roles', // Matches the API path /api/roles
    handler: createRole 
});