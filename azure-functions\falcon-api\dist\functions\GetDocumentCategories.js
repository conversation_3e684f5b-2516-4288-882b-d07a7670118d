"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDocumentCategories = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
async function getDocumentCategories(request, context) {
    context.log(`GetDocumentCategories function invoked.`);
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }
        const query = `
            SELECT 
                dc.CategoryID,
                dc.CategoryName,
                dc.ParentCategoryID,
                COUNT(d.DocumentID) as DocumentCount
            FROM DocumentCategories dc
            LEFT JOIN Documents d ON dc.CategoryID = d.CategoryID 
                AND d.IsActive = 1 
                AND d.IsPublished = 1
            WHERE dc.IsActive = 1
            GROUP BY dc.CategoryID, dc.CategoryName, dc.ParentCategoryID
            ORDER BY dc.CategoryName
        `;
        const result = await (0, db_1.executeQuery)(query, []);
        const categories = result.recordset.map((row) => ({
            id: row.CategoryID.toString(),
            name: row.CategoryName,
            parentId: row.ParentCategoryID?.toString(),
            documentCount: row.DocumentCount || 0
        }));
        logger_1.logger.info(`GetDocumentCategories: Retrieved ${categories.length} categories for user ${userId || 'dev-user'}`);
        return {
            status: 200,
            jsonBody: categories
        };
    }
    catch (error) {
        logger_1.logger.error("GetDocumentCategories: Error retrieving categories:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error retrieving document categories.",
                error: errorMessage
            }
        };
    }
}
exports.getDocumentCategories = getDocumentCategories;
// Register the function
functions_1.app.http('GetDocumentCategories', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'documents/categories',
    handler: getDocumentCategories
});
//# sourceMappingURL=GetDocumentCategories.js.map