"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.simpleTest = void 0;
const functions_1 = require("@azure/functions");
async function simpleTest(request, context) {
    context.log('SimpleTest function executed');
    return {
        status: 200,
        jsonBody: {
            message: "Hello from SimpleTest function!",
            timestamp: new Date().toISOString()
        }
    };
}
exports.simpleTest = simpleTest;
functions_1.app.http('SimpleTest', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'simple-test',
    handler: simpleTest
});
//# sourceMappingURL=SimpleTest.js.map