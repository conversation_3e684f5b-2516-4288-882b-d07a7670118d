import React, { useState, useEffect } from 'react';
import { Announcement, fetchAllAnnouncements } from '../services/dashboardApi';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';

// --- Helper Functions (Similar to Dashboard) ---
// TODO: Move shared helpers to a utility file
const getSeverityBorder = (severity: 'high' | 'medium' | 'low') => {
  switch (severity) {
    case 'high': return 'border-red-500';
    case 'medium': return 'border-yellow-500';
    case 'low': return 'border-gray-400';
    default: return 'border-gray-500';
  }
};

const formatRelativeTime = (isoDate: string) => {
  try {
    return formatDistanceToNow(new Date(isoDate), { addSuffix: true });
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return 'Invalid date';
  }
};

const AnnouncementsPage: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // --- Mock User Context (Replace with actual context later) ---
  const MOCK_USER_COMPANY_ID = 'SASMOS HET'; // Example

  useEffect(() => {
    const loadAnnouncements = async () => {
      setLoading(true);
      setError(null);
      try {
        // Pass mock company ID for filtering
        const data = await fetchAllAnnouncements(MOCK_USER_COMPANY_ID);
        setAnnouncements(data);
      } catch (err) {
        console.error("Failed to load announcements:", err);
        setError("Failed to load announcements.");
      }
      setLoading(false);
    };
    loadAnnouncements();
  }, []); // Fetch once on mount

  const handleDetailsClick = (link: string) => {
      if (link.startsWith('/')) {
          navigate(link);
      } else {
          // Assuming external link
          window.open(link, '_blank', 'noopener,noreferrer');
      }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Announcements</h1>
      
      {loading && <div className="text-center text-gray-500 py-6">Loading announcements...</div>}
      {error && <div className="text-center text-red-600 py-6">Error: {error}</div>}
      
      {!loading && !error && (
        <div className="space-y-4">
          {announcements.length === 0 && (
            <p className="text-gray-600">No announcements found.</p>
          )}
          {announcements.map(ann => (
            <div key={ann.id} className={`bg-white p-4 rounded shadow border-l-4 ${getSeverityBorder(ann.severity)}`}>
              <div className="flex justify-between items-start mb-1">
                <h2 className="text-lg font-semibold">{ann.title}</h2>
                <span className="text-xs text-gray-500 whitespace-nowrap ml-4">{formatRelativeTime(ann.publishedAt)}</span>
              </div>
              <p className="text-sm text-gray-700 mb-2">{ann.description}</p>
              <div className="text-xs text-gray-500 flex items-center justify-between">
                {ann.scope !== 'Group-wide' ? (
                    <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
                        {ann.scope}
                    </span>
                 ) : (
                    <span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                         Group-wide
                    </span>
                 )
                }
                {ann.link && (
                  <button 
                    onClick={() => handleDetailsClick(ann.link!)} 
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Details
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      {/* TODO: Add pagination controls if needed */}
    </div>
  );
};

export default AnnouncementsPage; 