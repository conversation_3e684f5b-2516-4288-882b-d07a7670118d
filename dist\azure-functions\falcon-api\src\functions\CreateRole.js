"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRole = createRole;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = require("mssql");
function createRole(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        context.log(`Http function processed request for url "${request.url}"`);
        let body;
        try {
            body = (yield request.json());
        }
        catch (e) {
            context.error("ERROR parsing request body:", e);
            return {
                status: 400,
                jsonBody: { message: "Invalid JSON in request body." }
            };
        }
        if (!body || !body.name || !body.name.trim()) {
            return {
                status: 400,
                jsonBody: { message: "Role 'name' is required." }
            };
        }
        try {
            const roleName = body.name.trim();
            const description = ((_a = body.description) === null || _a === void 0 ? void 0 : _a.trim()) || null;
            const createdBy = 1; // Placeholder
            // 1. Check if role already exists (case-insensitive)
            const checkQuery = 'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName)';
            // Refactored parameters
            const checkParams = [
                { name: 'RoleName', type: sql.NVarChar, value: roleName }
            ];
            const checkResult = yield (0, db_1.executeQuery)(checkQuery, checkParams);
            if (checkResult.recordset[0].Count > 0) {
                logger_1.logger.warn(`Role creation attempt failed: Role '${roleName}' already exists.`);
                return {
                    status: 409,
                    jsonBody: { message: `Role with name '${roleName}' already exists.` }
                };
            }
            // 3. Insert new role
            const query = `
            INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate, ModifiedDate) 
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription, INSERTED.IsActive 
            VALUES (@RoleNameParam, @DescriptionParam, 0, 1, @CreatedByParam, GETUTCDATE(), GETUTCDATE())
        `;
            // Refactored parameters
            const insertParams = [
                { name: 'RoleNameParam', type: sql.NVarChar, value: roleName },
                { name: 'DescriptionParam', type: sql.NVarChar, value: description },
                { name: 'CreatedByParam', type: sql.Int, value: createdBy }
            ];
            const result = yield (0, db_1.executeQuery)(query, insertParams);
            if (!result.recordset || result.recordset.length === 0) {
                logger_1.logger.error("Role creation failed: No record returned after insert.");
                return {
                    status: 500,
                    jsonBody: {
                        message: "Error creating role.",
                        error: "No record returned after insert."
                    }
                };
            }
            const outputRow = result.recordset[0];
            const newRole = {
                RoleID: outputRow.RoleID,
                RoleName: outputRow.RoleName,
                Description: outputRow.RoleDescription
            };
            return {
                status: 201,
                jsonBody: newRole
            };
        }
        catch (error) {
            context.error(`Error creating role: ${error instanceof Error ? error.message : String(error)}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error creating role.",
                    error: error instanceof Error ? error.message : String(error)
                }
            };
        }
    });
}
functions_1.app.http('CreateRole', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'roles',
    handler: createRole
});
//# sourceMappingURL=CreateRole.js.map