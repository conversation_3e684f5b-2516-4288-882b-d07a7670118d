const sql = require('mssql');

async function checkSchema() {
    const config = {
        server: 'fp-sql-falcon-dev-cin-001.database.windows.net',
        database: 'fp-sqldb-falcon-dev-cin-001',
        user: 'falconhub_admin',
        password: 'Str!k3ab0ltThor',
        options: {
            encrypt: true,
            trustServerCertificate: false
        }
    };

    try {
        const pool = await sql.connect(config);
        console.log('Connected to database successfully.');
        
        // Check Companies table schema
        console.log('\nCompanies table schema:');
        console.log('========================');
        const companiesSchema = await pool.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Companies'
            ORDER BY ORDINAL_POSITION
        `);
        
        companiesSchema.recordset.forEach(column => {
            console.log(`${column.COLUMN_NAME}: ${column.DATA_TYPE} (Nullable: ${column.IS_NULLABLE})`);
        });
        
        // Check Users table schema
        console.log('\nUsers table schema:');
        console.log('===================');
        const usersSchema = await pool.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Users'
            ORDER BY ORDINAL_POSITION
        `);
        
        usersSchema.recordset.forEach(column => {
            console.log(`${column.COLUMN_NAME}: ${column.DATA_TYPE} (Nullable: ${column.IS_NULLABLE})`);
        });
        
        // Check actual company data
        console.log('\nCompanies data:');
        console.log('===============');
        const companiesData = await pool.request().query(`SELECT * FROM Companies`);
        
        companiesData.recordset.forEach(company => {
            console.log(company);
        });
        
        await pool.close();
    } catch (error) {
        console.error('Database error:', error.message);
        console.error('Stack:', error.stack);
    }
}

checkSchema(); 