import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { EmailService, EmailNotificationData } from "../shared/services/emailService";
import * as sql from 'mssql';

export async function updateChangeRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('UpdateChangeRequest function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json() as any;
        const { 
            title, 
            description, 
            requestType, 
            priority, 
            businessJustification,
            risksAndMitigation,
            rollbackPlan,
            affectedSystems,
            requiredResources,
            proposedSchedule,
            userId 
        } = body;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required'
                    }
                }
            };
        }

        if (!title || !description || !requestType || !priority) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Title, description, request type, and priority are required'
                    }
                }
            };
        }

        // First, verify the change request exists and can be updated
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const changeRequest = checkResult.recordset[0];
        
        // Validate that the request can be updated (only if status is "Submitted" after info request)
        if (changeRequest.Status !== 'Submitted') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Cannot update request with status: ${changeRequest.Status}. Only submitted requests waiting for clarification can be updated.`
                    }
                }
            };
        }

        // Update the change request and resubmit it
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Title = @title,
                Description = @description,
                RequestType = @requestType,
                Priority = @priority,
                BusinessJustification = @businessJustification,
                RisksAndMitigation = @risksAndMitigation,
                RollbackPlan = @rollbackPlan,
                AffectedSystems = @affectedSystems,
                RequiredResources = @requiredResources,
                ProposedSchedule = @proposedSchedule,
                Status = 'Under Review',
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'title', type: sql.NVarChar, value: title.trim() },
            { name: 'description', type: sql.NVarChar, value: description.trim() },
            { name: 'requestType', type: sql.NVarChar, value: requestType },
            { name: 'priority', type: sql.NVarChar, value: priority },
            { name: 'businessJustification', type: sql.NVarChar, value: businessJustification?.trim() || null },
            { name: 'risksAndMitigation', type: sql.NVarChar, value: risksAndMitigation?.trim() || null },
            { name: 'rollbackPlan', type: sql.NVarChar, value: rollbackPlan?.trim() || null },
            { name: 'affectedSystems', type: sql.NVarChar, value: affectedSystems?.trim() || null },
            { name: 'requiredResources', type: sql.NVarChar, value: requiredResources?.trim() || null },
            { name: 'proposedSchedule', type: sql.NVarChar, value: proposedSchedule?.trim() || null },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry for the update and resubmission
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, @statusTo, @userId, GETDATE(), @comments
            )
        `;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: 'Submitted' },
            { name: 'statusTo', type: sql.NVarChar, value: 'Under Review' },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: 'Request updated with clarifications and resubmitted for review' }
        ];

        await executeQuery(historyQuery, historyParams);

        // Add a comment indicating the update
        const commentQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
            )
            VALUES (
                @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
            )
        `;

        const commentParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: 'Request has been updated with clarifications and resubmitted for review.' },
            { name: 'commentType', type: sql.NVarChar, value: 'General' },
            { name: 'isInternal', type: sql.Bit, value: false },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];

        await executeQuery(commentQuery, commentParams);

        // Get the updated change request details
        const getUpdatedQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.RequestType as requestType,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.RisksAndMitigation as risksAndMitigation,
                cr.RollbackPlan as rollbackPlan,
                cr.AffectedSystems as affectedSystems,
                cr.RequiredResources as requiredResources,
                cr.ProposedSchedule as proposedSchedule,
                cr.CreatedDate as createdDate,
                cr.ModifiedDate as modifiedDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(u.FirstName, ' ', u.LastName) as requesterName,
                u.Email as requesterEmail,
                CONCAT(m.FirstName, ' ', m.LastName) as modifiedByName,
                CONCAT(assignee.FirstName, ' ', assignee.LastName) as assigneeName,
                assignee.Email as assigneeEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequests cr
                LEFT JOIN Users u ON cr.RequestedBy = u.UserID
                LEFT JOIN Users m ON cr.ModifiedBy = m.UserID
                LEFT JOIN Users assignee ON cr.AssignedToDevID = assignee.UserID
                LEFT JOIN Companies companies ON u.CompanyID = companies.CompanyID
            WHERE cr.RequestID = @requestId
        `;

        const getUpdatedResult = await executeQuery(getUpdatedQuery, checkParams);
        const requestDetails = getUpdatedResult.recordset[0];

        // Send email notification for request update to Change Managers asynchronously
        try {
            const emailData: EmailNotificationData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description,
                priority: requestDetails.priority,
                status: 'Under Review',
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                assigneeName: requestDetails.assigneeName,
                assigneeEmail: requestDetails.assigneeEmail,
                companyName: requestDetails.companyName || 'SASMOS Group',
                comments: 'Request has been updated with clarifications and resubmitted for review.',
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${requestDetails.requestId}`,
                createdDate: requestDetails.createdDate,
                dueDate: requestDetails.dueDate
            };

            EmailService.getInstance().sendChangeRequestStatusUpdated(emailData).catch((error: any) => {
                context.error('Failed to send update notification email:', error);
            });

            context.log(`Email notification queued for updated change request ${requestId}`);
        } catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the update if email fails
        }

        context.log(`Successfully updated change request ${requestId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request updated and resubmitted for review',
                data: requestDetails
            }
        };

    } catch (error: any) {
        context.error('Error in UpdateChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while updating the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('UpdateChangeRequest', {
    methods: ['PUT'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}',
    handler: updateChangeRequest
}); 