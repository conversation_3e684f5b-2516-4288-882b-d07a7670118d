"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const adminApi_1 = require("../../services/adminApi");
const UserAddPage = () => {
    const navigate = (0, react_router_dom_1.useNavigate)();
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [saveError, setSaveError] = (0, react_1.useState)(null);
    // State for form fields
    const [email, setEmail] = (0, react_1.useState)('');
    const [selectedRoles, setSelectedRoles] = (0, react_1.useState)(['Employee']); // Default to Employee role
    const [selectedStatus, setSelectedStatus] = (0, react_1.useState)('Active');
    // --- State for Dynamic Roles ---
    const [availableRoles, setAvailableRoles] = (0, react_1.useState)([]);
    // --- Handler for Role Checkbox Change ---
    const handleRoleChange = (role, isChecked) => {
        setSelectedRoles(prevRoles => {
            if (isChecked) {
                // Add role if not already present
                return prevRoles.includes(role) ? prevRoles : [...prevRoles, role];
            }
            else {
                // Remove role
                return prevRoles.filter(r => r !== role);
            }
        });
    };
    // --- Fetch Available Roles ---
    (0, react_1.useEffect)(() => {
        const fetchRoles = () => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const roles = yield (0, adminApi_1.fetchPortalRoleNames)();
                setAvailableRoles(roles);
            }
            catch (err) {
                console.error("Error fetching roles:", err);
                // Fallback to basic roles if API fails
                setAvailableRoles(['Administrator', 'Employee', 'Manager', 'Executive', 'IT Admin', 'HR Admin', 'Content Admin']);
            }
        });
        fetchRoles();
    }, []);
    // Handle form submission
    const handleSave = () => __awaiter(void 0, void 0, void 0, function* () {
        if (!email || !email.trim()) {
            setSaveError("Email address is required.");
            return;
        }
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.trim())) {
            setSaveError("Please enter a valid email address.");
            return;
        }
        // Ensure at least one role is selected
        if (selectedRoles.length === 0) {
            setSaveError("User must have at least one role.");
            return;
        }
        setIsLoading(true);
        setSaveError(null);
        try {
            const userData = {
                email: email.trim(),
                roles: selectedRoles,
                status: selectedStatus
            };
            console.log('Creating user with:', userData);
            // Call the real API function
            const createdUser = yield (0, adminApi_1.createPortalUser)(userData);
            if (createdUser) {
                // Success! Navigate back to user management
                navigate('/admin/user-management');
            }
            else {
                setSaveError("User creation failed. Please try again.");
            }
        }
        catch (err) {
            console.error("Error creating user:", err);
            const message = err instanceof Error ? err.message : "Failed to create user. Please try again.";
            setSaveError(message);
        }
        finally {
            setIsLoading(false);
        }
    });
    return (<div className="p-6 bg-gray-50 min-h-screen">
            <h1 className="text-2xl font-semibold text-gray-800 mb-6">Add New User</h1>

            <div className="bg-white shadow rounded-lg p-6 max-w-2xl mx-auto">
                {/* Email Input */}
                <div className="mb-4">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span className="text-red-500">*</span>
                    </label>
                    <input type="email" id="email" value={email} onChange={(e) => setEmail(e.target.value)} placeholder="<EMAIL>" className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" required/>
                    <p className="mt-1 text-sm text-gray-500">
                        The user will be looked up in the company directory and their details will be automatically populated.
                    </p>
                </div>

                {/* Assign Roles */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Assign Roles <span className="text-red-500">*</span>
                    </label>
                    <div className="space-y-2 mt-1">
                        {availableRoles.map((role) => (<div key={role} className="flex items-center">
                                <input id={`role-${role}`} name="roles" type="checkbox" value={role} checked={selectedRoles.includes(role)} onChange={(e) => handleRoleChange(role, e.target.checked)} className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"/>
                                <label htmlFor={`role-${role}`} className="ml-2 block text-sm text-gray-900">
                                    {role}
                                </label>
                            </div>))}
                    </div>
                </div>

                {/* Set Status */}
                <div className="mb-6">
                    <label htmlFor="statusSelect" className="block text-sm font-medium text-gray-700 mb-1">
                        Initial Status <span className="text-red-500">*</span>
                    </label>
                     <select id="statusSelect" value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        {adminApi_1.PORTAL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
                    </select>
                </div>
                
                {saveError && <p className="text-sm text-red-600 mb-4">{saveError}</p>}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3">
                    <button type="button" onClick={() => navigate('/admin/user-management')} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </button>
                    <button type="button" onClick={handleSave} disabled={isLoading} className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        {isLoading ? 'Creating...' : 'Create User'}
                    </button>
                </div>
            </div>
        </div>);
};
exports.default = UserAddPage;
//# sourceMappingURL=UserAddPage.js.map