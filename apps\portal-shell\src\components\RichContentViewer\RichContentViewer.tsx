import React from 'react';
import { ContentBlock } from '../../services/changeManagementApi';

// Utility function to convert Azure blob URLs to authenticated proxy URLs
const getAuthenticatedImageUrl = (imageUrl: string): string => {
  if (!imageUrl) return imageUrl;
  
  // If it's a data URL, return as-is (already contains the image data)
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }
  
  // Check if it's an Azure blob storage URL
  if (imageUrl.includes('blob.core.windows.net')) {
    try {
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');
      // Remove the leading slash and container name to get the blob path
      const blobPath = pathParts.slice(2).join('/');
      return `/api/images/${blobPath}`;
    } catch (error) {
      console.warn('Failed to parse image URL:', imageUrl, error);
      return imageUrl;
    }
  }
  
  // Return as-is if not an Azure blob URL
  return imageUrl;
};

interface RichContentViewerProps {
  content: ContentBlock[];
  className?: string;
}

export const RichContentViewer: React.FC<RichContentViewerProps> = ({ 
  content, 
  className = '' 
}) => {
  if (!content || content.length === 0) {
    return null;
  }

  const renderContentBlock = (block: ContentBlock, index: number) => {
    const { contentType, contentData, imageUrl, imageCaption, imageAltText } = block;

    switch (contentType) {
      case 'heading':
        try {
          const headingData = JSON.parse(contentData);
          const level = headingData.level || 1;
          const headingClasses = {
            1: 'text-2xl font-bold text-gray-900 mb-4',
            2: 'text-xl font-semibold text-gray-900 mb-3',
            3: 'text-lg font-medium text-gray-900 mb-2'
          };
          const className = headingClasses[level as keyof typeof headingClasses] || headingClasses[1];
          const text = headingData.text || contentData;
          
          if (level === 1) {
            return <h1 key={index} className={className}>{text}</h1>;
          } else if (level === 2) {
            return <h2 key={index} className={className}>{text}</h2>;
          } else if (level === 3) {
            return <h3 key={index} className={className}>{text}</h3>;
          } else {
            return <h2 key={index} className={className}>{text}</h2>;
          }
        } catch {
          return (
            <h2 key={index} className="text-xl font-semibold text-gray-900 mb-3">
              {contentData}
            </h2>
          );
        }

      case 'text':
      case 'paragraph':
        return (
          <p key={index} className="text-gray-700 mb-4 leading-relaxed">
            {contentData}
          </p>
        );

      case 'image':
        if (!imageUrl) {
          return null;
        }
        return (
          <div key={index} className="mb-6">
            <img 
              src={getAuthenticatedImageUrl(imageUrl)} 
              alt={imageAltText || 'Uploaded image'} 
              className="max-w-full h-auto rounded-lg border border-gray-200 shadow-sm"
            />
            {imageCaption && (
              <p className="text-sm text-gray-500 mt-2 italic text-center">
                {imageCaption}
              </p>
            )}
          </div>
        );

      case 'code':
        try {
          const codeData = JSON.parse(contentData);
          return (
            <div key={index} className="mb-4">
              <pre className="bg-gray-50 border border-gray-200 rounded-lg p-4 overflow-x-auto">
                <code className="text-sm font-mono text-gray-800">
                  {codeData.code || contentData}
                </code>
              </pre>
              {codeData.language && (
                <p className="text-xs text-gray-500 mt-1">
                  Language: {codeData.language}
                </p>
              )}
            </div>
          );
        } catch {
          return (
            <pre key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-4 overflow-x-auto mb-4">
              <code className="text-sm font-mono text-gray-800">
                {contentData}
              </code>
            </pre>
          );
        }

      case 'list':
        try {
          const listData = JSON.parse(contentData);
          if (listData.type === 'bulleted' && Array.isArray(listData.items)) {
            return (
              <ul key={index} className="list-disc list-inside mb-4 space-y-1">
                {listData.items.map((item: string, itemIndex: number) => (
                  <li key={itemIndex} className="text-gray-700">
                    {item}
                  </li>
                ))}
              </ul>
            );
          }
          if (listData.type === 'numbered' && Array.isArray(listData.items)) {
            return (
              <ol key={index} className="list-decimal list-inside mb-4 space-y-1">
                {listData.items.map((item: string, itemIndex: number) => (
                  <li key={itemIndex} className="text-gray-700">
                    {item}
                  </li>
                ))}
              </ol>
            );
          }
          return null;
        } catch {
          // Fallback for simple list format
          return (
            <ul key={index} className="list-disc list-inside mb-4 space-y-1">
              <li className="text-gray-700">{contentData}</li>
            </ul>
          );
        }

      case 'html':
        return (
          <div 
            key={index} 
            className="mb-4"
            dangerouslySetInnerHTML={{ __html: contentData }}
          />
        );

      default:
        // Fallback for unknown content types
        return (
          <div key={index} className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded">
            <p className="text-xs text-gray-500 mb-1">Content Type: {contentType}</p>
            <p className="text-gray-700">{contentData}</p>
          </div>
        );
    }
  };

  return (
    <div className={`rich-content-viewer ${className}`}>
      {content
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map((block, index) => renderContentBlock(block, index))}
    </div>
  );
};

export default RichContentViewer; 