"use client"

import { useState, useEffect, useRef, useCallback } from "react"
// import * as FeatherIcons from 'feather-icons-react'; // Unused import

import { Edit, AlertCircle, FileText, ImageIcon, XCircle, Search, Filter, ArrowLeft } from "lucide-react"

export default function EditAlert() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedAlert, setSelectedAlert] = useState(null)
  const [selectedAlertDetails, setSelectedAlertDetails] = useState(null)
  const [journalData, setJournalData] = useState([])
  const [currentView, setCurrentView] = useState("list") // "list" or "edit"
  const [rejectedAlerts, setRejectedAlerts] = useState([])
  const [employees, setEmployees] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loggedInUser, setLoggedInUser] = useState({
    login_userid: "",
    login_username: "",
    login_company_name: "",
  })
  const [isLoading, setIsLoading] = useState(true)

  const [formData, setFormData] = useState({
    reference: "",
    itemDescription: "",
    itemNumber: "",
    changeDetails: "",
    alertRelatedTo: "PRODUCT",
    okDescription: "",
    notOkDescription: "",
    documentClass: false,
    exportControl: false,
    approver: "",
    okImage: null,
    notOkImage: null,
    existingOkImage: "",
    existingNotOkImage: "",
    fy_year:"",
  })

  const okFileInputRef = useRef(null)
  const notOkFileInputRef = useRef(null)

  const handleInputChange = useCallback((field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value || "",
    }))
  }, [])

  const [hasUpdatePermission, setHasUpdatePermission] = useState(false)
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true)

  useEffect(() => {
    const getUser = () => {
      try {
        const userData = sessionStorage.getItem("userData")
        if (userData) {
          const parsed = JSON.parse(userData)
          setLoggedInUser({
            login_userid: parsed.employee_id,
            login_username: parsed.employee_name,
            login_company_name: parsed.company_name || "Default Company",
          })
        }
      } catch (e) {
        console.error(e)
      }
    }

    const fetchEmployees = async () => {
      try {
        const res = await fetch("http://localhost:3000/api/employees")
        const data = await res.json()
        setEmployees(data)
      } catch (e) {
        console.error("Failed to fetch employees:", e)
      }
    }

    getUser()
    fetchEmployees()
  }, [])

  useEffect(() => {
    const checkPermissions = async () => {
      if (!loggedInUser.login_userid) return

      try {
        const response = await fetch(
          `http://localhost:3000/api/get_current_userrights?emp_id=${loggedInUser.login_userid}`,
        )
        if (response.ok) {
          const data = await response.json()
          const userRights = data.user_rights || []
          const canUpdate = userRights.includes("update") || userRights.includes("MASTER USER")
          setHasUpdatePermission(canUpdate)
        } else {
          setHasUpdatePermission(false)
        }
      } catch (error) {
        console.error("Error checking permissions:", error)
        setHasUpdatePermission(false)
      } finally {
        setIsCheckingPermissions(false)
      }
    }

    checkPermissions()
  }, [loggedInUser.login_userid])

  useEffect(() => {
    const fetchRejectedAlerts = async () => {
      if (!loggedInUser.login_userid) {
        console.log("No user ID available yet")
        return
      }

      try {
        const response = await fetch(`http://localhost:3000/api/edit_alert?employee_id=${loggedInUser.login_userid}`)
        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status}`)
        }
        const data = await response.json()
        setRejectedAlerts(data)
      } catch (error) {
        console.error("Failed to fetch rejected alerts:", error)
        alert(`Error fetching rejected alerts: ${error.message}`)
      } finally {
        setIsLoading(false)
      }
    }

    fetchRejectedAlerts()
  }, [loggedInUser])

  useEffect(() => {
    const fetchJournalData = async () => {
      if (!selectedAlert) {
        setJournalData([])
        return
      }

      try {
        const response = await fetch(`http://localhost:3000/api/alert_journal?alert_no=${selectedAlert}`)
        const responseText = await response.text()
        if (!response.ok) {
          throw new Error(`Failed to fetch journal data: ${response.status} - ${responseText}`)
        }

        let data
        try {
          data = JSON.parse(responseText)
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          throw new Error("Invalid JSON response from server")
        }

        if (data) {
          if (Array.isArray(data)) {
            setJournalData(data)
          } else if (data.va_alert_no) {
            setJournalData([data])
          } else {
            setJournalData([])
          }
        } else {
          setJournalData([])
        }
      } catch (error) {
        console.error("Error fetching journal data:", error)
        setJournalData([])

        try {
          const fallbackResponse = await fetch(`http://localhost:3000/api/alert_details?alert_no=${selectedAlert}`)
          if (fallbackResponse.ok) {
            const fallbackData = await fallbackResponse.json()
            if (fallbackData) {
              setJournalData([
                {
                  va_alert_no: fallbackData.va_alert_no,
                  va_rev: fallbackData.va_rev,
                  create_datetime: fallbackData.create_datetime,
                  va_approver_name: fallbackData.va_approver_name || "Not Assigned",
                },
              ])
            }
          }
        } catch (fallbackError) {
          console.error("Fallback also failed:", fallbackError)
        }
      }
    }

    fetchJournalData()
  }, [selectedAlert])

  if (isCheckingPermissions) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Checking Permissions...</p>
        </div>
      </div>
    )
  }

  if (!hasUpdatePermission) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-3" />
          <p className="text-xl font-semibold text-gray-800">You are not authorized to edit visual alerts.</p>
          <p className="text-gray-600">Required permissions: Update or Master User</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  const filteredAlerts = rejectedAlerts.filter(
    (alert) =>
      alert.va_alert_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.create_username.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSelectAlert = async (alertNo) => {
    try {
      const response = await fetch(`http://localhost:3000/api/alert_details?alert_no=${alertNo}`)
      if (!response.ok) {
        throw new Error("Failed to fetch alert details")
      }
      const alertDetails = await response.json()

      if (!alertDetails) {
        throw new Error("No alert details received")
      }

      setSelectedAlert(alertNo)
      setSelectedAlertDetails(alertDetails)

      setFormData({
        reference: alertDetails.va_reference || "",
        itemDescription: alertDetails.va_item_desc || "",
        itemNumber: alertDetails.va_item_no || "",
        changeDetails: alertDetails.va_rev_change_reason || "",
        alertRelatedTo: alertDetails.va_alert_related_to || "PRODUCT",
        okDescription: alertDetails.va_ok_desc || "",
        notOkDescription: alertDetails.va_not_ok_desc || "",
        documentClass: alertDetails.document_class === "RESTRICTED",
        exportControl: alertDetails.export_control === "YES",
        approver: "",
        okImage: null,
        notOkImage: null,
        existingOkImage: alertDetails.va_ok_img || "",
        existingNotOkImage: alertDetails.va_not_ok_img || "",
        fy_year : alertDetails.fy_year,
      })

      setCurrentView("edit")
    } catch (error) {
      console.error("Error fetching alert details:", error)
      alert(`Failed to load alert details: ${error.message}`)
    }
  }

  const handleBackToList = () => {
    setCurrentView("list")
    setSelectedAlert(null)
    setSelectedAlertDetails(null)
    setJournalData([])
  }

  const handleImageUpload = (type, file) => {
    if (type === "ok") {
      setFormData((prev) => ({ ...prev, okImage: file }))
    } else {
      setFormData((prev) => ({ ...prev, notOkImage: file }))
    }
  }

  const handleDragOver = (e) => {
    e.preventDefault()
  }

  const handleDrop = (e, type) => {
    e.preventDefault()
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleImageUpload(type, files[0])
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!selectedAlert) {
      alert("No alert selected")
      return
    }

    if (!formData.approver) {
      alert("Please select an approver")
      return
    }

    setIsSubmitting(true)

    try {
      const submitFormData = new FormData()
      submitFormData.append("alertNo", selectedAlert)
      submitFormData.append("reference", formData.reference)
      submitFormData.append("itemDescription", formData.itemDescription)
      submitFormData.append("itemNumber", formData.itemNumber)
      submitFormData.append("changeDetails", formData.changeDetails)
      submitFormData.append("alertRelatedTo", formData.alertRelatedTo)
      submitFormData.append("okDescription", formData.okDescription)
      submitFormData.append("notOkDescription", formData.notOkDescription)
      submitFormData.append("documentClass", formData.documentClass ? "RESTRICTED" : "UNCLASSIFIED")
      submitFormData.append("exportControl", formData.exportControl ? "YES" : "NO")
      submitFormData.append("approver", formData.approver)
      submitFormData.append("userId", loggedInUser.login_userid)
      submitFormData.append("username", loggedInUser.login_username)
      submitFormData.append("companyName", loggedInUser.login_company_name)
      submitFormData.append("existingOkImage", formData.existingOkImage)
      submitFormData.append("existingNotOkImage", formData.existingNotOkImage)

      if (formData.okImage) {
        submitFormData.append("okImage", formData.okImage)
      }
      if (formData.notOkImage) {
        submitFormData.append("notOkImage", formData.notOkImage)
      }

      const response = await fetch("http://localhost:3000/api/edited_alert", {
        method: "POST",
        body: submitFormData,
      })

      if (!response.ok) {
        throw new Error("Failed to update alert")
      }

      const result = await response.json()
      alert(`Alert updated successfully! New revision: ${result.newRevision}`)

      // Refresh the alerts list
      const refreshResponse = await fetch(
        `http://localhost:3000/api/edit_alert?employee_id=${loggedInUser.login_userid}`,
      )
      if (refreshResponse.ok) {
        const refreshedData = await refreshResponse.json()
        setRejectedAlerts(refreshedData)
      }

      // Go back to list view
      handleBackToList()
    } catch (error) {
      console.error("Error updating alert:", error)
      alert("Failed to update alert. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusBadge = (status) => {
    if (status === "REJECTED" || status === "Rejected") {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
          <XCircle className="w-3 h-3" />
          Rejected
        </span>
      )
    }
    return (
      <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full border">
        {status}
      </span>
    )
  }

  const formatDate = (dateString) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "2-digit",
      })
      .replace(/ /g, "-")
      .toUpperCase()
  }

  // Edit Form View
  if (currentView === "edit" && selectedAlertDetails) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <div className="flex items-center gap-4">
              <button
                onClick={handleBackToList}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors flex items-center gap-2"
              >
                <ArrowLeft className="w-5 h-5" />
                Back to List
              </button>
              <h1 className="text-3xl font-bold mb-1 pb-3">Visual Alert - Edit</h1>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="max-w-6xl mx-auto space-y-4">
         {/* Document Class & Export Control Section */}
      <div className="flex flex-wrap justify-between mb-6">
        {/* Document Class - Left Side */}
        <div className="flex items-center space-x-4">
          <label className="text-sm font-bold text-gray-700">DOCUMENT CLASS:</label>
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${!formData.documentClass ? "font-semibold text-blue-600" : "text-gray-500"}`}>
              UNCLASSIFIED
            </span>
            <button
              type="button"
              onClick={() => handleInputChange("documentClass", !formData.documentClass)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                formData.documentClass ? "bg-blue-600" : "bg-gray-200"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formData.documentClass ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
            <span className={`text-sm ${formData.documentClass ? "font-semibold text-blue-600" : "text-gray-500"}`}>
              RESTRICTED
            </span>
          </div>
        </div>

        {/* Export Control - Right Side */}
        <div className="flex items-center space-x-4">
          <label className="text-sm font-bold text-gray-700">EXPORT CONTROL:</label>
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${!formData.exportControl ? "font-semibold text-blue-600" : "text-gray-500"}`}>
              NO
            </span>
            <button
              type="button"
              onClick={() => handleInputChange("exportControl", !formData.exportControl)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                formData.exportControl ? "bg-blue-600" : "bg-gray-200"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  formData.exportControl ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
            <span className={`text-sm ${formData.exportControl ? "font-semibold text-blue-600" : "text-gray-500"}`}>
              YES
            </span>
          </div>
        </div>
      </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 my-2">
              <div className="flex items-center">
                <label className="text-sm font-bold">DISTRIBUTION LIST:</label>&nbsp;
                <span className="text-xs text-gray-600">ALL SASMOS GROUP COMPANY EMPLOYEES</span>
              </div>
              <div className="flex items-center justify-end">
                <label className="text-sm font-bold mr-9">ALERT NO:</label>
                <span className="text-xs text-blue-600">SAS-{formData.fy_year}-{selectedAlert}</span>
              </div>
            </div>

           <div className="bg-gray-100 border border-gray-300 p-2 mt-2 text-left">
              <p className="text-xs font-bold">*DISCLAIMER*</p>
              <p className="border-l-2 border-gray-300 pl-1 mt-1 text-[7px] text-gray-600">
                THIS DOCUMENT IS FOR THE SOLE USE OF THE INTENDED RECIPIENT(S) AND MAY CONTAIN CONFIDENTIAL AND PRIVILEGED INFORMATION. IF YOU ARE NOT THE INTENDED RECIPIENT(S), PLEASE RETURN TO THE ISSUER AND DESTROY ALL COPIES OF THE ORIGINAL DOCUMENT. ANY UNAUTHORIZED REVIEW, USE, DISCLOSURE, DISSEMINATION, FORWARDING, PRINTING OR COPYING OF THIS DOCUMENT AND/OR ANY ACTION TAKEN IN RELIANCE ON THE CONTENTS OF THIS DOCUMENT IS STRICTLY PROHIBITED AND MAY BE UNLAWFUL. SASMOS GROUP COMPANIES ACCEPTS NO LIABILITY FOR ANY DAMAGE CAUSED BY USE OF THIS DOCUMENT.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">REFERENCE</label>
                <textarea
                  value={formData.reference}
                  onChange={(e) => handleInputChange("reference", e.target.value)}
                  placeholder="Write reference here like 'CONTACT BEND CONNECTOR'"
                  className="w-full border border-gray-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ITEM DESCRIPTION</label>
                <textarea
                  value={formData.itemDescription}
                  onChange={(e) => handleInputChange("itemDescription", e.target.value)}
                  placeholder="Write item description here like 'CONTACT BEND CONNECTOR'"
                  className="w-full border border-gray-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ITEM NO</label>
                <textarea
                  value={formData.itemNumber}
                  onChange={(e) => handleInputChange("itemNumber", e.target.value)}
                  placeholder="Write item number here like 'GENERAL-D-SUB CONNECTOR'"
                  className="w-full border border-gray-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">CHANGE DETAILS</label>
                <textarea
                  value={formData.changeDetails}
                  onChange={(e) => handleInputChange("changeDetails", e.target.value)}
                  placeholder="Write the change details here"
                  className="w-full border border-gray-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-green-300 rounded-lg overflow-hidden">
                <div className="bg-green-500 text-white text-center py-3 font-semibold">OK</div>
                <div className="p-4">
                  <div
                    className="border-2 border-dashed border-green-300 rounded-lg p-8 text-center cursor-pointer hover:border-green-400 transition-colors min-h-[200px] flex flex-col items-center justify-center"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, "ok")}
                    onClick={(e) => {
                      e.stopPropagation()
                      okFileInputRef.current?.click()
                    }}
                  >
                    {formData.okImage ? (
                      <div className="space-y-2">
                        <ImageIcon className="mx-auto h-12 w-12 text-green-400" />
                        <p className="text-sm text-green-600">Selected: {formData.okImage.name}</p>
                      </div>
                    ) : formData.existingOkImage ? (
                      <img
                        src={`http://localhost:3000/uploads/${formData.existingOkImage}`}
                        alt="OK Preview"
                        className="max-w-full max-h-32 mx-auto rounded"
                      />
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="mx-auto h-12 w-12 text-green-400" />
                        <p className="text-sm text-gray-500">Drag & drop image here or click to browse</p>
                      </div>
                    )}
                  </div>
                  <input
                    ref={okFileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) handleImageUpload("ok", file)
                    }}
                  />
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">REASON FOR ACCEPTANCE</label>
                    <textarea
                      value={formData.okDescription}
                      onChange={(e) => handleInputChange("okDescription", e.target.value)}
                      placeholder="Enter reason for acceptance"
                      className="w-full border border-green-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none resize-none"
                    />
                  </div>
                </div>
              </div>

              <div className="border border-red-300 rounded-lg overflow-hidden">
                <div className="bg-red-500 text-white text-center py-3 font-semibold">NOT OK</div>
                <div className="p-4">
                  <div
                    className="border-2 border-dashed border-red-300 rounded-lg p-8 text-center cursor-pointer hover:border-red-400 transition-colors min-h-[200px] flex flex-col items-center justify-center"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, "notOk")}
                    onClick={(e) => {
                      e.stopPropagation()
                      notOkFileInputRef.current?.click()
                    }}
                  >
                    {formData.notOkImage ? (
                      <div className="space-y-2">
                        <ImageIcon className="mx-auto h-12 w-12 text-red-400" />
                        <p className="text-sm text-red-600">Selected: {formData.notOkImage.name}</p>
                      </div>
                    ) : formData.existingNotOkImage ? (
                      <img
                        src={`http://localhost:3000/uploads/${formData.existingNotOkImage}`}
                        alt="NOT OK Preview"
                        className="max-w-full max-h-32 mx-auto rounded"
                      />
                    ) : (
                      <div className="space-y-2">
                        <ImageIcon className="mx-auto h-12 w-12 text-red-400" />
                        <p className="text-sm text-gray-500">Drag & drop image here or click to browse</p>
                      </div>
                    )}
                  </div>
                  <input
                    ref={notOkFileInputRef}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) handleImageUpload("notOk", file)
                    }}
                  />
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">REASON FOR REJECTION</label>
                    <textarea
                      value={formData.notOkDescription}
                      onChange={(e) => handleInputChange("notOkDescription", e.target.value)}
                      placeholder="Enter reason for rejection"
                      className="w-full border border-red-300 rounded px-4 py-2 text-sm min-h-[120px] focus:ring-2 focus:ring-red-500 focus:border-transparent outline-none resize-none"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">ALERT RELATED TO</label>
                <div className="flex space-x-6">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="alertRelatedTo"
                      value="PRODUCT"
                      checked={formData.alertRelatedTo === "PRODUCT"}
                      onChange={(e) => handleInputChange("alertRelatedTo", e.target.value)}
                      className="text-blue-600"
                    />
                    <span className="text-sm">PRODUCT</span>
                  </label>
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="alertRelatedTo"
                      value="PROCESS"
                      checked={formData.alertRelatedTo === "PROCESS"}
                      onChange={(e) => handleInputChange("alertRelatedTo", e.target.value)}
                      className="text-blue-600"
                    />
                    <span className="text-sm">PROCESS</span>
                  </label>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">REVIEWED AND APPROVED BY</label>
                <input
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  list="employees-list"
                  value={formData.approver}
                  onChange={(e) => handleInputChange("approver", e.target.value)}
                  placeholder="-SELECT-"
                  required
                />
                <datalist id="employees-list">
                  {employees.map((employee, index) => (
                    <option
                      key={`${employee.employee_id}-${index}`}
                      value={`${employee.employee_name} - ${employee.employee_id}`}
                    >
                      {employee.employee_name} - ({employee.employee_id})
                    </option>
                  ))}
                </datalist>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-900">Alert History (Journal)</h3>
              <div className="bg-white border border-gray-300 rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-700">
                        SL.NO
                      </th>
                      <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-700">
                        DATE
                      </th>
                      <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-700">
                        REV
                      </th>
                      <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-700">
                        PREPARED BY
                      </th>
                      <th className="border border-gray-300 px-4 py-3 text-center text-sm font-medium text-gray-700">
                        APPROVER
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {journalData.length > 0 ? (
                      journalData.map((journal, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-4 py-3 text-center text-sm text-blue-600">
                            {index + 1}
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center text-sm">
                            {formatDate(journal.create_datetime)}
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center text-sm font-medium">
                            {journal.va_rev}
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center text-sm">
                            {loggedInUser.login_username}
                          </td>
                          <td className="border border-gray-300 px-4 py-3 text-center text-sm">
                            {journal.va_approver_name || "Not Assigned"}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="border border-gray-300 px-4 py-8 text-center text-sm text-gray-500">
                          No journal data available
                          <br />
                          <small>
                            Alert: {selectedAlert} | API: /api/alert_journal?alert_no={selectedAlert}
                          </small>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-white border-t border-gray-200 pt-6 pb-8 mt-8">
              <div className="flex justify-center gap-4">
                <button
                  onClick={handleBackToList}
                  className="px-8 py-3 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !formData.approver}
                  className={`px-12 py-4 rounded-lg font-semibold text-lg shadow-lg flex items-center gap-3 transition-all transform ${
                    isSubmitting || !formData.approver
                      ? "bg-gray-400 cursor-not-allowed opacity-60"
                      : "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 hover:scale-105 text-white"
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Submitting Alert...
                    </>
                  ) : (
                    <>
                      <Edit className="w-5 h-5" />
                      SUBMIT EDITED ALERT
                    </>
                  )}
                </button>
              </div>

              {!formData.approver && !isSubmitting && (
                <p className="text-center text-sm text-red-600 mt-3">Please select an approver before submitting</p>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // List View (default)
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-br from-red-600 to-pink-600 rounded-xl shadow-lg">
              <Edit className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                Edit Alert
              </h1>
              <p className="text-gray-600 text-lg">Edit rejected visual alerts</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent outline-none transition-all"
              />
            </div>
            <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-xl border-0 overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-red-100 rounded-lg">
                  <AlertCircle className="text-red-600 w-5 h-5" />
                </div>
                <h2 className="text-xl font-semibold">Rejected Alerts</h2>
              </div>
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                {filteredAlerts.length}
              </span>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Alert No
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rev
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prepared By
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Raised Date
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAlerts.length > 0 ? (
                  filteredAlerts.map((alert) => (
                    <tr key={alert.va_alert_no} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">
                          {alert.company_name}-{alert.fy_year}-{alert.va_alert_no}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-left">{alert.va_rev}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-left">{alert.create_username}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-left">
                        {new Date(alert.create_datetime).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-left">{getStatusBadge(alert.va_approved_status)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-leftsss text-sm font-medium">
                        <button
                          onClick={() => handleSelectAlert(alert.va_alert_no)}
                          className="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg shadow-lg transition-all flex items-center gap-2 ml-auto"
                        >
                          <Edit className="w-4 h-4" />
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <FileText className="w-12 h-12 text-gray-300 mb-4" />
                        <p className="text-gray-500 text-lg">No rejected alerts found</p>
                        <p className="text-gray-400 text-sm">All alerts are approved or pending</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
