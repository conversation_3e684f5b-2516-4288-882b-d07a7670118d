# =============================================
# Azure Blob Storage Setup for Change Management
# Region: India Central (lowest cost in India)
# Configuration: Standard LRS (lowest cost tier)
# =============================================

# Variables - Modify these based on your environment
$resourceGroupName = "rg-falcon-dev-cin-001"
$storageAccountName = "stfalconchangemgmt001"  # Must be globally unique, 3-24 chars, lowercase + numbers only
$location = "Central India"
$containerName = "change-request-images"

Write-Host "Setting up Azure Blob Storage for Change Management..." -ForegroundColor Green

try {
    # Check if storage account exists
    $storageAccount = az storage account show --name $storageAccountName --resource-group $resourceGroupName 2>$null
    
    if ($storageAccount) {
        Write-Host "Storage account '$storageAccountName' already exists." -ForegroundColor Yellow
    } else {
        Write-Host "Creating storage account '$storageAccountName'..." -ForegroundColor Blue
        
        # Create storage account with lowest cost configuration
        az storage account create `
            --name $storageAccountName `
            --resource-group $resourceGroupName `
            --location $location `
            --sku Standard_LRS `
            --kind StorageV2 `
            --access-tier Hot `
            --allow-blob-public-access false `
            --min-tls-version TLS1_2 `
            --tags Environment=Development Purpose=ChangeManagement
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Storage account created successfully!" -ForegroundColor Green
        } else {
            throw "Failed to create storage account"
        }
    }
    
    # Get storage account key
    Write-Host "Retrieving storage account key..." -ForegroundColor Blue
    $storageKey = az storage account keys list --resource-group $resourceGroupName --account-name $storageAccountName --query "[0].value" --output tsv
    
    if (-not $storageKey) {
        throw "Failed to retrieve storage account key"
    }
    
    # Check if container exists
    $containerExists = az storage container exists --name $containerName --account-name $storageAccountName --account-key $storageKey --query "exists" --output tsv
    
    if ($containerExists -eq "true") {
        Write-Host "Container '$containerName' already exists." -ForegroundColor Yellow
    } else {
        Write-Host "Creating container '$containerName'..." -ForegroundColor Blue
        
        # Create container with private access (secure by default)
        az storage container create `
            --name $containerName `
            --account-name $storageAccountName `
            --account-key $storageKey `
            --public-access off
            
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Container created successfully!" -ForegroundColor Green
        } else {
            throw "Failed to create container"
        }
    }
    
    # Set up CORS for web access
    Write-Host "Configuring CORS for web access..." -ForegroundColor Blue
    az storage cors add `
        --methods GET POST PUT OPTIONS `
        --origins "http://localhost:5173" "https://*.azurewebsites.net" `
        --allowed-headers "*" `
        --exposed-headers "*" `
        --max-age 200 `
        --services b `
        --account-name $storageAccountName `
        --account-key $storageKey
    
    Write-Host "✅ CORS configured successfully!" -ForegroundColor Green
    
    # Generate SAS token for application use (valid for 1 year)
    Write-Host "Generating SAS token..." -ForegroundColor Blue
    $expiryDate = (Get-Date).AddYears(1).ToString("yyyy-MM-dd")
    
    $sasToken = az storage container generate-sas `
        --name $containerName `
        --account-name $storageAccountName `
        --account-key $storageKey `
        --permissions racwdl `
        --expiry $expiryDate `
        --output tsv
    
    # Get storage account connection string
    $connectionString = az storage account show-connection-string --name $storageAccountName --resource-group $resourceGroupName --query "connectionString" --output tsv
    
    # Output configuration details
    Write-Host "`n🎉 SETUP COMPLETE!" -ForegroundColor Green
    Write-Host "===========================================" -ForegroundColor Cyan
    Write-Host "Configuration Details:" -ForegroundColor White
    Write-Host "===========================================" -ForegroundColor Cyan
    Write-Host "Storage Account Name: $storageAccountName" -ForegroundColor White
    Write-Host "Resource Group: $resourceGroupName" -ForegroundColor White
    Write-Host "Location: $location" -ForegroundColor White
    Write-Host "Container Name: $containerName" -ForegroundColor White
    Write-Host "SKU: Standard_LRS (Lowest Cost)" -ForegroundColor White
    Write-Host "Access Tier: Hot" -ForegroundColor White
    Write-Host "Public Access: Disabled (Secure)" -ForegroundColor White
    Write-Host "===========================================" -ForegroundColor Cyan
    
    Write-Host "`nEnvironment Variables to Add:" -ForegroundColor Yellow
    Write-Host "===========================================" -ForegroundColor Cyan
    Write-Host "AZURE_STORAGE_ACCOUNT_NAME=$storageAccountName"
    Write-Host "AZURE_STORAGE_ACCOUNT_KEY=$storageKey"
    Write-Host "AZURE_STORAGE_CONNECTION_STRING=$connectionString"
    Write-Host "AZURE_STORAGE_CONTAINER_NAME=$containerName"
    Write-Host "AZURE_STORAGE_SAS_TOKEN=$sasToken"
    Write-Host "===========================================" -ForegroundColor Cyan
    
    Write-Host "`n📝 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Add the environment variables above to your Azure Functions local.settings.json"
    Write-Host "2. Update your Azure Functions app settings in production"
    Write-Host "3. Test image upload functionality"
    Write-Host "===========================================" -ForegroundColor Cyan
    
    # Create local.settings.json entries
    $localSettingsEntries = @"

{
  "AZURE_STORAGE_ACCOUNT_NAME": "$storageAccountName",
  "AZURE_STORAGE_ACCOUNT_KEY": "$storageKey", 
  "AZURE_STORAGE_CONNECTION_STRING": "$connectionString",
  "AZURE_STORAGE_CONTAINER_NAME": "$containerName",
  "AZURE_STORAGE_SAS_TOKEN": "$sasToken"
}
"@
    
    # Save to file for easy reference
    $configFile = "azure-infrastructure/change-management-storage-config.json"
    $localSettingsEntries | Out-File -FilePath $configFile -Encoding UTF8
    Write-Host "`n💾 Configuration saved to: $configFile" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please check your Azure CLI login and permissions." -ForegroundColor Red
    exit 1
}

Write-Host "`n🚀 Azure Blob Storage setup completed successfully!" -ForegroundColor Green
Write-Host "Total estimated monthly cost: $5-10 USD for 10GB storage + transactions" -ForegroundColor Green 