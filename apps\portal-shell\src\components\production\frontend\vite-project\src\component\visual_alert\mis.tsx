import { useState, useEffect } from "react"
import { FileText, Clock, CheckCircle, XCircle, AlertCircle, X, Download, History, Info } from "lucide-react"

export default function MISReport() {
  const [alerts, setAlerts] = useState([])
  const [filteredAlerts, setFilteredAlerts] = useState([])
  const [selectedAlert, setSelectedAlert] = useState(null)
  const [alertDetails, setAlertDetails] = useState(null)
  const [alertHistory, setAlertHistory] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("details") // "details" or "history"
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("ALL")
  const [dateFilter, setDateFilter] = useState({
    startDate: "",
    endDate: "",
  })

  useEffect(() => {
    fetchAllAlerts()
  }, [])

  useEffect(() => {
    filterAlerts()
  }, [alerts, searchTerm, statusFilter, dateFilter])

  const fetchAllAlerts = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("http://localhost:3000/api/mis_report")
      if (!response.ok) {
        throw new Error("Failed to fetch alerts")
      }
      const data = await response.json()
      setAlerts(data)
    } catch (error) {
      console.error("Error fetching alerts:", error)
      alert("Failed to fetch alerts data")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchAlertDetails = async (alertNo) => {
    try {
      // Fetch current alert details
      const detailsResponse = await fetch(`http://localhost:3000/api/alert_details?alert_no=${alertNo}`)
      if (!detailsResponse.ok) {
        throw new Error("Failed to fetch alert details")
      }
      const details = await detailsResponse.json()
      setAlertDetails(details)

      // Fetch alert history
      const historyResponse = await fetch(`http://localhost:3000/api/alert_history?alert_no=${alertNo}`)
      if (!historyResponse.ok) {
        throw new Error("Failed to fetch alert history")
      }
      const history = await historyResponse.json()
      setAlertHistory(history)

      setSelectedAlert(alertNo)
      setActiveTab("details")
      setIsHistoryModalOpen(true)
    } catch (error) {
      console.error("Error fetching alert data:", error)
      alert("Failed to fetch alert data")
    }
  }

  const filterAlerts = () => {
    let filtered = [...alerts]

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (alert) =>
          alert.va_alert_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
          alert.create_username.toLowerCase().includes(searchTerm.toLowerCase()) ||
          alert.va_reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
          alert.va_item_desc.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Status filter
    if (statusFilter !== "ALL") {
      filtered = filtered.filter((alert) => alert.va_approved_status === statusFilter)
    }

    // Date filter
    if (dateFilter.startDate && dateFilter.endDate) {
      filtered = filtered.filter((alert) => {
        const alertDate = new Date(alert.create_datetime)
        const startDate = new Date(dateFilter.startDate)
        const endDate = new Date(dateFilter.endDate)
        return alertDate >= startDate && alertDate <= endDate
      })
    }

    setFilteredAlerts(filtered)
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "APPROVED":
      case "Approved":
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
            <CheckCircle className="w-3 h-3" />
            Approved
          </span>
        )
      case "REJECTED":
      case "Rejected":
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
            <XCircle className="w-3 h-3" />
            Rejected
          </span>
        )
      case "PENDING":
      case "Pending":
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
            <Clock className="w-3 h-3" />
            Pending
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full border">
            {status}
          </span>
        )
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return ""
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDateShort = (dateString) => {
    if (!dateString) return ""
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "2-digit",
    })
  }

  const exportToCSV = () => {
    const headers = [
      "Alert No",
      "Rev",
      "Reference",
      "Item Description",
      "Created By",
      "Created Date",
      "Status",
      "Approver",
    ]
    const csvContent = [
      headers.join(","),
      ...filteredAlerts.map((alert) =>
        [
          alert.va_alert_no,
          alert.va_rev,
          `"${alert.va_reference}"`,
          `"${alert.va_item_desc}"`,
          alert.create_username,
          new Date(alert.create_datetime).toLocaleDateString(),
          alert.va_approved_status,
          alert.va_approver_name || "Not Assigned",
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `MIS_Report_${new Date().toISOString().split("T")[0]}.csv`
    link.click()
    window.URL.revokeObjectURL(url)
  }

  const getStatusCounts = () => {
    const counts = { APPROVED: 0, REJECTED: 0, PENDING: 0, TOTAL: filteredAlerts.length }
    filteredAlerts.forEach((alert) => {
      const status = alert.va_approved_status.toUpperCase()
      if (counts[status] !== undefined) {
        counts[status]++
      }
    })
    return counts
  }

  const statusCounts = getStatusCounts()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading MIS Report...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-xl shadow-lg">
              <FileText className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                MIS Report
              </h1>
              <p className="text-gray-600 text-lg">Management Information System - Visual Alerts</p>
            </div>
          </div>
          <button
            onClick={exportToCSV}
            className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Download className="w-4 h-4" />
            Export CSV
          </button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Alerts</p>
                <p className="text-2xl font-bold text-gray-900">{statusCounts.TOTAL}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-bold text-green-600">{statusCounts.APPROVED}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{statusCounts.REJECTED}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-500" />
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{statusCounts.PENDING}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <input
                type="text"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              >
                <option value="ALL">All Status</option>
                <option value="Pending">Pending</option>
                <option value="Approved">Approved</option>
                <option value="Rejected">Rejected</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
              <input
                type="date"
                value={dateFilter.startDate}
                onChange={(e) => setDateFilter((prev) => ({ ...prev, startDate: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
              <input
                type="date"
                value={dateFilter.endDate}
                onChange={(e) => setDateFilter((prev) => ({ ...prev, endDate: e.target.value }))}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
          </div>
        </div>

        {/* Alerts Table */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold">All Visual Alerts</h2>
            <p className="text-gray-600">
              Showing {filteredAlerts.length} of {alerts.length} alerts
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Alert No
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rev
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item Description
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created By
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created Date
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Approver
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAlerts.length > 0 ? (
                  filteredAlerts.map((alert) => (
                    <tr key={alert.va_alert_no} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-left">
                        <button
                          onClick={() => fetchAlertDetails(alert.va_alert_no)}
                          className="text-blue-600 hover:text-blue-800 font-medium underline decoration-2 underline-offset-2 flex items-center gap-2"
                        >
                          {alert.company_name}-{alert.fy_year}-{alert.va_alert_no}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium text-left">{alert.va_rev}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate text-left">{alert.va_reference}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate text-left">{alert.va_item_desc}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900  text-left">{alert.create_username}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                        {formatDate(alert.create_datetime)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-left">{getStatusBadge(alert.va_approved_status)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                        {alert.va_approver_name || "Not Assigned"}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <FileText className="w-12 h-12 text-gray-300 mb-4" />
                        <p className="text-gray-500 text-lg">No alerts found</p>
                        <p className="text-gray-400 text-sm">Try adjusting your filters</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Alert Details Modal */}
      {isHistoryModalOpen && alertDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl max-h-[95vh] overflow-hidden flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Visual Alert</h2>
                  <p className="text-sm text-gray-600">Alert No: {selectedAlert}</p>
                </div>
              </div>
              <button
                onClick={() => setIsHistoryModalOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors group"
              >
                <X className="w-6 h-6 text-gray-500 group-hover:text-gray-700" />
              </button>
            </div>

            {/* Tab Navigation */}
            <div className="flex border-b border-gray-200 bg-gray-50">
              <button
                onClick={() => setActiveTab("details")}
                className={`flex items-center gap-2 px-6 py-3 font-medium text-sm transition-colors ${
                  activeTab === "details"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-white"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                <Info className="w-4 h-4" />
                Alert Details
              </button>
              <button
                onClick={() => setActiveTab("history")}
                className={`flex items-center gap-2 px-6 py-3 font-medium text-sm transition-colors ${
                  activeTab === "history"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-white"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                <History className="w-4 h-4" />
                History
              </button>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === "details" ? (
                /* Alert Details Tab */
                <div className="p-6 space-y-6">
                  {/* Header Info */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 bg-gray-50 p-4 rounded-lg">
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase">Alert No</label>
                      <p className="text-sm font-semibold text-blue-600">{alertDetails.va_alert_no}</p>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase">Release Date</label>
                      <p className="text-sm font-semibold text-gray-900">
                        {formatDateShort(alertDetails.va_release_date)}
                      </p>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase">Alert Related To</label>
                      <p className="text-sm font-semibold text-gray-900">{alertDetails.va_alert_related_to}</p>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase">Status</label>
                      <div className="mt-1">{getStatusBadge(alertDetails.va_approved_status)}</div>
                    </div>
                  </div>

                  {/* Reference and Item Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">REFERENCE</label>
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <p className="text-sm text-gray-900">{alertDetails.va_reference}</p>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">ITEM NO</label>
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <p className="text-sm text-gray-900">{alertDetails.va_item_no}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">ITEM DESCRIPTION</label>
                    <div className="bg-gray-50 p-4 rounded-lg border">
                      <p className="text-sm text-gray-900">{alertDetails.va_item_desc}</p>
                    </div>
                  </div>

                  {/* OK/NOT OK Sections */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* OK Section */}
                    <div className="border border-green-200 rounded-xl overflow-hidden">
                      <div className="bg-green-500 text-white p-4 text-center font-semibold text-lg">OK</div>
                      <div className="p-6 bg-green-50">
                        {alertDetails.va_ok_img && (
                          <div className="mb-4">
                            <img
                              src={`http://localhost:3000/uploads/${alertDetails.va_ok_img}`}
                              alt="OK Image"
                              className="w-full h-64 object-cover rounded-lg border-2 border-green-200"
                            />
                          </div>
                        )}
                        <div>
                            <div className="bg-white p-4 rounded-lg border border-green-200">
                            <p className="text-sm text-gray-900">{alertDetails.va_ok_desc}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* NOT OK Section */}
                    <div className="border border-red-200 rounded-xl overflow-hidden">
                      <div className="bg-red-500 text-white p-4 text-center font-semibold text-lg">NOT OK</div>
                      <div className="p-6 bg-red-50">
                        {alertDetails.va_not_ok_img && (
                          <div className="mb-4">
                            <img
                              src={`http://localhost:3000/uploads/${alertDetails.va_not_ok_img}`}
                              alt="NOT OK Image"
                              className="w-full h-64 object-cover rounded-lg border-2 border-red-200"
                            />
                          </div>
                        )}
                        <div>
                          <div className="bg-white p-4 rounded-lg border border-red-200">
                            <p className="text-sm text-gray-900">{alertDetails.va_not_ok_desc}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Footer Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6 border-t border-gray-200">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">PREPARED BY</label>
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <p className="text-sm text-gray-900">{alertDetails.create_username}</p>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">REVIEWED AND APPROVED BY</label>
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <p className="text-sm text-gray-900">{alertDetails.va_approver_name || "Not Assigned"}</p>
                      </div>
                    </div>
                  </div>

                  {alertDetails.remarks_by_approver && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">REMARKS</label>
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <p className="text-sm text-gray-900">{alertDetails.remarks_by_approver}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* History Tab */
                <div className="p-6">
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                            SL NO
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                            DATE
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                            REV
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                            CHANGE DETAILS
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                            PREPARED BY
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            REVIEWED AND APPROVED BY
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {alertHistory.length > 0 ? (
                          alertHistory.map((history, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200 text-center">
                                {index + 1}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                                {formatDateShort(history.create_datetime)}
                              </td>
                              <td className="px-4 py-3 text-sm font-medium text-gray-900 border-r border-gray-200 text-center">
                                {history.va_rev}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                                {history.va_rev_change_reason || "INITIAL RELEASE"}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200">
                                {history.create_username}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {history.va_approver_name || "Not Assigned"}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={6} className="px-4 py-8 text-center text-sm text-gray-500">
                              No history available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">
                  {activeTab === "details" ? "Current alert details" : `${alertHistory.length} revision(s) found`}
                </p>
                <button
                  onClick={() => setIsHistoryModalOpen(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
