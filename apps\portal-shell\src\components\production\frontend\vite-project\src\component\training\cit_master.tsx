import {useState,useEffect} from 'react';
import { Plus,Minus } from 'lucide-react';

function Level1TrainingTracker() {
 const [citnamechange,setcitname] = useState('');


const handleIconClick = () =>
{
  if(citnamechange)
  {
    const formData = new FormData();
    formData.append('cit_name',citnamechange);
   fetch('http://localhost:3000/api/cit_master', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cit_name: citnamechange }),
      })
      .then(response => response.json())
      .then(() => {
        alert('Successfully uploaded!');
        window.location.reload();
        setcitname('');
      });
  }
  else
  {
    alert("PLEASE FILL CIT NAME");
  }
}




const [exisiting_cm,setexistingcm] =useState([]);
useEffect(() => {
    fetch('http://localhost:3000/api/get_exisiting_cm')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          setexistingcm(data.data);
        } else {
          console.error("Failed to fetch CIT list");
        }
      })
      .catch(error => {
        console.error("Error fetching data:", error);
      });
  }, []);



const handleDelete = (id) => {
  if (window.confirm("Are you sure you want to delete this item?")) {
    fetch(`http://localhost:3000/api/delete_cit/${id}`, {
      method: 'DELETE'
    })
    .then(res => res.json())
    .then(data => {
      if (data.success) {
        alert("Deleted successfully");
        setexistingcm(prev => prev.filter(item => item.id !== id));
        window.location.reload()

      } else {
        alert("Deletion failed");
      }
    })
    .catch(err => {
      console.error("Error deleting:", err);
      alert("Server error during deletion");
    });
  }
};
  return (
    <div className="min-h-screen">
      <div className="max-w-xl mx-auto bg-white rounded-2xl shadow-md p-6">
        <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">CIT Name Master</h1>

        <div className="flex items-center justify-between space-x-4">
          <div className="flex-1">
            <input
              type="text"
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter CIT Name" value={citnamechange} onChange={(e)=>setcitname(e.target.value)}
            />
          </div>

          <button className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-3 transition duration-200"  onClick={handleIconClick}>
            <Plus className="w-5 h-5" />
          </button>
        </div>
      </div>
      <div>
       <table className="min-w-full bg-white shadow rounded-lg mt-6">
          <thead>
            <tr className="bg-gray-200 text-gray-700 text-left">
              <th className="py-2 px-4">#</th>
              <th className="py-2 px-4">CIT NAME</th>
              <th className="py-2 px-4">REMOVE</th>
            </tr>
          </thead>
          <tbody>
            {exisiting_cm.map((item, index) => (
              <tr key={index} className="border-b hover:bg-gray-50">
                <td className="py-2 px-4">{index + 1}</td>
                <td className="py-2 px-4">{item.cit_name}</td>
                <td className="py-2 px-4">
                  <button
                    className="bg-red-600 hover:bg-red-700 text-white rounded-lg p-2 transition duration-200"
                     onClick={() => handleDelete(item.id)} // optional delete handler
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default Level1TrainingTracker;
/*

{nonCalibratedMfgDetails.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50 transition">
                <td className="px-4 py-2">{index + 1}</td>
                <td className="px-4 py-2">{item.tool_category}</td>
                <td className="px-4 py-2">{item.manufacture_no}</td>
                <td className="px-4 py-2">{item.subtool_no}</td>
                <td className="px-4 py-2">{item.bu_name}</td>
                <td className="px-4 py-2">{item.ipt_name}</td>
                <td className="px-4 py-2">{item.create_type}</td>
                <td className="px-4 py-2">{item.quantity}</td>
                <td className="px-4 py-2">{item.user_name}</td>
                <td className="px-4 py-2">{item.create_date}</td>
              </tr>
            ))}*/