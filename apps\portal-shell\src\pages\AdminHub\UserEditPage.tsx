import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { 
    fetchPortalUser, 
    updatePortalUser, 
    PortalUser, 
    fetchPortalRoleNames,
    PORTAL_STATUSES,
    setMsalContext
} from '../../services/adminApi';

const UserEditPage: React.FC = () => {
    const { userId } = useParams<{ userId: string }>();
    const navigate = useNavigate();
    const { instance, accounts } = useMsal();
    const [user, setUser] = useState<PortalUser | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState(false);
    const [saveError, setSaveError] = useState<string | null>(null);

    // State for editable fields
    const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
    const [selectedStatus, setSelectedStatus] = useState<PortalUser['status'] | '' >('');

    // --- State for Dynamic Roles ---
    const [availableRoles, setAvailableRoles] = useState<string[]>([]);

    // Set MSAL context when component mounts
    useEffect(() => {
        setMsalContext(instance, accounts);
    }, [instance, accounts]);

    // --- Handler for Role Checkbox Change ---
    const handleRoleChange = (role: string, isChecked: boolean) => {
        setSelectedRoles(prevRoles => {
            if (isChecked) {
                // Add role if not already present
                return prevRoles.includes(role) ? prevRoles : [...prevRoles, role];
            } else {
                // Remove role
                return prevRoles.filter(r => r !== role);
            }
        });
    };

    // --- Fetch Available Roles ---
    useEffect(() => {
        const fetchRoles = async () => {
            try {
                const roles = await fetchPortalRoleNames();
                setAvailableRoles(roles);
                console.log('✅ Loaded roles from database:', roles);
            } catch (err) {
                console.error("Error fetching roles:", err);
                setError("Failed to load available roles from database. Please refresh the page.");
            }
        };
        fetchRoles();
    }, []);

    // Fetch user data on mount
    useEffect(() => {
        const loadUser = async () => {
            if (!userId) {
                setError("No user ID provided.");
                setIsLoading(false);
                return;
            }
            setIsLoading(true);
            setError(null);
            try {
                const fetchedUser = await fetchPortalUser(userId);
                if (fetchedUser) {
                    setUser(fetchedUser);
                    setSelectedRoles(fetchedUser.roles || []);
                    setSelectedStatus(fetchedUser.status || '');
                } else {
                    setError("User not found.");
                }
            } catch (err) {
                console.error("Error fetching user:", err);
                setError("Failed to load user data. Please try again.");
            } finally {
                setIsLoading(false);
            }
        };
        loadUser();
    }, [userId]);

    // TODO: Implement handleSave function
    const handleSave = async () => {
        if (!userId || !user) {
            setSaveError("User data is not available.");
            return;
        }
        if (!selectedStatus) {
            setSaveError("Please select a status.");
            return;
        }
        // Ensure at least one role is selected? Or default to 'User'? For now, allow empty.
        // if (selectedRoles.length === 0) {
        //     setSaveError("User must have at least one role.");
        //     return;
        // }

        setIsSaving(true);
        setSaveError(null);
        try {
            const updatedData = { 
                roles: selectedRoles, 
                status: selectedStatus 
            };
            await updatePortalUser(userId, updatedData);
            // Success! Navigate back to the list
            // Optionally show a success message first (e.g., using react-toastify)
            navigate('/admin/user-management'); 
        } catch (err) {
            console.error("Error updating user:", err);
            const message = err instanceof Error ? err.message : "Failed to save changes. Please try again.";
            setSaveError(message);
        } finally {
            setIsSaving(false);
        }
    };

    // --- Render Logic ---
    if (isLoading) {
        return <div className="p-6">Loading user data...</div>;
    }

    if (error) {
        return <div className="p-6 text-red-600">Error: {error}</div>;
    }

    if (!user) {
        // Should be covered by error state, but good practice
        return <div className="p-6">User not found.</div>;
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <h1 className="text-2xl font-semibold text-gray-800 mb-6">Manage User: {user.name}</h1>

            <div className="bg-white shadow rounded-lg p-6 max-w-2xl mx-auto">
                {/* Display basic info (read-only) */}
                <div className="mb-4 border-b pb-4">
                    <p><span className="font-medium">Email:</span> {user.email}</p>
                    <p><span className="font-medium">Company:</span> {user.company}</p>
                    <p><span className="font-medium">User ID:</span> {user.id}</p>
                </div>

                {/* Edit Roles */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Roles:</label>
                    {/* TODO: Use checkboxes or a multi-select component */}
                    <div className="space-y-2 mt-1">
                        {availableRoles.map((role) => (
                             <div key={role} className="flex items-center">
                                <input
                                    id={`role-${role}`}
                                    name="roles"
                                    type="checkbox"
                                    value={role}
                                    checked={selectedRoles.includes(role)}
                                    onChange={(e) => handleRoleChange(role, e.target.checked)}
                                    className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                />
                                <label htmlFor={`role-${role}`} className="ml-2 block text-sm text-gray-900">
                                    {role}
                                </label>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Edit Status */}
                <div className="mb-6">
                    <label htmlFor="statusSelect" className="block text-sm font-medium text-gray-700 mb-1">Status:</label>
                     <select
                        id="statusSelect"
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value as PortalUser['status'])}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    >
                        <option value="" disabled>Select status</option>
                        {PORTAL_STATUSES.map(status => (
                            <option key={status} value={status}>{status}</option>
                        ))}
                    </select>
                </div>
                
                 {saveError && <p className="text-sm text-red-600 mb-4">{saveError}</p>}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3">
                    <button 
                        type="button"
                        onClick={() => navigate('/admin/user-management')} // Go back
                        className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Cancel
                    </button>
                    <button 
                        type="button"
                        onClick={handleSave}
                        disabled={isSaving} // Disable while saving
                        className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                            isSaving ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'
                        }`}
                    >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default UserEditPage; 