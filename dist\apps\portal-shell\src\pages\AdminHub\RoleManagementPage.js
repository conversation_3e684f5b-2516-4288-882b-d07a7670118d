"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const adminApi_1 = require("../../services/adminApi");
const Modal_1 = require("../../components/common/Modal");
const RoleForm_1 = require("../../components/forms/RoleForm");
const react_hot_toast_1 = require("react-hot-toast");
const RoleManagementPage = () => {
    const [roles, setRoles] = (0, react_1.useState)([]);
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    // State for Add modal
    const [isAddModalOpen, setIsAddModalOpen] = (0, react_1.useState)(false);
    const [isSavingAdd, setIsSavingAdd] = (0, react_1.useState)(false);
    const [addError, setAddError] = (0, react_1.useState)(null);
    // State for Edit modal
    const [isEditModalOpen, setIsEditModalOpen] = (0, react_1.useState)(false);
    const [isSavingEdit, setIsSavingEdit] = (0, react_1.useState)(false);
    const [editError, setEditError] = (0, react_1.useState)(null);
    // State for Delete confirmation
    const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = (0, react_1.useState)(false);
    const [isDeleting, setIsDeleting] = (0, react_1.useState)(false);
    const [deleteError, setDeleteError] = (0, react_1.useState)(null);
    // Role selected for edit/delete
    const [selectedRole, setSelectedRole] = (0, react_1.useState)(null);
    const loadRoles = () => __awaiter(void 0, void 0, void 0, function* () {
        setIsLoading(true);
        setError(null);
        try {
            const fetchedRoles = yield (0, adminApi_1.fetchRoleDefinitions)();
            setRoles(fetchedRoles);
        }
        catch (err) {
            console.error("Error fetching roles:", err);
            setError("Failed to load role definitions. Please try again.");
        }
        finally {
            setIsLoading(false);
        }
    });
    (0, react_1.useEffect)(() => {
        loadRoles();
    }, []);
    // --- Handlers for Add ---
    const handleOpenAddModal = () => {
        setAddError(null);
        setIsAddModalOpen(true);
    };
    const handleCloseAddModal = () => {
        setIsAddModalOpen(false);
    };
    const handleAddSubmit = (formData) => __awaiter(void 0, void 0, void 0, function* () {
        setIsSavingAdd(true);
        setAddError(null);
        try {
            yield (0, adminApi_1.createRoleDefinition)(formData);
            react_hot_toast_1.default.success(`Role '${formData.name}' created successfully!`);
            handleCloseAddModal();
            loadRoles();
        }
        catch (err) {
            const message = err instanceof Error ? err.message : "An unknown error occurred.";
            console.error("Error creating role:", err);
            setAddError(message);
        }
        finally {
            setIsSavingAdd(false);
        }
    });
    // --- Handlers for Edit ---
    const handleOpenEditModal = (role) => {
        setSelectedRole(role);
        setEditError(null);
        setIsEditModalOpen(true);
    };
    const handleCloseEditModal = () => {
        setIsEditModalOpen(false);
        setSelectedRole(null);
    };
    const handleEditSubmit = (formData) => __awaiter(void 0, void 0, void 0, function* () {
        if (!selectedRole)
            return;
        setIsSavingEdit(true);
        setEditError(null);
        try {
            yield (0, adminApi_1.updateRoleDefinition)(selectedRole.id, formData);
            react_hot_toast_1.default.success(`Role '${formData.name}' updated successfully!`);
            handleCloseEditModal();
            loadRoles();
        }
        catch (err) {
            const message = err instanceof Error ? err.message : "An unknown error occurred.";
            console.error("Error updating role:", err);
            setEditError(message);
        }
        finally {
            setIsSavingEdit(false);
        }
    });
    // --- Handlers for Delete ---
    const handleOpenDeleteConfirm = (role) => {
        setSelectedRole(role);
        setDeleteError(null);
        setIsDeleteConfirmOpen(true);
    };
    const handleCloseDeleteConfirm = () => {
        setIsDeleteConfirmOpen(false);
        setSelectedRole(null);
    };
    const handleConfirmDelete = () => __awaiter(void 0, void 0, void 0, function* () {
        if (!selectedRole)
            return;
        setIsDeleting(true);
        setDeleteError(null);
        try {
            yield (0, adminApi_1.deleteRoleDefinition)(selectedRole.id);
            react_hot_toast_1.default.success(`Role '${selectedRole.name}' deleted successfully!`);
            handleCloseDeleteConfirm();
            loadRoles();
        }
        catch (err) {
            const message = err instanceof Error ? err.message : "An unknown error occurred.";
            console.error("Error deleting role:", err);
            setDeleteError(message);
            react_hot_toast_1.default.error(`Failed to delete role: ${message}`);
        }
        finally {
            setIsDeleting(false);
        }
    });
    // --- Render Logic ---
    return (<div className="p-6 bg-white min-h-screen">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-semibold text-gray-800">Role Management</h1>
                <button onClick={handleOpenAddModal} className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Add New Role
                </button>
            </div>

            {isLoading && <p className="text-sm text-gray-500">Loading roles...</p>}
            {error && <p className="text-sm text-red-600">Error: {error}</p>}

            {!isLoading && !error && (<div className="bg-white shadow rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {roles.map((role) => (<tr key={role.id}>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{role.name}</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{role.description || '-'}</td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-4">
                                        <button onClick={() => handleOpenEditModal(role)} className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400 disabled:cursor-not-allowed" disabled={role.name === 'User'}>
                                            Edit
                                        </button>
                                        <button onClick={() => handleOpenDeleteConfirm(role)} className="text-red-600 hover:text-red-900 disabled:text-gray-400 disabled:cursor-not-allowed" disabled={role.name === 'User' || role.name === 'Portal Admin'}>
                                            Delete
                                        </button>
                                    </td>
                                </tr>))}
                        </tbody>
                    </table>
                </div>)}

            {/* Add Role Modal */}
            <Modal_1.default isOpen={isAddModalOpen} onClose={handleCloseAddModal} title="Add New Role">
                 <RoleForm_1.default onSubmit={handleAddSubmit} onCancel={handleCloseAddModal} isSaving={isSavingAdd} submitError={addError} renderFooter={(isValid) => (<>
                            <button type="button" onClick={handleCloseAddModal} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Cancel
                            </button>
                            <button type="submit" form="role-form" disabled={isSavingAdd || !isValid} className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(isSavingAdd || !isValid) ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}>
                                {isSavingAdd ? 'Saving...' : 'Add Role'}
                            </button>
                        </>)}/>
            </Modal_1.default>

            {/* Edit Role Modal */}
            <Modal_1.default isOpen={isEditModalOpen} onClose={handleCloseEditModal} title={`Edit Role: ${selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}`}>
                 <RoleForm_1.default initialData={selectedRole} onSubmit={handleEditSubmit} onCancel={handleCloseEditModal} isSaving={isSavingEdit} submitError={editError} renderFooter={(isValid) => (<>
                            <button type="button" onClick={handleCloseEditModal} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Cancel
                            </button>
                            <button type="submit" form="role-form" disabled={isSavingEdit || !isValid} className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${(isSavingEdit || !isValid) ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}>
                                {isSavingEdit ? 'Saving...' : 'Save Changes'}
                            </button>
                        </>)}/>
            </Modal_1.default>

            {/* Delete Confirmation Modal */}
            <Modal_1.default isOpen={isDeleteConfirmOpen} onClose={handleCloseDeleteConfirm} title={`Delete Role: ${selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}`} footer={<>
                        <button type="button" onClick={handleCloseDeleteConfirm} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 ..." disabled={isDeleting}>
                            Cancel
                        </button>
                        <button type="button" onClick={handleConfirmDelete} disabled={isDeleting} className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm ... ${isDeleting ? 'bg-red-300 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'}`}>
                            {isDeleting ? 'Deleting...' : 'Delete Role'}
                        </button>
                    </>}>
                <p className="text-sm text-gray-600">
                    Are you sure you want to delete the role "<span className="font-medium">{selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}</span>"?
                </p>
                <p className="text-sm text-orange-600 mt-2">
                    By deleting the Role, all associated users will be converted to General Users and they will lose elevated privileges. Please confirm if you are okay with this action.
                </p>
                 {/* Display delete error within the confirmation modal */}
                 {deleteError && <p className="text-sm text-red-600 mt-3">Error: {deleteError}</p>}
            </Modal_1.default>
        </div>);
};
exports.default = RoleManagementPage;
//# sourceMappingURL=RoleManagementPage.js.map