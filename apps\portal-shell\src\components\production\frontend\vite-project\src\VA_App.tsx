"use client"

import { useState, useEffect } from "react"
import { useLocation } from "react-router-dom"
import Create_Va from "./component/visual_alert/create_va"
import Edit_Va from "./component/visual_alert/edit_va"
import Update_Va from "./component/visual_alert/update_va"
import RevisionApproval from "./component/visual_alert/rev_approval"
import Header_Page_Va from "./component/visual_alert/va_header_page"
import Footer_Va from "./component/visual_alert/footer_va"
import Sidebar_Va from "./component/visual_alert/sidebar_va"
import Login_Page from "./component/visual_alert/login_page_va"

function VA_App() {
  const location = useLocation()
  const [currentPage, setCurrentPage] = useState("create")
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [userData, setUserData] = useState({
    emp_id: "",
    emp_name: "",
    employee_mail: "",
    employee_dept: "",
    employee_reporting: "",
  })

  useEffect(() => {
    checkLoginStatus()

    const globalSidebar = document.getElementById("global-sidebar")
    if (globalSidebar) globalSidebar.style.display = "none"

    return () => {
      if (globalSidebar) globalSidebar.style.display = ""
    }
  }, [])

  // Check for path-based navigation
  useEffect(() => {
    // Extract the page from the URL path
    const pathParts = location.pathname.split("/")
    const pageName = pathParts[pathParts.length - 1]

    if (["create", "edit", "update", "approval"].includes(pageName)) {
      setCurrentPage(pageName)
    } else if (pathParts[1] === "visual_alert" && !pathParts[2]) {
      // Default to "create" if just /visual_alert
      setCurrentPage("create")
    }
  }, [location])

  const checkLoginStatus = () => {
    // First check sessionStorage (for API login)
    const sessionData = sessionStorage.getItem("userData")

    if (sessionData) {
      try {
        const parsedData = JSON.parse(sessionData)
        setUserData({
          emp_id: parsedData.employee_id || "",
          emp_name: parsedData.employee_name || "",
          employee_mail: parsedData.employee_mail || "",
          employee_dept: parsedData.employee_dept || "",
          employee_reporting: parsedData.employee_reporting || "",
        })
        setIsLoggedIn(true)
        return
      } catch (error) {
        console.error("Error parsing session data:", error)
      }
    }

    // Then check localStorage (for existing login method)
    const loginStatus = localStorage.getItem("isLoggedIn") === "true"
    if (loginStatus) {
      setUserData({
        emp_id: localStorage.getItem("emp_id") || "",
        emp_name: localStorage.getItem("emp_name") || "",
        employee_mail: localStorage.getItem("employee_mail") || "",
        employee_dept: localStorage.getItem("employee_dept") || "",
        employee_reporting: localStorage.getItem("employee_reporting") || "",
      })
      setIsLoggedIn(true)
    } else {
      setIsLoggedIn(false)
    }
  }

  const handleLoginSuccess = () => {
    checkLoginStatus()
  }

  const handleLogout = () => {
    localStorage.clear()
    setIsLoggedIn(false)
  }

  // Keep the hash-based navigation as well for backward compatibility
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#/", "")
      if (["create", "edit", "update", "approval"].includes(hash)) {
        setCurrentPage(hash)
      } else if (!hash) {
        setCurrentPage("create")
      }
    }

    handleHashChange()
    window.addEventListener("hashchange", handleHashChange)
    return () => window.removeEventListener("hashchange", handleHashChange)
  }, [])

  const renderContent = () => {
    switch (currentPage) {
      case "create":
        return <Create_Va />
      case "edit":
        return <Edit_Va />
      case "update":
        return <Update_Va />
      case "approval":
        return <RevisionApproval />
      default:
        return <Create_Va />
    }
  }

  const handleToggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  if (!isLoggedIn) {
    return <Login_Page onLoginSuccess={handleLoginSuccess} />
  }

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <div
        className={`fixed top-20 left-0 bg-gray-800 z-20 transition-all duration-300 ease-in-out ${
          isSidebarOpen ? "w-64" : "w-20"
        } bottom-10`}
      >
        <Sidebar_Va isOpen={isSidebarOpen} isMobile={false} />
      </div>

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-white z-10 border-b border-gray-200">
        <Header_Page_Va onToggleSidebar={handleToggleSidebar} userData={userData} onLogout={handleLogout} />
      </header>

      {/* Main Content */}
      <main className="flex-grow pt-20 pl-64 pr-4 pb-16">{renderContent()}</main>

      {/* Footer */}
      <footer className="fixed bottom-0 left-0 right-0 bg-white z-10 border-t border-gray-200">
        <Footer_Va />
      </footer>
    </div>
  )
}

export default VA_App