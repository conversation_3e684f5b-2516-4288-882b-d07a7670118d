"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClientPrincipal = getClientPrincipal;
exports.hasRequiredRole = hasRequiredRole;
exports.isAdmin = isAdmin;
exports.getUserIdFromPrincipal = getUserIdFromPrincipal;
const logger_1 = require("./utils/logger");
const sql = require("mssql");
const db_1 = require("./db");
const userManagementService_1 = require("./services/userManagementService");
/**
 * Parses the X-MS-CLIENT-PRINCIPAL header to get user information.
 *
 * @param request - The incoming HttpRequest object.
 * @returns The parsed ClientPrincipal object or null if the header is missing or invalid.
 */
function getClientPrincipal(request) {
    const header = request.headers.get('x-ms-client-principal');
    if (!header) {
        logger_1.logger.warn("getClientPrincipal: Missing x-ms-client-principal header.");
        return null;
    }
    try {
        const decoded = Buffer.from(header, 'base64').toString('utf-8');
        const principal = JSON.parse(decoded);
        // Basic validation
        if (!principal || !principal.userId || !principal.userRoles) {
            logger_1.logger.warn("getClientPrincipal: Parsed principal is missing required fields (userId, userRoles).");
            return null;
        }
        return principal;
    }
    catch (error) {
        logger_1.logger.error("getClientPrincipal: Error parsing x-ms-client-principal header:", error);
        return null;
    }
}
/**
 * Checks if the authenticated user has at least one of the required roles.
 * Assumes roles/groups are passed in the userRoles array or specific claims.
 *
 * @param principal - The ClientPrincipal object.
 * @param requiredRoles - An array of role names that are allowed.
 * @returns True if the user has permission, false otherwise.
 */
function hasRequiredRole(principal, requiredRoles) {
    if (!principal) {
        return false; // Not authenticated
    }
    // Check direct userRoles first (common for App Service Auth roles)
    if (principal.userRoles.some(role => requiredRoles.includes(role))) {
        return true;
    }
    // Check claims for roles/groups (common for Entra ID group membership)
    // The claim type for roles/groups might vary (e.g., 'roles', 'groups')
    if (principal.claims) {
        if (principal.claims.some(claim => (claim.typ === 'roles' || claim.typ === 'groups') &&
            requiredRoles.includes(claim.val))) {
            return true;
        }
    }
    return false;
}
/**
 * Checks if the authenticated user has admin privileges.
 *
 * @param principal - The ClientPrincipal object.
 * @returns True if the user is an admin, false otherwise.
 */
function isAdmin(principal) {
    return hasRequiredRole(principal, ['Admin', 'Administrator', 'admin']);
}
/**
 * Retrieves the internal UserID from the database based on the Entra ID Object ID (oid).
 * Also updates the user's LastLoginDate upon successful retrieval.
 *
 * @param principal - The ClientPrincipal object.
 * @param context - InvocationContext for logging.
 * @returns The internal UserID or null if not found or an error occurs.
 */
function getUserIdFromPrincipal(principal, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        if (!principal) {
            context.log("getUserIdFromPrincipal: Cannot get UserID, client principal is null.");
            return null;
        }
        // Find the Entra Object ID (oid) claim
        const oidClaim = (_a = principal.claims) === null || _a === void 0 ? void 0 : _a.find(claim => claim.typ === 'http://schemas.microsoft.com/identity/claims/objectidentifier');
        const entraId = oidClaim === null || oidClaim === void 0 ? void 0 : oidClaim.val;
        if (!entraId) {
            context.log("getUserIdFromPrincipal: Could not find Entra ID (oid claim) in principal.");
            logger_1.logger.warn("getUserIdFromPrincipal: Could not find Entra ID (oid claim) in principal.", principal);
            return null;
        }
        try {
            const query = `
            SELECT u.UserID, u.IsActive 
            WHERE u.EntraID = @EntraID
        `;
            const params = [
                { name: 'EntraID', type: sql.NVarChar, value: entraId }
            ];
            const result = yield (0, db_1.executeQuery)(query, params);
            if (!result.recordset || result.recordset.length === 0) {
                logger_1.logger.warn(`User lookup failed in checkUserExists: EntraID ${entraId} not found in local DB.`);
                return null;
            }
            const userId = result.recordset[0].UserID;
            // Update LastLoginDate asynchronously (fire and forget - don't wait for it)
            (0, userManagementService_1.updateUserLastLogin)(userId).catch(err => {
                logger_1.logger.error(`getUserIdFromPrincipal: Failed to update LastLoginDate for UserID ${userId}:`, err);
                // Log error but don't block the main function return
            });
            return userId;
        }
        catch (error) {
            context.log(`getUserIdFromPrincipal: Error fetching UserID for EntraID ${entraId}:`, error);
            logger_1.logger.error(`getUserIdFromPrincipal: Error fetching UserID for EntraID ${entraId}:`, error);
            return null;
        }
    });
}
//# sourceMappingURL=authUtils.js.map