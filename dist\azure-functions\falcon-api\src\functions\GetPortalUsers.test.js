// import { InvocationContext } from "@azure/functions";
// import { HttpRequest } from "@azure/functions";
// import { UserManagementService } from "../../src/shared/services/userManagementService";
// import { PortalUser } from "../../src/shared/interfaces";
// import { getPortalUsers } from "../../src/functions/GetPortalUsers"; 
// // Mock the UserManagementService
// import { mockDeep } from 'jest-mock-extended';
// // Mock logger (can be simple console object or a more sophisticated mock)
// const mockContext = mockDeep<InvocationContext>();
// describe('GetPortalUsers Function', () => {
//     let mockUserManagementService: ReturnType<typeof mockDeep<UserManagementService>>;
//     beforeEach(() => {
//         // Create a new mock before each test
//         mockUserManagementService = mockDeep<UserManagementService>();
//         // Mock the service instance used within the function (this requires dependency injection or a way to override)
//         // For simplicity, if userManagementService is imported directly, you might need to use jest.mock
//         jest.mock('../../src/shared/services/userManagementService', () => ({
//             userManagementService: mockUserManagementService
//         }));
//         // Reset mock context logs if necessary
//         // mockContext.log.info.mockClear(); 
//     });
//     afterEach(() => {
//         jest.resetAllMocks();
//     });
//     it('should return 400 if page parameter is missing', async () => {
//         const request = new HttpRequest({
//             method: "GET",
//             url: "/api/portal-users?pageSize=10",
//             query: { pageSize: "10" }
//         });
//         const response = await getPortalUsers(request, mockContext);
//         expect(response.status).toBe(400);
//         expect(response.jsonBody).toEqual({ error: 'Invalid query parameters.' });
//     });
// Add more tests for other scenarios:
// - Missing pageSize
// - Invalid page/pageSize values (non-numeric, negative)
// - Successful retrieval of users (mocking getPortalUsersPaginated)
// - Handling errors from the service
// - Correct pagination calculation in the response
// });
//# sourceMappingURL=GetPortalUsers.test.js.map