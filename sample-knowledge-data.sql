-- Sample Knowledge Hub Data for Falcon Portal
-- Insert sample document categories
INSERT INTO DocumentCategories (CategoryName, ParentCategoryID, IsActive, CreatedBy, CreatedDate)
VALUES 
    ('Company Policies', NULL, 1, 1, GETDATE()),
    ('Standard Procedures', NULL, 1, 1, GETDATE()),
    ('Training Materials', NULL, 1, 1, GETDATE()),
    ('Forms & Templates', NULL, 1, 1, GETDATE()),
    ('Technical Documentation', NULL, 1, 1, GETDATE()),
    ('Safety Guidelines', NULL, 1, 1, GETDATE());

-- Insert sample documents
INSERT INTO Documents (Title, Description, FileName, FileExtension, FileSizeKB, CategoryID, CompanyID, IsPublished, IsActive, Version, DownloadCount, ViewCount, CreatedBy, CreatedDate)
VALUES 
    ('Employee Handbook 2025', 'Comprehensive guide for all employees covering policies, procedures, and company culture.', 'Employee_Handbook_2025.pdf', 'pdf', 2048, 1, 1, 1, 1, '2.1', 245, 892, 1, GETDATE()),
    ('Travel Expense Policy', 'Guidelines for submitting and approving travel expenses.', 'Travel_Expense_Policy.pdf', 'pdf', 512, 1, 1, 1, 1, '1.3', 156, 423, 1, GETDATE()),
    ('IT Security Procedures', 'Standard procedures for maintaining IT security.', 'IT_Security_Procedures.pdf', 'pdf', 1024, 2, 1, 1, 1, '1.0', 89, 234, 1, GETDATE()),
    ('New Employee Checklist', 'Checklist for new employee onboarding process.', 'New_Employee_Checklist.docx', 'docx', 256, 4, 1, 1, 1, '1.2', 134, 567, 1, GETDATE()),
    ('Safety Manual', 'Comprehensive safety guidelines for all employees.', 'Safety_Manual.pdf', 'pdf', 3072, 6, 1, 1, 1, '3.0', 78, 345, 1, GETDATE());

-- Insert sample knowledge articles
INSERT INTO KnowledgeArticles (Title, Summary, Content, CategoryID, CompanyID, IsPublished, IsActive, ViewCount, CreatedBy, CreatedDate)
VALUES 
    ('How to Submit a Travel Request', 'Step-by-step guide for submitting travel requests through the portal.', 'To submit a travel request:\n1. Login to the portal\n2. Navigate to Admin Hub\n3. Click on Travel Requests\n4. Fill out the required information\n5. Submit for approval', 2, 1, 1, 1, 567, 1, GETDATE()),
    ('Frequently Asked Questions - IT Support', 'Common IT support questions and their solutions.', 'Q: How do I reset my password?\nA: Contact IT support or use the self-service portal.\n\nQ: How do I connect to VPN?\nA: Download the VPN client and use your domain credentials.', 5, 1, 1, 1, 789, 1, GETDATE()),
    ('Emergency Procedures', 'What to do in case of workplace emergencies.', 'In case of emergency:\n1. Ensure personal safety first\n2. Call emergency services if needed\n3. Notify your supervisor\n4. Follow evacuation procedures if required', 6, 1, 1, 1, 234, 1, GETDATE());

-- Insert sample document tags
INSERT INTO DocumentTags (TagName, IsActive, CreatedBy, CreatedDate)
VALUES 
    ('policy', 1, 1, GETDATE()),
    ('handbook', 1, 1, GETDATE()),
    ('hr', 1, 1, GETDATE()),
    ('new-employee', 1, 1, GETDATE()),
    ('travel', 1, 1, GETDATE()),
    ('expenses', 1, 1, GETDATE()),
    ('finance', 1, 1, GETDATE()),
    ('it-security', 1, 1, GETDATE()),
    ('procedures', 1, 1, GETDATE()),
    ('safety', 1, 1, GETDATE()),
    ('emergency', 1, 1, GETDATE()),
    ('checklist', 1, 1, GETDATE());

-- Link documents to tags
INSERT INTO DocumentTags_Map (DocumentID, TagID)
VALUES 
    (1, 1), (1, 2), (1, 3), (1, 4),  -- Employee Handbook
    (2, 1), (2, 5), (2, 6), (2, 7),  -- Travel Expense Policy
    (3, 8), (3, 9),                  -- IT Security Procedures
    (4, 3), (4, 4), (4, 12),         -- New Employee Checklist
    (5, 10), (5, 11);                -- Safety Manual

-- Link knowledge articles to tags
INSERT INTO KnowledgeArticleTags (ArticleID, TagID)
VALUES 
    (1, 5), (1, 9),     -- Travel Request article
    (2, 8), (2, 9),     -- IT FAQ article
    (3, 10), (3, 11);   -- Emergency Procedures article

PRINT 'Sample Knowledge Hub data inserted successfully!'; 