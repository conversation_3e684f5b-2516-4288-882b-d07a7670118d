"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadDocument = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
// import { BlobServiceClient } from '@azure/storage-blob'; // TODO: Add @azure/storage-blob package
// Azure Blob Storage configuration
const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING || '';
const CONTAINER_NAME = 'knowledge-documents';
async function uploadDocument(request, context) {
    context.log(`UploadDocument function invoked.`);
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }
        // Parse form data (multipart/form-data)
        const contentType = request.headers.get('content-type') || '';
        if (!contentType.includes('application/json')) {
            return { status: 400, jsonBody: { error: "Content-Type must be application/json" } };
        }
        // Parse JSON data with base64 file content
        const requestData = await request.json();
        if (!requestData.file || !requestData.metadata) {
            return { status: 400, jsonBody: { error: "File and metadata are required" } };
        }
        const { file, metadata } = requestData;
        // Validate file
        if (!file.name || !file.content) {
            return { status: 400, jsonBody: { error: "File name and content are required" } };
        }
        // Validate metadata
        if (!metadata.title || metadata.title.trim() === '') {
            return { status: 400, jsonBody: { error: "Document title is required" } };
        }
        // Extract file information
        const fileName = file.name;
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
        const fileSizeKB = Math.round(file.size / 1024);
        // Validate file type
        const allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'md'];
        if (!allowedExtensions.includes(fileExtension)) {
            return { status: 400, jsonBody: {
                    error: `File type .${fileExtension} is not allowed. Allowed types: ${allowedExtensions.join(', ')}`
                } };
        }
        // Generate unique file name
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const uniqueFileName = `${timestamp}-${fileName}`;
        // Upload to Azure Blob Storage
        let blobUrl = '';
        try {
            if (AZURE_STORAGE_CONNECTION_STRING) {
                // TODO: Implement Azure Blob Storage upload when @azure/storage-blob package is added
                // const blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
                // const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
                // ... blob upload implementation
                // For now, use development mode URL
                blobUrl = `/api/files/${uniqueFileName}`;
                logger_1.logger.info(`UploadDocument: Azure Blob Storage not implemented yet, using mock URL: ${blobUrl}`);
            }
            else {
                // Development mode - use local storage or mock URL
                blobUrl = `/api/files/${uniqueFileName}`;
                logger_1.logger.info(`UploadDocument: Development mode - using mock file URL: ${blobUrl}`);
            }
        }
        catch (error) {
            logger_1.logger.error("UploadDocument: Error uploading to blob storage:", error);
            return { status: 500, jsonBody: { error: "Failed to upload file to storage" } };
        }
        // Get user's company for document association
        const userCompanyQuery = `
            SELECT CompanyID, CompanyName 
            FROM Users u
            JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE u.UserID = @userId
        `;
        const userCompanyResult = await (0, db_1.executeQuery)(userCompanyQuery, [
            { name: 'userId', type: sql.Int, value: userId || 0 }
        ]);
        const userCompany = userCompanyResult.recordset[0];
        const companyId = userCompany?.CompanyID || 1; // Default to first company if not found
        // Insert document record into database
        const insertQuery = `
            INSERT INTO Documents (
                Title, Description, FileName, FileExtension, FileSizeKB, 
                BlobUrl, CategoryID, CompanyID, IsPublished, Version,
                DownloadCount, ViewCount, CreatedBy, CreatedDate, ModifiedDate, IsActive
            )
            OUTPUT INSERTED.DocumentID
            VALUES (
                @title, @description, @fileName, @fileExtension, @fileSizeKB,
                @blobUrl, @categoryId, @companyId, @isPublished, @version,
                0, 0, @createdBy, GETUTCDATE(), GETUTCDATE(), 1
            )
        `;
        const insertParams = [
            { name: 'title', type: sql.NVarChar, value: metadata.title.trim() },
            { name: 'description', type: sql.NVarChar, value: metadata.description?.trim() || null },
            { name: 'fileName', type: sql.NVarChar, value: fileName },
            { name: 'fileExtension', type: sql.NVarChar, value: fileExtension },
            { name: 'fileSizeKB', type: sql.Int, value: fileSizeKB },
            { name: 'blobUrl', type: sql.NVarChar, value: blobUrl },
            { name: 'categoryId', type: sql.Int, value: metadata.categoryId ? parseInt(metadata.categoryId) : null },
            { name: 'companyId', type: sql.Int, value: companyId },
            { name: 'isPublished', type: sql.Bit, value: metadata.isPublished !== false },
            { name: 'version', type: sql.NVarChar, value: '1.0' },
            { name: 'createdBy', type: sql.Int, value: userId || 0 }
        ];
        const insertResult = await (0, db_1.executeQuery)(insertQuery, insertParams);
        const documentId = insertResult.recordset[0]?.DocumentID;
        if (!documentId) {
            logger_1.logger.error("UploadDocument: Failed to insert document record");
            return { status: 500, jsonBody: { error: "Failed to create document record" } };
        }
        // Insert tags if provided
        if (metadata.tags && metadata.tags.length > 0) {
            for (const tagName of metadata.tags) {
                try {
                    // First, ensure tag exists or create it
                    const tagInsertQuery = `
                        MERGE DocumentTags AS target
                        USING (SELECT @tagName AS TagName) AS source ON target.TagName = source.TagName
                        WHEN NOT MATCHED THEN
                            INSERT (TagName, CreatedDate) VALUES (@tagName, GETUTCDATE())
                        OUTPUT INSERTED.TagID;
                    `;
                    const tagResult = await (0, db_1.executeQuery)(tagInsertQuery, [
                        { name: 'tagName', type: sql.NVarChar, value: tagName.trim() }
                    ]);
                    const tagId = tagResult.recordset[0]?.TagID;
                    if (tagId) {
                        // Link tag to document
                        const linkQuery = `
                            INSERT INTO DocumentTags_Map (DocumentID, TagID, CreatedDate)
                            VALUES (@documentId, @tagId, GETUTCDATE())
                        `;
                        await (0, db_1.executeQuery)(linkQuery, [
                            { name: 'documentId', type: sql.Int, value: documentId },
                            { name: 'tagId', type: sql.Int, value: tagId }
                        ]);
                    }
                }
                catch (tagError) {
                    logger_1.logger.warn(`UploadDocument: Failed to process tag '${tagName}':`, tagError);
                    // Continue with other tags
                }
            }
        }
        // Retrieve the complete document record for response
        const selectQuery = `
            SELECT 
                d.DocumentID,
                d.Title,
                d.Description,
                d.FileName,
                d.FileExtension,
                d.FileSizeKB,
                d.BlobUrl,
                d.CategoryID,
                dc.CategoryName,
                d.CompanyID,
                c.CompanyName,
                d.IsPublished,
                d.Version,
                d.DownloadCount,
                d.ViewCount,
                d.CreatedBy,
                u.FirstName + ' ' + u.LastName as CreatedByName,
                u.Email as CreatedByEmail,
                d.CreatedDate,
                d.ModifiedDate
            FROM Documents d
            LEFT JOIN DocumentCategories dc ON d.CategoryID = dc.CategoryID
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            LEFT JOIN Users u ON d.CreatedBy = u.UserID
            WHERE d.DocumentID = @documentId
        `;
        const selectResult = await (0, db_1.executeQuery)(selectQuery, [
            { name: 'documentId', type: sql.Int, value: documentId }
        ]);
        const documentRecord = selectResult.recordset[0];
        const response = {
            id: documentRecord.DocumentID.toString(),
            title: documentRecord.Title,
            description: documentRecord.Description,
            fileName: documentRecord.FileName,
            fileExtension: documentRecord.FileExtension,
            fileSizeKB: documentRecord.FileSizeKB,
            blobUrl: documentRecord.BlobUrl,
            category: {
                id: documentRecord.CategoryID?.toString() || '',
                name: documentRecord.CategoryName || 'Uncategorized'
            },
            company: documentRecord.CompanyName,
            isPublished: documentRecord.IsPublished,
            version: documentRecord.Version,
            downloadCount: documentRecord.DownloadCount,
            viewCount: documentRecord.ViewCount,
            createdBy: {
                name: documentRecord.CreatedByName,
                email: documentRecord.CreatedByEmail
            },
            createdDate: documentRecord.CreatedDate,
            lastModified: documentRecord.ModifiedDate,
            tags: metadata.tags || []
        };
        logger_1.logger.info(`UploadDocument: Successfully uploaded document ${documentId} for user ${userId}`);
        return {
            status: 201,
            jsonBody: {
                message: "Document uploaded successfully",
                document: response
            }
        };
    }
    catch (error) {
        logger_1.logger.error("UploadDocument: Error uploading document:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error uploading document.",
                error: errorMessage
            }
        };
    }
}
exports.uploadDocument = uploadDocument;
// Register the function
functions_1.app.http('UploadDocument', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'documents/upload',
    handler: uploadDocument
});
//# sourceMappingURL=UploadDocument.js.map