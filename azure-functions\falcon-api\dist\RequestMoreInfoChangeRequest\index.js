"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestMoreInfoChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
const emailService_1 = require("../shared/services/emailService");
async function requestMoreInfoChangeRequest(request, context) {
    context.log('RequestMoreInfoChangeRequest function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json();
        const { comments, userId } = body;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required'
                    }
                }
            };
        }
        // First, verify the change request exists and is in a valid state
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        const changeRequest = checkResult.recordset[0];
        // Validate that the request can be sent back for more information
        if (!['Under Review'].includes(changeRequest.Status)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Cannot request more information for request with status: ${changeRequest.Status}. Only requests under review can be sent back for more information.`
                    }
                }
            };
        }
        // Update the change request status to "Submitted" (waiting for clarification)
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Submitted',
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add history entry for the status change
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, @statusTo, @userId, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: changeRequest.Status },
            { name: 'statusTo', type: sql.NVarChar, value: 'Submitted' },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: comments || 'Additional information requested from submitter' }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Add a comment if provided
        if (comments && comments.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
                )
            `;
            const commentParams = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: `[INFO_REQUEST] ${comments.trim()}` },
                { name: 'commentType', type: sql.NVarChar, value: 'ApprovalNote' },
                { name: 'isInternal', type: sql.Bit, value: false },
                { name: 'userId', type: sql.Int, value: parseInt(userId) }
            ];
            await (0, db_1.executeQuery)(commentQuery, commentParams);
        }
        // Get comprehensive request details for email notification
        const getUpdatedQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Status as status,
                cr.Priority as priority,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                cr.ModifiedDate as modifiedDate,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                CONCAT(reviewer.FirstName, ' ', reviewer.LastName) as approverName
            FROM ChangeRequests cr
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON requester.CompanyID = c.CompanyID
                LEFT JOIN Users reviewer ON cr.ModifiedBy = reviewer.UserID
            WHERE cr.RequestID = @requestId
        `;
        const getUpdatedParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const updatedResult = await (0, db_1.executeQuery)(getUpdatedQuery, getUpdatedParams);
        const requestDetails = updatedResult.recordset[0];
        // Send email notification asynchronously
        try {
            const emailData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                comments: comments,
                approverName: requestDetails.approverName,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.dueDate ? new Date(requestDetails.dueDate) : undefined
            };
            // Send information request notification (don't await to avoid blocking the response)
            emailService_1.EmailService.getInstance().sendChangeRequestInfoRequested(emailData).catch((error) => {
                context.error('Failed to send info request email notification:', error);
            });
            context.log(`Email notification queued for info request on change request ${requestId}`);
        }
        catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the info request if email fails
        }
        context.log(`Successfully requested more information for change request ${requestId}`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'More information requested successfully. The submitter will be notified.',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    description: requestDetails.description,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    createdDate: requestDetails.createdDate,
                    modifiedDate: requestDetails.modifiedDate,
                    requesterName: requestDetails.requesterName,
                    modifiedByName: requestDetails.approverName
                }
            }
        };
    }
    catch (error) {
        context.error('Error in RequestMoreInfoChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while requesting more information',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.requestMoreInfoChangeRequest = requestMoreInfoChangeRequest;
functions_1.app.http('RequestMoreInfoChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/request-info',
    handler: requestMoreInfoChangeRequest
});
//# sourceMappingURL=index.js.map