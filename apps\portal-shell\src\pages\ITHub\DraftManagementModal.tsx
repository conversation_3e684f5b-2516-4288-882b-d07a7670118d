import React, { useState, useEffect } from 'react';
import { X, Edit3, Send, Trash2, Clock, User, Calendar, AlertCircle } from 'feather-icons-react';
import { draftManagementApi, type ChangeRequestDraft } from '../../services/draftManagementApi';

interface DraftManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEditDraft: (draft: ChangeRequestDraft) => void;
  onDraftSubmitted: () => void;
}

const DraftManagementModal: React.FC<DraftManagementModalProps> = ({
  isOpen,
  onClose,
  onEditDraft,
  onDraftSubmitted
}) => {
  const [drafts, setDrafts] = useState<ChangeRequestDraft[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingDraftId, setDeletingDraftId] = useState<string | null>(null);
  const [submittingDraftId, setSubmittingDraftId] = useState<string | null>(null);

  // Load drafts when modal opens
  useEffect(() => {
    if (isOpen) {
      loadDrafts();
    }
  }, [isOpen]);

  const loadDrafts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // In a real implementation, get userId from user context
      const userId = 1; // This should come from user context
      const response = await draftManagementApi.getDrafts(userId);
      
      if (response.success) {
        setDrafts(response.drafts);
      } else {
        setError('Failed to load drafts');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load drafts');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDraft = async (draftId: string) => {
    if (!confirm('Are you sure you want to delete this draft? This action cannot be undone.')) {
      return;
    }

    try {
      setDeletingDraftId(draftId);
      
      // In a real implementation, get userId from user context
      const userId = 1;
      const response = await draftManagementApi.deleteDraft(draftId, userId);
      
      if (response.success) {
        setDrafts(prev => prev.filter(draft => draft.draftId !== draftId));
      } else {
        setError('Failed to delete draft');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete draft');
    } finally {
      setDeletingDraftId(null);
    }
  };

  const handleSubmitDraft = async (draftId: string) => {
    if (!confirm('Are you sure you want to submit this draft as a change request?')) {
      return;
    }

    try {
      setSubmittingDraftId(draftId);
      
      // In a real implementation, get userId from user context
      const userId = 1;
      const response = await draftManagementApi.submitDraft(draftId, userId);
      
      if (response.success) {
        setDrafts(prev => prev.filter(draft => draft.draftId !== draftId));
        onDraftSubmitted();
      } else {
        setError('Failed to submit draft');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit draft');
    } finally {
      setSubmittingDraftId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Draft Change Requests</h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage your saved drafts - edit, submit, or delete them
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-3">
                <AlertCircle size={20} className="text-red-600" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="text-center py-8">
              <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">Loading drafts...</p>
            </div>
          ) : drafts.length === 0 ? (
            /* Empty State */
            <div className="text-center py-12">
              <Edit3 size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Drafts Found</h3>
              <p className="text-gray-600">
                You don't have any saved drafts yet. Start creating a change request and save it as a draft.
              </p>
            </div>
          ) : (
            /* Drafts List */
            <div className="space-y-4">
              {drafts.map((draft) => (
                <div
                  key={draft.draftId}
                  className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Title and Type */}
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{draft.title}</h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {draft.typeName}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(draft.priority)}`}>
                          {draft.priority}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {draft.description || 'No description provided'}
                      </p>

                      {/* Metadata */}
                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <User size={14} />
                          <span>{draft.requestedByName}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock size={14} />
                          <span>Last modified: {formatDate(draft.lastModified)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar size={14} />
                          <span>Created: {formatDate(draft.createdDate)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => onEditDraft(draft)}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <Edit3 size={16} className="mr-2" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleSubmitDraft(draft.draftId)}
                        disabled={submittingDraftId === draft.draftId}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        {submittingDraftId === draft.draftId ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Submitting...
                          </>
                        ) : (
                          <>
                            <Send size={16} className="mr-2" />
                            Submit
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleDeleteDraft(draft.draftId)}
                        disabled={deletingDraftId === draft.draftId}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {deletingDraftId === draft.draftId ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 size={16} className="mr-2" />
                            Delete
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 flex-shrink-0 bg-gray-50">
          <div className="text-sm text-gray-600">
            {drafts.length > 0 && `${drafts.length} draft${drafts.length === 1 ? '' : 's'} found`}
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={loadDrafts}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              Refresh
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DraftManagementModal; 