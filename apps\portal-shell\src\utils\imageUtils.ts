/**
 * Utility functions for handling image loading and URL transformations
 */

/**
 * Convert Azure Blob Storage URL to authenticated proxy URL
 */
export const getAuthenticatedImageUrl = (imageUrl: string): string => {
  if (!imageUrl) return '';
  
  // If it's already a data URL, use it directly
  if (imageUrl.startsWith('data:')) {
    return imageUrl;
  }
  
  // If it's a blob storage URL, convert to our authenticated proxy
  if (imageUrl.includes('falconhubstorage.blob.core.windows.net') || 
      imageUrl.includes('.blob.core.windows.net')) {
    try {
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');
      // Remove the container name from the path (first part after /)
      // Path format: /container-name/folder/file.ext -> folder/file.ext
      const imagePath = pathParts.slice(2).join('/');
      return `/api/images/${imagePath}`;
    } catch (error) {
      console.error('Error parsing blob URL:', error);
      return imageUrl;
    }
  }
  
  // For other URLs, use as-is
  return imageUrl;
};

/**
 * Check if an image URL is a blob storage URL that needs authentication
 */
export const isPrivateBlobUrl = (imageUrl: string): boolean => {
  return imageUrl.includes('.blob.core.windows.net') && !imageUrl.startsWith('data:');
};

/**
 * Preload an image and return a promise that resolves when loaded or rejects on error
 */
export const preloadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
    img.src = src;
  });
};

/**
 * Get fallback image placeholder SVG as data URL
 */
export const getImagePlaceholder = (width: number = 300, height: number = 200): string => {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <path d="M${width/2 - 20} ${height/2 - 15}h40v10h-40z" fill="#9ca3af"/>
      <path d="M${width/2 - 15} ${height/2 - 5}h30v20h-30z" fill="#9ca3af"/>
      <circle cx="${width/2 - 5}" cy="${height/2 + 5}" r="3" fill="#6b7280"/>
      <text x="${width/2}" y="${height/2 + 25}" text-anchor="middle" fill="#6b7280" font-family="Arial, sans-serif" font-size="12">Image unavailable</text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}; 