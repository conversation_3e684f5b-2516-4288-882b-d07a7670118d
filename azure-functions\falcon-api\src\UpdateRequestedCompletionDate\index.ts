import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import sql from 'mssql';

export async function updateRequestedCompletionDate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('UpdateRequestedCompletionDate function invoked.');
    
    try {
        const requestId = request.params.requestId;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'MISSING_REQUEST_ID',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        // Parse request body
        const body = await request.json() as {
            requestedCompletionDate: string;
            reason?: string;
            userId: number;
        };

        if (!body.requestedCompletionDate) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'MISSING_COMPLETION_DATE',
                        message: 'Requested completion date is required'
                    }
                }
            };
        }

        if (!body.userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'MISSING_USER_ID',
                        message: 'User ID is required'
                    }
                }
            };
        }

        // TODO: Add role-based authorization check
        // Verify user has Admin or IT Admin role
        
        // First, get the current change request to check status
        const getCurrentRequestQuery = `
            SELECT Status, RequestedCompletionDate, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const getCurrentRequestParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const currentRequestResult = await executeQuery(getCurrentRequestQuery, getCurrentRequestParams);
        
        if (currentRequestResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'REQUEST_NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const changeRequest = currentRequestResult.recordset[0];

        // Check if request is in a status that allows date override
        if (['Completed', 'In Development'].includes(changeRequest.Status)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Cannot update completion date for requests with status: ${changeRequest.Status}`
                    }
                }
            };
        }

        // Update the requested completion date
        const updateQuery = `
            UPDATE ChangeRequests 
            SET RequestedCompletionDate = @requestedCompletionDate,
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'requestedCompletionDate', type: sql.Date, value: new Date(body.requestedCompletionDate) },
            { name: 'userId', type: sql.Int, value: body.userId }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, @statusTo, @userId, GETDATE(), @comments
            )
        `;

        const originalDate = changeRequest.RequestedCompletionDate 
            ? new Date(changeRequest.RequestedCompletionDate).toLocaleDateString()
            : 'Not specified';
        const newDate = new Date(body.requestedCompletionDate).toLocaleDateString();
        
        const historyComments = body.reason 
            ? `Completion date updated from ${originalDate} to ${newDate}. Reason: ${body.reason}`
            : `Completion date updated from ${originalDate} to ${newDate}`;

        const historyParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: changeRequest.Status },
            { name: 'statusTo', type: sql.NVarChar, value: changeRequest.Status },
            { name: 'userId', type: sql.Int, value: body.userId },
            { name: 'comments', type: sql.NVarChar, value: historyComments }
        ];

        await executeQuery(historyQuery, historyParams);

        // Add a comment if reason was provided
        if (body.reason && body.reason.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
                )
            `;

            const commentParams: QueryParameter[] = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: `[DATE_OVERRIDE] ${body.reason.trim()}` },
                { name: 'commentType', type: sql.NVarChar, value: 'General' },
                { name: 'isInternal', type: sql.Bit, value: false },
                { name: 'userId', type: sql.Int, value: body.userId }
            ];

            await executeQuery(commentQuery, commentParams);
        }

        // Return updated change request data
        const getUpdatedRequestQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.ExpectedBenefit as expectedBenefit,
                cr.RequestedCompletionDate as requestedCompletionDate,
                ISNULL(cr.DevelopmentProgress, 0) as developmentProgress,
                cr.PlannedStartDate as plannedStartDate,
                cr.PlannedCompletionDate as plannedCompletionDate,
                cr.ActualStartDate as actualStartDate,
                cr.ActualCompletionDate as actualCompletionDate,
                cr.CreatedDate as createdDate,
                cr.ApprovedDate as approvedDate,
                crt.TypeName as typeName,
                crt.Description as typeDescription,
                ISNULL(crt.EstimatedDays, 0) as estimatedDays,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                d.DepartmentName as departmentName,
                CONCAT(approver.FirstName, ' ', approver.LastName) as approverName,
                CONCAT(developer.FirstName, ' ', developer.LastName) as developerName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
                LEFT JOIN Departments d ON cr.DepartmentID = d.DepartmentID
                LEFT JOIN Users approver ON cr.ApprovedBy = approver.UserID
                LEFT JOIN Users developer ON cr.AssignedToDevID = developer.UserID
            WHERE cr.RequestID = @requestId
        `;

        const getUpdatedRequestParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const updatedResult = await executeQuery(getUpdatedRequestQuery, getUpdatedRequestParams);

        context.log(`Successfully updated completion date for change request ${requestId}`);

        return {
            status: 200,
            jsonBody: {
                data: updatedResult.recordset[0]
            }
        };

    } catch (error) {
        context.error('Error in UpdateRequestedCompletionDate:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while updating the completion date'
                }
            }
        };
    }
}

app.http('UpdateRequestedCompletionDate', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/update-completion-date',
    handler: updateRequestedCompletionDate
}); 