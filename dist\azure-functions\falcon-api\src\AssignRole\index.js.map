{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/AssignRole/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAUA,gCAwFC;AAlGD,gDAAyF;AACzF,oFAA4E;AAC5E,mDAAgD;AAChD,mDAAkG,CAAC,oBAAoB;AACvH,mEAA4G;AAE5G,0BAA0B;AAC1B,mHAAmH;AACnH,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,qBAAqB;AAE3D,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,OAAO,CAAC,GAAG,CAAC,uDAAuD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QACnF,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE5C,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;QAC7F,CAAC;QAED,8EAA8E;QAC9E,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,6CAA6C,aAAa,IAAI,CAAC,CAAC;YAClI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yDAAyD,EAAE,EAAE,CAAC;QAC9G,CAAC;QAED,MAAM,mBAAmB,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACvB,kGAAkG;YAClG,eAAM,CAAC,KAAK,CAAC,6EAA6E,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;YACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,6EAA6E,EAAE,EAAE,CAAC;QACjI,CAAC;QACD,gFAAgF;QAChF,6EAA6E;QAC7E,6EAA6E;QAC7E,eAAM,CAAC,IAAI,CAAC,iCAAiC,mBAAmB,EAAE,CAAC,CAAC;QACpE,oBAAoB;QAEpB,4BAA4B;QAC5B,4BAA4B;QAC5B,MAAM,WAAW,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,yBAAyB;QAChF,IAAI,eAAe,GAAG,IAAA,mCAAe,EAAC,0CAAsB,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QACxG,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAC5C,wCAAwC;QACxC,MAAM,oBAAoB,GAAG,0CAAsB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8BAA8B;QACtG,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,mBAAmB;QAE/D,wBAAwB;QACxB,IAAI,UAAe,CAAC;QACpB,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;QACnF,CAAC;QAED,eAAe,GAAG,IAAA,mCAAe,EAAC,wCAAoB,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC7F,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAE5C,wCAAwC;QACxC,MAAM,aAAa,GAAG,wCAAoB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC7D,MAAM,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,mBAAmB;QACrD,0BAA0B;QAE1B,IAAI,CAAC;YACD,uDAAuD;YACvD,MAAM,OAAO,GAAG,MAAM,IAAA,wCAAgB,EAAC,MAAM,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;YAE5E,IAAI,OAAO,EAAE,CAAC;gBACV,eAAM,CAAC,IAAI,CAAC,0CAA0C,MAAM,aAAa,MAAM,cAAc,mBAAmB,GAAG,CAAC,CAAC;gBACrH,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE;iBACvD,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,sDAAsD;gBACtD,6CAA6C;gBAC7C,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;iBAClD,CAAC;YACN,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,sEAAsE;YACtE,eAAM,CAAC,KAAK,CAAC,oDAAoD,MAAM,UAAU,MAAM,cAAc,mBAAmB,GAAG,EAAE,KAAK,CAAC,CAAC;YACpI,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACN,OAAO,EAAE,wDAAwD;oBACjE,KAAK,EAAE,YAAY;iBACtB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW,EAAE,wCAAwC;IAChE,KAAK,EAAE,sBAAsB,EAAE,sBAAsB;IACrD,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}