# Zoho Desk Integration Guide for Falcon Portal

## Overview

This document outlines the integration of Zoho Desk with Falcon Portal's IT Hub, replacing the previous FreshService integration. Zoho Desk provides comprehensive help desk and customer support functionality that aligns perfectly with SASMOS Group's multi-company structure.

## Current Implementation Status

### ✅ Completed Components

#### 1. TypeScript Interfaces
- **ZohoDeskTicket**: Complete ticket data structure
- **ZohoDeskContact**: Customer/user contact management
- **ZohoDeskAgent**: Support staff and assignment
- **ZohoDeskDepartment**: Multi-department support
- **ZohoDeskCategory**: Service categorization
- **ZohoDeskThread**: Conversation management
- **ZohoDeskAttachment**: File handling

#### 2. API Service Layer
- **ZohoDeskAPI Class**: Comprehensive service layer with all endpoints
- **Authentication Framework**: OAuth 2.0 token management structure
- **CRUD Operations**: Create, read, update, delete for all entities
- **Search and Filtering**: Advanced query capabilities
- **Bulk Operations**: Multi-record operations support

#### 3. Data Transformation
- **convertZohoDeskTicket()**: Maps Zoho Desk data to internal UI format
- **convertZohoDeskThread()**: Transforms conversations to comments
- **Contact Resolution**: Links contacts to companies automatically

#### 4. UI Integration Framework
- **Enhanced ITTicketsPage**: Updated for Zoho Desk compatibility
- **Department Filtering**: Multi-department ticket routing
- **Status Mapping**: Zoho Desk status to internal status conversion
- **Priority Handling**: Urgent/High/Medium/Low priority support

### 🔄 In Progress

#### 1. OAuth Configuration
- OAuth app registration needed in Zoho Desk
- Client credentials configuration
- Redirect URL setup
- Scope permissions configuration

#### 2. Backend Token Management
- Server-side OAuth token exchange
- Token refresh handling
- Secure token storage
- Error handling and retry logic

### 📋 Pending Requirements

#### 1. Zoho Desk Setup
- Register OAuth application
- Configure organization settings
- Set up departments for SASMOS Group companies
- Create service categories and subcategories
- Import/create initial contacts and agents

## Integration Architecture

### API Endpoints Implemented

#### Tickets
```typescript
// Fetch tickets with filtering and pagination
GET /api/v1/tickets?limit=25&from=0&searchStr=keyword&status=Open&priority=High

// Create new ticket
POST /api/v1/tickets
{
  "subject": "Laptop Issue",
  "description": "Detailed description",
  "priority": "High",
  "departmentId": "it-support",
  "contactId": "contact123",
  "channel": "WEB"
}

// Update ticket
PATCH /api/v1/tickets/{ticketId}
{
  "status": "In Progress",
  "assigneeId": "agent123"
}

// Get ticket conversations
GET /api/v1/tickets/{ticketId}/threads
```

#### Departments
```typescript
// List all departments
GET /api/v1/departments

// Get department details
GET /api/v1/departments/{departmentId}
```

#### Contacts
```typescript
// Search contacts by email
GET /api/v1/contacts/search?email=<EMAIL>

// Create new contact
POST /api/v1/contacts
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "accountId": "sasmos-het"
}
```

#### Categories
```typescript
// Get categories for department
GET /api/v1/departments/{departmentId}/categories

// List all categories
GET /api/v1/categories
```

### Authentication Flow

#### OAuth 2.0 Implementation
```typescript
class ZohoDeskAPI {
  private static async getAuthToken(): Promise<string> {
    // Backend endpoint to get/refresh token
    const response = await fetch('/api/auth/zoho-desk-token', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    
    const { accessToken } = await response.json();
    return accessToken;
  }

  private static async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = await this.getAuthToken();
    
    const response = await fetch(`${ZOHO_DESK_CONFIG.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Zoho-oauthtoken ${token}`,
        'orgId': ZOHO_DESK_CONFIG.orgId,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Zoho Desk API Error: ${response.status}`);
    }

    return response.json();
  }
}
```

## Setup Requirements

### 1. Zoho Desk OAuth App Registration

#### Step 1: Create OAuth Application
1. Log in to Zoho Desk Admin Panel
2. Navigate to Setup → Developer Space → OAuth
3. Click "Add OAuth App"
4. Configure the following:
   - **App Name**: Falcon Portal Integration
   - **Client Type**: Web Application
   - **Redirect URLs**: 
     - `https://your-backend.com/api/auth/zoho-desk/callback`
     - `http://localhost:7071/api/auth/zoho-desk/callback` (development)

#### Step 2: Configure Scopes
Required OAuth scopes:
```
Desk.tickets.ALL
Desk.contacts.ALL
Desk.agents.READ
Desk.departments.READ
Desk.categories.READ
Desk.threads.ALL
Desk.attachments.READ
```

#### Step 3: Get Credentials
After registration, note down:
- **Client ID**: Used in OAuth authorization
- **Client Secret**: Used for token exchange
- **Organization ID**: Required for API calls

### 2. Environment Configuration

#### Backend Configuration
```env
# Zoho Desk OAuth
ZOHO_DESK_CLIENT_ID=your_client_id
ZOHO_DESK_CLIENT_SECRET=your_client_secret
ZOHO_DESK_REDIRECT_URI=https://your-backend.com/api/auth/zoho-desk/callback

# Zoho Desk API
ZOHO_DESK_ORG_ID=your_organization_id
ZOHO_DESK_BASE_URL=https://desk.zoho.in/api/v1
```

#### Frontend Configuration
```env
# React App Environment
REACT_APP_ZOHO_DESK_OAUTH_URL=https://accounts.zoho.in/oauth/v2/auth
REACT_APP_BACKEND_URL=http://localhost:7071
```

### 3. Department Structure Setup

#### SASMOS Group Departments
```json
{
  "departments": [
    {
      "name": "IT Support",
      "description": "Information Technology support for all companies",
      "companies": ["SASMOS Group", "SASMOS HET", "Avirata Defence Systems"]
    },
    {
      "name": "HR Support", 
      "description": "Human Resources support",
      "companies": ["SASMOS Group", "SASMOS HET", "Avirata Defence Systems"]
    },
    {
      "name": "Administrative Support",
      "description": "General administrative assistance",
      "companies": ["SASMOS Group"]
    }
  ]
}
```

#### Service Categories
```json
{
  "categories": [
    {
      "name": "Hardware Issues",
      "department": "IT Support",
      "subcategories": [
        "Laptop/Desktop Problems",
        "Printer Issues", 
        "Network Equipment",
        "Mobile Device Support"
      ]
    },
    {
      "name": "Software Support",
      "department": "IT Support", 
      "subcategories": [
        "Microsoft Office",
        "Email Issues",
        "Application Support",
        "License Management"
      ]
    },
    {
      "name": "Access & Security",
      "department": "IT Support",
      "subcategories": [
        "Password Reset",
        "File/Folder Access",
        "VPN Issues",
        "Security Incidents"
      ]
    }
  ]
}
```

## Implementation Guide

### Phase 1: OAuth Setup (Current Priority)

#### Backend OAuth Handler
Create Azure Function for OAuth token management:

```typescript
// /api/auth/zoho-desk/authorize
export async function authorizeZohoDesk(req: HttpRequest): Promise<HttpResponse> {
  const authUrl = `https://accounts.zoho.in/oauth/v2/auth?` +
    `response_type=code&` +
    `client_id=${process.env.ZOHO_DESK_CLIENT_ID}&` +
    `redirect_uri=${encodeURIComponent(process.env.ZOHO_DESK_REDIRECT_URI)}&` +
    `scope=Desk.tickets.ALL,Desk.contacts.ALL,Desk.agents.READ&` +
    `access_type=offline`;

  return { status: 302, headers: { 'Location': authUrl } };
}

// /api/auth/zoho-desk/callback  
export async function zohoDeskCallback(req: HttpRequest): Promise<HttpResponse> {
  const { code } = req.query;
  
  // Exchange code for access token
  const tokenResponse = await fetch('https://accounts.zoho.in/oauth/v2/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: process.env.ZOHO_DESK_CLIENT_ID,
      client_secret: process.env.ZOHO_DESK_CLIENT_SECRET,
      redirect_uri: process.env.ZOHO_DESK_REDIRECT_URI,
      code: code
    })
  });

  const tokens = await tokenResponse.json();
  
  // Store tokens securely (Azure Key Vault or encrypted database)
  await storeTokens(tokens);
  
  return { status: 200, body: { success: true } };
}
```

### Phase 2: API Integration

#### Enable Real API Calls
Update `ITTicketsPage.tsx` to use real Zoho Desk data:

```typescript
const loadAllData = async () => {
  setIsLoading(true);
  setError(null);

  try {
    // Replace mock data with real API calls
    const deptData = await ZohoDeskAPI.fetchDepartments();
    setDepartments(deptData);

    const agentData = await ZohoDeskAPI.fetchAgents();
    setAgents(agentData);

    const catData = await ZohoDeskAPI.fetchCategories();
    const groupedCategories = groupCategoriesByDepartment(catData);
    setCategories(groupedCategories);
    
  } catch (err) {
    console.error('Error loading Zoho Desk data:', err);
    setError('Failed to load data from Zoho Desk. Please check your configuration.');
  } finally {
    setIsLoading(false);
  }
};
```

### Phase 3: Contact Management

#### Auto-create Contacts
```typescript
const ensureContactExists = async (userEmail: string, userName: string, company: string): Promise<string> => {
  // Search for existing contact
  const existingContacts = await ZohoDeskAPI.searchContacts(userEmail);
  
  if (existingContacts.length > 0) {
    return existingContacts[0].id;
  }

  // Create new contact
  const [firstName, ...lastNameParts] = userName.split(' ');
  const lastName = lastNameParts.join(' ') || '';

  const newContact = await ZohoDeskAPI.createContact({
    firstName,
    lastName,
    email: userEmail,
    accountId: getAccountIdForCompany(company)
  });

  return newContact.id;
};
```

## Benefits of Zoho Desk Integration

### 1. Multi-Company Support
- **Department-based routing**: Route tickets to appropriate company departments
- **Company-specific agents**: Assign agents based on company affiliation
- **Unified reporting**: Cross-company analytics and reporting

### 2. Advanced Features
- **SLA Management**: Automatic SLA assignment and tracking
- **Automation**: Workflow automation for common ticket types
- **Knowledge Base**: Integrated help articles and FAQs
- **Customer Portal**: Self-service portal for end users

### 3. Integration Capabilities
- **Webhooks**: Real-time updates for ticket changes
- **Custom Fields**: Company-specific data fields
- **API Flexibility**: Extensive API for custom integrations
- **Bulk Operations**: Efficient mass operations

### 4. Reporting & Analytics
- **Performance Metrics**: Agent productivity and ticket resolution times
- **Customer Satisfaction**: CSAT surveys and feedback collection
- **Trend Analysis**: Historical data analysis and forecasting
- **Custom Reports**: Tailored reporting for management

## Next Steps

### Immediate Actions Required

1. **Register Zoho Desk OAuth App**
   - Set up OAuth application in Zoho Desk
   - Configure redirect URLs and scopes
   - Obtain client credentials

2. **Implement Backend OAuth Handler**
   - Create Azure Functions for OAuth flow
   - Implement token storage and refresh
   - Add error handling and security

3. **Configure Environment Variables**
   - Set up Zoho Desk credentials
   - Configure organization settings
   - Update frontend configuration

4. **Test API Connectivity**
   - Verify OAuth authentication flow
   - Test basic API operations
   - Validate data transformation

5. **Department and Category Setup**
   - Create departments for SASMOS companies
   - Set up service categories
   - Configure agent assignments

### Development Timeline

- **Week 1**: OAuth setup and authentication
- **Week 2**: API integration and testing
- **Week 3**: Contact management and automation
- **Week 4**: Advanced features and optimization

This comprehensive integration will provide SASMOS Group with a powerful, unified IT support system that scales across all companies while maintaining proper data separation and access control. 