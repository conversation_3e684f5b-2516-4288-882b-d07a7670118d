import * as sql from 'mssql';

const config: sql.config = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_SERVER || '',
    database: process.env.DB_DATABASE || '',
    options: {
        encrypt: true,
        trustServerCertificate: process.env.NODE_ENV === 'development' // Set NODE_ENV=development for local run if needed
    },
     pool: { // Optional pool configuration
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
     }
};

// Store the pool promise globally
let pool: sql.ConnectionPool | null = null;
let poolConnectPromise: Promise<sql.ConnectionPool> | null = null;

export const getPool = async (): Promise<sql.ConnectionPool> => {
    if (pool && pool.connected) {
        return pool;
    }
    // If connection is in progress, wait for it
    if (poolConnectPromise) {
        return poolConnectPromise;
    }

    // Create a new connection promise
    poolConnectPromise = new Promise(async (resolve, reject) => {
        try {
            console.log(`Attempting to connect to DB: ${config.server}/${config.database} as ${config.user}`);
            pool = new sql.ConnectionPool(config);
            await pool.connect();
            console.log("DB Connection Pool Established.");
            // Clear the promise once connected
            poolConnectPromise = null;
            resolve(pool);
        } catch (err) {
            console.error('Database Connection Failed!', err);
            pool = null; // Reset pool on error
            poolConnectPromise = null; // Reset promise on error
            reject(err);
        }
    });

    return poolConnectPromise;
};

// Define the structure for a parameter object
export interface QueryParameter {
    name: string;
    type: sql.ISqlTypeFactoryWithNoParams | sql.ISqlTypeFactoryWithLength | sql.ISqlTypeFactoryWithPrecisionScale | sql.ISqlTypeFactoryWithScale | sql.ISqlTypeFactoryWithTvpType;
    value: any;
}

// Updated function signature to expect an array of QueryParameter objects
export async function executeQuery(query: string, params?: QueryParameter[]): Promise<sql.IResult<any>> {
    const currentPool = await getPool(); // Ensure pool is connected
    const request = currentPool.request(); // Get a request object from the pool

    if (params) {
        // Use the type provided in the params object, don't try to infer
        for (const param of params) { 
            if (param.name && param.type) { // Check for required properties, allow null/undefined values
                request.input(param.name, param.type, param.value);
             } else {
                // Log a warning if a parameter object is incomplete
                console.warn(`Skipping invalid parameter object in executeQuery: ${JSON.stringify(param)}`);
             }
        }
    }
    try {
         const result = await request.query(query);
         return result;
    } catch(err) {
         console.error('SQL Error executing query:', query, 'Params:', params, 'Error:', err);
         throw err; // Re-throw to be handled by the calling Function
    }
}

// Optional: Close pool on process exit (useful for local dev)
process.on('exit', async () => {
    if (pool) {
         console.log("Closing DB connection pool...");
        await pool.close();
         console.log("DB connection pool closed.");
    }
});
