import { AzureFunction, Context, HttpRequest } from '@azure/functions';
import { getPool } from '../shared/db';
import * as sql from 'mssql';

const httpTrigger: AzureFunction = async function (context: Context, req: HttpRequest): Promise<void> {
  try {
    const page = parseInt(req.query?.page as string) || 1;
    const pageSize = parseInt(req.query?.pageSize as string) || 10;
    const search = req.query?.search as string || '';
    const type = req.query?.type as string || '';

    const pool = await getPool();
    let where = 'WHERE IsActive = 1';
    const params: { name: string; type: any; value: any }[] = [];

    if (search) {
      where += ' AND (Title LIKE @search OR Description LIKE @search OR Tags LIKE @search)';
      params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
    }
    if (type) {
      where += ' AND Type = @type';
      params.push({ name: 'type', type: sql.NVarChar, value: type });
    }

    const offset = (page - 1) * pageSize;
    const query = `
      SELECT * FROM Policies
      ${where}
      ORDER BY LastUpdated DESC
      OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY;
      SELECT COUNT(*) as total FROM Policies ${where};
    `;

    const request = pool.request();
    params.forEach(p => request.input(p.name, p.type, p.value));
    request.input('offset', sql.Int, offset);
    request.input('pageSize', sql.Int, pageSize);

    const result = await request.query(query);
    const policies = result.recordsets[0];
    const total = result.recordsets[1][0]?.total || 0;

    context.res = {
      status: 200,
      body: {
        policies,
        total,
        page,
        pageSize
      }
    };
  } catch (err) {
    context.log.error('GetPolicies error:', err);
    context.res = {
      status: 500,
      body: { error: 'Failed to fetch policies.' }
    };
  }
};

export default httpTrigger; 