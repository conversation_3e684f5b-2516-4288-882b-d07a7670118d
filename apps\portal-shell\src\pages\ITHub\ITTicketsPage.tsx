import React, { useState, useEffect } from 'react';
import { Search, Plus, AlertCircle, CheckCircle, Clock, X, User, UserCheck, Tag, MessageSquare, Info } from 'feather-icons-react';
import { useCurrentUser } from '../../services/userContext';
import { useSearchParams } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { loginRequest } from '../../authConfig';
import type { IPublicClientApplication, AccountInfo } from '@azure/msal-browser';

// Actual Zoho Desk API response interface (different from our internal interface)
interface ZohoDeskAPITicket {
  id: string;
  ticketNumber: string;
  subject: string;
  status: string;
  createdTime: string;
  priority: string | null;
  channel: string;
  dueDate?: string;
  departmentId: string;
  contactId: string;
  assigneeId?: string;
  categoryId?: string;
  subCategory?: string;
  email?: string;
  webUrl?: string;
  attachments?: Array<{ name: string; [key: string]: unknown }>;
  resolution?: string;
  modifiedTime?: string;
  [key: string]: unknown; // For any additional fields
}

// Zoho Desk API interfaces
interface ZohoDeskTicket {
  id: string;
  ticketNumber: string;
  subject: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  status: 'Open' | 'In Progress' | 'On Hold' | 'Escalated' | 'Closed';
  departmentId: string;
  categoryId?: string;
  subCategory?: string;
  contactId: string;
  assigneeId?: string;
  createdTime: string;
  modifiedTime: string;
  dueDate?: string;
  resolution?: string;
  attachments: ZohoDeskAttachment[];
  threadCount?: number;
  // Additional Zoho Desk specific fields
  channel: 'EMAIL' | 'PHONE' | 'CHAT' | 'WEB' | 'TWITTER' | 'FACEBOOK';
  classification?: string;
  productId?: string;
  layoutId?: string;
  webUrl?: string;
  isSpam?: boolean;
  tagCount?: number;
  threadAccessCount?: number;
  isArchived?: boolean;
}

interface ZohoDeskContact {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  mobile?: string;
  title?: string;
  description?: string;
  accountId?: string;
  // Company/Account information
  account?: {
    id: string;
    accountName: string;
  };
}

interface ZohoDeskAgent {
  id: string;
  firstName: string;
  lastName: string;
  emailId: string;
  roleId: string;
  profileId: string;
  isConfirmed: boolean;
  isEnabled: boolean;
  departmentIds: string[];
}

interface ZohoDeskDepartment {
  id: string;
  name: string;
  description?: string;
  isEnabled: boolean;
  chatStatus?: string;
  creatorId?: string;
  associatedAgentIds?: string[];
}

interface ZohoDeskCategory {
  id: string;
  name: string;
  description?: string;
  isEnabled: boolean;
  departmentId: string;
}

interface ZohoDeskThread {
  id: string;
  type: 'thread' | 'comment';
  direction?: 'in' | 'out'; // Only for threads
  summary?: string; // Only for threads
  content?: string; // For comments and some threads
  contentType: string;
  isForward?: boolean; // Only for threads
  channel?: string; // Only for threads
  createdTime?: string; // For threads
  commentedTime?: string; // For comments
  isPublic?: boolean; // For comments
  author?: {
    id: string;
    name: string;
    email?: string;
    emailId?: string;
    type: 'AGENT' | 'CONTACT' | 'SYSTEM' | 'END_USER';
    firstName?: string;
    lastName?: string;
  };
  commenter?: {
    id: string;
    name: string;
    email?: string;
    type: 'AGENT' | 'CONTACT' | 'SYSTEM' | 'END_USER';
    firstName?: string;
    lastName?: string;
    photoURL?: string;
    roleName?: string;
  };
  attachments?: ZohoDeskAttachment[];
}

interface ZohoDeskAttachment {
  id: string;
  name: string;
  size: number;
  href: string;
  contentType: string;
}

// Internal interfaces for UI
interface ITTicket {
  id: string;
  ticketNumber: string;
  subject: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Urgent';
  status: 'Open' | 'In Progress' | 'On Hold' | 'Escalated' | 'Closed';
  category: {
    id: string;
    name: string;
  };
  subcategory?: {
    id: string;
    name: string;
  };
  assignedTo?: {
    name: string;
    email: string;
  };
  requestedBy: {
    name: string;
    email: string;
    company: string;
  };
  createdDate: string;
  lastUpdated: string;
  dueDate?: string;
  resolution?: string;
  attachments: string[];
  comments: ITTicketComment[];
  timeline: ITTicketTimelineEvent[];
  // Zoho Desk specific fields
  channel: string;
  webUrl?: string;
  departmentName?: string;
}

interface ITTicketComment {
  id: string;
  author: {
    name: string;
    email: string;
    type: 'AGENT' | 'CONTACT' | 'SYSTEM';
  };
  message: string;
  isInternal: boolean;
  createdDate: string;
  direction: 'IN' | 'OUT';
}

interface ITCategory {
  id: string;
  name: string;
  departmentId: string;
  subcategories: ITSubcategory[];
}

interface ITSubcategory {
  id: string;
  name: string;
  categoryId?: string;
}

interface ITTicketTimelineEvent {
  id: string;
  type: 'comment' | 'status_change' | 'assignment' | 'created' | 'updated' | 'escalated' | 'priority_change';
  author: {
    name: string;
    email: string;
    type: 'AGENT' | 'CONTACT' | 'SYSTEM';
  };
  description: string;
  timestamp: string;
  details?: {
    from?: string;
    to?: string;
    reason?: string;
    comment?: string;
  };
}

// Add interface for flattened category
interface FlatCategory {
  id: string;
  name: string;
  departmentId: string;
}

// Zoho Desk API Functions - Updated to use backend
class ZohoDeskAPI {
  // Add MSAL instance and account as static variables
  private static msalInstance: IPublicClientApplication | null = null;
  private static msalAccount: AccountInfo | null = null;

  // Method to set MSAL instance and account
  static setMsalContext(instance: IPublicClientApplication, account: AccountInfo) {
    this.msalInstance = instance;
    this.msalAccount = account;
  }

  private static async makeBackendRequest(endpoint: string, options: RequestInit = {}): Promise<unknown> {
    const url = `/api/zoho-desk/${endpoint}`;
    console.log(`ZohoDeskAPI.makeBackendRequest: Making request to ${url}`);
    
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Try to get MSAL token if available
    if (this.msalInstance && this.msalAccount) {
      try {
        const tokenRequest = {
          ...loginRequest,
          account: this.msalAccount,
        };
        
        const response = await this.msalInstance.acquireTokenSilent(tokenRequest);
        if (response.idToken) {
          defaultHeaders['Authorization'] = `Bearer ${response.idToken}`;
          console.log('ZohoDeskAPI: Added MSAL ID token to request');
        } else {
          console.warn('ZohoDeskAPI: No ID token received from MSAL');
        }
      } catch (tokenError) {
        console.warn('ZohoDeskAPI: Failed to acquire token silently:', tokenError);
        // Continue without token - backend will handle appropriately
      }
    } else {
      console.warn('ZohoDeskAPI: No MSAL context available, continuing without token');
    }

    try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

      console.log(`ZohoDeskAPI.makeBackendRequest: Response status: ${response.status}`);

    if (!response.ok) {
        const errorText = await response.text();
        console.error(`ZohoDeskAPI.makeBackendRequest: HTTP error ${response.status}:`, errorText);
        throw new Error(`Backend API Error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      console.log(`ZohoDeskAPI.makeBackendRequest: Response data:`, responseData);
      return responseData;
    } catch (error) {
      console.error(`ZohoDeskAPI.makeBackendRequest: Request failed:`, error);
      throw error;
    }
  }

  static async fetchTickets(params: {
    limit?: number;
    from?: number;
    sortBy?: string;
    searchStr?: string;
    status?: string;
    priority?: string;
    departmentId?: string;
  } = {}): Promise<{ data: ZohoDeskAPITicket[], hasMore: boolean }> {
    const queryParams = new URLSearchParams();
    
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.from) queryParams.append('from', params.from.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.searchStr) queryParams.append('searchStr', params.searchStr);
    if (params.status) queryParams.append('status', params.status);
    if (params.priority) queryParams.append('priority', params.priority);
    if (params.departmentId) queryParams.append('departmentId', params.departmentId);

    const endpoint = `tickets?${queryParams.toString()}`;
    const response = await this.makeBackendRequest(endpoint) as { data?: ZohoDeskAPITicket[], hasMore?: boolean };
    
    return {
      data: response.data || [],
      hasMore: response.hasMore || false
    };
  }

  static async createTicket(ticketData: {
    subject: string;
    description: string;
    priority: string;
    departmentId: string | number; // Accept both string and number
    categoryId?: string;
    subCategory?: string;
    contactId: string;
    channel?: string;
    userEmail?: string;
    userName?: string;
  }): Promise<ZohoDeskTicket> {
    const response = await this.makeBackendRequest('tickets', {
      method: 'POST',
      body: JSON.stringify({
        ...ticketData,
        channel: ticketData.channel || 'WEB',
        status: 'Open'
      }),
    }) as ZohoDeskTicket;
    
    return response;
  }

  static async fetchDepartments(): Promise<ZohoDeskDepartment[]> {
    const response = await this.makeBackendRequest('departments') as { data?: ZohoDeskDepartment[] };
    return response.data || [];
  }

  static async fetchCategories(): Promise<ZohoDeskCategory[]> {
    const endpoint = 'categories'; // Use categories endpoint which now fetches from Zoho Desk Products API
    const response = await this.makeBackendRequest(endpoint);
    
    console.log('ZohoDeskAPI.fetchCategories: Raw response:', response);
    
    // Handle direct array response from backend (new format)
    if (Array.isArray(response)) {
      console.log(`ZohoDeskAPI.fetchCategories: Found ${response.length} categories from Zoho Desk`);
      return response.map((category: { id: string; name: string; description?: string; isEnabled?: boolean; departmentId?: string }) => ({
        id: category.id,
        name: category.name,
        description: category.description || category.name,
        isEnabled: category.isEnabled !== false,
        departmentId: category.departmentId || 'it-support'
      }));
    }
    
    // Handle object response with data property (fallback)
    const responseObj = response as { data?: ITCategory[] };
    if (responseObj.data) {
      console.log(`ZohoDeskAPI.fetchCategories: Found ${responseObj.data.length} categories in data property`);
      return responseObj.data.map(category => ({
        id: category.id,
        name: category.name,
        description: category.name,
        isEnabled: true,
        departmentId: category.departmentId
      }));
    }
    
    console.log('ZohoDeskAPI.fetchCategories: No categories found, returning empty array');
    return [];
  }

  static async fetchSubcategories(): Promise<{ id: string; name: string; categoryId: string; isEnabled: boolean }[]> {
    const endpoint = 'subcategories'; // Use subcategories endpoint (not products)
    const response = await this.makeBackendRequest(endpoint);
    
    console.log('ZohoDeskAPI.fetchSubcategories: Raw response:', response);
    
    // Handle direct array response from backend
    if (Array.isArray(response)) {
      console.log(`ZohoDeskAPI.fetchSubcategories: Found ${response.length} subcategories from Zoho Desk`);
      return response.map((subcategory: { id: string; name: string; categoryId?: string; isEnabled?: boolean }) => ({
        id: subcategory.id,
        name: subcategory.name,
        categoryId: subcategory.categoryId || 'general',
        isEnabled: subcategory.isEnabled !== false
      }));
    }
    
    console.log('ZohoDeskAPI.fetchSubcategories: No subcategories found, returning empty array');
    return [];
  }

  static async initializeOAuth(): Promise<string> {
    const headers: Record<string, string> = {};

    // Try to get MSAL token if available
    if (this.msalInstance && this.msalAccount) {
      try {
        const tokenRequest = {
          ...loginRequest,
          account: this.msalAccount,
        };
        
        const response = await this.msalInstance.acquireTokenSilent(tokenRequest);
        if (response.idToken) {
          headers['Authorization'] = `Bearer ${response.idToken}`;
          console.log('ZohoDeskAPI.initializeOAuth: Added MSAL ID token to request');
        }
      } catch (tokenError) {
        console.warn('ZohoDeskAPI.initializeOAuth: Failed to acquire token silently:', tokenError);
      }
    }

    const response = await fetch('/api/auth/zoho-desk/authorize', {
      headers: headers
    });

    if (!response.ok) {
      throw new Error('Failed to initialize OAuth');
    }

    const { authUrl } = await response.json();
    return authUrl;
  }

  static async fetchContacts(): Promise<ZohoDeskContact[]> {
    const response = await this.makeBackendRequest('contacts') as { data: ZohoDeskContact[] };
    return response.data || [];
  }

  static async fetchTicketThreads(ticketId: string): Promise<ZohoDeskThread[]> {
    console.log(`ZohoDeskAPI.fetchTicketThreads: Fetching conversations for ticket ${ticketId}`);
    try {
      // Use conversations endpoint to get only public threads/conversations
      const response = await this.makeBackendRequest(`tickets/${ticketId}/conversations`);
      console.log(`ZohoDeskAPI.fetchTicketThreads: Raw API response:`, response);
      
      // Handle different response formats
      let threads: ZohoDeskThread[] = [];
      
      if (Array.isArray(response)) {
        // Direct array response
        threads = response as ZohoDeskThread[];
      } else if (response && typeof response === 'object') {
        const responseObj = response as Record<string, unknown>;
        // Check for data property first
        if ('data' in responseObj && Array.isArray(responseObj.data)) {
          threads = responseObj.data as ZohoDeskThread[];
        } else if ('threads' in responseObj && Array.isArray(responseObj.threads)) {
          threads = responseObj.threads as ZohoDeskThread[];
        } else {
          console.warn(`ZohoDeskAPI.fetchTicketThreads: Unexpected response format:`, response);
          return [];
        }
      }
      
      console.log(`ZohoDeskAPI.fetchTicketThreads: Found ${threads.length} threads`);
      threads.forEach((thread, index) => {
        console.log(`Thread ${index + 1}:`, {
          id: thread.id,
          direction: thread.direction,
          content: thread.content?.substring(0, 100) + '...',
          author: thread.author,
          createdTime: thread.createdTime
        });
      });
      
      return threads;
    } catch (error) {
      console.error(`ZohoDeskAPI.fetchTicketThreads: Error fetching threads:`, error);
      throw error;
    }
  }

  static async fetchAgents(): Promise<ZohoDeskAgent[]> {
    const response = await this.makeBackendRequest('agents') as { data: ZohoDeskAgent[] };
    return response.data || [];
  }

  static async addTicketComment(ticketId: string, commentData: {
    content: string;
    contentType: string;
    isPublic: boolean;
  }): Promise<ZohoDeskThread> {
    console.log(`ZohoDeskAPI.addTicketComment: Adding comment to ticket ${ticketId}`, commentData);
    const response = await this.makeBackendRequest(`tickets/${ticketId}/comments`, {
      method: 'POST',
      body: JSON.stringify(commentData),
    }) as ZohoDeskThread;
    
    console.log(`ZohoDeskAPI.addTicketComment: Response:`, response);
    return response;
  }
}

// Helper function to convert Zoho Desk ticket to internal format
const convertZohoDeskTicket = (
  zohoDeskTicket: ZohoDeskAPITicket,
  contacts: ZohoDeskContact[],
  agents: ZohoDeskAgent[],
  departments: ZohoDeskDepartment[],
  categories: FlatCategory[] = [],
  currentUser?: { name: string; email: string; company?: string } | null
): ITTicket => {
  const contact = contacts.find(c => c.id === zohoDeskTicket.contactId);
  const assignee = agents.find(a => a.id === zohoDeskTicket.assigneeId);
  const department = departments.find(d => d.id === zohoDeskTicket.departmentId);
  
  // Handle both categoryId and productId (Zoho Desk uses productId for categories)
  const categoryId = zohoDeskTicket.categoryId || (zohoDeskTicket as ZohoDeskAPITicket & { productId?: string }).productId;
  const category = categories.find(c => c.id === categoryId);

  // Handle missing or null fields from actual Zoho Desk API
  const priority = zohoDeskTicket.priority || 'Medium'; // Default to Medium if null
  const description = (zohoDeskTicket as { description?: string }).description || zohoDeskTicket.subject || 'No description available';
  const lastUpdated = zohoDeskTicket.modifiedTime || zohoDeskTicket.createdTime;
  
  // Better category and subcategory handling
  let categoryInfo = { id: '', name: 'Uncategorized' };
  let subcategoryInfo = undefined;
  
  if (category) {
    categoryInfo = { id: category.id, name: category.name };
  } else {
    // If no category found, don't use department name as category
    categoryInfo = { id: '', name: 'Uncategorized' };
  }
  
  // Handle subcategory - it could be a separate category or a string, or classification field
  const subcategoryValue = zohoDeskTicket.subCategory || (zohoDeskTicket as ZohoDeskAPITicket & { classification?: string }).classification;
  if (subcategoryValue) {
    // Try to find it as a category first
    const subCat = categories.find(c => c.name === subcategoryValue || c.id === subcategoryValue);
    if (subCat) {
      subcategoryInfo = { id: subCat.id, name: subCat.name };
    } else {
      // Use the string value directly
      subcategoryInfo = { id: subcategoryValue, name: subcategoryValue };
    }
  }
  
  return {
    id: zohoDeskTicket.id,
    ticketNumber: zohoDeskTicket.ticketNumber,
    subject: zohoDeskTicket.subject,
    description: description,
    priority: priority as ITTicket['priority'],
    status: zohoDeskTicket.status as ITTicket['status'],
    category: categoryInfo,
    subcategory: subcategoryInfo,
    assignedTo: assignee ? {
      name: `${assignee.firstName} ${assignee.lastName}`,
      email: assignee.emailId
    } : undefined,
    requestedBy: {
      name: contact ? `${contact.firstName || ''} ${contact.lastName || ''}`.trim() : 
            (currentUser?.name || zohoDeskTicket.email?.split('@')[0] || 'Unknown'),
      email: contact?.email || zohoDeskTicket.email || currentUser?.email || '<EMAIL>',
      company: contact?.account?.accountName || department?.name || currentUser?.company || 'Unknown Company'
    },
    createdDate: zohoDeskTicket.createdTime,
    lastUpdated: lastUpdated,
    dueDate: zohoDeskTicket.dueDate,
    resolution: zohoDeskTicket.resolution,
    attachments: zohoDeskTicket.attachments?.map(att => att.name) || [],
    channel: zohoDeskTicket.channel,
    webUrl: zohoDeskTicket.webUrl,
    departmentName: department?.name || 'Unknown Department',
    comments: [], // Will be populated separately when viewing ticket details
    timeline: [] // Will be populated separately when viewing ticket details
  };
};

// Helper function to convert Zoho Desk thread to internal comment format
const convertZohoDeskThread = (thread: ZohoDeskThread): ITTicketComment => {
  console.log('Converting thread:', {
    id: thread.id,
    type: thread.type,
    summary: thread.summary,
    content: thread.content,
    contentLength: thread.content?.length || 0,
    authorName: thread.author?.name || thread.commenter?.name,
    direction: thread.direction
  });
  
  // Use content first, then summary as fallback, then a default message
  let message = thread.content || thread.summary || '';
  
  // Get author info from either author or commenter field
  const authorInfo = thread.author || thread.commenter;
  if (!authorInfo) {
    console.warn('No author or commenter found for thread:', thread.id);
  return {
      id: thread.id,
      author: { name: 'Unknown', email: '', type: 'SYSTEM' },
      message: message || 'No content available',
      isInternal: false,
      createdDate: thread.createdTime || thread.commentedTime || new Date().toISOString(),
      direction: 'IN'
    };
  }
  
  // If still empty, create a meaningful message based on type and direction
  if (!message.trim()) {
    if (thread.type === 'comment') {
      message = `Comment (content not available)`;
    } else {
      message = thread.direction === 'in' 
        ? `Customer message (content not available)` 
        : `Agent message (content not available)`;
    }
  }
  
  const converted = {
    id: thread.id,
    author: {
      name: authorInfo.name,
      email: authorInfo.email || ('emailId' in authorInfo ? authorInfo.emailId : '') || '',
      type: authorInfo.type === 'END_USER' ? 'CONTACT' as const : authorInfo.type as 'AGENT' | 'CONTACT' | 'SYSTEM'
    },
    message: message,
    isInternal: thread.type === 'comment' ? !thread.isPublic : (thread.direction === 'out' && authorInfo.type === 'AGENT'),
    createdDate: thread.createdTime || thread.commentedTime || new Date().toISOString(),
    direction: thread.direction ? thread.direction.toUpperCase() as 'IN' | 'OUT' : (thread.type === 'comment' ? 'IN' : 'OUT')
  };
  
  console.log('Converted to comment:', {
    id: converted.id,
    author: converted.author.name,
    messageLength: converted.message.length,
    messagePreview: converted.message.substring(0, 50) + '...'
  });
  
  return converted;
};



// Helper function to generate timeline events
const generateTicketTimeline = (ticket: ITTicket, comments: ITTicketComment[]): ITTicketTimelineEvent[] => {
  const events: ITTicketTimelineEvent[] = [];

  // Ticket created event
  events.push({
    id: `created-${ticket.id}`,
    type: 'created',
    author: { ...ticket.requestedBy, type: 'CONTACT' as const },
    description: `Ticket created by ${ticket.requestedBy.name}`,
    timestamp: ticket.createdDate,
    details: {
      comment: ticket.description
    }
  });

  // Convert comments to timeline events
  comments.forEach(comment => {
    events.push({
      id: `comment-${comment.id}`,
      type: 'comment' as const,
      author: comment.author,
      description: comment.direction === 'IN' ? 'Customer replied' : 'Agent replied',
      timestamp: comment.createdDate,
      details: {
        comment: comment.message
      }
    });
  });

  // Add status change events (if we can infer them)
  if (ticket.assignedTo) {
    events.push({
      id: `assigned-${ticket.id}`,
      type: 'assignment',
      author: { name: 'System', email: '<EMAIL>', type: 'SYSTEM' },
      description: `Ticket assigned to ${ticket.assignedTo.name}`,
      timestamp: ticket.lastUpdated,
      details: {
        to: ticket.assignedTo.name
      }
    });
  }

  // Sort events by timestamp
  return events.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
};

// Helper function to get default subcategories for a category
const getDefaultSubcategories = (categoryId: string): ITSubcategory[] => {
  const subcategoriesMap: Record<string, ITSubcategory[]> = {
    'application': [
      { id: 'data-sync-issue', name: 'Data Sync Issue', categoryId: 'application' },
      { id: 'application-error', name: 'Application Error', categoryId: 'application' },
      { id: 'performance-issue', name: 'Performance Issue', categoryId: 'application' },
      { id: 'feature-request', name: 'Feature Request', categoryId: 'application' }
    ],
    'hardware': [
      { id: 'laptop-issue', name: 'Laptop Issue', categoryId: 'hardware' },
      { id: 'printer-issue', name: 'Printer Issues', categoryId: 'hardware' },
      { id: 'phone', name: 'Phone/Mobile Device', categoryId: 'hardware' },
      { id: 'accessories', name: 'Accessories & Peripherals', categoryId: 'hardware' }
    ],
    'network': [
      { id: 'connectivity', name: 'Connectivity', categoryId: 'network' },
      { id: 'wifi', name: 'WiFi/Wireless Issues', categoryId: 'network' },
      { id: 'vpn', name: 'VPN Access', categoryId: 'network' },
      { id: 'internet', name: 'Internet Connectivity', categoryId: 'network' }
    ],
    'software': [
      { id: 'os', name: 'Operating System', categoryId: 'software' },
      { id: 'email', name: 'Email Issues', categoryId: 'software' },
      { id: 'office', name: 'Microsoft Office', categoryId: 'software' },
      { id: 'software-install', name: 'Software Installation', categoryId: 'software' }
    ],
    'security': [
      { id: 'access', name: 'Account Access', categoryId: 'security' },
      { id: 'permissions', name: 'Permissions & Rights', categoryId: 'security' },
      { id: 'password', name: 'Password Reset', categoryId: 'security' },
      { id: 'mfa', name: 'Multi-Factor Authentication', categoryId: 'security' }
    ]
  };
  
  return subcategoriesMap[categoryId] || [];
};

// Email domain to department mapping function
const getDepartmentByEmailDomain = (email: string, departments: ZohoDeskDepartment[]): ZohoDeskDepartment | null => {
  if (!email || !email.includes('@')) {
    return null;
  }

  const domain = email.split('@')[1].toLowerCase();
  
  // Define email domain to department mapping
  const domainToDepartmentMap: { [key: string]: string[] } = {
    // Avirata Defence Systems
    'aviratadefsys.com': ['Avirata Defence Systems', 'Avirata', 'Defence Systems'],
    
    // SASMOS HET Technologies Ltd
    'sasmos.com': ['SASMOS HET Technologies Ltd', 'SASMOS', 'HET Technologies'],
    
    // FESIL
    'fe-sil.com': ['FESIL'],
    
    // Westwire Harnessing Ltd
    'westwireharnessing.co.uk': ['Westwire Harnessing Ltd', 'Westwire', 'Harnessing'],
    
    // LiDER Technologies Limited
    'lider.tech': ['LiDER Technologies Limited', 'LiDER', 'Technologies'],
    
    // Development fallback
    'falconhub.dev': ['IT Support', 'General Support', 'Development'],
  };

  // Find matching department names for the domain
  const possibleDepartmentNames = domainToDepartmentMap[domain];
  if (!possibleDepartmentNames) {
    console.warn(`No department mapping found for domain: ${domain}`);
    return null;
  }

  // Find the first matching department from the available departments
  for (const possibleName of possibleDepartmentNames) {
    const matchingDepartment = departments.find(dept => 
      dept.name.toLowerCase().includes(possibleName.toLowerCase()) ||
      possibleName.toLowerCase().includes(dept.name.toLowerCase())
    );
    
    if (matchingDepartment) {
      console.log(`Mapped email domain "${domain}" to department: ${matchingDepartment.name} (ID: ${matchingDepartment.id})`);
      return matchingDepartment;
    }
  }

  console.warn(`No matching department found for domain "${domain}" in available departments:`, departments.map(d => d.name));
  return null;
};

const ITTicketsPage: React.FC = () => {
  // Get current user for personalized filtering
  const { user: currentUser } = useCurrentUser();
  const { accounts, instance } = useMsal();
  
  // Set MSAL context for ZohoDeskAPI
  useEffect(() => {
    if (accounts.length > 0 && instance) {
      ZohoDeskAPI.setMsalContext(instance, accounts[0]);
    }
  }, [accounts, instance]);
  
  // State management
  const [tickets, setTickets] = useState<ITTicket[]>([]);
  const [categories, setCategories] = useState<ITCategory[]>([]);
  const [departments, setDepartments] = useState<ZohoDeskDepartment[]>([]);
  const [agents, setAgents] = useState<ZohoDeskAgent[]>([]);
  const [contacts, setContacts] = useState<ZohoDeskContact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const [assigneeFilter, setAssigneeFilter] = useState<string>('all');
  const [departmentFilter] = useState<string>('all');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(25); // Zoho Desk supports up to 100 per page

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<ITTicket | null>(null);
  const [isLoadingThreads, setIsLoadingThreads] = useState(false);
  const [ticketTimeline, setTicketTimeline] = useState<ITTicketTimelineEvent[]>([]);
  const [activeTab, setActiveTab] = useState<'comments' | 'timeline'>('comments');
  const [newComment, setNewComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [expandedTimelineEvents, setExpandedTimelineEvents] = useState<Set<string>>(new Set());

  // URL parameter handling
  const [searchParams, setSearchParams] = useSearchParams();

  // Create ticket form state
  const [createTicketData, setCreateTicketData] = useState({
    subject: '',
    description: '',
    priority: 'Medium' as ITTicket['priority'],
    departmentId: ''
  });

  // Load initial data
  useEffect(() => {
    loadAllData();
  }, []);

  // Handle URL parameters for auto-opening create modal
  useEffect(() => {
    const action = searchParams.get('action');
    if (action === 'create') {
      setIsCreateModalOpen(true);
      // Clean up URL parameter after opening modal
      setSearchParams(new URLSearchParams());
    }
  }, [searchParams, setSearchParams]);

  const loadAllData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load departments first (required for tickets)
      const deptData = await ZohoDeskAPI.fetchDepartments();
      setDepartments(deptData);
      console.log('Loaded departments:', deptData.length);

      // Load categories and subcategories
      const catResponse = await ZohoDeskAPI.fetchCategories();
      console.log('Raw categories response:', catResponse);
      
      // Load subcategories
      let subcategoriesData: { id: string; name: string; categoryId: string; isEnabled: boolean }[] = [];
      try {
        subcategoriesData = await ZohoDeskAPI.fetchSubcategories();
        console.log('Raw subcategories response:', subcategoriesData);
      } catch (subcatError) {
        console.warn('Failed to load subcategories:', subcatError);
      }
      
      // Convert ZohoDeskCategory[] to ITCategory[] format with real subcategories
      if (Array.isArray(catResponse)) {
        const convertedCategories: ITCategory[] = catResponse.map(cat => {
          // Find real subcategories for this category
          const realSubcategories = subcategoriesData
            .filter(sub => sub.categoryId === cat.id && sub.isEnabled)
            .map(sub => ({ id: sub.id, name: sub.name, categoryId: cat.id }));
          
          // If no real subcategories found, use default ones
          const subcategories = realSubcategories.length > 0 
            ? realSubcategories 
            : getDefaultSubcategories(cat.id);
          
          return {
            id: cat.id,
            name: cat.name,
            departmentId: cat.departmentId,
            subcategories: subcategories
          };
        });
        setCategories(convertedCategories);
        console.log('Loaded categories with subcategories:', convertedCategories.map(c => ({ 
          name: c.name, 
          subcategoryCount: c.subcategories.length 
        })));
      } else {
        console.warn('Unexpected categories response format:', catResponse);
        setCategories([]);
      }

      // Load contacts
      let contactsData: ZohoDeskContact[] = [];
      try {
        contactsData = await ZohoDeskAPI.fetchContacts();
        setContacts(contactsData);
        console.log('Loaded contacts:', contactsData.length);
      } catch (contactError) {
        console.warn('Failed to load contacts:', contactError);
        setContacts([]);
      }

      // Load agents
      let agentsData: ZohoDeskAgent[] = [];
      try {
        agentsData = await ZohoDeskAPI.fetchAgents();
        setAgents(agentsData);
        console.log('Loaded agents:', agentsData.length);
      } catch (agentError) {
        console.warn('Failed to load agents:', agentError);
        setAgents([]);
      }

      // Now load tickets, using the freshly loaded contacts and agents
      await loadTicketsDirectly(contactsData, agentsData, deptData);
      setIsLoading(false);
      console.info('Successfully loaded all data from Zoho Desk');
    } catch (err) {
      console.error('Error loading core data from Zoho Desk:', err);
      setError('Failed to load some data from Zoho Desk, but tickets should still work.');
      
      // Set up basic demo data structure for development
      setDepartments([
        { id: 'it-support', name: 'IT Support', isEnabled: true },
        { id: 'network', name: 'Network', isEnabled: true }
      ]);
      setCategories([
        { 
          id: 'hardware', 
          name: 'Hardware', 
          departmentId: 'it-support', 
          subcategories: [
            { id: 'laptop', name: 'Laptop Issues', categoryId: 'hardware' },
            { id: 'desktop', name: 'Desktop Issues', categoryId: 'hardware' },
            { id: 'printer', name: 'Printer Issues', categoryId: 'hardware' },
            { id: 'monitor', name: 'Monitor Issues', categoryId: 'hardware' },
            { id: 'peripheral', name: 'Peripheral Devices', categoryId: 'hardware' }
          ]
        },
        { 
          id: 'software', 
          name: 'Software', 
          departmentId: 'it-support', 
          subcategories: [
            { id: 'application', name: 'Application Issues', categoryId: 'software' },
            { id: 'os', name: 'Operating System', categoryId: 'software' },
            { id: 'email', name: 'Email Issues', categoryId: 'software' },
            { id: 'browser', name: 'Browser Issues', categoryId: 'software' },
            { id: 'security', name: 'Security Software', categoryId: 'software' }
          ]
        },
        { 
          id: 'network', 
          name: 'Network', 
          departmentId: 'it-support', 
          subcategories: [
            { id: 'connectivity', name: 'Connectivity Issues', categoryId: 'network' },
            { id: 'wifi', name: 'WiFi Problems', categoryId: 'network' },
            { id: 'vpn', name: 'VPN Issues', categoryId: 'network' },
            { id: 'firewall', name: 'Firewall Issues', categoryId: 'network' }
          ]
        },
        { 
          id: 'access', 
          name: 'Access & Permissions', 
          departmentId: 'it-support', 
          subcategories: [
            { id: 'password', name: 'Password Reset', categoryId: 'access' },
            { id: 'account', name: 'Account Access', categoryId: 'access' },
            { id: 'permissions', name: 'File Permissions', categoryId: 'access' },
            { id: 'system-access', name: 'System Access', categoryId: 'access' }
          ]
        }
      ]);
      setContacts([]);
      setAgents([]);
      setIsLoading(false);
    }
  };

  const loadTicketsDirectly = async (contactsArg = contacts, agentsArg = agents, departmentsArg = departments) => {
    try {
      // Load tickets from Zoho Desk API
      const response = await ZohoDeskAPI.fetchTickets({
        limit: pageSize,
        from: (currentPage - 1) * pageSize,
        searchStr: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        priority: priorityFilter !== 'all' ? priorityFilter : undefined,
        departmentId: departmentFilter !== 'all' ? departmentFilter : undefined,
      });
      console.log('Raw API response for tickets:', response);
      
      // Get the current categories from state or use the passed categories
      const currentCategories = categories.length > 0 ? categories : [];
      const flatCategories = currentCategories.flatMap(cat => 
        cat.subcategories ? [{ id: cat.id, name: cat.name, departmentId: cat.departmentId }, ...cat.subcategories.map(sub => ({ id: sub.id, name: sub.name, departmentId: cat.departmentId }))] : [{ id: cat.id, name: cat.name, departmentId: cat.departmentId }]
      );

      const convertedTickets = response.data.map(ticket => 
        convertZohoDeskTicket(ticket, contactsArg, agentsArg, departmentsArg, flatCategories, currentUser)
      );
      console.log('Converted tickets:', convertedTickets);

      // Backend now handles user-specific filtering, so we can use all returned tickets
      setTickets(convertedTickets);
      console.info('Successfully loaded user-specific tickets from Zoho Desk:', convertedTickets.length, 'tickets');
      
      // Log ticket details for debugging
      convertedTickets.forEach((ticket, index) => {
        console.log(`User Ticket ${index + 1}:`, {
          id: ticket.id,
          ticketNumber: ticket.ticketNumber,
          subject: ticket.subject,
          status: ticket.status,
          priority: ticket.priority,
          category: ticket.category,
          subcategory: ticket.subcategory,
          requestedBy: ticket.requestedBy,
          assignedTo: ticket.assignedTo,
          createdDate: ticket.createdDate
        });
      });
    } catch (err) {
      console.error('Error loading tickets from Zoho Desk:', err);
      setTickets([]);
    }
  };

  // Filter tickets
  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    const matchesAssignee = assigneeFilter === 'all' || 
                           (assigneeFilter === 'unassigned' && !ticket.assignedTo) ||
                           (assigneeFilter === 'assigned' && ticket.assignedTo);

    // Backend already filters by user email, so no need for client-side user email filtering
    return matchesSearch && matchesStatus && matchesPriority && matchesAssignee;
  });

  // Pagination
  const totalPages = Math.ceil(filteredTickets.length / pageSize);
  const paginatedTickets = filteredTickets.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Helper functions
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: ITTicket['priority']): string => {
    switch (priority) {
      case 'Urgent': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: ITTicket['status']): string => {
    switch (status) {
      case 'Open': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-purple-100 text-purple-800';
      case 'On Hold': return 'bg-yellow-100 text-yellow-800';
      case 'Escalated': return 'bg-red-100 text-red-800';
      case 'Closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: ITTicket['status']) => {
    switch (status) {
      case 'Open': return <AlertCircle size={16} />;
      case 'In Progress': return <Clock size={16} />;
      case 'On Hold': return <Clock size={16} />;
      case 'Escalated': return <AlertCircle size={16} />;
      case 'Closed': return <CheckCircle size={16} />;
      default: return <AlertCircle size={16} />;
    }
  };

  // Handle create ticket
  const handleCreateTicket = async () => {
    if (!createTicketData.subject.trim() || !createTicketData.description.trim()) {
      setError('Subject and description are required.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // First, try to auto-assign department based on user's email domain
      let selectedDepartment: ZohoDeskDepartment | null = null;
      let departmentId: string | number | undefined = createTicketData.departmentId;
      
      // Auto-assign department based on email domain if user email is available
      if (currentUser?.email && departments.length > 0) {
        const emailBasedDepartment = getDepartmentByEmailDomain(currentUser.email, departments);
        if (emailBasedDepartment) {
          selectedDepartment = emailBasedDepartment;
          departmentId = emailBasedDepartment.id;
          console.log(`Auto-assigned department based on email domain: ${emailBasedDepartment.name} (ID: ${emailBasedDepartment.id})`);
        }
      }
      
      // If no email-based department found, use manually selected department
      if (!selectedDepartment && createTicketData.departmentId) {
        const manuallySelectedDepartment = departments.find(dept => dept.id === createTicketData.departmentId);
        if (manuallySelectedDepartment) {
          selectedDepartment = manuallySelectedDepartment;
          departmentId = manuallySelectedDepartment.id;
          console.log(`Using manually selected department: ${departmentId} (${manuallySelectedDepartment.name})`);
        }
      }
      
      // If still no department, use the first available department as fallback
      if (!selectedDepartment && departments.length > 0) {
        selectedDepartment = departments[0];
        departmentId = departments[0].id;
        console.log(`No valid department found, using first available department: ${departmentId} (${departments[0].name})`);
      } else if (!departmentId) {
        // Last resort: let the backend handle department selection
        departmentId = undefined; // Backend will fetch and use a valid department
        console.log(`No departments available in frontend, letting backend handle department selection`);
      }

      // Create ticket using the API with correct Zoho Desk field names
      const newTicketData: {
        subject: string;
        description: string;
        priority: string;
        contactId: string;
        channel: string;
        departmentId: string | number;
        userEmail?: string;
        userName?: string;
      } = {
        subject: createTicketData.subject,
        description: createTicketData.description,
        priority: createTicketData.priority,
        contactId: 'auto-create', // Backend will create/find contact automatically
        channel: 'WEB',
        departmentId: departmentId !== undefined ? departmentId : "1", // Ensure we always have a departmentId
        // Pass current user information to backend
        userEmail: currentUser?.email,
        userName: currentUser?.name
      };

      console.log('Creating ticket with data:', newTicketData);
      const createdTicket = await ZohoDeskAPI.createTicket(newTicketData);
      console.log('Created ticket response:', createdTicket);

      // Convert the created ticket to our internal format
      const convertedTicket: ITTicket = {
        id: createdTicket.id,
        ticketNumber: createdTicket.ticketNumber,
        subject: createdTicket.subject,
        description: createdTicket.description || createdTicket.subject,
        priority: createdTicket.priority as ITTicket['priority'],
        status: createdTicket.status as ITTicket['status'],
        category: { id: 'general', name: 'General' }, // Default category since we removed category selection
        subcategory: undefined, // No subcategory since we removed subcategory selection
        requestedBy: { 
          name: currentUser?.name || 'Current User', 
          email: currentUser?.email || '<EMAIL>', 
          company: currentUser?.company || 'Unknown Company' 
        },
        createdDate: createdTicket.createdTime,
        lastUpdated: createdTicket.modifiedTime,
        dueDate: createdTicket.dueDate,
        resolution: createdTicket.resolution,
        attachments: createdTicket.attachments?.map(att => att.name) || [],
        channel: createdTicket.channel,
        webUrl: createdTicket.webUrl,
        departmentName: selectedDepartment?.name || 'IT Support',
        comments: [],
        timeline: []
      };

      // Add the new ticket to the list
      setTickets([convertedTicket, ...tickets]);
      
      // Close modal and reset form
      setIsCreateModalOpen(false);
      setCreateTicketData({
        subject: '',
        description: '',
        priority: 'Medium',
        departmentId: ''
      });

      console.info('Successfully created ticket:', convertedTicket.ticketNumber);
      
    } catch (err) {
      console.error('Error creating ticket:', err);
      setError('Failed to create ticket. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTicketClick = async (ticket: ITTicket) => {
    setSelectedTicket(ticket);
    setIsLoadingThreads(true);
    setTicketTimeline([]);
    setActiveTab('comments');

    console.log('Loading ticket threads for ticket:', ticket.id);

    try {
      // Fetch ticket threads/conversations from Zoho Desk
      const threads = await ZohoDeskAPI.fetchTicketThreads(ticket.id);
      console.log('API Response - Raw threads:', threads);
      
      if (threads && threads.length > 0) {
      const convertedThreads = threads.map(thread => convertZohoDeskThread(thread));
        console.log('Converted threads to comments:', convertedThreads);

      // Generate timeline events
      const timeline = generateTicketTimeline(ticket, convertedThreads);
      setTicketTimeline(timeline);
      
        // Update selected ticket with the conversations
      setSelectedTicket({
        ...ticket,
        comments: convertedThreads
      });
      
        console.log('Successfully loaded', convertedThreads.length, 'comments from Zoho Desk');
      } else {
        console.warn('No threads returned from Zoho Desk API');
        // Set empty comments array - no fallback data
        setSelectedTicket({
          ...ticket,
          comments: []
        });
        setTicketTimeline(generateTicketTimeline(ticket, []));
      }
    } catch (error) {
      console.error('API Error loading ticket threads:', error);
      // Set empty comments array - no fallback data
      setSelectedTicket({
        ...ticket,
        comments: []
      });
      setTicketTimeline(generateTicketTimeline(ticket, []));
    } finally {
      setIsLoadingThreads(false);
    }
  };

  const handleCloseModal = () => {
    setSelectedTicket(null);
    setNewComment('');
    setExpandedTimelineEvents(new Set());
  };

  const handleSubmitComment = async () => {
    if (!selectedTicket || !newComment.trim()) return;

    setIsSubmittingComment(true);
    try {
      // Post comment to Zoho Desk API
      console.log('Posting comment to Zoho Desk...');
      const response = await ZohoDeskAPI.addTicketComment(selectedTicket.id, {
        content: newComment.trim(),
        contentType: 'plainText',
        isPublic: true
      });

      console.log('Comment posted successfully, response:', response);
      
      // Clear comment input and switch to comments tab
      setNewComment('');
      setActiveTab('comments');

      // Refresh the ticket data from Zoho Desk to get the latest comments
      console.log('Refreshing ticket data after comment submission...');
      
      // Add a small delay to allow Zoho Desk to process the comment
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      try {
        const threads = await ZohoDeskAPI.fetchTicketThreads(selectedTicket.id);
        console.log('Refreshed threads after comment:', threads);
        
        if (threads && threads.length > 0) {
          const convertedThreads = threads.map(thread => convertZohoDeskThread(thread));
          console.log('Refreshed converted threads:', convertedThreads);
          
          // Update selected ticket with refreshed comments
          const updatedTicket = { ...selectedTicket, comments: convertedThreads };
          setSelectedTicket(updatedTicket);

          // Update timeline
          const updatedTimeline = generateTicketTimeline(updatedTicket, convertedThreads);
          setTicketTimeline(updatedTimeline);
        } else {
          console.warn('No threads returned after refresh, keeping existing comments');
        }
      } catch (refreshError) {
        console.error('Failed to refresh ticket data after comment:', refreshError);
      }

      console.info('Comment added and data refreshed successfully');
    } catch (error) {
      console.error('Failed to add comment:', error);
      setError('Failed to add comment. Please try again.');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  return (
    <div className="p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My IT Support Tickets</h1>
        <p className="text-gray-600">
          {currentUser?.email ? (
            <>Viewing tickets for <span className="font-medium text-gray-800">{currentUser.email}</span></>
          ) : (
            'Manage and track your IT support requests.'
          )}
        </p>
      </div>

      {/* Actions Bar */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-gray-500">
            {filteredTickets.length} {filteredTickets.length === 1 ? 'ticket' : 'tickets'} 
            {currentUser?.email ? ' for your account' : ' found'}
          </div>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus size={16} className="mr-2" />
            Create Ticket
          </button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search tickets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Statuses</option>
            <option value="Open">Open</option>
            <option value="In Progress">In Progress</option>
            <option value="On Hold">On Hold</option>
            <option value="Escalated">Escalated</option>
            <option value="Closed">Closed</option>
          </select>

          {/* Priority Filter */}
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Priorities</option>
            <option value="Urgent">Urgent</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>

          {/* Assignee Filter */}
          <select
            value={assigneeFilter}
            onChange={(e) => setAssigneeFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Tickets</option>
            <option value="assigned">Assigned</option>
            <option value="unassigned">Unassigned</option>
          </select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle size={20} className="text-red-400 mr-3" />
            <div className="text-sm text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* OAuth Authorization Status */}
      {error && error.includes('authorization') && (
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex">
              <AlertCircle size={20} className="text-yellow-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">Zoho Desk Authorization Required</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Complete OAuth authorization to access real Zoho Desk tickets and data.
                </p>
              </div>
            </div>
            <button
              onClick={async () => {
                try {
                  const authUrl = await ZohoDeskAPI.initializeOAuth();
                  window.open(authUrl, '_blank', 'width=600,height=600');
                } catch (err) {
                  console.error('Failed to initialize OAuth:', err);
                  alert('Failed to initialize OAuth. Please check the console for details.');
                }
              }}
              className="ml-4 px-4 py-2 bg-yellow-600 text-white text-sm rounded-md hover:bg-yellow-700"
            >
              Authorize Zoho Desk
            </button>
          </div>
        </div>
      )}

      {/* Demo Data Warning */}
      {error && error.includes('demo data') && (
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex">
              <AlertCircle size={20} className="text-blue-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-blue-800">Using Demo Data</h3>
                <p className="text-sm text-blue-700 mt-1">
                  You're viewing demo data. Configure Zoho Desk integration to see real tickets.
                </p>
              </div>
            </div>
            <button
              onClick={() => window.open('/it/setup', '_blank')}
              className="ml-4 px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
            >
              Setup Integration
            </button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading tickets...</p>
        </div>
      )}

      {/* Tickets Display - Clean List View */}
      {!isLoading && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {paginatedTickets.length > 0 ? (
            <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticket
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Assignee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Updated
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedTickets.map((ticket) => (
                    <tr 
                      key={ticket.id} 
                      className="hover:bg-gray-50 cursor-pointer transition-colors duration-150" 
                      onClick={() => handleTicketClick(ticket)}
                    >
                    <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <div className="text-sm font-medium text-blue-600">#{ticket.ticketNumber}</div>
                          <div className="text-sm text-gray-900 font-medium truncate max-w-xs" title={ticket.subject}>
                          {ticket.subject}
                        </div>
                          <div className="text-xs text-gray-500 truncate max-w-xs" title={ticket.description}>
                            {ticket.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                        <span className="mr-1">{getStatusIcon(ticket.status)}</span>
                        {ticket.status}
                      </span>
                    </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex flex-col">
                          <span className="font-medium">{ticket.category.name}</span>
                          {ticket.subcategory && (
                            <span className="text-xs text-gray-500">{ticket.subcategory.name}</span>
                          )}
                        </div>
                    </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {ticket.assignedTo ? (
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8">
                              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-xs font-medium text-blue-600">
                                  {ticket.assignedTo.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900">{ticket.assignedTo.name}</div>
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">Unassigned</span>
                        )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex flex-col">
                          <span>{formatDate(ticket.lastUpdated)}</span>
                          <span className="text-xs text-gray-400">
                            by {ticket.requestedBy.name}
                            {ticket.requestedBy.company && ticket.requestedBy.company !== 'Unknown Company' && (
                              <span className="text-gray-500"> • {ticket.requestedBy.company}</span>
                            )}
                          </span>
                        </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTicketClick(ticket);
                        }}
                          className="text-indigo-600 hover:text-indigo-900 transition-colors duration-150"
                      >
                        View
                      </button>
                    </td>
                  </tr>
                  ))}
              </tbody>
            </table>
          </div>
          ) : (
            <div className="text-center py-12">
              <AlertCircle size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No tickets found</h3>
              <p className="text-gray-500 mb-6">No tickets match your current filter criteria.</p>
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Plus size={16} className="mr-2" />
                Create New Ticket
              </button>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span> to{' '}
                    <span className="font-medium">{Math.min(currentPage * pageSize, filteredTickets.length)}</span> of{' '}
                    <span className="font-medium">{filteredTickets.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Previous</span>
                      Previous
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNum === currentPage
                              ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">Next</span>
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Create Ticket Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Create New IT Support Ticket
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject *
                  </label>
                  <input
                    type="text"
                    value={createTicketData.subject}
                    onChange={(e) => setCreateTicketData({ ...createTicketData, subject: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Brief description of the issue"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    value={createTicketData.description}
                    onChange={(e) => setCreateTicketData({ ...createTicketData, description: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Detailed description of the issue including steps to reproduce"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority *
                    </label>
                    <select
                      value={createTicketData.priority}
                      onChange={(e) => setCreateTicketData({ ...createTicketData, priority: e.target.value as ITTicket['priority'] })}
                      className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                      <option value="Urgent">Urgent</option>
                    </select>
                  </div>

                  {/* Category and Subcategory fields removed as requested */}
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setCreateTicketData({
                      subject: '',
                      description: '',
                      priority: 'Medium',
                      departmentId: ''
                    });
                    setError(null);
                  }}
                  className="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateTicket}
                  className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-300"
                >
                  Create Ticket
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Ticket Detail Modal */}
      {selectedTicket && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" onClick={handleCloseModal}>
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white" onClick={(e) => e.stopPropagation()}>
            {/* Modal Header */}
            <div className="flex items-start justify-between p-6 border-b border-gray-200">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h3 className="text-2xl font-bold text-gray-900">#{selectedTicket.ticketNumber}</h3>
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(selectedTicket.status)}`}>
                    <span className="mr-1">{getStatusIcon(selectedTicket.status)}</span>
                    {selectedTicket.status}
                  </span>
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${getPriorityColor(selectedTicket.priority)}`}>
                    {selectedTicket.priority}
                  </span>
              </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">{selectedTicket.subject}</h4>
                <p className="text-gray-600">{selectedTicket.description}</p>
              </div>
              <div className="flex items-center space-x-2 ml-6">
              <button
                  onClick={handleCloseModal}
                  className="inline-flex items-center p-2 border border-transparent rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                  <X size={20} />
              </button>
              </div>
            </div>

            {/* Modal Body */}
            <div className="p-6">
              {/* Ticket Information Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {/* Contact Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <User size={16} className="mr-2 text-blue-600" />
                    Requested By
                  </h5>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {selectedTicket.requestedBy.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </span>
                    </div>
                <div>
                      <div className="text-sm font-medium text-gray-900">{selectedTicket.requestedBy.name}</div>
                      <div className="text-xs text-gray-500">{selectedTicket.requestedBy.email}</div>
                      <div className="text-xs text-gray-500">{selectedTicket.requestedBy.company}</div>
                    </div>
                  </div>
                </div>

                {/* Assignment Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <UserCheck size={16} className="mr-2 text-green-600" />
                    Assigned To
                  </h5>
                  {selectedTicket.assignedTo ? (
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-green-600">
                          {selectedTicket.assignedTo.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </span>
                      </div>
                  <div>
                        <div className="text-sm font-medium text-gray-900">{selectedTicket.assignedTo.name}</div>
                        <div className="text-xs text-gray-500">{selectedTicket.assignedTo.email}</div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <User size={16} className="text-gray-400" />
                      </div>
                      <div className="text-sm text-gray-500">Unassigned</div>
                  </div>
                )}
                </div>

                {/* Category Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Tag size={16} className="mr-2 text-purple-600" />
                    Category
                  </h5>
                <div>
                    <div className="text-sm font-medium text-gray-900">{selectedTicket.category.name}</div>
                    {selectedTicket.subcategory && (
                      <div className="text-xs text-gray-500">{selectedTicket.subcategory.name}</div>
                    )}
                  </div>
                </div>

                {/* Timing Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Clock size={16} className="mr-2 text-orange-600" />
                    Timeline
                  </h5>
                  <div className="space-y-2">
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Created:</span> {formatDate(selectedTicket.createdDate)}
                    </div>
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Updated:</span> {formatDate(selectedTicket.lastUpdated)}
                    </div>
                    {selectedTicket.dueDate && (
                      <div className="text-xs text-gray-600">
                        <span className="font-medium">Due:</span> {formatDate(selectedTicket.dueDate)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Channel & Attachments */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <MessageSquare size={16} className="mr-2 text-indigo-600" />
                    Channel & Files
                  </h5>
                  <div className="space-y-2">
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Channel:</span> {selectedTicket.channel}
                    </div>
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Attachments:</span> {selectedTicket.attachments.length}
                    </div>
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Comments:</span> {selectedTicket.comments.length}
                    </div>
                  </div>
                </div>

                {/* Additional Info */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                    <Info size={16} className="mr-2 text-gray-600" />
                    Additional Info
                  </h5>
                  <div className="space-y-2">
                    {selectedTicket.webUrl && (
                      <div className="text-xs text-gray-600">
                        <span className="font-medium">Ticket ID:</span> {selectedTicket.id}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Tabs for Comments and Timeline */}
              <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                      <button
                        onClick={() => setActiveTab('comments')}
                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'comments'
                            ? 'border-indigo-500 text-indigo-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                    <MessageSquare size={16} className="inline mr-2" />
                        Comments ({selectedTicket.comments.length})
                      </button>
                      <button
                        onClick={() => setActiveTab('timeline')}
                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                          activeTab === 'timeline'
                            ? 'border-indigo-500 text-indigo-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                    <Clock size={16} className="inline mr-2" />
                        Timeline ({ticketTimeline.length})
                      </button>
                    </nav>
                  </div>

                  {/* Tab Content */}
              <div className="max-h-96 overflow-y-auto mt-4">
                    {activeTab === 'comments' && (
                  <div className="space-y-4">
                        {isLoadingThreads ? (
                      <div className="text-center py-8">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                            <p className="mt-2 text-sm text-gray-500">Loading conversations...</p>
                          </div>
                        ) : selectedTicket.comments.length > 0 ? (
                      selectedTicket.comments.map((comment, index) => (
                        <div key={`comment-${index}`} className="border-l-4 border-gray-200 pl-4 py-3">
                          <div className="flex items-start space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              comment.author.type === 'AGENT' ? 'bg-blue-100' : 
                              comment.author.type === 'CONTACT' ? 'bg-green-100' : 'bg-gray-100'
                            }`}>
                              <User size={16} className={`${
                                comment.author.type === 'AGENT' ? 'text-blue-600' : 
                                comment.author.type === 'CONTACT' ? 'text-green-600' : 'text-gray-600'
                              }`} />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                  <span className="text-sm font-medium text-gray-900">{comment.author.name}</span>
                                <span className="text-xs text-gray-500">{formatDate(comment.createdDate)}</span>
                                {comment.author.type === 'AGENT' && (
                                  <span className="px-2 py-0.5 bg-blue-100 text-blue-700 rounded text-xs font-medium">Agent</span>
                                )}
                                {comment.author.type === 'CONTACT' && (
                                  <span className="px-2 py-0.5 bg-green-100 text-green-700 rounded text-xs font-medium">Customer</span>
                                  )}
                                </div>
                              <div className="text-sm text-gray-700 bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div dangerouslySetInnerHTML={{ __html: comment.message }} />
                              </div>
                            </div>
                          </div>
                            </div>
                          ))
                        ) : (
                      <div className="text-center py-8">
                        <MessageSquare size={48} className="mx-auto text-gray-400 mb-4" />
                        <p className="text-gray-500">No comments yet</p>
                      </div>
                        )}
                      </div>
                    )}

                    {activeTab === 'timeline' && (
                      <div className="space-y-4">
                    {ticketTimeline.length > 0 ? (
                      ticketTimeline.map((event, index) => {
                        const isExpanded = expandedTimelineEvents.has(event.id);
                        // Check for conversation content - ensure all comment events have expandable content
                        const hasConversation = event.details?.comment && event.details.comment.trim().length > 0;

                        
                        return (
                          <div key={`timeline-${index}`} className="flex items-start space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              event.type === 'created' ? 'bg-green-100' :
                              event.type === 'comment' ? 'bg-blue-100' :
                              event.type === 'assignment' ? 'bg-purple-100' :
                              'bg-gray-100'
                            }`}>
                              {event.type === 'created' ? (
                                <Plus size={16} className="text-green-600" />
                              ) : event.type === 'comment' ? (
                                <MessageSquare size={16} className="text-blue-600" />
                              ) : event.type === 'assignment' ? (
                                <User size={16} className="text-purple-600" />
                              ) : (
                                <Clock size={16} className="text-gray-600" />
                              )}
                                    </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="text-sm font-medium text-gray-900">{event.description}</span>
                                <span className="text-xs text-gray-500">{formatDate(event.timestamp)}</span>
                                {/* Chat icon for expandable conversation - show for ALL events with conversation content */}
                                {hasConversation && (
                                  <button
                                    onClick={() => {
                                      const newExpanded = new Set(expandedTimelineEvents);
                                      if (isExpanded) {
                                        newExpanded.delete(event.id);
                                      } else {
                                        newExpanded.add(event.id);
                                      }
                                      setExpandedTimelineEvents(newExpanded);
                                    }}
                                    className="p-1 hover:bg-gray-100 rounded transition-colors duration-150"
                                    title={isExpanded ? "Hide conversation" : "Show conversation"}
                                  >
                                    <MessageSquare size={14} className={`${isExpanded ? 'text-blue-600' : 'text-gray-400'} transition-colors duration-150 hover:text-blue-500`} />
                                  </button>
                                  )}
                                </div>
                              
                              {/* Show assignment details */}
                              {event.details?.to && (
                                <div className="text-sm text-gray-600 italic mb-1">
                                  Assigned to: {event.details.to}
                              </div>
                              )}
                              
                              {/* Expandable conversation content */}
                              {hasConversation && isExpanded && event.details?.comment && (
                                <div className="text-sm text-gray-700 bg-white border border-gray-200 rounded-lg p-3 mt-2 shadow-sm">
                                  <div className="text-xs text-gray-500 mb-2 font-medium">
                                    {event.type === 'created' ? 'Initial Description:' : 
                                     event.description.includes('Agent') ? 'Agent Message:' :
                                     event.description.includes('Customer') ? 'Customer Message:' : 'Conversation:'}
                      </div>
                                  <div dangerouslySetInnerHTML={{ __html: event.details.comment }} />
                  </div>
                              )}
                              
                              {event.author && (
                                <div className="text-xs text-gray-500 mt-1 flex items-center space-x-1">
                                  <span>by</span>
                                  <span className="font-medium">{event.author.name}</span>
                                  {event.author.type === 'AGENT' && (
                                    <span className="px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded text-xs">Agent</span>
                                  )}
                                  {event.author.type === 'CONTACT' && (
                                    <span className="px-1.5 py-0.5 bg-green-100 text-green-700 rounded text-xs">Customer</span>
                                  )}
                                  {event.author.type === 'SYSTEM' && (
                                    <span className="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">System</span>
                                  )}
                    </div>
                              )}
                    </div>
                    </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <Clock size={48} className="mx-auto text-gray-400 mb-4" />
                        <p className="text-gray-500">No timeline events</p>
                      </div>
                    )}
                      </div>
                    )}
                    </div>

              {/* Add Comment Section */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h5 className="text-sm font-semibold text-gray-900 mb-3">Add Comment</h5>
                <div className="space-y-3">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Type your comment here..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                  />
                  <div className="flex justify-end">
                    <button
                      onClick={handleSubmitComment}
                      disabled={!newComment.trim() || isSubmittingComment}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmittingComment ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Posting...
                        </>
                      ) : (
                        <>
                          <MessageSquare size={16} className="mr-2" />
                          Post Comment
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ITTicketsPage; 