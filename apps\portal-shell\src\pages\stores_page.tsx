// import { Link } from 'react-router-dom'; // Unused import
import {
  Package,
  Layers,
  Printer,
  RotateCw,
  Trash2,
  AlertCircle,
  Truck,
} from 'feather-icons-react';

const host_var = "http://**************/"//"http://**************/";
const handleToolClick = (tool: any) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);
const storeTools = [
  {
    label: 'GRIN TRACKER',
    name: "grin_tracker",
    link: host_var+'grin_tracker/index.php',
    desc: 'Standard part number creation',
    category: 'Stores',
    priority: 'medium',
    type: "internal",
    icon: <Package size={24} />, // Package = part number/item
  },
  {
    label: 'KIT TRACKER',
    name: "kit_tracker",
    link: host_var+'kit_tracker/index.php',
    desc: 'Engineering Change Notice system',
    category: 'Stores',
    priority: 'high',
    type: "internal",
    icon: <Layers size={24} />, // Layers = kit/components
  },
  {
    label: 'SAP GRIN PRINT',
    name: "scm_part_master",
    link: host_var+'scm_part_master/label_print.php',
    desc: 'QA checklists and reports',
    category: 'Stores',
    priority: 'high',
    type: "internal",
    icon: <Printer size={24} />, // Printer = print labels
  },
  {
    label: 'RETURN MATERIAL AUTHORISATION',
    name: "rma",
    link: host_var+'rma/index.php',
    desc: 'Legacy to UDS format',
    category: 'Stores',
    priority: 'medium',
    type: "internal",
    icon: <RotateCw size={24} />, // RotateCw = returns/loop
  },
  {
    label: 'SCRAP NOTE',
    name: "scrap_note",
    link: host_var+'scrap_note/index.php',
    desc: 'Track engineering issues',
    category: 'Stores',
    priority: 'high',
    type: "internal",
    icon: <Trash2 size={24} />, // Trash2 = scrap
  },
  {
    label: 'MATERIAL UNDER POSITIVE RECALL',
    name: "positive_recall",
    link: host_var+'positive_recall/index.php',
    desc: 'Decision support tool',
    category: 'Stores',
    priority: 'medium',
    type: "internal",
    icon: <AlertCircle size={24} />, // AlertCircle = recalls/issues
  },
  {
    label: 'LOGISTICS DASHBOARD',
    name: "logistics_dashboard",
    link: host_var+'logistics_dashboard/index.php',
    desc: 'Project & client contract review',
    category: 'Stores',
    priority: 'high',
    type: "internal",
    icon: <Truck size={24} />, // Truck = logistics
  },
];

const StoreDashboard = () => {



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
                <h1 className="text-4xl font-light text-gray-900 mb-2">Stores Management Tools</h1>
                <p className="text-gray-600 text-lg">Efficiently manage inventory, material issuance, and stock tracking</p>
            </div>

          </div>
        </div>
      </div>
<div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {storeTools.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  );
};

export default StoreDashboard;