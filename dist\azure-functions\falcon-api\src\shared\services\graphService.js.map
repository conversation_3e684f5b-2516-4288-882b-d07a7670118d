{"version": 3, "file": "graphService.js", "sourceRoot": "", "sources": ["../../../../../../azure-functions/falcon-api/src/shared/services/graphService.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,8EAA2D;AAC3D,iHAA8H;AAC9H,8CAAyD;AACzD,4BAA0B,CAAC,qBAAqB;AAChD,4CAAyC;AAEzC,oFAAoF;AACpF,sFAAsF;AACtF,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAErD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;IAC1C,eAAM,CAAC,KAAK,CAAC,6FAA6F,CAAC,CAAC;IAC5G,0EAA0E;IAC1E,iEAAiE;IACjE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AAC5E,CAAC;AAED,mCAAmC;AACnC,MAAM,UAAU,GAAG,IAAI,iCAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AAEhF,iDAAiD;AACjD,MAAM,YAAY,GAAG,IAAI,6DAAqC,CAAC,UAAU,EAAE;IACvE,MAAM,EAAE,CAAC,sCAAsC,CAAC,CAAC,gDAAgD;CACpG,CAAC,CAAC;AAEH,oCAAoC;AACpC,MAAM,WAAW,GAAG,+BAAM,CAAC,kBAAkB,CAAC;IAC1C,YAAY,EAAE,YAAY;CAC7B,CAAC,CAAC;AAEH,MAAa,YAAY;IAErB,+BAA+B;IAClB,WAAW;6DAAC,WAAmB,EAAE,QAAgB,EAAE;;YAC5D,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,eAAM,CAAC,IAAI,CAAC,uEAAuE,CAAC,CAAC;gBACrF,8DAA8D;gBAC9D,OAAO,EAAE,CAAC;YACf,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,8CAA8C,WAAW,aAAa,KAAK,EAAE,CAAC,CAAC;YAE3F,IAAI,CAAC;gBACD,gDAAgD;gBAChD,oEAAoE;gBACpE,qFAAqF;gBACrF,6CAA6C;gBAC7C,MAAM,MAAM,GAAG,2BAA2B,WAAW,+BAA+B,WAAW,6BAA6B,WAAW,uCAAuC,WAAW,0BAA0B,WAAW,IAAI,CAAC;gBAEnO,6FAA6F;gBAC7F,MAAM,MAAM,GAAG,oEAAoE,CAAC;gBAEpF,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;qBAC3C,MAAM,CAAC,MAAM,CAAC;qBACd,MAAM,CAAC,MAAM,CAAC;qBACd,GAAG,CAAC,KAAK,CAAC;oBACV,4DAA4D;oBAC5D,0DAA0D;qBAC1D,MAAM,CAAC,kBAAkB,EAAE,UAAU,CAAC;qBACtC,KAAK,CAAC,IAAI,CAAC,CAAC,yCAAyC;qBACrD,GAAG,EAAE,CAAC;gBAEX,qFAAqF;gBACrF,eAAM,CAAC,IAAI,CAAC,uBAAuB,MAAA,MAAA,QAAQ,CAAC,cAAc,CAAC,mCAAI,MAAA,QAAQ,CAAC,KAAK,0CAAE,MAAM,mCAAI,CAAC,2BAA2B,WAAW,oBAAoB,MAAA,MAAA,QAAQ,CAAC,KAAK,0CAAE,MAAM,mCAAI,CAAC,GAAG,CAAC,CAAC;gBACpL,OAAO,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,mCAAmC;YAEpE,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE;oBAC5D,KAAK,EAAE,WAAW;oBAClB,KAAK,EAAE,KAAK;oBACZ,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,qCAAqC;oBACnE,6EAA6E;iBAChF,CAAC,CAAC;gBACJ,gEAAgE;gBAC/D,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACtD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;gBACrE,CAAC;YACN,CAAC;QACL,CAAC;KAAA;IAED,kDAAkD;IACrC,WAAW,CAAC,MAAc;;YAClC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;YAE5D,IAAI,CAAC;gBACD,4BAA4B;gBAC5B,MAAM,MAAM,GAAG,oEAAoE,CAAC;gBAEpF,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,CAAC;qBACjD,MAAM,CAAC,MAAM,CAAC;qBACd,GAAG,EAAE,CAAC;gBAEX,eAAM,CAAC,IAAI,CAAC,6CAA6C,MAAM,EAAE,CAAC,CAAC;gBACnE,OAAO,IAAI,CAAC,CAAC,yCAAyC;YAE1D,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACjB,eAAM,CAAC,KAAK,CAAC,4CAA4C,MAAM,iBAAiB,EAAE;oBAC9E,MAAM,EAAE,MAAM;oBACd,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC/B,CAAC,CAAC;gBAEH,4CAA4C;gBAC5C,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC3B,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,aAAa,CAAC,CAAC;oBAC/D,OAAO,IAAI,CAAC,CAAC,gCAAgC;gBACjD,CAAC;gBACD,qBAAqB;gBACpB,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACvD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBAC3E,CAAC;YACN,CAAC;QACL,CAAC;KAAA;IAED,gDAAgD;IACnC,YAAY,CAAC,GAAW;;YACjC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACP,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,EAAE,CAAC,CAAC;YAEzD,IAAI,CAAC;gBACD,0DAA0D;gBAC1D,MAAM,MAAM,GAAG,8IAA8I,CAAC;gBAE9J,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,CAAC;qBAC9C,MAAM,CAAC,MAAM,CAAC;qBACd,GAAG,EAAE,CAAC;gBAEX,eAAM,CAAC,IAAI,CAAC,oDAAoD,GAAG,EAAE,CAAC,CAAC;gBACvE,OAAO,IAAI,CAAC;YAEhB,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,eAAM,CAAC,KAAK,CAAC,6CAA6C,GAAG,iBAAiB,EAAE;oBAC5E,GAAG,EAAE,GAAG;oBACR,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC/B,CAAC,CAAC;gBAEH,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC3B,eAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,aAAa,CAAC,CAAC;oBAC7D,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACvD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;gBACzF,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;QACL,CAAC;KAAA;IAED,+BAA+B;IAClB,qBAAqB,CAAC,OAAe;;YAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,eAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;gBAC5E,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,kDAAkD,OAAO,EAAE,CAAC,CAAC;YAEzE,IAAI,CAAC;gBACD,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,8IAA8I,CAAC;gBAE9J,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE,CAAC;qBAClD,MAAM,CAAC,MAAM,CAAC;qBACd,GAAG,EAAE,CAAC;gBAEX,eAAM,CAAC,IAAI,CAAC,mDAAmD,OAAO,EAAE,CAAC,CAAC;gBAE1E,iEAAiE;gBACjE,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAE1D,uCACO,IAAI,KACP,YAAY,IACd;YAEN,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,eAAM,CAAC,KAAK,CAAC,6CAA6C,OAAO,iBAAiB,EAAE;oBAChF,OAAO,EAAE,OAAO;oBAChB,YAAY,EAAE,KAAK,CAAC,OAAO;oBAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;iBAC/B,CAAC,CAAC;gBAEH,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC3B,eAAM,CAAC,IAAI,CAAC,oCAAoC,OAAO,aAAa,CAAC,CAAC;oBACtE,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACvD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;gBACzF,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;gBACzE,CAAC;YACL,CAAC;QACL,CAAC;KAAA;IAED,iEAAiE;IACzD,yBAAyB,CAAC,IAAS;QACvC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AA1LD,oCA0LC;AAED,6CAA6C;AAChC,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}