import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';

export async function getChangeRequestById(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('GetChangeRequestById function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        // Query to get a single change request with all details
        // Using correct table names from database schema
        const query = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.BusinessJustification as businessJustification,
                cr.ExpectedBenefit as expectedBenefit,
                cr.RequestedCompletionDate as requestedCompletionDate,
                ISNULL(cr.DevelopmentProgress, 0) as developmentProgress,
                cr.PlannedStartDate as plannedStartDate,
                cr.PlannedCompletionDate as plannedCompletionDate,
                cr.ActualStartDate as actualStartDate,
                cr.ActualCompletionDate as actualCompletionDate,
                cr.CreatedDate as createdDate,
                cr.ApprovedDate as approvedDate,
                crt.TypeName as typeName,
                crt.Description as typeDescription,
                ISNULL(crt.EstimatedDays, 0) as estimatedDays,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                d.DepartmentName as departmentName,
                CONCAT(approver.FirstName, ' ', approver.LastName) as approverName,
                CONCAT(developer.FirstName, ' ', developer.LastName) as developerName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
                LEFT JOIN Departments d ON cr.DepartmentID = d.DepartmentID
                LEFT JOIN Users approver ON cr.ApprovedBy = approver.UserID
                LEFT JOIN Users developer ON cr.AssignedToDevID = developer.UserID
            WHERE cr.RequestID = @requestId
        `;

        const parameters: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        context.log(`Executing query for requestId: ${requestId}`);
        const result = await executeQuery(query, parameters);
        context.log(`Query executed, found ${result.recordset.length} records`);

        if (result.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        const changeRequest = result.recordset[0];

        // Fetch rich content separately from ChangeRequestContent table
        const contentQuery = `
            SELECT 
                ContentType as contentType,
                ContentData as contentData,
                SortOrder as sortOrder,
                ImageUrl as imageUrl,
                ImageCaption as imageCaption,
                ImageAltText as imageAltText
            FROM ChangeRequestContent 
            WHERE RequestID = @requestId
            ORDER BY SortOrder ASC
        `;

        const contentParameters: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const contentResult = await executeQuery(contentQuery, contentParameters);
        
        // Add content array to the change request
        changeRequest.content = contentResult.recordset || [];

        context.log(`Successfully retrieved change request ${requestId} with ${changeRequest.content.length} content blocks`);

        return {
            status: 200,
            jsonBody: changeRequest
        };

    } catch (error: any) {
        context.error('Error in GetChangeRequestById:', error);
        context.error('Error details:', {
            message: error?.message || 'Unknown error',
            stack: error?.stack || 'No stack trace',
            requestId: request.params.requestId
        });
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while fetching the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('GetChangeRequestById', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}',
    handler: getChangeRequestById
}); 