"use client"

// import { useState, useEffect } from "react" // Unused imports
import Create_Vd from "./component/vcs/create_vd"
import Visit_Registration from './component/vcs/visit_registration';
import Invite_Details from './component/vcs/invitee_details'
import Visitor_List_Form from './component/vcs/visitors_list_form';
import Restricted_Area_Approvals from "./component/vcs/restricted_area_approvals";
import Visitor_Details from './component/vcs/visitor_details'


function App() {



  return (
    <div className="flex min-h-screen">
        <main className="flex-grow">
          <Create_Vd/>
          <Visit_Registration/>
          <Invite_Details/>
          <Visitor_List_Form/>
          <Restricted_Area_Approvals/>
          <Visitor_Details/>
        </main>

      </div>
  )
}

export default App
