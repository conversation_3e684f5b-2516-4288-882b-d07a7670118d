"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const logger_1 = require("../shared/utils/logger");
// TODO: Add request body validation (e.g., using Zod)
// interface CreateUserRequestBody {
//   email: string; 
// }
async function createUser(request, context) {
    context.log = logger_1.logger.info; // Use shared logger
    logger_1.logger.info(`[CreateUser] HTTP trigger function processed a request.`);
    // TODO: Implement proper authentication check later
    // const principal = getClientPrincipal(request);
    // if (!principal) {
    //     return { status: 401, jsonBody: { error: "Unauthorized" } };
    // }
    // Check roles if needed: hasRequiredRole(principal, ['PortalAdmin'])
    // Placeholder for the ID of the user performing the creation
    const createdByUserId = 1; // TODO: Replace with actual ID from getUserIdFromPrincipal(principal);
    try {
        // 1. Parse and Validate Request Body
        // TODO: Add proper validation
        const body = await request.json();
        const userEmailOrUpn = body?.email;
        if (!userEmailOrUpn || typeof userEmailOrUpn !== 'string') {
            logger_1.logger.warn("[CreateUser] Invalid request body: Missing or invalid email.", body);
            return { status: 400, jsonBody: { error: "Request body must contain a valid 'email' (or UPN) string." } };
        }
        logger_1.logger.info(`[CreateUser] Attempting to create user with email/UPN: ${userEmailOrUpn}`);
        // 2. Prepare data for findOrCreateUser
        // In a real scenario, we might query Graph API here first if only email is given,
        // to get the Entra ID (object ID), full name, company, etc.
        // For now, we assume the provided email IS the UPN/Entra ID link.
        const entraUserData = {
            id: userEmailOrUpn,
            userPrincipalName: userEmailOrUpn,
            mail: userEmailOrUpn,
            // Provide default/empty values for other required fields if not fetching from Graph
            givenName: '',
            surname: 'User',
            companyName: '',
            department: undefined
        };
        // 3. Call the service function
        const createdDbUser = await (0, userManagementService_1.findOrCreateUser)(entraUserData, createdByUserId);
        // 4. Handle Service Response (findOrCreateUser throws on critical errors)
        if (!createdDbUser) {
            // This case might not be reachable if findOrCreateUser throws, but handle defensively
            logger_1.logger.error("[CreateUser] findOrCreateUser returned null unexpectedly.");
            return { status: 500, jsonBody: { error: "Failed to create or find user." } };
        }
        // 5. Map DbUser to PortalUser for the response (Need a mapping function)
        // TODO: Create or import a suitable mapping function if mapDbUserToPortalUser isn't directly usable/exported
        const portalUserResponse = {
            id: createdDbUser.EntraID ?? undefined,
            internalId: createdDbUser.UserID,
            name: createdDbUser.Username,
            email: createdDbUser.Email,
            company: createdDbUser.CompanyName,
            companyId: createdDbUser.CompanyID,
            // Roles are usually assigned separately or defaulted in findOrCreateUser
            roles: [],
            status: createdDbUser.IsActive ? 'Active' : 'Inactive',
            lastLogin: createdDbUser.LastLoginDate?.toISOString()
        };
        logger_1.logger.info(`[CreateUser] Successfully created/found user: ${createdDbUser.UserID}`);
        // Return 201 Created status code
        return { status: 201, jsonBody: portalUserResponse };
    }
    catch (error) {
        logger_1.logger.error("[CreateUser] Error processing request:", error);
        // Check for specific error types if needed (e.g., company not found error from findOrCreateUser)
        // if (error.message.includes("Company") && error.message.includes("not found")) {
        //     return { status: 400, jsonBody: { error: error.message } };
        // }
        return { status: 500, jsonBody: { error: `Internal server error: ${error.message || 'Unknown error'}` } };
    }
}
functions_1.app.http('CreateUser', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'users',
    handler: createUser
});
// Export the function for the Azure Functions runtime
exports.default = createUser; // Or adjust export based on v4 model specifics if needed 
//# sourceMappingURL=index.js.map