-- Fix Tenant ID Mapping for Multi-Tenant Architecture
-- This script corrects the TenantID assignments based on actual Azure Entra ID tenants
-- Execute this script in Azure Portal Query Editor

-- Step 1: Add TenantID column to Companies table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'TenantID')
BEGIN
    ALTER TABLE Companies ADD TenantID NVARCHAR(50) NULL;
    PRINT 'Added TenantID column to Companies table';
END
ELSE
BEGIN
    PRINT 'TenantID column already exists in Companies table';
END

-- Step 2: Update Companies table with correct TenantID mappings
-- Based on Azure Entra ID tenant information provided by user

-- Avirata Defence Systems
UPDATE Companies 
SET TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea'
WHERE CompanyName = 'Avirata Defence Systems';

-- SASMOS HET
UPDATE Companies 
SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2'
WHERE CompanyName = 'SASMOS HET';

-- SASMOS Group (using same tenant as SASMOS HET for now)
UPDATE Companies 
SET TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2'
WHERE CompanyName = 'SASMOS Group';

-- FE-SIL
UPDATE Companies 
SET TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1'
WHERE CompanyName = 'FE-SIL';

-- Glodesi
UPDATE Companies 
SET TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0'
WHERE CompanyName = 'Glodesi';

-- Hanuka
UPDATE Companies 
SET TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775'
WHERE CompanyName = 'Hanuka';

-- West Wire Harnessing (placeholder - will be updated when tenant ID is provided)
UPDATE Companies 
SET TenantID = 'PLACEHOLDER_WWH_TENANT_ID'
WHERE CompanyName = 'West Wire Harnessing';

PRINT 'Updated Companies table with correct TenantID mappings';

-- Step 3: Update Users table with correct TenantID based on their company
-- This fixes the issue where all users currently have Avirata's tenant ID

UPDATE Users 
SET TenantID = c.TenantID
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE u.TenantID != c.TenantID OR u.TenantID IS NULL;

PRINT 'Updated Users table with correct TenantID mappings based on company affiliation';

-- Step 4: Verification - Show current tenant mappings
PRINT '';
PRINT 'Current Company-to-Tenant Mappings:';
SELECT 
    CompanyID,
    CompanyName,
    CompanyCode,
    TenantID,
    CASE 
        WHEN TenantID = 'ecb4a448-4a99-443b-aaff-063150b6c9ea' THEN 'Avirata Defence Systems'
        WHEN TenantID = '334d188b-2ac3-43a9-8bad-590957b087c2' THEN 'SASMOS HET'
        WHEN TenantID = 'd6a5d909-b6c5-4724-a46d-2641d73acff1' THEN 'FE-SIL'
        WHEN TenantID = '7732add2-c45c-472b-8da8-4e2b4699bbb0' THEN 'Glodesi'
        WHEN TenantID = 'a8dcc1ff-5cc0-4432-827b-9da18737a775' THEN 'Hanuka'
        WHEN TenantID = 'PLACEHOLDER_WWH_TENANT_ID' THEN 'West Wire Harnessing (TBD)'
        ELSE 'Unknown'
    END AS TenantName
FROM Companies
ORDER BY CompanyName;

-- Step 5: Show user distribution across tenants
PRINT '';
PRINT 'User Distribution by Tenant:';
SELECT 
    c.CompanyName,
    c.TenantID,
    COUNT(u.UserID) as UserCount,
    STRING_AGG(u.Email, ', ') as Users
FROM Companies c
LEFT JOIN Users u ON c.CompanyID = u.CompanyID
GROUP BY c.CompanyName, c.TenantID
ORDER BY c.CompanyName;

-- Step 6: Show any users with mismatched tenant IDs
PRINT '';
PRINT 'Users with Mismatched TenantIDs (should be empty after fix):';
SELECT 
    u.UserID,
    u.Email,
    u.TenantID as UserTenantID,
    c.TenantID as CompanyTenantID,
    c.CompanyName
FROM Users u
INNER JOIN Companies c ON u.CompanyID = c.CompanyID
WHERE u.TenantID != c.TenantID OR u.TenantID IS NULL;

PRINT '';
PRINT 'Tenant ID mapping fix complete!';
PRINT '';
PRINT 'IMPORTANT: Update WestWire Harnessing TenantID once you have the actual tenant ID.';
PRINT 'Run this command when you have it:';
PRINT 'UPDATE Companies SET TenantID = ''[ACTUAL_WWH_TENANT_ID]'' WHERE CompanyName = ''West Wire Harnessing'';'; 