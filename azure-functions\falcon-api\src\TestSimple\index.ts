import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';

export async function TestSimple(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}"`);

    return { 
        status: 200,
        body: 'Hello from simple test function!' 
    };
}

app.http('TestSimple', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'test-simple',
    handler: TestSimple
}); 