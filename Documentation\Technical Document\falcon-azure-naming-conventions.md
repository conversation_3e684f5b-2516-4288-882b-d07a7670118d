# Appendix A: Azure Resource Naming Conventions

*This document outlines the naming conventions for all Azure resources provisioned for the Falcon Portal project.*

## Principles

*   **Clarity:** Names should clearly indicate the resource's purpose, environment, and location.
*   **Consistency:** Follow the defined pattern strictly across all resources, noting specific exceptions.
*   **Azure Limits:** Adhere to Azure naming rules and restrictions (length, allowed characters).
*   **Cost-Consciousness:** While not directly part of the name, resource selection should prioritize free/low-cost tiers where feasible.

## Naming Pattern

*(Note: The decision has been made to use a simpler name for the primary Resource Group. This convention will apply to other resources unless otherwise specified.)*

The general pattern for Azure resources will be:

`<project-abbr>-<resource-type-abbr>-<instance-name>-<environment-abbr>-<region-abbr>-<instance-number>`

*   **`<project-abbr>`:** `fp` (Falcon Portal)
*   **`<resource-type-abbr>`:** See table below.
*   **`<instance-name>`:** A descriptive name for the specific instance (e.g., `webapp`, `sqldb`, `storage`).
*   **`<environment-abbr>`:**
    *   `dev` (Development)
    *   `stg` (Staging)
    *   `prd` (Production)
*   **`<region-abbr>`:** `cin` (Central India) - *As per requirement.*
*   **`<instance-number>`:** A numeric suffix (e.g., `001`, `002`) if multiple instances of the same type/name exist in the same environment/region. Usually `001` if only one.

**Example (Standard):** `fp-app-portal-dev-cin-001` (App Service for Falcon Portal dev environment)
**Example (Exception - Resource Group):** `FalconHub` (Primary Resource Group for the project - Created Manually)

## Resource Type Abbreviations

| Azure Service           | Abbreviation | Example Instance Name | Example Full Name (Dev)                 |
| :---------------------- | :----------- | :-------------------- | :-------------------------------------- |
| Resource Group          | `rg`         | `portal`              | `FalconHub` (Exception)                 |
| App Service Plan        | `asp`        | `frontend`            | `fp-asp-frontend-dev-cin-001`           |
| App Service (Web App)   | `app`        | `portal`              | `fp-app-portal-dev-cin-001`             |
| SQL Server              | `sql`        | `falcon`              | `fp-sql-falcon-dev-cin-001`             |
| SQL Database            | `sqldb`      | `falcon`              | `fp-sqldb-falcon-dev-cin-001`           |
| Storage Account         | `st`         | `portalassets`        | `fpstportalassetsdevcin001`             |
| Key Vault               | `kv`         | `secrets`             | `fp-kv-secrets-dev-cin-001`             |
| Application Insights    | `ai`         | `portal`              | `fp-ai-portal-dev-cin-001`              |
| Log Analytics Workspace | `log`        | `portal`              | `fp-log-portal-dev-cin-001`             |
| Virtual Network         | `vnet`       | `main`                | `fp-vnet-main-dev-cin-001`              |
| Subnet                  | `snet`       | `appservice`          | `fp-snet-appservice-dev-cin-001`        |
| Network Security Group  | `nsg`        | `appservice`          | `fp-nsg-appservice-dev-cin-001`         |
| Public IP Address       | `pip`        | `appgw`               | `fp-pip-appgw-dev-cin-001`              |
| Application Gateway     | `agw`        | `portal`              | `fp-agw-portal-dev-cin-001`             |
| CDN Profile             | `cdn`        | `portal`              | `fp-cdn-portal-dev-cin-001`             |
| CDN Endpoint            | `cdne`       | `assets`              | `fp-cdne-assets-dev-cin-001`            |
| Entra ID (Tenant)       | `entra`      | *N/A (Use Directory ID)* | *N/A*                                  |
| Entra ID App Reg        | `entraapp`   | `portal`              | `fp-entraapp-portal-dev-001`            |

*(Note: Storage Accounts have stricter naming constraints - lowercase letters and numbers only, 3-24 characters. The abbreviation is adjusted accordingly.)*

## Tagging Strategy

All resources should be tagged with at least the following:

*   `Project`: `Falcon Portal` (*Note: Resource Group `FalconHub` may use `FalconHub` initially*)
*   `Environment`: `Development` / `Staging` / `Production`
*   `Owner`: `[Your Team/Email]`
*   `CostCenter`: `[Applicable Cost Center]`
*   `CreatedDate`: `YYYY-MM-DD`

## Review and Updates

This document should be reviewed periodically and updated as new resource types are introduced or conventions need refinement. 