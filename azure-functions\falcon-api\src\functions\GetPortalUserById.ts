import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { userManagementService } from "../shared/services/userManagementService";
import { getClientPrincipal } from "../shared/authUtils"; 
import { logger } from "../shared/utils/logger";
import { PortalUser } from "../shared/interfaces"; // Import PortalUser if not already via service

export async function getPortalUserById(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log = logger.info; 
    logger.info(`Http function processed request for url "${request.url}" to get user by ID.`);

    // 1. Authentication (basic check, adapt as needed)
    // For local development, you might want to bypass this if headers are not easily emulated.
    const isLocalDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    if (!isLocalDevelopment && process.env.NODE_ENV !== 'test') {
        const principal = getClientPrincipal(request);
        if (!principal) {
            logger.warn("GetPortalUserById: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        // TODO: Add role checks if necessary, e.g., only admins or the user themselves
        // Example: if (!hasRequiredRole(principal, ['PortalAdmin']) && principal.userId !== entraId) { return { status: 403, ... }}
    } else {
        logger.warn("GetPortalUserById: Bypassing auth in development/test mode.");
    }

    // 2. Extract user ID from route parameter
    const userIdParam = request.params.entraId; // Keep the same parameter name for route consistency
    if (!userIdParam) {
        logger.warn("GetPortalUserById: User ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "User ID must be provided in the path." } };
    }

    try {
        let user: PortalUser | null = null;
        
        // 3. Determine if the parameter is a numeric UserID or an EntraID
        if (/^\d+$/.test(userIdParam)) {
            // Parameter is numeric, treat as UserID
            const userId = parseInt(userIdParam, 10);
            logger.info(`Fetching user by UserID: ${userId}`);
            user = await userManagementService.getPortalUserByUserId(userId);
        } else {
            // Parameter is not numeric, treat as EntraID
            logger.info(`Fetching user by EntraID: ${userIdParam}`);
            user = await userManagementService.getPortalUserByEntraId(userIdParam);
        }

        // 4. Return Response
        if (user) {
            return { status: 200, jsonBody: user };
        } else {
            logger.warn(`GetPortalUserById: User with ID ${userIdParam} not found.`);
            return { status: 404, jsonBody: { error: "User not found" } };
        }
    } catch (err) {
        logger.error(`Error in GetPortalUserById for ID ${userIdParam}:`, err);
        const error = err as Error;
        // Check if the error message is one that should be propagated or a generic one
        const message = error.message.startsWith("Database error while fetching user") ? error.message : "Failed to fetch user due to an internal error.";
        return { status: 500, jsonBody: { error: message } };
    }
}

app.http('GetPortalUserById', { 
    methods: ['GET'],
    route: 'portal-users/{entraId}', // Keep the same route for consistency
    authLevel: 'anonymous', 
    handler: getPortalUserById
});
