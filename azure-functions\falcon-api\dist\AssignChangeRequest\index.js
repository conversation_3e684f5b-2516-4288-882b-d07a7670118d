"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const emailService_1 = require("../shared/services/emailService");
const sql = __importStar(require("mssql"));
async function assignChangeRequest(request, context) {
    context.log('AssignChangeRequest function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json();
        const { developerId, userId, assignmentNotes = '' } = body;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        if (!developerId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Developer ID is required'
                    }
                }
            };
        }
        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required for assignment'
                    }
                }
            };
        }
        // First, check if the request exists and can be assigned
        const checkQuery = `
            SELECT RequestID, Status, AssignedToDevID 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        const currentRequest = checkResult.recordset[0];
        // Check if request can be assigned (must be approved)
        if (currentRequest.Status !== 'Approved') {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be assigned in current status: ${currentRequest.Status}. Request must be approved first.`
                    }
                }
            };
        }
        // Verify the developer exists and is active
        const developerCheckQuery = `
            SELECT UserID, FirstName, LastName, IsActive 
            FROM Users 
            WHERE UserID = @developerId AND IsActive = 1
        `;
        const developerCheckParams = [
            { name: 'developerId', type: sql.Int, value: parseInt(developerId) }
        ];
        const developerResult = await (0, db_1.executeQuery)(developerCheckQuery, developerCheckParams);
        if (developerResult.recordset.length === 0) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_DEVELOPER',
                        message: 'Selected developer not found or inactive'
                    }
                }
            };
        }
        const developer = developerResult.recordset[0];
        // Update the request with assignment
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                AssignedToDevID = @developerId,
                AssignedDate = GETDATE(),
                Status = 'Assigned',
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'developerId', type: sql.Int, value: parseInt(developerId) },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Assigned', @userId, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: `Assigned to ${developer.FirstName} ${developer.LastName}${assignmentNotes ? '. Notes: ' + assignmentNotes : ''}` }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Add assignment comment if notes provided
        if (assignmentNotes.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, 'DevUpdate', 0, @userId, GETDATE()
                )
            `;
            const commentParams = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: `Assignment Notes: ${assignmentNotes.trim()}` },
                { name: 'userId', type: sql.Int, value: parseInt(userId) }
            ];
            await (0, db_1.executeQuery)(commentQuery, commentParams);
        }
        // Return updated request details
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Status as status,
                cr.Priority as priority,
                cr.Description as description,
                cr.AssignedDate as assignedDate,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(dev.FirstName, ' ', dev.LastName) as developerName,
                dev.Email as developerEmail,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequests cr
                LEFT JOIN Users dev ON cr.AssignedToDevID = dev.UserID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies companies ON requester.CompanyID = companies.CompanyID
            WHERE cr.RequestID = @requestId
        `;
        const detailsResult = await (0, db_1.executeQuery)(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];
        // Send email notification to assigned developer and requester asynchronously
        try {
            const emailData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description,
                priority: requestDetails.priority,
                status: 'Assigned',
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                assigneeName: requestDetails.developerName,
                assigneeEmail: requestDetails.developerEmail,
                companyName: requestDetails.companyName || 'SASMOS Group',
                comments: assignmentNotes,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${requestDetails.requestId}`,
                createdDate: requestDetails.createdDate,
                dueDate: requestDetails.dueDate
            };
            emailService_1.EmailService.getInstance().sendChangeRequestAssigned(emailData).catch((error) => {
                context.error('Failed to send assignment email notification:', error);
            });
            context.log(`Email notification queued for assigned change request ${requestId}`);
        }
        catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the assignment if email fails
        }
        context.log(`Successfully assigned change request ${requestId} to developer ${developerId}`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request assigned successfully',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    assignedDate: requestDetails.assignedDate,
                    developerName: requestDetails.developerName,
                    requesterName: requestDetails.requesterName
                }
            }
        };
    }
    catch (error) {
        context.error('Error in AssignChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while assigning the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.assignChangeRequest = assignChangeRequest;
functions_1.app.http('AssignChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/assign',
    handler: assignChangeRequest
});
//# sourceMappingURL=index.js.map