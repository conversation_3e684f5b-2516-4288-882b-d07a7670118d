# =============================================
# Test Database Connection
# =============================================

$ServerInstance = "fp-sql-falcon-dev-cin-001.database.windows.net"
$Database = "fp-sqldb-falcon-dev-cin-001"
$Username = "falconAdmin"

# Prompt for password securely
$SecurePassword = Read-Host -AsSecureString -Prompt "Enter database password for $Username"

Write-Host "Testing connection to Azure SQL Database..." -ForegroundColor Yellow
Write-Host "Server: $ServerInstance" -ForegroundColor Gray
Write-Host "Database: $Database" -ForegroundColor Gray
Write-Host "Username: $Username" -ForegroundColor Gray

try {
    # Import SqlServer module if not already loaded
    if (!(Get-Module -Name SqlServer -ListAvailable)) {
        Write-Host "Installing SqlServer module..." -ForegroundColor Yellow
        Install-Module -Name SqlServer -Force -AllowClobber
    }

    # Test with a simple query
    $TestQuery = "SELECT GETDATE() as CurrentTime, @@VERSION as SqlVersion"
    
    Write-Host "Executing test query..." -ForegroundColor Yellow
    $Result = Invoke-Sqlcmd -ServerInstance $ServerInstance -Database $Database -Username $Username -Password $SecurePassword -Query $TestQuery -Verbose
    
    Write-Host "✅ Connection successful!" -ForegroundColor Green
    Write-Host "Current Time: $($Result.CurrentTime)" -ForegroundColor White
    Write-Host "SQL Version: $($Result.SqlVersion)" -ForegroundColor White
    
    # Test if ChangeRequests table exists
    $TableQuery = "SELECT COUNT(*) as RecordCount FROM ChangeRequests"
    $TableResult = Invoke-Sqlcmd -ServerInstance $ServerInstance -Database $Database -Username $Username -Password $SecurePassword -Query $TableQuery
    Write-Host "ChangeRequests table has $($TableResult.RecordCount) records" -ForegroundColor White
    
} catch {
    Write-Host "❌ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Possible issues:" -ForegroundColor Yellow
    Write-Host "1. Incorrect username or password" -ForegroundColor Yellow
    Write-Host "2. Database server name is wrong" -ForegroundColor Yellow
    Write-Host "3. Database name is wrong" -ForegroundColor Yellow
    Write-Host "4. Firewall blocking connection" -ForegroundColor Yellow
    Write-Host "5. DNS records not propagated yet" -ForegroundColor Yellow
}

Write-Host "Test completed." -ForegroundColor Cyan 