{"version": 3, "file": "UploadDocument.js", "sourceRoot": "", "sources": ["../../src/functions/UploadDocument.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAiF;AACjF,2CAA6B;AAC7B,oGAAoG;AAEpG,mCAAmC;AACnC,MAAM,+BAA+B,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAAC;AAC1F,MAAM,cAAc,GAAG,qBAAqB,CAAC;AAWtC,KAAK,UAAU,cAAc,CAAC,OAAoB,EAAE,OAA0B;IACjF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAEhD,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE;YAC3B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,CAAC;SACjE;QAED,wCAAwC;QACxC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QAC9D,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3C,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,CAAC;SACxF;QAED,2CAA2C;QAC3C,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,EAQrC,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;YAC5C,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,CAAC;SACjF;QAED,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;QAEvC,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAC7B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,CAAC;SACrF;QAED,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACjD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,CAAC;SAC7E;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAEhD,qBAAqB;QACrB,MAAM,iBAAiB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC5C,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE;oBAC5B,KAAK,EAAE,cAAc,aAAa,mCAAmC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBACtG,EAAC,CAAC;SACN;QAED,4BAA4B;QAC5B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,cAAc,GAAG,GAAG,SAAS,IAAI,QAAQ,EAAE,CAAC;QAElD,+BAA+B;QAC/B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI;YACA,IAAI,+BAA+B,EAAE;gBACjC,sFAAsF;gBACtF,qGAAqG;gBACrG,gFAAgF;gBAChF,iCAAiC;gBAEjC,oCAAoC;gBACpC,OAAO,GAAG,cAAc,cAAc,EAAE,CAAC;gBACzC,eAAM,CAAC,IAAI,CAAC,2EAA2E,OAAO,EAAE,CAAC,CAAC;aACrG;iBAAM;gBACH,mDAAmD;gBACnD,OAAO,GAAG,cAAc,cAAc,EAAE,CAAC;gBACzC,eAAM,CAAC,IAAI,CAAC,2DAA2D,OAAO,EAAE,CAAC,CAAC;aACrF;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;SACnF;QAED,8CAA8C;QAC9C,MAAM,gBAAgB,GAAG;;;;;SAKxB,CAAC;QAEF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE;YAC3D,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE;SACxD,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,WAAW,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,wCAAwC;QAEvF,uCAAuC;QACvC,MAAM,WAAW,GAAG;;;;;;;;;;;;SAYnB,CAAC;QAEF,MAAM,YAAY,GAAqB;YACnC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACnE,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE;YACxF,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;YACzD,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE;YACnE,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE;YACxD,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;YACvD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YACxG,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;YACtD,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,WAAW,KAAK,KAAK,EAAE;YAC7E,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE;YACrD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE;SAC3D,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACnE,MAAM,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;QAEzD,IAAI,CAAC,UAAU,EAAE;YACb,eAAM,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;SACnF;QAED,0BAA0B;QAC1B,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3C,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;gBACjC,IAAI;oBACA,wCAAwC;oBACxC,MAAM,cAAc,GAAG;;;;;;qBAMtB,CAAC;oBAEF,MAAM,SAAS,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE;wBACjD,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE;qBACjE,CAAC,CAAC;oBAEH,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;oBAE5C,IAAI,KAAK,EAAE;wBACP,uBAAuB;wBACvB,MAAM,SAAS,GAAG;;;yBAGjB,CAAC;wBAEF,MAAM,IAAA,iBAAY,EAAC,SAAS,EAAE;4BAC1B,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE;4BACxD,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;yBACjD,CAAC,CAAC;qBACN;iBACJ;gBAAC,OAAO,QAAQ,EAAE;oBACf,eAAM,CAAC,IAAI,CAAC,0CAA0C,OAAO,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC7E,2BAA2B;iBAC9B;aACJ;SACJ;QAED,qDAAqD;QACrD,MAAM,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;SA2BnB,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE;YACjD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE;SAC3D,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEjD,MAAM,QAAQ,GAAG;YACb,EAAE,EAAE,cAAc,CAAC,UAAU,CAAC,QAAQ,EAAE;YACxC,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,QAAQ,EAAE;gBACN,EAAE,EAAE,cAAc,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC/C,IAAI,EAAE,cAAc,CAAC,YAAY,IAAI,eAAe;aACvD;YACD,OAAO,EAAE,cAAc,CAAC,WAAW;YACnC,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,SAAS,EAAE;gBACP,IAAI,EAAE,cAAc,CAAC,aAAa;gBAClC,KAAK,EAAE,cAAc,CAAC,cAAc;aACvC;YACD,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;SAC5B,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,kDAAkD,UAAU,aAAa,MAAM,EAAE,CAAC,CAAC;QAE/F,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,gCAAgC;gBACzC,QAAQ,EAAE,QAAQ;aACrB;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,2BAA2B;gBACpC,KAAK,EAAE,YAAY;aACtB;SACJ,CAAC;KACL;AACL,CAAC;AAtQD,wCAsQC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE;IACvB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,kBAAkB;IACzB,OAAO,EAAE,cAAc;CAC1B,CAAC,CAAC"}