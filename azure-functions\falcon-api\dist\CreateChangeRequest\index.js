"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
// Azure Blob Storage configuration
const STORAGE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'falconhubstorage';
const STORAGE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const CONTAINER_NAME = 'change-request-images';
// Helper function to upload base64 image to blob storage
async function uploadBase64ImageToBlob(base64Data, requestId, blockIndex) {
    if (!STORAGE_ACCOUNT_KEY) {
        throw new Error('Azure Storage Account Key not configured');
    }
    // Extract image data and mime type from base64 data URL
    const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
        throw new Error('Invalid base64 data URL format');
    }
    const mimeType = matches[1];
    const imageData = matches[2];
    const buffer = Buffer.from(imageData, 'base64');
    // Generate unique blob name
    const fileExtension = mimeType.split('/')[1] || 'jpg';
    const blobName = `cr-${requestId}/image-${blockIndex}-${(0, uuid_1.v4)()}.${fileExtension}`;
    // Create blob service client
    const sharedKeyCredential = new storage_blob_1.StorageSharedKeyCredential(STORAGE_ACCOUNT_NAME, STORAGE_ACCOUNT_KEY);
    const blobServiceClient = new storage_blob_1.BlobServiceClient(`https://${STORAGE_ACCOUNT_NAME}.blob.core.windows.net`, sharedKeyCredential);
    // Get container client
    const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
    // Ensure container exists
    try {
        await containerClient.createIfNotExists({
            access: 'blob' // Allow public read access to blobs
        });
    }
    catch (error) {
        console.log('Container creation info:', error);
    }
    // Upload blob
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    await blockBlobClient.upload(buffer, buffer.length, {
        blobHTTPHeaders: {
            blobContentType: mimeType
        }
    });
    // Return the blob URL
    return blockBlobClient.url;
}
async function createChangeRequest(request, context) {
    context.log('CreateChangeRequest function invoked.');
    try {
        const body = await request.json();
        const { title, description, typeId, priority = 'Medium', businessJustification, expectedBenefit, requestedCompletionDate, requestedBy, companyId, departmentId, content = [], // Rich content array
        submitImmediately = false, // Whether to submit immediately or keep as draft
        draftId = null // Draft ID to clean up if submitting
         } = body;
        // Validate required fields
        if (!title || !description || !typeId || !requestedBy) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Missing required fields: title, description, typeId, requestedBy'
                    }
                }
            };
        }
        // Validate priority
        const validPriorities = ['Low', 'Medium', 'High', 'Critical'];
        if (!validPriorities.includes(priority)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Invalid priority. Must be one of: Low, Medium, High, Critical'
                    }
                }
            };
        }
        // Determine initial status based on submitImmediately flag
        const initialStatus = submitImmediately ? 'Under Review' : 'Draft';
        // Create the change request
        const insertQuery = `
            INSERT INTO ChangeRequests (
                Title, Description, TypeID, Priority, Status, 
                BusinessJustification, ExpectedBenefit, RequestedCompletionDate,
                RequestedBy, CompanyID, DepartmentID, CreatedDate
            )
            OUTPUT INSERTED.RequestID, INSERTED.RequestNumber
            VALUES (
                @title, @description, @typeId, @priority, @status,
                @businessJustification, @expectedBenefit, @requestedCompletionDate,
                @requestedBy, @companyId, @departmentId, GETDATE()
            )
        `;
        const parameters = [
            { name: 'title', type: sql.NVarChar, value: title },
            { name: 'description', type: sql.NVarChar, value: description },
            { name: 'typeId', type: sql.Int, value: parseInt(typeId.toString()) },
            { name: 'priority', type: sql.NVarChar, value: priority },
            { name: 'status', type: sql.NVarChar, value: initialStatus },
            { name: 'businessJustification', type: sql.NVarChar, value: businessJustification || null },
            { name: 'expectedBenefit', type: sql.NVarChar, value: expectedBenefit || null },
            { name: 'requestedCompletionDate', type: sql.DateTime, value: requestedCompletionDate ? new Date(requestedCompletionDate) : null },
            { name: 'requestedBy', type: sql.Int, value: parseInt(requestedBy.toString()) },
            { name: 'companyId', type: sql.Int, value: companyId ? parseInt(companyId.toString()) : null },
            { name: 'departmentId', type: sql.Int, value: departmentId ? parseInt(departmentId.toString()) : null }
        ];
        // Debug logging for parameters
        context.log('Parameters being sent to database:', parameters.map(p => ({
            name: p.name,
            value: p.value,
            isNull: p.value === null,
            isUndefined: p.value === undefined
        })));
        const result = await (0, db_1.executeQuery)(insertQuery, parameters);
        const newRequest = result.recordset[0];
        const requestId = newRequest.RequestID;
        // Create initial history entry
        const historyComment = submitImmediately ? 'Change request created and submitted for review' : 'Change request created';
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, NULL, @status, @requestedBy, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: requestId },
            { name: 'status', type: sql.NVarChar, value: initialStatus },
            { name: 'requestedBy', type: sql.Int, value: parseInt(requestedBy.toString()) },
            { name: 'comments', type: sql.NVarChar, value: historyComment }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Clean up draft if request was submitted immediately
        if (submitImmediately && draftId) {
            try {
                const deleteDraftQuery = `
                    DELETE FROM ChangeRequestDrafts 
                    WHERE DraftID = @draftId AND RequestedBy = @requestedBy
                `;
                const deleteDraftParams = [
                    { name: 'draftId', type: sql.NVarChar, value: draftId },
                    { name: 'requestedBy', type: sql.Int, value: parseInt(requestedBy.toString()) }
                ];
                await (0, db_1.executeQuery)(deleteDraftQuery, deleteDraftParams);
                context.log(`Cleaned up draft ${draftId} after submission`);
            }
            catch (draftError) {
                context.log(`Warning: Failed to clean up draft ${draftId}:`, draftError);
                // Don't fail the entire request if draft cleanup fails
            }
        }
        // Save rich content if provided
        context.log('Content received:', { content, isArray: Array.isArray(content), length: content?.length });
        if (content && Array.isArray(content) && content.length > 0) {
            context.log(`Saving ${content.length} content blocks for request ${requestId}`);
            for (let i = 0; i < content.length; i++) {
                const block = content[i];
                context.log(`Processing content block ${i}:`, block);
                let imageUrl = null;
                // If this is an image block with base64 data, upload to blob storage
                if (block.type === 'image' && block.imageUrl && block.imageUrl.startsWith('data:')) {
                    try {
                        context.log(`Uploading image for block ${i} to blob storage`);
                        imageUrl = await uploadBase64ImageToBlob(block.imageUrl, requestId, i);
                        context.log(`Image uploaded successfully: ${imageUrl}`);
                    }
                    catch (error) {
                        context.error(`Failed to upload image for block ${i}:`, error);
                        // Continue without the image rather than failing the entire request
                        imageUrl = null;
                    }
                }
                else if (block.imageUrl && !block.imageUrl.startsWith('data:')) {
                    // Already a URL, use as-is
                    imageUrl = block.imageUrl;
                }
                const contentQuery = `
                    INSERT INTO ChangeRequestContent (
                        RequestID, ContentType, ContentData, SortOrder,
                        ImageUrl, ImageCaption, ImageAltText, 
                        OriginalImageSize, CompressedImageSize
                    )
                    VALUES (
                        @requestId, @contentType, @contentData, @sortOrder,
                        @imageUrl, @imageCaption, @imageAltText,
                        @originalImageSize, @compressedImageSize
                    )
                `;
                const contentParams = [
                    { name: 'requestId', type: sql.Int, value: requestId },
                    { name: 'contentType', type: sql.NVarChar, value: block.type || 'text' },
                    { name: 'contentData', type: sql.NVarChar, value: typeof block.data === 'object' ? JSON.stringify(block.data) : (block.content || block.data || '') },
                    { name: 'sortOrder', type: sql.Int, value: i },
                    { name: 'imageUrl', type: sql.NVarChar, value: imageUrl },
                    { name: 'imageCaption', type: sql.NVarChar, value: block.imageCaption || null },
                    { name: 'imageAltText', type: sql.NVarChar, value: block.imageAltText || null },
                    { name: 'originalImageSize', type: sql.Int, value: block.originalImageSize || null },
                    { name: 'compressedImageSize', type: sql.Int, value: block.compressedImageSize || null }
                ];
                await (0, db_1.executeQuery)(contentQuery, contentParams);
                context.log(`Saved content block ${i}: ${block.type}`);
            }
        }
        // Fetch the complete created request
        const fetchQuery = `
            SELECT 
                cr.RequestID,
                cr.RequestNumber,
                cr.Title,
                cr.Description,
                cr.Priority,
                cr.Status,
                cr.BusinessJustification,
                cr.ExpectedBenefit,
                cr.RequestedCompletionDate,
                cr.CreatedDate,
                crt.TypeName,
                crt.Description AS TypeDescription,
                CONCAT(requester.FirstName, ' ', requester.LastName) AS RequesterName,
                c.CompanyName
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON cr.CompanyID = c.CompanyID
            WHERE cr.RequestID = @requestId
        `;
        const fetchParams = [
            { name: 'requestId', type: sql.Int, value: requestId }
        ];
        const createdRequest = await (0, db_1.executeQuery)(fetchQuery, fetchParams);
        const successMessage = submitImmediately
            ? 'Change request created and submitted for review'
            : 'Change request created successfully';
        return {
            status: 201,
            jsonBody: {
                success: true,
                message: successMessage,
                data: createdRequest.recordset[0]
            }
        };
    }
    catch (error) {
        context.error('Error in CreateChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while creating the change request'
                }
            }
        };
    }
}
exports.createChangeRequest = createChangeRequest;
functions_1.app.http('CreateChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests',
    handler: createChangeRequest
});
//# sourceMappingURL=index.js.map