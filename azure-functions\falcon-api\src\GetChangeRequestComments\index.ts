import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';

export async function getChangeRequestComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('GetChangeRequestComments function invoked.');
    
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        // First, verify the change request exists
        const checkQuery = `
            SELECT RequestID 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;

        const checkParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }

        // Get all comments for the request
        const query = `
            SELECT 
                c.CommentID as commentId,
                c.RequestID as requestId,
                c.CommentText as commentText,
                c.CommentType as commentType,
                c.IsInternal as isInternal,
                c.ParentCommentID as parentCommentId,
                c.CreatedBy as createdBy,
                CONCAT(u.FirstName, ' ', u.LastName) as createdByName,
                c.CreatedDate as createdDate,
                c.ModifiedBy as modifiedBy,
                c.ModifiedDate as modifiedDate,
                c.IsEdited as isEdited
            FROM ChangeRequestComments c
                LEFT JOIN Users u ON c.CreatedBy = u.UserID
            WHERE c.RequestID = @requestId
            ORDER BY c.CreatedDate ASC
        `;

        const params: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];

        const result = await executeQuery(query, params);

        context.log(`Retrieved ${result.recordset.length} comments for change request ${requestId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                data: result.recordset
            }
        };

    } catch (error: any) {
        context.error('Error in GetChangeRequestComments:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while retrieving comments',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}

app.http('GetChangeRequestComments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/comments',
    handler: getChangeRequestComments
}); 