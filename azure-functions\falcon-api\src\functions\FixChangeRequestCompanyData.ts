import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';

export async function FixChangeRequestCompanyData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('FixChangeRequestCompanyData function triggered.');
    try {
        const pool: ConnectionPool = await getPool();

        const companyIdUpdateQuery = `
            UPDATE ChangeRequests
            SET CompanyID = 4
            WHERE CompanyID IS NULL;
        `;

        const companyIdUpdateResult = await pool.request().query(companyIdUpdateQuery);

        const companyIdMessage = `Updated ${companyIdUpdateResult.rowsAffected[0]} change requests with CompanyID 4.`;
        context.log(companyIdMessage);
        
        // Use RequestedCompletionDate as the deployment date for any records where it's missing
        const deploymentDateUpdateQuery = `
            UPDATE ChangeRequests
            SET DeploymentDate = RequestedCompletionDate
            WHERE DeploymentDate IS NULL 
            AND RequestedCompletionDate IS NOT NULL
            AND CompanyID = 4;
        `;

        const deploymentDateUpdateResult = await pool.request().query(deploymentDateUpdateQuery);
        const deploymentMessage = `Updated ${deploymentDateUpdateResult.rowsAffected[0]} requests with deployment dates from completion dates.`;
        context.log(deploymentMessage);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: `${companyIdMessage} ${deploymentMessage}`,
                companyIdUpdateCount: companyIdUpdateResult.rowsAffected[0],
                deploymentDateUpdateCount: deploymentDateUpdateResult.rowsAffected[0],
            },
        };
    } catch (error) {
        context.log('Error in FixChangeRequestCompanyData:', error);
        const err = error as Error;
        return {
            status: 500,
            jsonBody: {
                success: false,
                error: 'Failed to update change requests.',
                details: err.message,
            },
        };
    }
}

app.http('FixChangeRequestCompanyData', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'data-fix/set-company-and-dates',
    handler: FixChangeRequestCompanyData,
}); 