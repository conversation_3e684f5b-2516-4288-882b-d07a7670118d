"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserOverrides = getUserOverrides;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
function getUserOverrides(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        const entraId = request.params.entraId;
        context.log(`Http function processed request for GetUserOverrides: ${entraId}`);
        if (!entraId) {
            return {
                status: 400,
                jsonBody: { message: "EntraID parameter is required." }
            };
        }
        try {
            // First check if the user exists in our database
            const userQuery = `
            SELECT UserID, IsActive
            FROM Users
            WHERE EntraID = @EntraID
        `;
            const userResult = yield (0, db_1.executeQuery)(userQuery, { EntraID: entraId });
            if (userResult.recordset.length === 0) {
                // User not found in our database
                return {
                    status: 404,
                    jsonBody: {
                        message: "User not found in the portal database."
                    }
                };
            }
            const userId = userResult.recordset[0].UserID;
            const isActive = userResult.recordset[0].IsActive;
            // Get user's roles
            const rolesQuery = `
            SELECT r.RoleID, r.RoleName
            FROM UserRoles ur
            JOIN Roles r ON ur.RoleID = r.RoleID
            WHERE ur.UserID = @UserID AND ur.IsActive = 1 AND r.IsActive = 1
        `;
            const rolesResult = yield (0, db_1.executeQuery)(rolesQuery, { UserID: userId });
            const roles = rolesResult.recordset.map(role => role.RoleName);
            return {
                status: 200,
                jsonBody: {
                    isActive: isActive === 1,
                    roles: roles
                }
            };
        }
        catch (error) {
            context.error(`Error fetching user overrides: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error fetching user overrides.",
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('GetUserOverrides', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'users/overrides/{entraId}',
    handler: getUserOverrides
});
//# sourceMappingURL=index.js.map