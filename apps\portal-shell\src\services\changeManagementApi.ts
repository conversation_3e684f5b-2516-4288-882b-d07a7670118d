// Change Management API Service

// Helper function to make API calls
const callApi = async (endpoint: string, options: RequestInit = {}): Promise<unknown> => {
  const response = await fetch(`/api${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  // Handle 204 No Content responses
  if (response.status === 204 || response.headers.get('content-length') === '0') {
    return null;
  }

  return response.json();
};

export interface ChangeRequestComment {
  commentId: number;
  requestId: number;
  commentText: string;
  commentType: 'General' | 'ApprovalNote' | 'DevUpdate' | 'Question' | 'Answer';
  isInternal: boolean;
  parentCommentId?: number;
  createdBy: number;
  createdByName: string;
  createdDate: string;
  modifiedBy?: number;
  modifiedDate?: string;
  isEdited: boolean;
}

export interface Developer {
  userId: number;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  departmentName?: string;
  companyName?: string;
  currentAssignments: number;
}

export interface ChangeRequestHistory {
  historyId: number;
  requestId: number;
  statusFrom?: string;
  statusTo: string;
  changedBy: number;
  changedByName: string;
  changeDate: string;
  comments?: string;
  progressFrom?: number;
  progressTo?: number;
}

export interface DashboardStats {
  totalRequests: number;
  pendingApproval: number;
  inDevelopment: number;
  completed: number;
  rejected: number;
  byPriority: Record<string, number>;
  byStatus: Record<string, number>;
  byType: Record<string, number>;
}

export interface CalendarEvent {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  status: string;
  priority: string;
  type: string;
  requestNumber?: string;
  description?: string;
  requesterName?: string;
  assigneeName?: string;
  companyName?: string;
  color?: string;
  deploymentDuration?: number;
  deploymentLocation?: string;
  deploymentType?: string;
  requiresSystemDowntime?: boolean;
}

export interface ChangeRequest {
  requestId: number;
  requestNumber: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Draft' | 'Submitted' | 'Under Review' | 'Approved' | 'Rejected' | 'In Development' | 'Completed';
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  developmentProgress: number;
  plannedStartDate?: string;
  plannedCompletionDate?: string;
  actualStartDate?: string;
  actualCompletionDate?: string;
  createdDate: string;
  approvedDate?: string;
  typeName: string;
  typeDescription: string;
  estimatedDays: number;
  requesterName: string;
  requesterEmail?: string;
  companyName: string;
  departmentName?: string;
  approverName?: string;
  developerName?: string;
  rejectionReason?: string;
}

export interface ChangeRequestDetails extends ChangeRequest {
  content: ContentBlock[];
  businessReason: string;
  implementationPlan: string;
  testingPlan: string;
  communicationPlan: string;
  rollbackPlan: string;
  modifiedDate?: string;
}

export interface ContentBlock {
  contentType: string;
  contentData: string;
  sortOrder: number;
  imageUrl?: string;
  imageCaption?: string;
  imageAltText?: string;
}

export interface CreateChangeRequestData {
  title: string;
  description: string;
  typeId: number;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  requestedBy: number;
  companyId?: number;
  departmentId?: number;
  content?: ContentItem[];
}

export interface ContentItem {
  type: 'text' | 'heading' | 'image' | 'code' | 'list';
  data?: string | object;
  content?: string;
  imageUrl?: string;
  imageCaption?: string;
  imageAltText?: string;
  originalImageSize?: number;
  compressedImageSize?: number;
}

export interface ChangeRequestType {
  typeId: number;
  typeName: string;
  description: string;
  requiresApproval: boolean;
  approvalRoleId: number;
  estimatedDays: number;
  formSchema?: string;
  isActive: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

export interface ChangeRequestFilters {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  priority?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface CreateChangeRequestRequest {
  title: string;
  description: string;
  typeId: number;
  priority: string;
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  requestedBy: number;
  companyId?: number;
  departmentId?: number;
  content?: ContentItem[];
  submitImmediately?: boolean; // Whether to submit immediately or keep as draft
  draftId?: string | null; // Draft ID to clean up if submitting
}

class ChangeManagementApiService {
  private baseUrl = '/api';

  // Get all change requests with filtering and pagination
  async getChangeRequests(filters: ChangeRequestFilters = {}): Promise<PaginatedResponse<ChangeRequest>> {
    const queryParams = new URLSearchParams();
    
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.pageSize) queryParams.append('pageSize', filters.pageSize.toString());
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.priority) queryParams.append('priority', filters.priority);
    if (filters.dateFrom) queryParams.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) queryParams.append('dateTo', filters.dateTo);

    const response = await fetch(`${this.baseUrl}/change-requests?${queryParams}`);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to fetch change requests');
    }

    return response.json();
  }

  // Get a single change request by ID  
  async getChangeRequestById(requestId: number): Promise<ChangeRequestDetails> {
    const response = await fetch(`${this.baseUrl}/change-requests/${requestId}`);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to fetch change request details');
    }

    const result = await response.json();
    return result; // Return the result directly, not result.data
  }

  // Create a new change request
  async createChangeRequest(request: CreateChangeRequestRequest): Promise<ChangeRequestDetails> {
    const response = await fetch(`${this.baseUrl}/change-requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to create change request');
    }

    const result = await response.json();
    return result.data;
  }

  // Update a change request (only for requests in "Under Review" status)
  async updateChangeRequest(requestId: number, data: Partial<CreateChangeRequestData>): Promise<ChangeRequest> {
    const response = await callApi(`/change-requests/${requestId}`, {
      method: 'PUT',
      body: JSON.stringify({
        ...data,
        userId: 1 // TODO: Get from user context
      })
    }) as { data: ChangeRequest };
    return response.data;
  }

  // Submit a change request for approval
  async submitChangeRequest(requestId: number): Promise<ChangeRequest> {
    const response = await callApi(`/change-requests/${requestId}/submit`, {
      method: 'POST'
    }) as { data: ChangeRequest };
    return response.data;
  }

  // Approve a change request
  async approveChangeRequest(requestId: number, comments: string = ''): Promise<ChangeRequest> {
    const response = await fetch(`${this.baseUrl}/change-requests/${requestId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        comments,
        userId: 1 // TODO: Get from user context
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to approve change request');
    }

    const result = await response.json();
    return result.data;
  }

  // Reject a change request
  async rejectChangeRequest(requestId: number, reason: string): Promise<ChangeRequest> {
    const response = await fetch(`${this.baseUrl}/change-requests/${requestId}/reject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        reason,
        userId: 1 // TODO: Get from user context
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to reject change request');
    }

    const result = await response.json();
    return result.data;
  }

  // Request more information on a change request
  async requestMoreInfoChangeRequest(requestId: number, comments: string = ''): Promise<ChangeRequest> {
    const response = await fetch(`${this.baseUrl}/change-requests/${requestId}/request-info`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        comments,
        userId: 1 // TODO: Get from user context
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to request more information');
    }

    const result = await response.json();
    return result.data;
  }

  // Update development progress
  async updateProgress(requestId: number, progress: number, notes?: string): Promise<ChangeRequest> {
    const response = await callApi(`/change-requests/${requestId}/progress`, {
      method: 'POST',
      body: JSON.stringify({ progress, notes })
    }) as { data: ChangeRequest };
    return response.data;
  }

  // Get change request types
  async getChangeRequestTypes(): Promise<ChangeRequestType[]> {
    const response = await callApi('/change-request-types') as ChangeRequestType[];
    return response;
  }

  // Get change request comments
  async getComments(requestId: number): Promise<ChangeRequestComment[]> {
    const response = await callApi(`/change-requests/${requestId}/comments`) as { success: boolean; data: ChangeRequestComment[] };
    return response.data;
  }

  // Add a comment to a change request
  async addComment(requestId: number, comment: string, commentType: string = 'General', isInternal: boolean = false, parentCommentId?: number): Promise<ChangeRequestComment> {
    const response = await callApi(`/change-requests/${requestId}/comments`, {
      method: 'POST',
      body: JSON.stringify({
        comment: comment,
        commentType,
        isInternal,
        parentCommentId,
        userId: 1 // TODO: Get from user context
      })
    }) as { success: boolean; data: ChangeRequestComment };
    return response.data;
  }

  // Get change request history
  async getHistory(requestId: number): Promise<ChangeRequestHistory[]> {
    const response = await callApi(`/change-requests/${requestId}/history`) as ChangeRequestHistory[];
    return response;
  }

  // Upload image for rich content
  async uploadImage(file: File, requestId?: number): Promise<{ imageUrl: string; originalSize: number; compressedSize: number }> {
    const formData = new FormData();
    formData.append('image', file);
    if (requestId) {
      formData.append('requestId', requestId.toString());
    }

    const response = await callApi('/change-requests/upload-image', {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    }) as { imageUrl: string; originalSize: number; compressedSize: number };
    return response;
  }

  // Get dashboard statistics
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await callApi('/change-management/dashboard-stats') as DashboardStats;
    return response;
  }

  // Get change calendar data
  async getChangeCalendar(year: number, month: number): Promise<CalendarEvent[]> {
    try {
      // Get all change requests and filter for the specific month
      const allRequests = await this.getChangeRequests({ 
        pageSize: 1000, // Get a large number to ensure we get all requests
        status: undefined // Get all statuses
      });
      
      // Filter requests that have dates in the specified month/year
      const monthStart = new Date(year, month - 1, 1);
      const monthEnd = new Date(year, month, 0);
      
      const calendarEvents: CalendarEvent[] = allRequests.data
        .filter(request => {
          // Check if the request has a date that falls in the specified month
          const requestDate = request.requestedCompletionDate 
            ? new Date(request.requestedCompletionDate)
            : request.plannedCompletionDate 
            ? new Date(request.plannedCompletionDate)
            : null;
            
          if (!requestDate) return false;
          
          return requestDate >= monthStart && requestDate <= monthEnd;
        })
        .map(request => {
          const eventDate = request.requestedCompletionDate 
            ? new Date(request.requestedCompletionDate)
            : new Date(request.plannedCompletionDate!);
            
          return {
            id: request.requestId,
            title: request.title,
            startDate: eventDate.toISOString(),
            endDate: eventDate.toISOString(),
            status: request.status,
            priority: request.priority,
            type: request.typeName || 'General',
            requestNumber: request.requestNumber,
            description: request.description,
            requesterName: request.requesterName,
            assigneeName: request.developerName || '',
            companyName: request.companyName || '',
            color: this.getPriorityColor(request.priority)
          };
        });
      
      return calendarEvents;
    } catch (error) {
      console.error('Failed to fetch calendar events:', error);
      return [];
    }
  }

  // Helper method to get priority colors
  private getPriorityColor(priority: string): string {
    switch (priority) {
      case 'Critical': return '#FF4444';
      case 'High': return '#FF8800';
      case 'Medium': return '#4488FF';
      case 'Low': return '#44AA44';
      default: return '#666666';
    }
  }

  // Get available developers for assignment
  async getDevelopers(): Promise<Developer[]> {
    const response = await callApi('/developers') as { success: boolean; data: Developer[] };
    return response.data;
  }

  // Assign a change request to a developer
  async assignChangeRequest(requestId: number, developerId: number, assignmentNotes: string = ''): Promise<ChangeRequest> {
    const response = await callApi(`/change-requests/${requestId}/assign`, {
      method: 'POST',
      body: JSON.stringify({
        developerId,
        assignmentNotes,
        userId: 1 // TODO: Get from user context
      })
    }) as { data: ChangeRequest };
    return response.data;
  }

  // Update requested completion date (Admin/IT Head only)
  async updateRequestedCompletionDate(requestId: number, newDate: string, reason: string = ''): Promise<ChangeRequest> {
    const response = await fetch(`${this.baseUrl}/change-requests/${requestId}/update-completion-date`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        requestedCompletionDate: newDate,
        reason: reason,
        userId: 1 // TODO: Get from user context
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error?.message || 'Failed to update completion date');
    }

    const result = await response.json();
    return result.data;
  }

}

export const changeManagementApi = new ChangeManagementApiService(); 