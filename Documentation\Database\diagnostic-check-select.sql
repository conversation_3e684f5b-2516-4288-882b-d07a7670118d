-- Diagnostic Script Using SELECT Statements
-- This will show results in the Results tab instead of Messages tab

-- Check Companies table structure
SELECT 'Companies Table Columns' as DiagnosticSection, 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Companies'
ORDER BY ORDINAL_POSITION;

-- Check Users table structure  
SELECT 'Users Table Columns' as DiagnosticSection,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users'
ORDER BY ORDINAL_POSITION;

-- Check if TenantID exists in Companies table
SELECT 'TenantID Check' as DiagnosticSection,
    'Companies' as TableName,
    CASE 
        WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'TenantID')
        THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END as TenantIDStatus;

-- Check if TenantID exists in Users table
SELECT 'TenantID Check' as DiagnosticSection,
    'Users' as TableName,
    CASE 
        WHEN EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
        THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END as TenantIDStatus;

-- Show ALL Companies table data
SELECT 'Companies Data' as DiagnosticSection, * FROM Companies ORDER BY CompanyName;

-- Show ALL Users table data (basic info only)
SELECT 'Users Data' as DiagnosticSection, UserID, Email, CompanyID FROM Users ORDER BY Email; 