# Azure Entra ID - Multi-Tenant Authentication Guide

**Purpose**: Enable seamless authentication for all SASMOS Group companies in Falcon Portal  
**Date**: July 4, 2025  
**Status**: ✅ **CONFIGURATION COMPLETE**

## 🎯 Architecture Decision: Multi-Tenant Application

It was determined that Falcon Portal must operate as a **Multi-Tenant Application**. This is the correct architectural pattern because each SASMOS Group company (`sasmos.com`, `fe-sil.com`, etc.) maintains its own Microsoft 365 instance and has already verified its domain within its own Entra ID tenant.

**Key Constraint**: A domain name can only be verified in **one** Entra ID tenant at a time. Therefore, we **cannot** verify these domains again in the Falcon Portal's primary tenant.

**The Solution**: The portal's App Registration is configured to be multi-tenant, allowing users from any trusted organizational directory to log in. When a user from another company (e.g., `<EMAIL>`) logs in, they are added to the portal's primary directory as a "Guest" user, but our application logic treats them as a first-class citizen.

---

## ✅ Step 1: Azure App Registration Configuration

This step confirms that the Falcon Portal application is registered to accept logins from any trusted organization. This has been verified as complete.

1.  **Navigate to Azure Portal** -> **Microsoft Entra ID** -> **App registrations**.
2.  Select the **Falcon Portal** application.
3.  Go to the **Authentication** tab.
4.  **Confirm Setting**: "Supported account types" is set to:
    > ✅ **Accounts in any organizational directory (Any Azure AD directory - Multitenant)**

---

## 🚀 Step 2: Backend Identity Management (Next Development Task)

This is the crucial next step to ensure the application logic is robust and secure in a multi-tenant environment.

**Objective**: Uniquely identify users by the combination of their **Object ID (`oid`)** and their home **Tenant ID (`tid`)**.

### **Implementation Plan:**

1.  **Database Schema Update**:
    *   Add a `TenantID` column to the `Users` table. This column is mandatory.
    *   The primary key for a user's identity within the portal will become the composite of `(EntraID, TenantID)`.

2.  **Enhance `GetCurrentUser` API Logic**:
    *   On user login, extract the `oid` and `tid` claims from the user's ID token.
    *   **Lookup User**: Query the `Users` table using `WHERE EntraID = @oid AND TenantID = @tid`.
    *   **Existing User**: If found, retrieve their assigned portal roles and return the user context.
    *   **New User (First Login)**:
        *   Create a new record in the `Users` table with the user's `EntraID` and `TenantID`.
        *   Use existing domain/tenant mapping logic to determine the correct `CompanyID`.
        *   Assign the default "Employee" role in the `UserRoles` table.
        *   Return the new user context.

3.  **Verify Data Filtering**:
    *   Ensure all data-fetching APIs (for documents, change requests, etc.) use the `CompanyID` from the user's context to filter results, maintaining data segregation between companies.

---

## 📈 Expected Outcome

This architecture ensures a seamless and secure login experience for all SASMOS Group employees without requiring manual guest invitations or impossible domain re-verification. It is a scalable, enterprise-grade solution that correctly handles identity in a federated environment.

## Current Situation Analysis

### ✅ Currently Working
- **Avirata Defence Systems**: Full authentication support
  - Domain: `aviratadefsys.com` ✅ Verified
  - Default Domain: `AVIRATADEFENCESYSTEMS.onmicrosoft.com` ✅ Verified
  - Tenant ID: `ecb4a448-4a99-443b-aaff-063150b6c9ea`

### ❌ Missing Configuration  
- **SASMOS Group Companies**: No authentication support
  - `sasmos.com` - ❌ Not verified
  - `fe-sil.com` - ❌ Not verified  
  - `glodesi.com` - ❌ Not verified
  - `hanuka.com` - ❌ Not verified
  - `westwireharnessing.co.uk` - ❌ Not verified

## Portal Code Status

### ✅ Code Ready
- **Frontend**: Company mapping updated in `userContext.ts`
- **Backend**: Company resolution updated in `GetCurrentUser.ts`  
- **Database Script**: `insert-sasmos-companies.sql` ready to execute
- **Fallback Logic**: Defaults to Avirata Defence Systems for unknown domains

### ⚠️ Current Limitation
- Users from missing domains cannot authenticate
- Company filtering shows all companies but only Avirata users can log in
- Multi-company features are prepared but not functional

## Step-by-Step Domain Addition Guide

### Prerequisites
- Azure tenant admin access
- DNS management access for each domain
- SQL database access to run company insertion script

### Step 1: Azure Portal Domain Addition

1. **Navigate to Azure Portal**
   ```
   https://portal.azure.com → Azure Active Directory → Custom domain names
   ```

2. **Add Each Domain**
   - Click "Add custom domain"
   - Enter domain name (e.g., `sasmos.com`)
   - Click "Add domain"

3. **Repeat for All Domains**
   - `sasmos.com`
   - `fe-sil.com`
   - `glodesi.com`
   - `hanuka.com`
   - `westwireharnessing.co.uk`

### Step 2: DNS Verification

For each domain, Azure will provide verification records:

#### Option A: TXT Record (Recommended)
```
Host: @
Type: TXT
Value: MS=ms12345678 (Azure will provide the actual value)
TTL: 3600
```

#### Option B: MX Record
```
Host: @
Type: MX
Priority: 0
Value: ms12345678.msv1.invalid (Azure will provide the actual value)
TTL: 3600
```

### Step 3: Domain Verification

1. **Add DNS Records**: Contact each company's IT team to add verification records
2. **Wait for Propagation**: DNS changes may take up to 24 hours
3. **Verify in Azure**: Click "Verify" for each domain in Azure Portal
4. **Confirm Status**: All domains should show "Verified" status

### Step 4: Database Company Setup

Run the SQL script to add companies:

```sql
-- Execute in SQL Server Management Studio or Azure Data Studio
-- File: azure-functions/falcon-api/scripts/insert-sasmos-companies.sql

USE [FalconPortal];
GO

-- The script will insert all missing companies
-- Check results with:
SELECT CompanyID, CompanyName, CompanyCode, IsActive 
FROM Companies 
ORDER BY CompanyName;
```

### Step 5: Portal Testing

1. **Start Portal**: `cd apps/portal-shell && npm run dev`
2. **Test Authentication**: Try logging in with users from each domain
3. **Verify Company Assignment**: Check that users are assigned to correct companies
4. **Test Company Filtering**: Verify admin features filter by user's company

## Expected Results After Setup

### ✅ Authentication Working
- Users from all domains can log in
- Company assignment automatic based on email domain
- Multi-tenant authentication functional

### ✅ Company Filtering Active
- Data filtered by user's company
- Admin functions respect company boundaries
- Cross-company access controlled

### ✅ Portal Admin Features
- User management shows all companies
- Company-specific filtering works
- Multi-company reporting available

## Troubleshooting

### Domain Verification Issues
- **DNS Propagation**: Use `nslookup` or online DNS tools to verify record exists
- **Record Format**: Ensure exact match with Azure-provided values
- **TTL Settings**: Lower TTL (300) for faster propagation during setup

### Authentication Issues
- **Clear Browser Cache**: Force refresh after domain addition
- **Check User Directory**: Verify users exist in correct directory
- **Test with Incognito**: Avoid cached authentication state

### Portal Issues
- **Database Connection**: Verify company records exist in database
- **API Responses**: Check browser network tab for API errors
- **Company Mapping**: Verify domain-to-company mapping in logs

## Verification Commands

```bash
# Check domain DNS records
nslookup -type=TXT sasmos.com
nslookup -type=TXT fe-sil.com
nslookup -type=TXT glodesi.com
nslookup -type=TXT hanuka.com
nslookup -type=TXT westwireharnessing.co.uk

# Test Azure domains
az rest --method GET --url "https://graph.microsoft.com/v1.0/domains"
```

## Next Steps After Domain Setup

1. **Email Notifications**: Implement automated workflow notifications
2. **Role-Based Access Control**: Enhanced permission system
3. **Cross-Company Features**: Shared resources and communication
4. **Reporting Dashboard**: Multi-company analytics

---

**Contact**: Technical team for DNS record assistance  
**Timeline**: 1-2 days (depending on DNS propagation)  
**Priority**: Critical for multi-company functionality 