"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const adminApi_1 = require("../../services/adminApi");
const UserEditPage = () => {
    const { userId } = (0, react_router_dom_1.useParams)();
    const navigate = (0, react_router_dom_1.useNavigate)();
    const [user, setUser] = (0, react_1.useState)(null);
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    const [isSaving, setIsSaving] = (0, react_1.useState)(false);
    const [saveError, setSaveError] = (0, react_1.useState)(null);
    // State for editable fields
    const [selectedRoles, setSelectedRoles] = (0, react_1.useState)([]);
    const [selectedStatus, setSelectedStatus] = (0, react_1.useState)('');
    // --- State for Dynamic Roles ---
    const [availableRoles, setAvailableRoles] = (0, react_1.useState)([]);
    // --- Handler for Role Checkbox Change ---
    const handleRoleChange = (role, isChecked) => {
        setSelectedRoles(prevRoles => {
            if (isChecked) {
                // Add role if not already present
                return prevRoles.includes(role) ? prevRoles : [...prevRoles, role];
            }
            else {
                // Remove role
                return prevRoles.filter(r => r !== role);
            }
        });
    };
    // --- Fetch Available Roles ---
    (0, react_1.useEffect)(() => {
        const fetchRoles = () => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const roles = yield (0, adminApi_1.fetchPortalRoleNames)();
                setAvailableRoles(roles);
            }
            catch (err) {
                console.error("Error fetching roles:", err);
                // Fallback to basic roles if API fails
                setAvailableRoles(['Administrator', 'Employee', 'Manager', 'Executive', 'IT Admin', 'HR Admin', 'Content Admin']);
            }
        });
        fetchRoles();
    }, []);
    // Fetch user data on mount
    (0, react_1.useEffect)(() => {
        const loadUser = () => __awaiter(void 0, void 0, void 0, function* () {
            if (!userId) {
                setError("No user ID provided.");
                setIsLoading(false);
                return;
            }
            setIsLoading(true);
            setError(null);
            try {
                const fetchedUser = yield (0, adminApi_1.fetchPortalUser)(userId);
                if (fetchedUser) {
                    setUser(fetchedUser);
                    setSelectedRoles(fetchedUser.roles || []);
                    setSelectedStatus(fetchedUser.status || '');
                }
                else {
                    setError("User not found.");
                }
            }
            catch (err) {
                console.error("Error fetching user:", err);
                setError("Failed to load user data. Please try again.");
            }
            finally {
                setIsLoading(false);
            }
        });
        loadUser();
    }, [userId]);
    // TODO: Implement handleSave function
    const handleSave = () => __awaiter(void 0, void 0, void 0, function* () {
        if (!userId || !user) {
            setSaveError("User data is not available.");
            return;
        }
        if (!selectedStatus) {
            setSaveError("Please select a status.");
            return;
        }
        // Ensure at least one role is selected? Or default to 'User'? For now, allow empty.
        // if (selectedRoles.length === 0) {
        //     setSaveError("User must have at least one role.");
        //     return;
        // }
        setIsSaving(true);
        setSaveError(null);
        try {
            const updatedData = {
                roles: selectedRoles,
                status: selectedStatus
            };
            yield (0, adminApi_1.updatePortalUser)(userId, updatedData);
            // Success! Navigate back to the list
            // Optionally show a success message first (e.g., using react-toastify)
            navigate('/admin/user-management');
        }
        catch (err) {
            console.error("Error updating user:", err);
            const message = err instanceof Error ? err.message : "Failed to save changes. Please try again.";
            setSaveError(message);
        }
        finally {
            setIsSaving(false);
        }
    });
    // --- Render Logic ---
    if (isLoading) {
        return <div className="p-6">Loading user data...</div>;
    }
    if (error) {
        return <div className="p-6 text-red-600">Error: {error}</div>;
    }
    if (!user) {
        // Should be covered by error state, but good practice
        return <div className="p-6">User not found.</div>;
    }
    return (<div className="p-6 bg-gray-50 min-h-screen">
            <h1 className="text-2xl font-semibold text-gray-800 mb-6">Manage User: {user.name}</h1>

            <div className="bg-white shadow rounded-lg p-6 max-w-2xl mx-auto">
                {/* Display basic info (read-only) */}
                <div className="mb-4 border-b pb-4">
                    <p><span className="font-medium">Email:</span> {user.email}</p>
                    <p><span className="font-medium">Company:</span> {user.company}</p>
                    <p><span className="font-medium">User ID:</span> {user.id}</p>
                </div>

                {/* Edit Roles */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Roles:</label>
                    {/* TODO: Use checkboxes or a multi-select component */}
                    <div className="space-y-2 mt-1">
                        {availableRoles.map((role) => (<div key={role} className="flex items-center">
                                <input id={`role-${role}`} name="roles" type="checkbox" value={role} checked={selectedRoles.includes(role)} onChange={(e) => handleRoleChange(role, e.target.checked)} className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"/>
                                <label htmlFor={`role-${role}`} className="ml-2 block text-sm text-gray-900">
                                    {role}
                                </label>
                            </div>))}
                    </div>
                </div>

                {/* Edit Status */}
                <div className="mb-6">
                    <label htmlFor="statusSelect" className="block text-sm font-medium text-gray-700 mb-1">Status:</label>
                     <select id="statusSelect" value={selectedStatus} onChange={(e) => setSelectedStatus(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="" disabled>Select status</option>
                        {adminApi_1.PORTAL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
                    </select>
                </div>
                
                 {saveError && <p className="text-sm text-red-600 mb-4">{saveError}</p>}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3">
                    <button type="button" onClick={() => navigate('/admin/user-management')} // Go back
     className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </button>
                    <button type="button" onClick={handleSave} disabled={isSaving} // Disable while saving
     className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${isSaving ? 'bg-indigo-300 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}>
                        {isSaving ? 'Saving...' : 'Save Changes'}
                    </button>
                </div>
            </div>
        </div>);
};
exports.default = UserEditPage;
//# sourceMappingURL=UserEditPage.js.map