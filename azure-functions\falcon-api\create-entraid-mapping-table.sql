-- EntraID Mapping Table for Automatic Authentication Capture
-- This table captures real EntraID data when users login, solving cross-domain access issues

CREATE TABLE UserEntraIDMappings (
    MappingID int IDENTITY(1,1) PRIMARY KEY,
    Email nvarchar(255) NOT NULL UNIQUE,
    EntraID nvarchar(255) NOT NULL UNIQUE,
    TenantID nvarchar(255) NOT NULL,
    DisplayName nvarchar(255),
    CompanyName nvarchar(255),
    FirstCaptured datetime NOT NULL DEFAULT GETDATE(),
    LastSeen datetime NOT NULL DEFAULT GETDATE(),
    CaptureSource nvarchar(50) NOT NULL DEFAULT 'AutoLogin', -- 'AutoLogin', 'Manual', 'Migration'
    IsActive bit NOT NULL DEFAULT 1,
    CreatedBy int NOT NULL DEFAULT 1,
    ModifiedBy int,
    ModifiedDate datetime
);

-- Indexes for performance
CREATE INDEX IX_UserEntraIDMappings_Email ON UserEntraIDMappings(Email);
CREATE INDEX IX_UserEntraIDMappings_EntraID ON UserEntraIDMappings(EntraID);
CREATE INDEX IX_UserEntraIDMappings_TenantID ON UserEntraIDMappings(TenantID);
CREATE INDEX IX_UserEntraIDMappings_LastSeen ON UserEntraIDMappings(LastSeen);

GO

-- Sample stored procedure to upsert mapping data
CREATE PROCEDURE UpsertUserEntraIDMapping
    @Email NVARCHAR(255),
    @EntraID NVARCHAR(255),
    @TenantID NVARCHAR(255),
    @DisplayName NVARCHAR(255) = NULL,
    @CompanyName NVARCHAR(255) = NULL,
    @CaptureSource NVARCHAR(50) = 'AutoLogin'
AS
BEGIN
    SET NOCOUNT ON;
    
    IF EXISTS (SELECT 1 FROM UserEntraIDMappings WHERE Email = @Email)
    BEGIN
        -- Update existing record
        UPDATE UserEntraIDMappings 
        SET 
            EntraID = @EntraID,
            TenantID = @TenantID,
            DisplayName = COALESCE(@DisplayName, DisplayName),
            CompanyName = COALESCE(@CompanyName, CompanyName),
            LastSeen = GETDATE(),
            ModifiedDate = GETDATE(),
            ModifiedBy = 1
        WHERE Email = @Email;
    END
    ELSE
    BEGIN
        -- Insert new record
        INSERT INTO UserEntraIDMappings (Email, EntraID, TenantID, DisplayName, CompanyName, CaptureSource)
        VALUES (@Email, @EntraID, @TenantID, @DisplayName, @CompanyName, @CaptureSource);
    END
END;

GO

-- View to join with existing Users table
CREATE VIEW vw_UsersWithEntraIDMappings AS
SELECT 
    u.*,
    m.EntraID as MappedEntraID,
    m.TenantID as MappedTenantID,
    m.DisplayName as MappedDisplayName,
    m.FirstCaptured,
    m.LastSeen,
    m.CaptureSource,
    CASE 
        WHEN m.EntraID IS NOT NULL THEN 'Mapped'
        WHEN u.EntraID LIKE '%placeholder%' OR u.EntraID LIKE '%simulated%' OR u.EntraID LIKE '00000000-%' THEN 'Placeholder'
        ELSE 'Manual'
    END as EntraIDStatus
FROM Users u
LEFT JOIN UserEntraIDMappings m ON u.Email = m.Email
WHERE u.IsActive = 1; 