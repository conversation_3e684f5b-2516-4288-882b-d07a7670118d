-- Diagnostic Script - Check Actual Table Structures
-- This script will show us exactly what columns exist in both tables

PRINT '============================================';
PRINT 'DIAGNOSTIC: Checking actual table structures';
PRINT '============================================';

-- Check Companies table structure
PRINT '';
PRINT 'Companies table columns:';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Companies'
ORDER BY ORDINAL_POSITION;

-- Check Users table structure  
PRINT '';
PRINT 'Users table columns:';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users'
ORDER BY ORDINAL_POSITION;

-- Check if TenantID exists in Companies table
PRINT '';
PRINT 'TenantID in Companies table:';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Companies' AND COLUMN_NAME = 'TenantID')
BEGIN
    PRINT 'TenantID column EXISTS in Companies table';
END
ELSE
BEGIN
    PRINT 'TenantID column DOES NOT EXIST in Companies table';
END

-- Check if TenantID exists in Users table
PRINT '';
PRINT 'TenantID in Users table:';
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
BEGIN
    PRINT 'TenantID column EXISTS in Users table';
END
ELSE
BEGIN
    PRINT 'TenantID column DOES NOT EXIST in Users table';
END

-- Show sample data from Companies table
PRINT '';
PRINT 'Sample Companies table data:';
SELECT TOP 5 * FROM Companies ORDER BY CompanyName;

-- Show sample data from Users table (without TenantID reference)
PRINT '';
PRINT 'Sample Users table data:';
SELECT TOP 5 UserID, Email, CompanyID FROM Users ORDER BY Email;

PRINT '';
PRINT '============================================';
PRINT 'DIAGNOSTIC COMPLETE';
PRINT '============================================'; 