-- Falcon Portal Database Schema
-- This schema supports all requirements specified in the Falcon Portal SRD

-- =============================================
-- DROP EXISTING TABLES (for idempotency)
-- =============================================
-- Note: Order might matter if FKs were inline, but they are now at the end.
DROP TABLE IF EXISTS HRPolicyAcknowledgments;
DROP TABLE IF EXISTS HRRequestApprovals;
DROP TABLE IF EXISTS HRRequests;
DROP TABLE IF EXISTS HRRequestTypes;
DROP TABLE IF EXISTS HRPolicies;
DROP TABLE IF EXISTS HRPolicyCategories;
DROP TABLE IF EXISTS ITTicketComments;
DROP TABLE IF EXISTS ITTicketAttachments;
DROP TABLE IF EXISTS ITTickets;
DROP TABLE IF EXISTS ITTicketCategories;
DROP TABLE IF EXISTS SoftwareAccessRequests;
DROP TABLE IF EXISTS CompanySoftware;
DROP TABLE IF EXISTS SoftwareCatalog;
DROP TABLE IF EXISTS ITAnnouncements;
DROP TABLE IF EXISTS KnowledgeArticleFeedback;
DROP TABLE IF EXISTS KnowledgeArticleTags;
DROP TABLE IF EXISTS KnowledgeArticles;
DROP TABLE IF EXISTS DocumentTags_Map;
DROP TABLE IF EXISTS DocumentApprovals;
DROP TABLE IF EXISTS DocumentVersions;
DROP TABLE IF EXISTS Documents;
DROP TABLE IF EXISTS DocumentTags;
DROP TABLE IF EXISTS DocumentCategories;
DROP TABLE IF EXISTS UserPreferences;
DROP TABLE IF EXISTS UserRoles;
DROP TABLE IF EXISTS RolePermissions;
DROP TABLE IF EXISTS Permissions;
DROP TABLE IF EXISTS AuditLog; -- Drop before Users due to potential FK
DROP TABLE IF EXISTS Users;
DROP TABLE IF EXISTS Roles;
DROP TABLE IF EXISTS Departments;
DROP TABLE IF EXISTS SystemSettings;
DROP TABLE IF EXISTS Notifications;
DROP TABLE IF EXISTS NotificationTypes;
DROP TABLE IF EXISTS CustomerInteractionMedia;
DROP TABLE IF EXISTS CustomerInteractions;
DROP TABLE IF EXISTS EventRegistrations;
DROP TABLE IF EXISTS Events;
DROP TABLE IF EXISTS EventCategories;
DROP TABLE IF EXISTS AnnouncementReadStatus;
DROP TABLE IF EXISTS AnnouncementApprovals;
DROP TABLE IF EXISTS Announcements;
DROP TABLE IF EXISTS AnnouncementCategories;
DROP TABLE IF EXISTS ProcurementRequestApprovals;
DROP TABLE IF EXISTS ProcurementRequestItems;
DROP TABLE IF EXISTS ProcurementRequests;
DROP TABLE IF EXISTS ProcurementItems;
DROP TABLE IF EXISTS ProcurementCategories;
DROP TABLE IF EXISTS VisitorAppointments;
DROP TABLE IF EXISTS Visitors;
DROP TABLE IF EXISTS MaintenanceRequests;
DROP TABLE IF EXISTS MaintenanceRequestTypes;
DROP TABLE IF EXISTS MeetingRoomBookings;
DROP TABLE IF EXISTS MeetingRoomFacilities_Map;
DROP TABLE IF EXISTS MeetingRooms;
DROP TABLE IF EXISTS MeetingRoomFacilities;
DROP TABLE IF EXISTS TransportationBookings;
DROP TABLE IF EXISTS TransportationProviders;
DROP TABLE IF EXISTS ShuttleSchedules;
DROP TABLE IF EXISTS VehicleReservations;
DROP TABLE IF EXISTS CompanyVehicles;
DROP TABLE IF EXISTS GuestHouseBookings;
DROP TABLE IF EXISTS GuestHouseRooms;
DROP TABLE IF EXISTS GuestHouses;
DROP TABLE IF EXISTS Accommodations;
DROP TABLE IF EXISTS TravelExpenses;
DROP TABLE IF EXISTS TravelRequestApprovals;
DROP TABLE IF EXISTS TravelRequests;
DROP TABLE IF EXISTS TravelRequestTypes;
DROP TABLE IF EXISTS Locations;
DROP TABLE IF EXISTS Companies;

-- =============================================
-- CREATE TABLES
-- =============================================

-- Companies within SASMOS Group
CREATE TABLE Companies (
    CompanyID INT PRIMARY KEY IDENTITY(1,1),
    CompanyName NVARCHAR(100) NOT NULL,
    CompanyCode NVARCHAR(20) NOT NULL,
    LogoURL NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL, -- Initially no FK, assumes User 1 exists or will be added
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Locations for each company
CREATE TABLE Locations (
    LocationID INT PRIMARY KEY IDENTITY(1,1),
    LocationName NVARCHAR(100) NOT NULL,
    CompanyID INT NOT NULL,
    Address NVARCHAR(255) NULL,
    City NVARCHAR(100) NULL,
    State NVARCHAR(100) NULL,
    Country NVARCHAR(100) NULL,
    PostalCode NVARCHAR(20) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Departments within companies
CREATE TABLE Departments (
    DepartmentID INT PRIMARY KEY IDENTITY(1,1),
    DepartmentName NVARCHAR(100) NOT NULL,
    CompanyID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Roles defined in the system
CREATE TABLE Roles (
    RoleID INT PRIMARY KEY IDENTITY(1,1),
    RoleName NVARCHAR(100) NOT NULL,
    RoleDescription NVARCHAR(255) NULL,
    IsSystemRole BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Permissions available in the system
CREATE TABLE Permissions (
    PermissionID INT PRIMARY KEY IDENTITY(1,1),
    PermissionName NVARCHAR(100) NOT NULL,
    PermissionDescription NVARCHAR(255) NULL,
    PermissionCategory NVARCHAR(50) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Users table for all employees
CREATE TABLE Users (
    UserID INT PRIMARY KEY IDENTITY(1,1),
    EntraID NVARCHAR(100) NULL,
    Username NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100) NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    CompanyID INT NOT NULL,
    LocationID INT NULL,
    DepartmentID INT NULL,
    EmployeeID NVARCHAR(50) NULL,
    ContactNumber NVARCHAR(20) NULL,
    ProfileImageURL NVARCHAR(255) NULL,
    ManagerID INT NULL, -- Self-reference to manager
    DateOfJoining DATE NULL,
    LastLoginDate DATETIME NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Accommodations directory
CREATE TABLE Accommodations (
    AccommodationID INT PRIMARY KEY IDENTITY(1,1),
    AccommodationName NVARCHAR(100) NOT NULL,
    Address NVARCHAR(255) NOT NULL,
    City NVARCHAR(100) NOT NULL,
    State NVARCHAR(100) NULL,
    Country NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100) NULL,
    ContactEmail NVARCHAR(100) NULL,
    ContactPhone NVARCHAR(20) NULL,
    RoomTypes NVARCHAR(255) NULL,
    AverageRate DECIMAL(18, 2) NULL,
    Currency NVARCHAR(3) NOT NULL DEFAULT 'INR',
    IsPreferred BIT NOT NULL DEFAULT 0,
    IsCompanyOwned BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Guest house details
CREATE TABLE GuestHouses (
    GuestHouseID INT PRIMARY KEY IDENTITY(1,1),
    GuestHouseName NVARCHAR(100) NOT NULL,
    LocationID INT NOT NULL,
    Address NVARCHAR(255) NOT NULL,
    TotalRooms INT NOT NULL,
    ContactPerson NVARCHAR(100) NULL,
    ContactEmail NVARCHAR(100) NULL,
    ContactPhone NVARCHAR(20) NULL,
    Description NVARCHAR(1000) NULL,
    Amenities NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Guest house rooms
CREATE TABLE GuestHouseRooms (
    RoomID INT PRIMARY KEY IDENTITY(1,1),
    GuestHouseID INT NOT NULL,
    RoomNumber NVARCHAR(20) NOT NULL,
    RoomType NVARCHAR(50) NOT NULL, -- Single, Double, Suite, etc.
    Capacity INT NOT NULL DEFAULT 1,
    Floor NVARCHAR(10) NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Guest house bookings
CREATE TABLE GuestHouseBookings (
    BookingID INT PRIMARY KEY IDENTITY(1,1),
    RoomID INT NOT NULL,
    BookedBy INT NOT NULL,
    GuestName NVARCHAR(100) NOT NULL,
    GuestCompanyID INT NULL,
    CheckInDate DATE NOT NULL,
    CheckOutDate DATE NOT NULL,
    NumberOfGuests INT NOT NULL DEFAULT 1,
    Purpose NVARCHAR(255) NULL,
    Status NVARCHAR(20) NOT NULL, -- Pending, Confirmed, Cancelled, Completed
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Company vehicles
CREATE TABLE CompanyVehicles (
    VehicleID INT PRIMARY KEY IDENTITY(1,1),
    VehicleNumber NVARCHAR(20) NOT NULL,
    VehicleType NVARCHAR(50) NOT NULL, -- Car, Van, Bus, etc.
    Make NVARCHAR(50) NULL,
    Model NVARCHAR(50) NULL,
    Year NVARCHAR(4) NULL,
    Capacity INT NOT NULL DEFAULT 4,
    LocationID INT NOT NULL,
    CompanyID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Vehicle reservations
CREATE TABLE VehicleReservations (
    ReservationID INT PRIMARY KEY IDENTITY(1,1),
    VehicleID INT NOT NULL,
    ReservedBy INT NOT NULL,
    Purpose NVARCHAR(255) NOT NULL,
    StartDateTime DATETIME NOT NULL,
    EndDateTime DATETIME NOT NULL,
    Origin NVARCHAR(255) NULL,
    Destination NVARCHAR(255) NULL,
    NumberOfPassengers INT NOT NULL DEFAULT 1,
    Status NVARCHAR(20) NOT NULL, -- Pending, Confirmed, Cancelled, Completed
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Shuttle schedules
CREATE TABLE ShuttleSchedules (
    ScheduleID INT PRIMARY KEY IDENTITY(1,1),
    LocationID INT NOT NULL,
    RouteDescription NVARCHAR(255) NOT NULL,
    DeparturePoint NVARCHAR(100) NOT NULL,
    ArrivalPoint NVARCHAR(100) NOT NULL,
    DepartureTime TIME NOT NULL,
    ArrivalTime TIME NOT NULL,
    WeekdaysOnly BIT NOT NULL DEFAULT 1,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Transportation service providers
CREATE TABLE TransportationProviders (
    ProviderID INT PRIMARY KEY IDENTITY(1,1),
    ProviderName NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100) NULL,
    ContactEmail NVARCHAR(100) NULL,
    ContactPhone NVARCHAR(20) NULL,
    Address NVARCHAR(255) NULL,
    City NVARCHAR(100) NULL,
    ServiceTypes NVARCHAR(255) NULL, -- Taxi, Car Rental, etc.
    IsPreferred BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Transportation bookings
CREATE TABLE TransportationBookings (
    BookingID INT PRIMARY KEY IDENTITY(1,1),
    ProviderID INT NULL,
    BookedBy INT NOT NULL,
    ServiceType NVARCHAR(50) NOT NULL, -- Taxi, Car Rental, etc.
    PickupDateTime DATETIME NOT NULL,
    PickupLocation NVARCHAR(255) NOT NULL,
    DropoffLocation NVARCHAR(255) NOT NULL,
    NumberOfPassengers INT NOT NULL DEFAULT 1,
    Purpose NVARCHAR(255) NULL,
    Status NVARCHAR(20) NOT NULL, -- Pending, Confirmed, Cancelled, Completed
    CompanyID INT NOT NULL,
    DepartmentID INT NULL,
    EstimatedCost DECIMAL(18, 2) NULL,
    ActualCost DECIMAL(18, 2) NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Meeting room facilities
CREATE TABLE MeetingRoomFacilities (
    FacilityID INT PRIMARY KEY IDENTITY(1,1),
    FacilityName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Meeting rooms
CREATE TABLE MeetingRooms (
    RoomID INT PRIMARY KEY IDENTITY(1,1),
    RoomName NVARCHAR(100) NOT NULL,
    LocationID INT NOT NULL,
    Floor NVARCHAR(10) NULL,
    Capacity INT NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Meeting room facilities mapping
CREATE TABLE MeetingRoomFacilities_Map (
    MapID INT PRIMARY KEY IDENTITY(1,1),
    RoomID INT NOT NULL,
    FacilityID INT NOT NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Meeting room bookings
CREATE TABLE MeetingRoomBookings (
    BookingID INT PRIMARY KEY IDENTITY(1,1),
    RoomID INT NOT NULL,
    BookedBy INT NOT NULL,
    Purpose NVARCHAR(255) NOT NULL,
    StartDateTime DATETIME NOT NULL,
    EndDateTime DATETIME NOT NULL,
    NumberOfAttendees INT NOT NULL DEFAULT 1,
    RecurrencePattern NVARCHAR(50) NULL,
    RecurrenceEndDate DATE NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Maintenance request types
CREATE TABLE MaintenanceRequestTypes (
    RequestTypeID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Maintenance requests
CREATE TABLE MaintenanceRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeID INT NOT NULL,
    RequestedBy INT NOT NULL,
    LocationID INT NOT NULL,
    Area NVARCHAR(100) NOT NULL,
    Description NVARCHAR(1000) NOT NULL,
    Priority NVARCHAR(20) NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    AssignedTo INT NULL,
    CompletedDate DATETIME NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Visitor management
CREATE TABLE Visitors (
    VisitorID INT PRIMARY KEY IDENTITY(1,1),
    VisitorName NVARCHAR(100) NOT NULL,
    CompanyName NVARCHAR(100) NULL,
    Email NVARCHAR(100) NULL,
    Phone NVARCHAR(20) NULL,
    IdentificationType NVARCHAR(50) NULL,
    IdentificationNumber NVARCHAR(50) NULL,
    PhotoStoragePath NVARCHAR(1000) NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Visitor appointments
CREATE TABLE VisitorAppointments (
    AppointmentID INT PRIMARY KEY IDENTITY(1,1),
    VisitorID INT NOT NULL,
    HostUserID INT NOT NULL,
    LocationID INT NOT NULL,
    Purpose NVARCHAR(255) NOT NULL,
    VisitDate DATE NOT NULL,
    ExpectedArrivalTime TIME NOT NULL,
    ExpectedDepartureTime TIME NULL,
    ActualArrivalTime TIME NULL,
    ActualDepartureTime TIME NULL,
    BadgeNumber NVARCHAR(20) NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Procurement categories
CREATE TABLE ProcurementCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Procurement items
CREATE TABLE ProcurementItems (
    ItemID INT PRIMARY KEY IDENTITY(1,1),
    ItemName NVARCHAR(100) NOT NULL,
    CategoryID INT NOT NULL,
    Description NVARCHAR(255) NULL,
    UnitOfMeasure NVARCHAR(20) NULL,
    ApproxUnitPrice DECIMAL(18, 2) NULL,
    IsInventoryItem BIT NOT NULL DEFAULT 0,
    MinimumOrderQuantity INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Procurement requests
CREATE TABLE ProcurementRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    RequestedBy INT NOT NULL,
    CompanyID INT NOT NULL,
    DepartmentID INT NULL,
    DeliveryLocationID INT NOT NULL,
    Purpose NVARCHAR(255) NOT NULL,
    TotalEstimatedAmount DECIMAL(18, 2) NULL,
    Status NVARCHAR(20) NOT NULL,
    CurrentApproverID INT NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Procurement request items
CREATE TABLE ProcurementRequestItems (
    RequestItemID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    ItemID INT NOT NULL,
    Quantity INT NOT NULL,
    UnitPrice DECIMAL(18, 2) NULL,
    TotalPrice DECIMAL(18, 2) NULL,
    DeliveryDate DATE NULL,
    Justification NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Procurement request approvals
CREATE TABLE ProcurementRequestApprovals (
    ApprovalID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    ApproverID INT NOT NULL,
    ApprovalOrder INT NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    ApprovalDate DATETIME NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Announcement categories
CREATE TABLE AnnouncementCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Announcements
CREATE TABLE Announcements (
    AnnouncementID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    CategoryID INT NULL,
    Importance NVARCHAR(20) NOT NULL,
    PublishStartDate DATETIME NOT NULL,
    PublishEndDate DATETIME NULL,
    IsGroupWide BIT NOT NULL DEFAULT 0,
    CompanyID INT NULL,
    LocationID INT NULL,
    DepartmentID INT NULL,
    ViewCount INT NOT NULL DEFAULT 0,
    Status NVARCHAR(20) NOT NULL,
    BannerImagePath NVARCHAR(1000) NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Announcement approvals
CREATE TABLE AnnouncementApprovals (
    ApprovalID INT PRIMARY KEY IDENTITY(1,1),
    AnnouncementID INT NOT NULL,
    ApproverID INT NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    ApprovalDate DATETIME NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Announcement read status
CREATE TABLE AnnouncementReadStatus (
    ReadStatusID INT PRIMARY KEY IDENTITY(1,1),
    AnnouncementID INT NOT NULL,
    UserID INT NOT NULL,
    ReadDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Event categories
CREATE TABLE EventCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Events
CREATE TABLE Events (
    EventID INT PRIMARY KEY IDENTITY(1,1),
    EventName NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX) NULL,
    CategoryID INT NULL,
    StartDateTime DATETIME NOT NULL,
    EndDateTime DATETIME NOT NULL,
    Location NVARCHAR(255) NULL,
    MeetingRoomID INT NULL,
    IsVirtual BIT NOT NULL DEFAULT 0,
    VirtualMeetingLink NVARCHAR(1000) NULL,
    MaxAttendees INT NULL,
    RegistrationRequired BIT NOT NULL DEFAULT 0,
    RegistrationDeadline DATETIME NULL,
    IsGroupWide BIT NOT NULL DEFAULT 0,
    CompanyID INT NULL,
    LocationID INT NULL,
    DepartmentID INT NULL,
    Status NVARCHAR(20) NOT NULL,
    OrganizerID INT NOT NULL,
    BannerImagePath NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Event registrations
CREATE TABLE EventRegistrations (
    RegistrationID INT PRIMARY KEY IDENTITY(1,1),
    EventID INT NOT NULL,
    UserID INT NOT NULL,
    RegistrationDate DATETIME NOT NULL DEFAULT GETDATE(),
    AttendanceStatus NVARCHAR(20) NULL,
    Comments NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Customer interactions
CREATE TABLE CustomerInteractions (
    InteractionID INT PRIMARY KEY IDENTITY(1,1),
    CustomerName NVARCHAR(100) NOT NULL,
    CustomerCompany NVARCHAR(100) NOT NULL,
    InteractionType NVARCHAR(50) NOT NULL,
    InteractionDate DATE NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    CompanyID INT NOT NULL,
    LocationID INT NULL,
    IsHighlighted BIT NOT NULL DEFAULT 0,
    Status NVARCHAR(20) NOT NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Customer interaction media
CREATE TABLE CustomerInteractionMedia (
    MediaID INT PRIMARY KEY IDENTITY(1,1),
    InteractionID INT NOT NULL,
    MediaType NVARCHAR(20) NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    Description NVARCHAR(255) NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Notification types
CREATE TABLE NotificationTypes (
    NotificationTypeID INT PRIMARY KEY IDENTITY(1,1),
    TypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    DefaultPriority NVARCHAR(20) NOT NULL,
    DefaultIcon NVARCHAR(50) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Notifications
CREATE TABLE Notifications (
    NotificationID INT PRIMARY KEY IDENTITY(1,1),
    NotificationTypeID INT NOT NULL,
    UserID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    Message NVARCHAR(1000) NOT NULL,
    Priority NVARCHAR(20) NOT NULL,
    LinkURL NVARCHAR(1000) NULL,
    IsRead BIT NOT NULL DEFAULT 0,
    ReadDate DATETIME NULL,
    ExpiryDate DATETIME NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Audit log
CREATE TABLE AuditLog (
    LogID INT PRIMARY KEY IDENTITY(1,1),
    UserID INT NULL,
    Action NVARCHAR(100) NOT NULL,
    EntityType NVARCHAR(100) NOT NULL,
    EntityID NVARCHAR(100) NULL,
    OldValues NVARCHAR(MAX) NULL,
    NewValues NVARCHAR(MAX) NULL,
    IPAddress NVARCHAR(50) NULL,
    UserAgent NVARCHAR(255) NULL,
    LogDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- System settings
CREATE TABLE SystemSettings (
    SettingID INT PRIMARY KEY IDENTITY(1,1),
    SettingKey NVARCHAR(100) NOT NULL,
    SettingValue NVARCHAR(MAX) NULL,
    SettingDescription NVARCHAR(255) NULL,
    IsSecure BIT NOT NULL DEFAULT 0,
    Category NVARCHAR(50) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Role-Permission mapping
CREATE TABLE RolePermissions (
    RolePermissionID INT PRIMARY KEY IDENTITY(1,1),
    RoleID INT NOT NULL,
    PermissionID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- User-Role mapping
CREATE TABLE UserRoles (
    UserRoleID INT PRIMARY KEY IDENTITY(1,1),
    UserID INT NOT NULL,
    RoleID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- User preferences
CREATE TABLE UserPreferences (
    UserPreferenceID INT PRIMARY KEY IDENTITY(1,1),
    UserID INT NOT NULL,
    PreferenceKey NVARCHAR(100) NOT NULL,
    PreferenceValue NVARCHAR(MAX) NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Document categories
CREATE TABLE DocumentCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    ParentCategoryID INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Document tags
CREATE TABLE DocumentTags (
    TagID INT PRIMARY KEY IDENTITY(1,1),
    TagName NVARCHAR(50) NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Documents
CREATE TABLE Documents (
    DocumentID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000) NULL,
    FileName NVARCHAR(255) NOT NULL,
    FileExtension NVARCHAR(10) NULL,
    FileSizeKB INT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    CategoryID INT NULL,
    CompanyID INT NULL,
    DepartmentID INT NULL,
    IsPublished BIT NOT NULL DEFAULT 0,
    Version NVARCHAR(20) NOT NULL DEFAULT '1.0',
    DownloadCount INT NOT NULL DEFAULT 0,
    ViewCount INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Document versions
CREATE TABLE DocumentVersions (
    VersionID INT PRIMARY KEY IDENTITY(1,1),
    DocumentID INT NOT NULL,
    Version NVARCHAR(20) NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    FileSizeKB INT NULL,
    ChangeNotes NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Document approvals
CREATE TABLE DocumentApprovals (
    ApprovalID INT PRIMARY KEY IDENTITY(1,1),
    DocumentID INT NOT NULL,
    Version NVARCHAR(20) NOT NULL,
    ApproverID INT NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    ApprovedDate DATETIME NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Document-Tag mapping
CREATE TABLE DocumentTags_Map (
    DocumentTagID INT PRIMARY KEY IDENTITY(1,1),
    DocumentID INT NOT NULL,
    TagID INT NOT NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Knowledge base articles
CREATE TABLE KnowledgeArticles (
    ArticleID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    Summary NVARCHAR(1000) NULL,
    CategoryID INT NULL,
    CompanyID INT NULL,
    DepartmentID INT NULL,
    IsPublished BIT NOT NULL DEFAULT 0,
    ViewCount INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Knowledge article tags
CREATE TABLE KnowledgeArticleTags (
    ArticleTagID INT PRIMARY KEY IDENTITY(1,1),
    ArticleID INT NOT NULL,
    TagID INT NOT NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Knowledge article feedback
CREATE TABLE KnowledgeArticleFeedback (
    FeedbackID INT PRIMARY KEY IDENTITY(1,1),
    ArticleID INT NOT NULL,
    UserID INT NOT NULL,
    Rating INT NULL,
    Comments NVARCHAR(1000) NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- IT ticket categories
CREATE TABLE ITTicketCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- IT tickets (for internal tracking, syncs with FreshService)
CREATE TABLE ITTickets (
    TicketID INT PRIMARY KEY IDENTITY(1,1),
    ExternalTicketID NVARCHAR(50) NULL,
    Subject NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX) NULL,
    CategoryID INT NULL,
    Priority NVARCHAR(20) NULL,
    Status NVARCHAR(50) NULL,
    RequestedBy INT NOT NULL,
    AssignedTo INT NULL,
    CompanyID INT NOT NULL,
    DepartmentID INT NULL,
    ResolutionNotes NVARCHAR(MAX) NULL,
    ResolutionDate DATETIME NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- IT ticket attachments
CREATE TABLE ITTicketAttachments (
    AttachmentID INT PRIMARY KEY IDENTITY(1,1),
    TicketID INT NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    FileSizeKB INT NULL,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- IT ticket comments
CREATE TABLE ITTicketComments (
    CommentID INT PRIMARY KEY IDENTITY(1,1),
    TicketID INT NOT NULL,
    CommentText NVARCHAR(MAX) NOT NULL,
    IsPrivate BIT NOT NULL DEFAULT 0,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Software catalog
CREATE TABLE SoftwareCatalog (
    SoftwareID INT PRIMARY KEY IDENTITY(1,1),
    SoftwareName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(1000) NULL,
    Version NVARCHAR(50) NULL,
    Vendor NVARCHAR(100) NULL,
    LicenseType NVARCHAR(50) NULL,
    AccessRequestWorkflow NVARCHAR(50) NULL,
    DownloadURL NVARCHAR(255) NULL,
    DocumentationURL NVARCHAR(255) NULL,
    IconURL NVARCHAR(255) NULL,
    IsWebApplication BIT NOT NULL DEFAULT 0,
    WebAppURL NVARCHAR(255) NULL,
    SupportsSingleSignOn BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Company-Software mapping
CREATE TABLE CompanySoftware (
    CompanySoftwareID INT PRIMARY KEY IDENTITY(1,1),
    CompanyID INT NOT NULL,
    SoftwareID INT NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Software access requests
CREATE TABLE SoftwareAccessRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    SoftwareID INT NOT NULL,
    RequestedBy INT NOT NULL,
    RequestReason NVARCHAR(1000) NULL,
    Status NVARCHAR(20) NOT NULL,
    ApprovedBy INT NULL,
    ApprovalDate DATETIME NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- IT announcements
CREATE TABLE ITAnnouncements (
    AnnouncementID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME NULL,
    Priority NVARCHAR(20) NULL,
    CompanyID INT NULL,
    LocationID INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- HR policy categories
CREATE TABLE HRPolicyCategories (
    CategoryID INT PRIMARY KEY IDENTITY(1,1),
    CategoryName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- HR policies
CREATE TABLE HRPolicies (
    PolicyID INT PRIMARY KEY IDENTITY(1,1),
    PolicyName NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000) NULL,
    DocumentID INT NULL,
    CategoryID INT NULL,
    CompanyID INT NULL,
    LocationID INT NULL,
    IsAcknowledgementRequired BIT NOT NULL DEFAULT 0,
    EffectiveDate DATE NOT NULL,
    ExpiryDate DATE NULL,
    Version NVARCHAR(20) NOT NULL DEFAULT '1.0',
    Status NVARCHAR(20) NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- HR policy acknowledgments
CREATE TABLE HRPolicyAcknowledgments (
    AcknowledgmentID INT PRIMARY KEY IDENTITY(1,1),
    PolicyID INT NOT NULL,
    UserID INT NOT NULL,
    AcknowledgmentDate DATETIME NOT NULL,
    Comments NVARCHAR(1000) NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- HR request types
CREATE TABLE HRRequestTypes (
    RequestTypeID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    ApprovalWorkflow NVARCHAR(50) NULL,
    FormTemplate NVARCHAR(MAX) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- HR requests
CREATE TABLE HRRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeID INT NOT NULL,
    RequestedBy INT NOT NULL,
    FormData NVARCHAR(MAX) NULL,
    Status NVARCHAR(20) NOT NULL,
    CurrentApproverID INT NULL,
    CompletedDate DATETIME NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- HR request approvals
CREATE TABLE HRRequestApprovals (
    ApprovalID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    ApproverID INT NOT NULL,
    ApprovalOrder INT NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    ApprovalDate DATETIME NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Travel request types
CREATE TABLE TravelRequestTypes (
    RequestTypeID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Travel requests
CREATE TABLE TravelRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    RequestTypeID INT NOT NULL,
    RequestedBy INT NOT NULL,
    Purpose NVARCHAR(1000) NOT NULL,
    TravelFrom NVARCHAR(255) NOT NULL,
    TravelTo NVARCHAR(255) NOT NULL,
    DepartureDate DATE NOT NULL,
    ReturnDate DATE NULL,
    AccommodationRequired BIT NOT NULL DEFAULT 0,
    TransportationRequired BIT NOT NULL DEFAULT 0,
    AdvanceAmount DECIMAL(18, 2) NULL,
    Status NVARCHAR(20) NOT NULL,
    CompanyID INT NOT NULL,
    DepartmentID INT NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- Travel request approvals
CREATE TABLE TravelRequestApprovals (
    ApprovalID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    ApproverID INT NOT NULL,
    ApprovalOrder INT NOT NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    ApprovalDate DATETIME NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- Travel expenses
CREATE TABLE TravelExpenses (
    ExpenseID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    ExpenseType NVARCHAR(50) NOT NULL,
    ExpenseDate DATE NOT NULL,
    Amount DECIMAL(18, 2) NOT NULL,
    Currency NVARCHAR(3) NOT NULL DEFAULT 'INR',
    Description NVARCHAR(255) NULL,
    ReceiptStoragePath NVARCHAR(1000) NULL,
    Status NVARCHAR(20) NOT NULL,
    Comments NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);

-- =============================================
-- INITIAL DATA SCRIPTS
-- =============================================

-- Insert SASMOS Group companies
INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
VALUES 
('SASMOS HET', 'SASMOS', 1, 1, GETDATE()),
('FESIL', 'FESIL', 1, 1, GETDATE()),
('WestWire Harnesses', 'WWH', 1, 1, GETDATE()),
('Avirata Defence Systems', 'ADS', 1, 1, GETDATE()),
('LiDER Technologies', 'LIDER', 1, 1, GETDATE()),
('GloDesi Technologies', 'GLODESI', 1, 1, GETDATE()),
('Hanuka', 'HANUKA', 1, 1, GETDATE());

-- Insert default roles
INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate)
VALUES 
('Administrator', 'System administrator with full access', 1, 1, 1, GETDATE()),
('Employee', 'Regular employee with basic access', 1, 1, 1, GETDATE()),
('Manager', 'Manager with team oversight and approvals', 1, 1, 1, GETDATE()),
('Executive', 'Executive leadership with high-level views', 1, 1, 1, GETDATE()),
('IT Admin', 'IT administrator with IT Hub management access', 1, 1, 1, GETDATE()),
('HR Admin', 'HR administrator with HR Hub management access', 1, 1, 1, GETDATE()),
('Content Admin', 'Administrator for content and announcements', 1, 1, 1, GETDATE());

-- =============================================
-- ADD CONSTRAINTS (FOREIGN KEYS and UNIQUE)
-- =============================================
-- NOTE: Constraints that were originally inline are moved here to avoid dependency issues during table creation.

-- Locations
ALTER TABLE Locations ADD CONSTRAINT FK_Locations_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);

-- GuestHouseBookings
ALTER TABLE GuestHouseBookings ADD CONSTRAINT FK_GuestHouseBookings_GuestHouseRooms FOREIGN KEY (RoomID) REFERENCES GuestHouseRooms(RoomID);
ALTER TABLE GuestHouseBookings ADD CONSTRAINT FK_GuestHouseBookings_Users FOREIGN KEY (BookedBy) REFERENCES Users(UserID);
ALTER TABLE GuestHouseBookings ADD CONSTRAINT FK_GuestHouseBookings_Companies FOREIGN KEY (GuestCompanyID) REFERENCES Companies(CompanyID);

-- CompanyVehicles
ALTER TABLE CompanyVehicles ADD CONSTRAINT FK_CompanyVehicles_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);
ALTER TABLE CompanyVehicles ADD CONSTRAINT FK_CompanyVehicles_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);

-- VehicleReservations
ALTER TABLE VehicleReservations ADD CONSTRAINT FK_VehicleReservations_CompanyVehicles FOREIGN KEY (VehicleID) REFERENCES CompanyVehicles(VehicleID);
ALTER TABLE VehicleReservations ADD CONSTRAINT FK_VehicleReservations_Users FOREIGN KEY (ReservedBy) REFERENCES Users(UserID);

-- ShuttleSchedules
ALTER TABLE ShuttleSchedules ADD CONSTRAINT FK_ShuttleSchedules_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- TransportationBookings
ALTER TABLE TransportationBookings ADD CONSTRAINT FK_TransportationBookings_TransportationProviders FOREIGN KEY (ProviderID) REFERENCES TransportationProviders(ProviderID);
ALTER TABLE TransportationBookings ADD CONSTRAINT FK_TransportationBookings_Users FOREIGN KEY (BookedBy) REFERENCES Users(UserID);
ALTER TABLE TransportationBookings ADD CONSTRAINT FK_TransportationBookings_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE TransportationBookings ADD CONSTRAINT FK_TransportationBookings_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- MeetingRooms
ALTER TABLE MeetingRooms ADD CONSTRAINT FK_MeetingRooms_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- MeetingRoomFacilities_Map
ALTER TABLE MeetingRoomFacilities_Map ADD CONSTRAINT FK_MeetingRoomFacilities_Map_MeetingRooms FOREIGN KEY (RoomID) REFERENCES MeetingRooms(RoomID);
ALTER TABLE MeetingRoomFacilities_Map ADD CONSTRAINT FK_MeetingRoomFacilities_Map_MeetingRoomFacilities FOREIGN KEY (FacilityID) REFERENCES MeetingRoomFacilities(FacilityID);
ALTER TABLE MeetingRoomFacilities_Map ADD CONSTRAINT UQ_MeetingRoomFacilities_Map UNIQUE (RoomID, FacilityID);

-- MeetingRoomBookings
ALTER TABLE MeetingRoomBookings ADD CONSTRAINT FK_MeetingRoomBookings_MeetingRooms FOREIGN KEY (RoomID) REFERENCES MeetingRooms(RoomID);
ALTER TABLE MeetingRoomBookings ADD CONSTRAINT FK_MeetingRoomBookings_Users FOREIGN KEY (BookedBy) REFERENCES Users(UserID);

-- MaintenanceRequests
ALTER TABLE MaintenanceRequests ADD CONSTRAINT FK_MaintenanceRequests_MaintenanceRequestTypes FOREIGN KEY (RequestTypeID) REFERENCES MaintenanceRequestTypes(RequestTypeID);
ALTER TABLE MaintenanceRequests ADD CONSTRAINT FK_MaintenanceRequests_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE MaintenanceRequests ADD CONSTRAINT FK_MaintenanceRequests_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);
ALTER TABLE MaintenanceRequests ADD CONSTRAINT FK_MaintenanceRequests_Users_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Users(UserID);

-- VisitorAppointments
ALTER TABLE VisitorAppointments ADD CONSTRAINT FK_VisitorAppointments_Visitors FOREIGN KEY (VisitorID) REFERENCES Visitors(VisitorID);
ALTER TABLE VisitorAppointments ADD CONSTRAINT FK_VisitorAppointments_Users FOREIGN KEY (HostUserID) REFERENCES Users(UserID);
ALTER TABLE VisitorAppointments ADD CONSTRAINT FK_VisitorAppointments_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- ProcurementItems
ALTER TABLE ProcurementItems ADD CONSTRAINT FK_ProcurementItems_ProcurementCategories FOREIGN KEY (CategoryID) REFERENCES ProcurementCategories(CategoryID);

-- ProcurementRequests
ALTER TABLE ProcurementRequests ADD CONSTRAINT FK_ProcurementRequests_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE ProcurementRequests ADD CONSTRAINT FK_ProcurementRequests_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE ProcurementRequests ADD CONSTRAINT FK_ProcurementRequests_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);
ALTER TABLE ProcurementRequests ADD CONSTRAINT FK_ProcurementRequests_Locations FOREIGN KEY (DeliveryLocationID) REFERENCES Locations(LocationID);
ALTER TABLE ProcurementRequests ADD CONSTRAINT FK_ProcurementRequests_Users_CurrentApproverID FOREIGN KEY (CurrentApproverID) REFERENCES Users(UserID);

-- ProcurementRequestItems
ALTER TABLE ProcurementRequestItems ADD CONSTRAINT FK_ProcurementRequestItems_ProcurementRequests FOREIGN KEY (RequestID) REFERENCES ProcurementRequests(RequestID);
ALTER TABLE ProcurementRequestItems ADD CONSTRAINT FK_ProcurementRequestItems_ProcurementItems FOREIGN KEY (ItemID) REFERENCES ProcurementItems(ItemID);

-- ProcurementRequestApprovals
ALTER TABLE ProcurementRequestApprovals ADD CONSTRAINT FK_ProcurementRequestApprovals_ProcurementRequests FOREIGN KEY (RequestID) REFERENCES ProcurementRequests(RequestID);
ALTER TABLE ProcurementRequestApprovals ADD CONSTRAINT FK_ProcurementRequestApprovals_Users FOREIGN KEY (ApproverID) REFERENCES Users(UserID);

-- Announcements
ALTER TABLE Announcements ADD CONSTRAINT FK_Announcements_AnnouncementCategories FOREIGN KEY (CategoryID) REFERENCES AnnouncementCategories(CategoryID);
ALTER TABLE Announcements ADD CONSTRAINT FK_Announcements_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE Announcements ADD CONSTRAINT FK_Announcements_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);
ALTER TABLE Announcements ADD CONSTRAINT FK_Announcements_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- AnnouncementApprovals
ALTER TABLE AnnouncementApprovals ADD CONSTRAINT FK_AnnouncementApprovals_Announcements FOREIGN KEY (AnnouncementID) REFERENCES Announcements(AnnouncementID);
ALTER TABLE AnnouncementApprovals ADD CONSTRAINT FK_AnnouncementApprovals_Users FOREIGN KEY (ApproverID) REFERENCES Users(UserID);

-- AnnouncementReadStatus
ALTER TABLE AnnouncementReadStatus ADD CONSTRAINT FK_AnnouncementReadStatus_Announcements FOREIGN KEY (AnnouncementID) REFERENCES Announcements(AnnouncementID);
ALTER TABLE AnnouncementReadStatus ADD CONSTRAINT FK_AnnouncementReadStatus_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);
ALTER TABLE AnnouncementReadStatus ADD CONSTRAINT UQ_AnnouncementReadStatus UNIQUE (AnnouncementID, UserID);

-- Events
ALTER TABLE Events ADD CONSTRAINT FK_Events_EventCategories FOREIGN KEY (CategoryID) REFERENCES EventCategories(CategoryID);
ALTER TABLE Events ADD CONSTRAINT FK_Events_MeetingRooms FOREIGN KEY (MeetingRoomID) REFERENCES MeetingRooms(RoomID);
ALTER TABLE Events ADD CONSTRAINT FK_Events_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE Events ADD CONSTRAINT FK_Events_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);
ALTER TABLE Events ADD CONSTRAINT FK_Events_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);
ALTER TABLE Events ADD CONSTRAINT FK_Events_Users FOREIGN KEY (OrganizerID) REFERENCES Users(UserID);

-- EventRegistrations
ALTER TABLE EventRegistrations ADD CONSTRAINT FK_EventRegistrations_Events FOREIGN KEY (EventID) REFERENCES Events(EventID);
ALTER TABLE EventRegistrations ADD CONSTRAINT FK_EventRegistrations_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);
ALTER TABLE EventRegistrations ADD CONSTRAINT UQ_EventRegistrations UNIQUE (EventID, UserID);

-- CustomerInteractions
ALTER TABLE CustomerInteractions ADD CONSTRAINT FK_CustomerInteractions_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE CustomerInteractions ADD CONSTRAINT FK_CustomerInteractions_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- CustomerInteractionMedia
ALTER TABLE CustomerInteractionMedia ADD CONSTRAINT FK_CustomerInteractionMedia_CustomerInteractions FOREIGN KEY (InteractionID) REFERENCES CustomerInteractions(InteractionID);

-- Notifications
ALTER TABLE Notifications ADD CONSTRAINT FK_Notifications_NotificationTypes FOREIGN KEY (NotificationTypeID) REFERENCES NotificationTypes(NotificationTypeID);
ALTER TABLE Notifications ADD CONSTRAINT FK_Notifications_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);

-- AuditLog
ALTER TABLE AuditLog ADD CONSTRAINT FK_AuditLog_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);

-- SystemSettings
ALTER TABLE SystemSettings ADD CONSTRAINT UQ_SystemSettings UNIQUE (SettingKey);

-- Departments
ALTER TABLE Departments ADD CONSTRAINT FK_Departments_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);

-- RolePermissions
ALTER TABLE RolePermissions ADD CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (RoleID) REFERENCES Roles(RoleID);
ALTER TABLE RolePermissions ADD CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (PermissionID) REFERENCES Permissions(PermissionID);
ALTER TABLE RolePermissions ADD CONSTRAINT UQ_RolePermissions UNIQUE (RoleID, PermissionID);

-- Users
ALTER TABLE Users ADD CONSTRAINT FK_Users_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE Users ADD CONSTRAINT FK_Users_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);
ALTER TABLE Users ADD CONSTRAINT FK_Users_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);
ALTER TABLE Users ADD CONSTRAINT FK_Users_Manager FOREIGN KEY (ManagerID) REFERENCES Users(UserID); -- Self-referencing FK

-- UserRoles
ALTER TABLE UserRoles ADD CONSTRAINT FK_UserRoles_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);
ALTER TABLE UserRoles ADD CONSTRAINT FK_UserRoles_Roles FOREIGN KEY (RoleID) REFERENCES Roles(RoleID);
ALTER TABLE UserRoles ADD CONSTRAINT UQ_UserRoles UNIQUE (UserID, RoleID);

-- UserPreferences
ALTER TABLE UserPreferences ADD CONSTRAINT FK_UserPreferences_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);
ALTER TABLE UserPreferences ADD CONSTRAINT UQ_UserPreferences UNIQUE (UserID, PreferenceKey);

-- DocumentCategories
ALTER TABLE DocumentCategories ADD CONSTRAINT FK_DocumentCategories_Parent FOREIGN KEY (ParentCategoryID) REFERENCES DocumentCategories(CategoryID);

-- Documents
ALTER TABLE Documents ADD CONSTRAINT FK_Documents_DocumentCategories FOREIGN KEY (CategoryID) REFERENCES DocumentCategories(CategoryID);
ALTER TABLE Documents ADD CONSTRAINT FK_Documents_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE Documents ADD CONSTRAINT FK_Documents_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- DocumentVersions
ALTER TABLE DocumentVersions ADD CONSTRAINT FK_DocumentVersions_Documents FOREIGN KEY (DocumentID) REFERENCES Documents(DocumentID);

-- DocumentApprovals
ALTER TABLE DocumentApprovals ADD CONSTRAINT FK_DocumentApprovals_Documents FOREIGN KEY (DocumentID) REFERENCES Documents(DocumentID);
ALTER TABLE DocumentApprovals ADD CONSTRAINT FK_DocumentApprovals_Users FOREIGN KEY (ApproverID) REFERENCES Users(UserID);

-- DocumentTags_Map
ALTER TABLE DocumentTags_Map ADD CONSTRAINT FK_DocumentTags_Map_Documents FOREIGN KEY (DocumentID) REFERENCES Documents(DocumentID);
ALTER TABLE DocumentTags_Map ADD CONSTRAINT FK_DocumentTags_Map_DocumentTags FOREIGN KEY (TagID) REFERENCES DocumentTags(TagID);
ALTER TABLE DocumentTags_Map ADD CONSTRAINT UQ_DocumentTags_Map UNIQUE (DocumentID, TagID);

-- KnowledgeArticles
ALTER TABLE KnowledgeArticles ADD CONSTRAINT FK_KnowledgeArticles_DocumentCategories FOREIGN KEY (CategoryID) REFERENCES DocumentCategories(CategoryID);
ALTER TABLE KnowledgeArticles ADD CONSTRAINT FK_KnowledgeArticles_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE KnowledgeArticles ADD CONSTRAINT FK_KnowledgeArticles_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- KnowledgeArticleTags
ALTER TABLE KnowledgeArticleTags ADD CONSTRAINT FK_KnowledgeArticleTags_KnowledgeArticles FOREIGN KEY (ArticleID) REFERENCES KnowledgeArticles(ArticleID);
ALTER TABLE KnowledgeArticleTags ADD CONSTRAINT FK_KnowledgeArticleTags_DocumentTags FOREIGN KEY (TagID) REFERENCES DocumentTags(TagID);
ALTER TABLE KnowledgeArticleTags ADD CONSTRAINT UQ_KnowledgeArticleTags UNIQUE (ArticleID, TagID);

-- KnowledgeArticleFeedback
ALTER TABLE KnowledgeArticleFeedback ADD CONSTRAINT FK_KnowledgeArticleFeedback_KnowledgeArticles FOREIGN KEY (ArticleID) REFERENCES KnowledgeArticles(ArticleID);
ALTER TABLE KnowledgeArticleFeedback ADD CONSTRAINT FK_KnowledgeArticleFeedback_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);

-- ITTickets
ALTER TABLE ITTickets ADD CONSTRAINT FK_ITTickets_ITTicketCategories FOREIGN KEY (CategoryID) REFERENCES ITTicketCategories(CategoryID);
ALTER TABLE ITTickets ADD CONSTRAINT FK_ITTickets_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE ITTickets ADD CONSTRAINT FK_ITTickets_Users_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Users(UserID);
ALTER TABLE ITTickets ADD CONSTRAINT FK_ITTickets_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE ITTickets ADD CONSTRAINT FK_ITTickets_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- ITTicketAttachments
ALTER TABLE ITTicketAttachments ADD CONSTRAINT FK_ITTicketAttachments_ITTickets FOREIGN KEY (TicketID) REFERENCES ITTickets(TicketID);

-- ITTicketComments
ALTER TABLE ITTicketComments ADD CONSTRAINT FK_ITTicketComments_ITTickets FOREIGN KEY (TicketID) REFERENCES ITTickets(TicketID);

-- CompanySoftware
ALTER TABLE CompanySoftware ADD CONSTRAINT FK_CompanySoftware_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE CompanySoftware ADD CONSTRAINT FK_CompanySoftware_SoftwareCatalog FOREIGN KEY (SoftwareID) REFERENCES SoftwareCatalog(SoftwareID);
ALTER TABLE CompanySoftware ADD CONSTRAINT UQ_CompanySoftware UNIQUE (CompanyID, SoftwareID);

-- SoftwareAccessRequests
ALTER TABLE SoftwareAccessRequests ADD CONSTRAINT FK_SoftwareAccessRequests_SoftwareCatalog FOREIGN KEY (SoftwareID) REFERENCES SoftwareCatalog(SoftwareID);
ALTER TABLE SoftwareAccessRequests ADD CONSTRAINT FK_SoftwareAccessRequests_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE SoftwareAccessRequests ADD CONSTRAINT FK_SoftwareAccessRequests_Users_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID);

-- ITAnnouncements
ALTER TABLE ITAnnouncements ADD CONSTRAINT FK_ITAnnouncements_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE ITAnnouncements ADD CONSTRAINT FK_ITAnnouncements_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- HRPolicies
ALTER TABLE HRPolicies ADD CONSTRAINT FK_HRPolicies_Documents FOREIGN KEY (DocumentID) REFERENCES Documents(DocumentID);
ALTER TABLE HRPolicies ADD CONSTRAINT FK_HRPolicies_HRPolicyCategories FOREIGN KEY (CategoryID) REFERENCES HRPolicyCategories(CategoryID);
ALTER TABLE HRPolicies ADD CONSTRAINT FK_HRPolicies_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE HRPolicies ADD CONSTRAINT FK_HRPolicies_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- HRPolicyAcknowledgments
ALTER TABLE HRPolicyAcknowledgments ADD CONSTRAINT FK_HRPolicyAcknowledgments_HRPolicies FOREIGN KEY (PolicyID) REFERENCES HRPolicies(PolicyID);
ALTER TABLE HRPolicyAcknowledgments ADD CONSTRAINT FK_HRPolicyAcknowledgments_Users FOREIGN KEY (UserID) REFERENCES Users(UserID);
ALTER TABLE HRPolicyAcknowledgments ADD CONSTRAINT UQ_HRPolicyAcknowledgments UNIQUE (PolicyID, UserID);

-- HRRequests
ALTER TABLE HRRequests ADD CONSTRAINT FK_HRRequests_HRRequestTypes FOREIGN KEY (RequestTypeID) REFERENCES HRRequestTypes(RequestTypeID);
ALTER TABLE HRRequests ADD CONSTRAINT FK_HRRequests_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE HRRequests ADD CONSTRAINT FK_HRRequests_Users_CurrentApproverID FOREIGN KEY (CurrentApproverID) REFERENCES Users(UserID);

-- HRRequestApprovals
ALTER TABLE HRRequestApprovals ADD CONSTRAINT FK_HRRequestApprovals_HRRequests FOREIGN KEY (RequestID) REFERENCES HRRequests(RequestID);
ALTER TABLE HRRequestApprovals ADD CONSTRAINT FK_HRRequestApprovals_Users FOREIGN KEY (ApproverID) REFERENCES Users(UserID);

-- TravelRequests
ALTER TABLE TravelRequests ADD CONSTRAINT FK_TravelRequests_TravelRequestTypes FOREIGN KEY (RequestTypeID) REFERENCES TravelRequestTypes(RequestTypeID);
ALTER TABLE TravelRequests ADD CONSTRAINT FK_TravelRequests_Users FOREIGN KEY (RequestedBy) REFERENCES Users(UserID);
ALTER TABLE TravelRequests ADD CONSTRAINT FK_TravelRequests_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID);
ALTER TABLE TravelRequests ADD CONSTRAINT FK_TravelRequests_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID);

-- TravelRequestApprovals
ALTER TABLE TravelRequestApprovals ADD CONSTRAINT FK_TravelRequestApprovals_TravelRequests FOREIGN KEY (RequestID) REFERENCES TravelRequests(RequestID);
ALTER TABLE TravelRequestApprovals ADD CONSTRAINT FK_TravelRequestApprovals_Users FOREIGN KEY (ApproverID) REFERENCES Users(UserID);

-- TravelExpenses
ALTER TABLE TravelExpenses ADD CONSTRAINT FK_TravelExpenses_TravelRequests FOREIGN KEY (RequestID) REFERENCES TravelRequests(RequestID);

-- GuestHouses
ALTER TABLE GuestHouses ADD CONSTRAINT FK_GuestHouses_Locations FOREIGN KEY (LocationID) REFERENCES Locations(LocationID);

-- GuestHouseRooms
ALTER TABLE GuestHouseRooms ADD CONSTRAINT FK_GuestHouseRooms_GuestHouses FOREIGN KEY (GuestHouseID) REFERENCES GuestHouses(GuestHouseID);
    