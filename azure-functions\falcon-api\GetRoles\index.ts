import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { RoleDefinition } from "../shared/interfaces";

export async function getRoles(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}"`);

    try {
        // Use RoleDescription column name and alias it as Description for consistency
        const result = await executeQuery('SELECT RoleID, RoleName, RoleDescription as Description, IsSystemRole, IsActive FROM Roles WHERE IsActive = 1 ORDER BY RoleName');

        // Map the database result to the format expected by the frontend
        const roles = result.recordset.map(role => ({
            id: role.RoleID.toString(),
            name: role.RoleName,
            description: role.Description || null // Ensure description is never undefined
        }));

        context.log("Mapped roles:", JSON.stringify(roles));

        // Log the raw database result for debugging
        context.log("Raw database result:", JSON.stringify(result.recordset));

        context.log("Returning roles:", JSON.stringify(roles));

        // The roles are already in the format expected by the frontend
        context.log("Roles ready for frontend:", JSON.stringify(roles));

        return {
            status: 200,
            jsonBody: roles
        };
    } catch (error) {
        context.error(`Error fetching roles: ${error instanceof Error ? error.message : error}`);

        return {
            status: 500,
            jsonBody: {
                message: "Error fetching roles.",
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}

app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'roles',
    handler: getRoles
});