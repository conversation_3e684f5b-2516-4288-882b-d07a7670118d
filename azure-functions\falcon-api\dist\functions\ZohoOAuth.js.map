{"version": 3, "file": "ZohoOAuth.js", "sourceRoot": "", "sources": ["../../src/functions/ZohoOAuth.ts"], "names": [], "mappings": ";;AAAA,gDAAyF;AACzF,mDAAyD;AACzD,qEAAkE;AAClE,mDAAgD;AAwBhD,kEAAkE;AAClE,SAAS,kBAAkB;IACvB,oCAAoC;IACpC,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC,kBAAkB;IACnE,MAAM,OAAO,GAAG,yBAAyB,MAAM,WAAW,CAAC;IAE3D,OAAO;QACH,+CAA+C;QAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE;QAC/C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,EAAE;QACvD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,8CAA8C;QACjG,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE;YACJ,8BAA8B;YAC9B,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,0CAA0C;YAC1C,wBAAwB;YACxB,sBAAsB;YACtB,wBAAwB;YACxB,0BAA0B;SAC7B;KACJ,CAAC;AACN,CAAC;AAED,iEAAiE;AACjE,KAAK,UAAU,aAAa,CAAC,GAAgB,EAAE,OAA0B;IACrE,eAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IAEpE,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,kBAAkB,EAAE,CAAC;QAEpC,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC1C,eAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;SAChF;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,OAAO,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACrD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1D,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAChE,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACtD,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEjD,mCAAmC;QACnC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACrC,MAAM,EAAE,SAAS,EAAE,MAAM,IAAI,UAAU;YACvC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACvB,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE5C,eAAM,CAAC,IAAI,CAAC,mDAAmD,SAAS,EAAE,MAAM,IAAI,UAAU,EAAE,CAAC,CAAC;QAElG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;gBAC3B,OAAO,EAAE,qFAAqF;gBAC9F,MAAM,EAAE,MAAM,CAAC,MAAM;aACxB;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACtE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE,EAAE,CAAC;KACvF;AACL,CAAC;AAED,iEAAiE;AACjE,KAAK,UAAU,YAAY,CAAC,GAAgB,EAAE,OAA0B;IACpE,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IAEpD,IAAI;QACA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE5C,IAAI,KAAK,EAAE;YACP,eAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;YACjD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,KAAK,EAAE,EAAE,EAAE,CAAC;SACvF;QAED,IAAI,CAAC,IAAI,EAAE;YACP,eAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC1D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,CAAC;SAC/E;QAED,MAAM,MAAM,GAAG,kBAAkB,EAAE,CAAC;QAEpC,+CAA+C;QAC/C,MAAM,gBAAgB,GAAG,IAAI,eAAe,CAAC;YACzC,UAAU,EAAE,oBAAoB;YAChC,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,MAAM,CAAC,YAAY;YAClC,YAAY,EAAE,MAAM,CAAC,WAAW;YAChC,IAAI,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,QAAQ,EAAE;YACzD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,gBAAgB;SACzB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE;YACnB,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,eAAM,CAAC,KAAK,CAAC,qCAAqC,SAAS,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kDAAkD,EAAE,EAAE,CAAC;SACnG;QAED,MAAM,MAAM,GAAe,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;QAEtD,sCAAsC;QACtC,IAAI,MAAM,GAAG,SAAS,CAAC;QACvB,IAAI,KAAK,EAAE;YACP,IAAI;gBACA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtE,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;aAC7B;YAAC,OAAO,GAAG,EAAE;gBACV,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;aAC7D;SACJ;QAED,wBAAwB;QACxB,MAAM,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEtC,eAAM,CAAC,IAAI,CAAC,oDAAoD,MAAM,iBAAiB,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAEvG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,4DAA4D;gBACrE,SAAS,EAAE,MAAM,CAAC,UAAU;gBAC5B,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,CAAC,WAAW,EAAE,4BAA4B,CAAC;aACxD;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,CAAC;KACnF;AACL,CAAC;AAED,gEAAgE;AAChE,KAAK,UAAU,YAAY,CAAC,GAAgB,EAAE,OAA0B;IACpE,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAE/C,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,SAAS,EAAE,MAAM,IAAI,UAAU,CAAC;QAE/C,oBAAoB;QACpB,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,EAAE,CAAC,CAAC;YAC7D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,sDAAsD,EAAE,EAAE,CAAC;SACvG;QAED,kEAAkE;QAClE,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,iCAAiC;QAC9E,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QACzD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QAElC,IAAI,UAAU,GAAG,GAAG,GAAG,WAAW,EAAE;YAChC,gBAAgB;YAChB,MAAM,eAAe,GAAG,MAAM,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,eAAe,EAAE;gBACjB,MAAM,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC/C,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,eAAe,CAAC,YAAY,EAAE,EAAE,CAAC;aACnF;iBAAM;gBACH,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,8CAA8C,EAAE,EAAE,CAAC;aAC/F;SACJ;QAED,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC;KAE1E;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,CAAC;KAC7E;AACL,CAAC;AAED,0CAA0C;AAC1C,KAAK,UAAU,gBAAgB,CAAC,YAAoB;IAChD,IAAI;QACA,MAAM,MAAM,GAAG,kBAAkB,EAAE,CAAC;QAEpC,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC;YAC3C,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,MAAM,CAAC,QAAQ;YAC1B,aAAa,EAAE,MAAM,CAAC,YAAY;YAClC,aAAa,EAAE,YAAY;SAC9B,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,QAAQ,EAAE;YAC3D,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACL,cAAc,EAAE,mCAAmC;aACtD;YACD,IAAI,EAAE,kBAAkB;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;YACrB,eAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAe,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC;QAC3D,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAE9D,OAAO,SAAS,CAAC;KAEpB;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAED,6DAA6D;AAC7D,KAAK,UAAU,eAAe,CAAC,MAAc,EAAE,MAAkB;IAC7D,IAAI;QACA,MAAM,OAAO,GAAG,MAAM,qCAAiB,CAAC,SAAS,CAC7C,MAAM,EACN,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,aAAa,EACpB,MAAM,CAAC,UAAU,IAAI,IAAI,EACzB,MAAM,CAAC,KAAK,EACZ,MAAM,EACN,MAAM,EACN,MAAM,CAAC,UAAU,IAAI,QAAQ,CAChC,CAAC;QAEF,IAAI,OAAO,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,qCAAqC,MAAM,qBAAqB,OAAO,EAAE,CAAC,CAAC;SAC1F;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACvD;KAEJ;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,KAAK,CAAC;KACf;AACL,CAAC;AAED,qDAAqD;AACrD,KAAK,UAAU,mBAAmB,CAAC,MAAc;IAC7C,IAAI;QACA,MAAM,KAAK,GAAG,MAAM,qCAAiB,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAE7E,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,4EAA4E;QAC5E,OAAO;YACH,YAAY,EAAE,KAAK,CAAC,WAAW;YAC/B,aAAa,EAAE,KAAK,CAAC,YAAY;YACjC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;YACvE,UAAU,EAAE,KAAK,CAAC,SAAS;YAC3B,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE;SACtD,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AAED,2BAA2B;AAC3B,eAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE;IAC7B,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,qBAAqB;IAC5B,OAAO,EAAE,aAAa;CACzB,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE;IAC5B,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,YAAY;CACxB,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,iBAAiB;IACxB,OAAO,EAAE,YAAY;CACxB,CAAC,CAAC"}