import React, { useState, useEffect, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { 
    fetchPortalUsers, 
    PortalUser,
    PaginatedPortalUsersResponse, 
    fetchPortalRoleNames,
    fetchCompanyNames,
    deletePortalUser,
    updatePortalUser,
    PORTAL_STATUSES,
    setMsalContext
} from '../../services/adminApi';

const UserManagementPage: React.FC = () => {
    const location = useLocation();
    const { instance, accounts } = useMsal();
    
    // --- State for Success Message ---
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    
    // --- State for User List ---
    const [userList, setUserList] = useState<PortalUser[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [totalUsers, setTotalUsers] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10);

    // --- State for Filtering User List ---
    const [searchTerm, setSearchTerm] = useState('');
    const [companyFilter, setCompanyFilter] = useState<string>('All');
    const [roleFilter, setRoleFilter] = useState<string>('All');
    const [statusFilter, setStatusFilter] = useState<PortalUser['status'] | 'All'>('Active');

    // --- State for Dynamic Roles and Companies ---
    const [availableRoles, setAvailableRoles] = useState<string[]>([]);
    const [availableCompanies, setAvailableCompanies] = useState<string[]>([]);

    // --- State for Delete Confirmation ---
    const [deleteConfirmation, setDeleteConfirmation] = useState<{
        isOpen: boolean;
        user: PortalUser | null;
    }>({ isOpen: false, user: null });
    const [isDeleting, setIsDeleting] = useState(false);

    // --- State for Bulk Operations ---
    const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
    const [bulkOperationModal, setBulkOperationModal] = useState<{
        isOpen: boolean;
        type: 'role' | 'status' | null;
    }>({ isOpen: false, type: null });
    const [bulkRoleAssignment, setBulkRoleAssignment] = useState<string[]>([]);
    const [bulkStatusChange, setBulkStatusChange] = useState<'Active' | 'Inactive'>('Active');
    const [isBulkProcessing, setIsBulkProcessing] = useState(false);
    const [bulkOperationError, setBulkOperationError] = useState<string | null>(null);

    // Set MSAL context when component mounts
    useEffect(() => {
        setMsalContext(instance, accounts);
    }, [instance, accounts]);

    // --- Handle Success Message from Navigation ---
    useEffect(() => {
        if (location.state?.successMessage) {
            setSuccessMessage(location.state.successMessage);
            
            // Clear the success message after 5 seconds
            const timer = setTimeout(() => {
                setSuccessMessage(null);
            }, 5000);
            
            // Clean up navigation state to prevent message from showing again on refresh
            window.history.replaceState({}, document.title);
            
            return () => clearTimeout(timer);
        }
    }, [location.state]);

    // --- Fetch Available Roles and Companies ---
    const fetchRolesAndCompanies = useCallback(async () => {
        try {
            const [roles, companies] = await Promise.all([
                fetchPortalRoleNames(),
                fetchCompanyNames()
            ]);
            setAvailableRoles(roles);
            setAvailableCompanies(companies);
        } catch (err) {
            console.error("Error fetching roles or companies:", err);
            // Set empty arrays and show error - don't use fallback data
            setAvailableRoles([]);
            setAvailableCompanies([]);
            setError("Failed to load roles and companies from database. Please refresh the page.");
        }
    }, []);

    // --- Handlers for Filter Changes (reset page) ---
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); 
    };
    const handleCompanyFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setCompanyFilter(e.target.value);
        setCurrentPage(1);
    };
    const handleRoleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setRoleFilter(e.target.value);
        setCurrentPage(1);
    };
    const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setStatusFilter(e.target.value as PortalUser['status'] | 'All');
        setCurrentPage(1);
    };

    // --- Fetching User List (Combined Directory/Managed) ---
    const fetchUsers = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response: PaginatedPortalUsersResponse = await fetchPortalUsers(
                searchTerm,
                companyFilter, 
                roleFilter, 
                statusFilter, 
                currentPage, 
                pageSize
            );
            console.log('🔍 User list fetched from API:', response.users.length, 'users');
            response.users.forEach(user => {
                console.log('📋 User from API:', user.name, 'ID:', user.id, 'Email:', user.email);
            });
            setUserList(response.users);
            setTotalUsers(response.totalCount);
        } catch (err) {
            console.error("Error fetching users:", err);
            setError("Failed to load users. Please try again.");
        } finally {
            setIsLoading(false);
        }
    }, [searchTerm, companyFilter, roleFilter, statusFilter, currentPage, pageSize]);

    useEffect(() => {
        fetchRolesAndCompanies();
    }, [fetchRolesAndCompanies]);

    useEffect(() => {
        fetchUsers();
    }, [fetchUsers]);

    // --- Pagination Calculation ---
    const totalPages = Math.ceil(totalUsers / pageSize);

    const handlePageChange = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    // --- Delete User Handlers ---
    const handleDeleteClick = (user: PortalUser) => {
        setDeleteConfirmation({ isOpen: true, user });
    };

    const handleDeleteConfirm = async () => {
        if (!deleteConfirmation.user) return;
        
        setIsDeleting(true);
        try {
            await deletePortalUser(deleteConfirmation.user.id);
            
            // Refresh the user list after successful deletion
            await fetchUsers();
            
            // Close the confirmation dialog
            setDeleteConfirmation({ isOpen: false, user: null });
        } catch (err) {
            console.error('Error deleting user:', err);
            setError(err instanceof Error ? err.message : 'Failed to remove user from portal');
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteCancel = () => {
        setDeleteConfirmation({ isOpen: false, user: null });
    };

    // --- Bulk Operation Handlers ---
    const handleUserSelection = (userId: string, isSelected: boolean) => {
        const newSelection = new Set(selectedUsers);
        if (isSelected) {
            newSelection.add(userId);
        } else {
            newSelection.delete(userId);
        }
        setSelectedUsers(newSelection);
    };

    const handleSelectAll = (isSelected: boolean) => {
        if (isSelected) {
            setSelectedUsers(new Set(userList.map(user => user.id)));
        } else {
            setSelectedUsers(new Set());
        }
    };

    const openBulkRoleModal = () => {
        if (selectedUsers.size === 0) {
            setError('Please select users to assign roles to.');
            return;
        }
        setBulkOperationModal({ isOpen: true, type: 'role' });
        setBulkRoleAssignment(['Employee']); // Default role
        setBulkOperationError(null);
    };

    const openBulkStatusModal = () => {
        if (selectedUsers.size === 0) {
            setError('Please select users to change status for.');
            return;
        }
        setBulkOperationModal({ isOpen: true, type: 'status' });
        setBulkStatusChange('Active'); // Default status
        setBulkOperationError(null);
    };

    const closeBulkModal = () => {
        setBulkOperationModal({ isOpen: false, type: null });
        setBulkOperationError(null);
    };

    const handleBulkRoleAssignment = async () => {
        if (bulkRoleAssignment.length === 0) {
            setBulkOperationError('Please select at least one role.');
            return;
        }

        setIsBulkProcessing(true);
        setBulkOperationError(null);

        try {
            const updatePromises = Array.from(selectedUsers).map(userId => 
                updatePortalUser(userId, { 
                    roles: bulkRoleAssignment,
                    status: userList.find(u => u.id === userId)?.status || 'Active'
                })
            );

            await Promise.all(updatePromises);
            
            // Refresh user list
            await fetchUsers();
            
            // Clear selection and close modal
            setSelectedUsers(new Set());
            closeBulkModal();
            
            setError(null); // Clear any previous errors
        } catch (err) {
            console.error('Error in bulk role assignment:', err);
            setBulkOperationError(err instanceof Error ? err.message : 'Failed to update user roles');
        } finally {
            setIsBulkProcessing(false);
        }
    };

    const handleBulkStatusChange = async () => {
        setIsBulkProcessing(true);
        setBulkOperationError(null);

        try {
            const updatePromises = Array.from(selectedUsers).map(userId => {
                const user = userList.find(u => u.id === userId);
                return updatePortalUser(userId, { 
                    roles: user?.roles || ['Employee'],
                    status: bulkStatusChange
                });
            });

            await Promise.all(updatePromises);
            
            // Refresh user list
            await fetchUsers();
            
            // Clear selection and close modal
            setSelectedUsers(new Set());
            closeBulkModal();
            
            setError(null); // Clear any previous errors
        } catch (err) {
            console.error('Error in bulk status change:', err);
            setBulkOperationError(err instanceof Error ? err.message : 'Failed to update user status');
        } finally {
            setIsBulkProcessing(false);
        }
    };

    // --- Render Logic ---
    return (
        <div className="p-6 bg-white min-h-screen">
            <h1 className="text-2xl font-semibold text-gray-800 mb-6">User Management</h1>

            {/* Success Message Display */}
            {successMessage && (
                <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg flex items-center justify-between transition-opacity duration-500 opacity-100">
                    <div className="flex items-center">
                        <svg className="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span>{successMessage}</span>
                    </div>
                    <button
                        onClick={() => setSuccessMessage(null)}
                        className="text-green-700 hover:text-green-900"
                    >
                        <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                </div>
            )}

            {/* Updated Section Title with Add User Button */}
            <div className="p-4 bg-white shadow rounded-lg">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-medium text-gray-700">Manage User Roles & Status</h2>
                    <div className="flex gap-2">
                        {/* Bulk Operations Buttons */}
                        {selectedUsers.size > 0 && (
                            <>
                                <button 
                                    onClick={openBulkRoleModal}
                                    className="inline-flex items-center px-3 py-2 border border-orange-600 text-sm font-medium rounded-md text-orange-600 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                                >
                                    <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Assign Roles ({selectedUsers.size})
                                </button>
                                <button 
                                    onClick={openBulkStatusModal}
                                    className="inline-flex items-center px-3 py-2 border border-purple-600 text-sm font-medium rounded-md text-purple-600 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                                >
                                    <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                                    </svg>
                                    Change Status ({selectedUsers.size})
                                </button>
                            </>
                        )}
                    <Link 
                        to="/portal-admin/add-user"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Add User
                    </Link>
                    </div>
                </div>
                
                {/* Filter Controls */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        {/* Search Filter */}
                        <div>
                            <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 mb-1">
                                Search Users
                            </label>
                            <input
                                type="search"
                                id="userSearch" 
                                value={searchTerm}
                                onChange={handleSearchChange}
                                placeholder="Name or Email..."
                                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition-colors duration-200"
                            />
                        </div>
                        
                        {/* Company Filter */}
                        <div>
                            <label htmlFor="companyFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                Company
                            </label>
                            <select
                                id="companyFilter"
                                value={companyFilter}
                                onChange={handleCompanyFilterChange}
                                className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="All">All Companies</option>
                                {availableCompanies.map(company => (
                                    <option key={company} value={company}>{company}</option>
                                ))}
                            </select>
                        </div>
                        
                        {/* Role Filter */}
                        <div>
                            <label htmlFor="roleFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                Role
                            </label>
                            <select
                                id="roleFilter"
                                value={roleFilter}
                                onChange={handleRoleFilterChange}
                                className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="All">All Roles</option>
                                {availableRoles.map(role => (
                                    <option key={role} value={role}>{role}</option>
                                ))}
                            </select>
                        </div>
                        
                        {/* Status Filter */}
                        <div>
                            <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                id="statusFilter"
                                value={statusFilter}
                                onChange={handleStatusFilterChange}
                                className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="All">All Statuses</option>
                                {PORTAL_STATUSES.map(status => (
                                    <option key={status} value={status}>{status}</option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>

                {/* Display current filter/search state */}
                {(searchTerm || companyFilter !== 'All' || roleFilter !== 'All' || statusFilter !== 'All') && (
                    <div className="mb-4 text-sm text-gray-600">
                        Showing {totalUsers} user{totalUsers !== 1 ? 's' : ''} 
                        {searchTerm && ` matching "${searchTerm}"`}
                        {companyFilter !== 'All' && ` from ${companyFilter}`}
                        {roleFilter !== 'All' && ` with role "${roleFilter}"`}
                        {statusFilter !== 'All' && ` with status "${statusFilter}"`}
                    </div>
                )}
                
                {/* Error Display */}
                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                        </div>
                )}

                {/* Loading State */}
                {isLoading && (
                    <div className="text-center py-8">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                        <p className="mt-2 text-sm text-gray-500">Loading users...</p>
                    </div>
                )}
                
                {/* User Table */}
                {!isLoading && !error && (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input
                                            type="checkbox"
                                            checked={selectedUsers.size === userList.length && userList.length > 0}
                                            onChange={(e) => handleSelectAll(e.target.checked)}
                                            className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                        />
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Roles</th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Status</th>
                                    <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {userList.length > 0 ? userList.map((user) => {
                                    console.log('🔍 Rendering user row:', user.name, 'ID:', user.id, 'Email:', user.email);
                                    return (
                                    <tr key={user.id} className={selectedUsers.has(user.id) ? 'bg-indigo-50' : ''}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <input
                                                type="checkbox"
                                                checked={selectedUsers.has(user.id)}
                                                onChange={(e) => handleUserSelection(user.id, e.target.checked)}
                                                className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                            />
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.company}</td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.roles.join(', ')}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                                {user.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                            <Link
                                                to={`/portal-admin/manage-user/${user.id}`}
                                                className="text-indigo-600 hover:text-indigo-900"
                                                onClick={() => console.log('🎯 Manage button clicked for:', user.name, 'ID:', user.id)}
                                            >
                                                Manage
                                            </Link>
                                            <button
                                                onClick={() => handleDeleteClick(user)}
                                                className="text-red-600 hover:text-red-900"
                                            >
                                                Remove
                                            </button>
                                        </td>
                                    </tr>
                                    );
                                }) : (
                                    <tr>
                                        <td colSpan={7} className="px-6 py-8 text-center text-sm text-gray-500">
                                            No users found matching your criteria.
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                            </div>
                        )}
                        
                        {/* Pagination */}
                {!isLoading && !error && totalPages > 1 && (
                    <div className="mt-6 flex items-center justify-between">
                        <div className="flex-1 flex justify-between sm:hidden">
                                <button
                                    onClick={() => handlePageChange(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Previous
                                </button>
                                <button
                                    onClick={() => handlePageChange(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Next
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span> to{' '}
                                    <span className="font-medium">{Math.min(currentPage * pageSize, totalUsers)}</span> of{' '}
                                    <span className="font-medium">{totalUsers}</span> results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage === 1}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        <span className="sr-only">Previous</span>
                                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                        const pageNum = i + 1;
                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                    pageNum === currentPage
                                                        ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}
                                    <button
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={currentPage === totalPages}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        <span className="sr-only">Next</span>
                                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Delete Confirmation Modal */}
            {deleteConfirmation.isOpen && deleteConfirmation.user && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3 text-center">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">
                                Remove User from Portal
                            </h3>
                            <div className="mt-2 px-7 py-3">
                                <p className="text-sm text-gray-500">
                                    Are you sure you want to remove <strong>{deleteConfirmation.user.name}</strong> from the portal? 
                                    This action will deactivate their access but keep their account for potential reactivation.
                                </p>
                            </div>
                            <div className="items-center px-4 py-3">
                                <button
                                    onClick={handleDeleteCancel}
                                    disabled={isDeleting}
                                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleDeleteConfirm}
                                    disabled={isDeleting}
                                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                                >
                                    {isDeleting ? 'Removing...' : 'Remove'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Bulk Operations Modal */}
            {bulkOperationModal.isOpen && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                {bulkOperationModal.type === 'role' ? 'Bulk Role Assignment' : 'Bulk Status Change'}
                            </h3>
                            
                            {bulkOperationError && (
                                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
                                    {bulkOperationError}
                                </div>
                            )}

                            <p className="text-sm text-gray-600 mb-4">
                                This will {bulkOperationModal.type === 'role' ? 'assign roles to' : 'change status for'} {selectedUsers.size} selected user{selectedUsers.size !== 1 ? 's' : ''}.
                            </p>

                            {bulkOperationModal.type === 'role' ? (
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Select Roles to Assign
                                    </label>
                                    <div className="space-y-2 max-h-40 overflow-y-auto">
                                        {availableRoles.map(role => (
                                            <label key={role} className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    checked={bulkRoleAssignment.includes(role)}
                                                    onChange={(e) => {
                                                        if (e.target.checked) {
                                                            setBulkRoleAssignment([...bulkRoleAssignment, role]);
                                                        } else {
                                                            setBulkRoleAssignment(bulkRoleAssignment.filter(r => r !== role));
                                                        }
                                                    }}
                                                    className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 mr-2"
                                                />
                                                <span className="text-sm text-gray-700">{role}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Select Status
                                    </label>
                                    <select
                                        value={bulkStatusChange}
                                        onChange={(e) => setBulkStatusChange(e.target.value as 'Active' | 'Inactive')}
                                        className="block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        {PORTAL_STATUSES.map(status => (
                                            <option key={status} value={status}>{status}</option>
                                        ))}
                                    </select>
                                </div>
                            )}

                            <div className="flex justify-end space-x-2">
                                <button
                                    onClick={closeBulkModal}
                                    disabled={isBulkProcessing}
                                    className="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={bulkOperationModal.type === 'role' ? handleBulkRoleAssignment : handleBulkStatusChange}
                                    disabled={isBulkProcessing}
                                    className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-300 disabled:opacity-50"
                                >
                                    {isBulkProcessing ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Processing...
                                        </>
                                    ) : (
                                        bulkOperationModal.type === 'role' ? 'Assign Roles' : 'Update Status'
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default UserManagementPage; 