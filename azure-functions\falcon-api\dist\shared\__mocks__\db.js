"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetMocks = exports.executeQuery = exports.getPool = void 0;
// Create a mock recordset that satisfies the IRecordSet interface
const createMockRecordset = () => {
    return Object.assign([], { toTable: jest.fn(), columns: {} });
};
// Mock pool for testing
const mockPool = {
    connected: true,
    request: jest.fn().mockReturnThis(),
    input: jest.fn().mockReturnThis(),
    query: jest.fn(),
    connect: jest.fn().mockResolvedValue(true),
    close: jest.fn().mockResolvedValue(true),
    on: jest.fn(),
};
// Mock getPool function
exports.getPool = jest.fn().mockResolvedValue(mockPool);
// Mock executeQuery function
exports.executeQuery = jest.fn().mockImplementation(async (query, params) => {
    // Default empty result that satisfies the IResult interface
    const mockRecordset = createMockRecordset();
    return {
        recordset: mockRecordset,
        recordsets: [mockRecordset],
        output: {},
        rowsAffected: [0],
    };
});
// Reset all mocks between tests
const resetMocks = () => {
    exports.getPool.mockClear();
    exports.executeQuery.mockClear();
    mockPool.request.mockClear();
    mockPool.input.mockClear();
    mockPool.query.mockClear();
    mockPool.connect.mockClear();
    mockPool.close.mockClear();
    mockPool.on.mockClear();
};
exports.resetMocks = resetMocks;
//# sourceMappingURL=db.js.map