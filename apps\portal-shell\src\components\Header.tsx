import React, { useState, useEffect, useRef } from 'react';
import { Search, Bell } from 'feather-icons-react';
import UserProfileDropdown from './UserProfileDropdown';

// TODO: Add Header content from falcon-standalone.html
// TODO: Integrate search, notifications, user dropdown, MSAL login/logout

const Header: React.FC = () => {
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-10">
      <div className="flex items-center justify-between p-4">
        {/* Search Bar */}
        <div className="flex-1 max-w-xl">
          <div className="relative">
            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
              <Search size={16} className="text-gray-400" />
            </span>
            <input 
              type="text" 
              placeholder="Search Falcon Portal..." 
              className="w-full py-2 pl-10 pr-4 border rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
          
        {/* Right Side Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative" ref={notificationsRef}>
            <button 
              onClick={() => setNotificationsOpen(!notificationsOpen)}
              className="text-gray-600 hover:text-gray-800 relative focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg p-1"
              aria-label="Notifications"
            >
              <Bell size={20} />
              {/* TODO: Add dynamic notification indicator */}
              <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span> 
            </button>
              
            {notificationsOpen && (
              <div className="dropdown absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-20 border">
                <div className="px-4 py-2 border-b">
                  <div className="text-sm font-medium">Notifications</div>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {/* TODO: Populate with actual notifications */}
                  <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                    <div>Your travel request has been approved</div>
                    <div className="text-xs text-gray-500 mt-1">2 hours ago</div>
                  </div>
                  <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                    <div>New policy document requires acknowledgment</div>
                    <div className="text-xs text-gray-500 mt-1">Yesterday</div>
                  </div>
                  {/* ... more notifications ... */}
                </div>
                <div className="px-4 py-2 border-t text-center">
                  <button className="text-blue-600 text-sm hover:text-blue-800">
                    View All Notifications
                  </button>
                </div>
              </div>
            )}
          </div>
            
          {/* User Profile Dropdown */}
          <UserProfileDropdown />
        </div>
      </div>
    </header>
  );
};

export default Header; 