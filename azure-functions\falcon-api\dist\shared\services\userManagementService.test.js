"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const userManagementService_1 = require("./userManagementService");
const db_1 = require("../db");
const logger_1 = require("../utils/logger");
// Mock dependencies
jest.mock('../db');
jest.mock('../utils/logger', () => ({
    logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn(),
    }
}));
// Define mock data
const mockEntraUser = {
    id: 'entra-guid-123',
    userPrincipalName: '<EMAIL>',
    mail: '<EMAIL>',
    givenName: 'Test',
    surname: 'User',
    companyName: 'Test Company',
    department: 'Test Department'
};
const mockExistingDbUser = {
    UserID: 1,
    EntraID: 'entra-guid-123',
    Username: '<EMAIL>',
    Email: '<EMAIL>',
    FirstName: 'Test',
    LastName: 'User',
    CompanyID: 10,
    LocationID: null,
    DepartmentID: 20,
    EmployeeID: null,
    ContactNumber: null,
    ProfileImageURL: null,
    ManagerID: null,
    DateOfJoining: null,
    LastLoginDate: new Date(),
    IsActive: true,
    CreatedBy: 1,
    CreatedDate: new Date(),
    ModifiedBy: null,
    ModifiedDate: null,
    TenantID: 'test-tenant-id',
    CompanyName: 'Test Company',
    DepartmentName: 'Test Department'
};
const mockCreatedDbUser = {
    ...mockExistingDbUser,
    UserID: 2,
    CreatedDate: new Date(),
    LastLoginDate: null
};
describe('User Management Service', () => {
    // Hold original process.env
    const originalEnv = process.env;
    beforeEach(() => {
        jest.clearAllMocks();
        // Restore env before each test
        process.env = { ...originalEnv };
    });
    afterAll(() => {
        // Restore original env after all tests
        process.env = originalEnv;
    });
    // --- findOrCreateUser Tests ---
    describe('findOrCreateUser', () => {
        it('should return existing user if found', async () => {
            db_1.executeQuery.mockResolvedValueOnce({ recordset: [mockExistingDbUser] });
            const user = await (0, userManagementService_1.findOrCreateUser)(mockEntraUser);
            expect(user).toEqual(mockExistingDbUser);
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.any(String), { EntraID: mockEntraUser.id });
            expect(db_1.executeQuery).toHaveBeenCalledTimes(1);
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Found existing user'));
        });
        it('should create a new user if not found (with company and department)', async () => {
            process.env.DEFAULT_USER_ROLE = 'Employee'; // Set env var for test
            const mockCompanyResult = { recordset: [{ CompanyID: 10 }] };
            const mockDeptResult = { recordset: [{ DepartmentID: 20 }] };
            const mockRoleResult = { recordset: [{ RoleID: 5 }] }; // Default role 'Employee' ID
            const mockInsertUserResult = { recordset: [mockCreatedDbUser] };
            const mockInsertUserRoleResult = { rowsAffected: [1] }; // Simulate role assignment success
            db_1.executeQuery
                .mockResolvedValueOnce({ recordset: [] }) // 1. User check - not found
                .mockResolvedValueOnce(mockCompanyResult) // 2. Company lookup - found
                .mockResolvedValueOnce(mockDeptResult) // 3. Department lookup - found
                .mockResolvedValueOnce(mockInsertUserResult) // 4. User insert - success
                .mockResolvedValueOnce(mockRoleResult) // 5. Default role lookup - found
                .mockResolvedValueOnce(mockInsertUserRoleResult); // 6. UserRole insert - success
            const user = await (0, userManagementService_1.findOrCreateUser)(mockEntraUser, 99); // createdByUserId = 99
            expect(user).toEqual(mockCreatedDbUser);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(6);
            // Check user insert call
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.any(String), {
                EntraID: mockEntraUser.id,
                Username: mockEntraUser.userPrincipalName,
                Email: mockEntraUser.mail,
                FirstName: mockEntraUser.givenName,
                LastName: mockEntraUser.surname,
                CompanyID: 10,
                DepartmentID: 20,
                IsActive: true,
                CreatedBy: 99
            });
            // Check role assignment call
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.any(String), {
                UserID: mockCreatedDbUser.UserID,
                RoleID: 5,
                IsActive: true,
                CreatedBy: 99
            });
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Creating new user'));
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Successfully created user'));
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Assigned default role'));
        });
        it('should create user with default company if companyName is missing', async () => {
            // Mock the implementation of executeQuery for this specific test
            db_1.executeQuery.mockImplementation((query, params) => {
                // Check for user query
                if (query.includes('SELECT * FROM Users')) {
                    return { recordset: [] }; // User not found
                }
                // Check for user insert query
                if (query.includes('INSERT INTO Users')) {
                    return {
                        recordset: [{
                                UserID: 3,
                                EntraID: 'entra-guid-123',
                                Username: '<EMAIL>',
                                Email: '<EMAIL>',
                                FirstName: 'Test',
                                LastName: 'User',
                                CompanyID: 1,
                                DepartmentID: null,
                                IsActive: true,
                                CreatedBy: 99,
                                CreatedDate: new Date(),
                                TenantID: 'test-tenant-id'
                            }]
                    };
                }
                // Check for role query
                if (query.includes('SELECT RoleID FROM Roles')) {
                    return { recordset: [{ RoleID: 5 }] };
                }
                // Check for role assignment query
                if (query.includes('INSERT INTO UserRoles')) {
                    return { rowsAffected: [1] };
                }
                return { recordset: [] };
            });
            process.env.DEFAULT_USER_ROLE = 'Employee';
            const entraUserNoCompany = { ...mockEntraUser, companyName: undefined };
            const user = await (0, userManagementService_1.findOrCreateUser)(entraUserNoCompany, 99);
            // Verify the user object has the expected properties
            expect(user).toBeTruthy();
            expect(user?.UserID).toBe(3);
            expect(user?.CompanyID).toBe(1);
            expect(user?.DepartmentID).toBeNull();
            // Verify the warning about missing company name
            expect(logger_1.logger.warn).toHaveBeenCalledWith(expect.stringContaining('has no companyName. Assigning default CompanyID: 1'));
        });
        it('should throw error if companyName provided but not found in DB', async () => {
            const mockCompanyResult = { recordset: [] };
            db_1.executeQuery
                .mockResolvedValueOnce({ recordset: [] })
                .mockResolvedValueOnce(mockCompanyResult);
            // FIX: Expect the error re-thrown from the catch block in the service
            await expect((0, userManagementService_1.findOrCreateUser)(mockEntraUser, 99)).rejects.toThrow(`Database error during company lookup for user ${mockEntraUser.id}.`);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(2);
            // The specific error IS logged before being re-thrown
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining('provided by Entra ID not found or inactive'));
        });
        it('should create user but not assign role if default role not found', async () => {
            process.env.DEFAULT_USER_ROLE = 'NonExistentRole';
            const mockCompanyResult = { recordset: [{ CompanyID: 10 }] };
            const mockDeptResult = { recordset: [{ DepartmentID: 20 }] };
            const mockRoleResult = { recordset: [] }; // Default role NOT found
            const mockInsertUserResult = { recordset: [mockCreatedDbUser] };
            db_1.executeQuery
                .mockResolvedValueOnce({ recordset: [] }) // User check
                .mockResolvedValueOnce(mockCompanyResult) // Company lookup
                .mockResolvedValueOnce(mockDeptResult) // Department lookup
                .mockResolvedValueOnce(mockInsertUserResult) // User insert
                .mockResolvedValueOnce(mockRoleResult); // Role lookup (fails)
            // No role insert call expected
            const user = await (0, userManagementService_1.findOrCreateUser)(mockEntraUser, 99);
            expect(user).toEqual(mockCreatedDbUser);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(5);
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining("Default role 'NonExistentRole' not found"));
        });
        it('should handle DB error during user check', async () => {
            const dbError = new Error('Check failed');
            db_1.executeQuery.mockRejectedValueOnce(dbError);
            await expect((0, userManagementService_1.findOrCreateUser)(mockEntraUser)).rejects.toThrow('Check failed');
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining('Error in findOrCreateUser process'), dbError);
        });
    });
    // --- assignRoleToUser Tests ---
    describe('assignRoleToUser', () => {
        const userId = 1;
        const roleId = 5;
        const assignerId = 99;
        it('should insert new assignment if none exists', async () => {
            db_1.executeQuery
                .mockResolvedValueOnce({ recordset: [] })
                .mockResolvedValueOnce({ rowsAffected: [1] });
            const result = await (0, userManagementService_1.assignRoleToUser)(userId, roleId, assignerId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(2);
            expect(db_1.executeQuery).toHaveBeenNthCalledWith(1, expect.any(String), { UserID: userId, RoleID: roleId });
            expect(db_1.executeQuery).toHaveBeenNthCalledWith(2, expect.any(String), { UserID: userId, RoleID: roleId, IsActive: true, CreatedBy: assignerId });
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Creating new role assignment'));
        });
        it('should update (reactivate) assignment if it exists and is inactive', async () => {
            const existingInactive = { UserRoleID: 50, IsActive: false };
            db_1.executeQuery
                .mockResolvedValueOnce({ recordset: [existingInactive] })
                .mockResolvedValueOnce({ rowsAffected: [1] });
            const result = await (0, userManagementService_1.assignRoleToUser)(userId, roleId, assignerId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(2);
            expect(db_1.executeQuery).toHaveBeenNthCalledWith(2, expect.any(String), { UserRoleID: existingInactive.UserRoleID, ModifiedBy: assignerId });
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Reactivating existing role assignment'));
        });
        it('should do nothing and return true if assignment exists and is active', async () => {
            const existingActive = { UserRoleID: 50, IsActive: true };
            db_1.executeQuery.mockResolvedValueOnce({ recordset: [existingActive] }); // Check query finds active
            const result = await (0, userManagementService_1.assignRoleToUser)(userId, roleId, assignerId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledTimes(1); // Only the check query
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('already actively assigned'));
        });
        it('should return false if DB error occurs', async () => {
            const dbError = new Error('Assign failed');
            db_1.executeQuery.mockRejectedValue(dbError);
            const result = await (0, userManagementService_1.assignRoleToUser)(userId, roleId, assignerId);
            expect(result).toBe(false);
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining('Error assigning role'), dbError);
        });
    });
    // --- removeRoleFromUser Tests ---
    describe('removeRoleFromUser', () => {
        const userId = 1;
        const roleId = 5;
        const removerId = 98;
        it('should update assignment to inactive if it exists and is active', async () => {
            db_1.executeQuery.mockResolvedValueOnce({ rowsAffected: [1] });
            const result = await (0, userManagementService_1.removeRoleFromUser)(userId, roleId, removerId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.any(String), { UserID: userId, RoleID: roleId, ModifiedBy: removerId });
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Successfully deactivated role assignment'));
        });
        it('should return true (noop) if assignment is already inactive or doesnt exist', async () => {
            db_1.executeQuery.mockResolvedValueOnce({ rowsAffected: [0] });
            const result = await (0, userManagementService_1.removeRoleFromUser)(userId, roleId, removerId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.any(String), { UserID: userId, RoleID: roleId, ModifiedBy: removerId });
            expect(logger_1.logger.warn).toHaveBeenCalledWith(expect.stringContaining('No active role assignment found'));
        });
        it('should return false if DB error occurs', async () => {
            const dbError = new Error('Remove failed');
            db_1.executeQuery.mockRejectedValue(dbError);
            const result = await (0, userManagementService_1.removeRoleFromUser)(userId, roleId, removerId);
            expect(result).toBe(false);
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining('Error removing role'), dbError);
        });
    });
    // --- updateUserLastLogin Tests ---
    describe('updateUserLastLogin', () => {
        const userId = 123;
        it('should execute update query successfully', async () => {
            db_1.executeQuery.mockResolvedValueOnce({ rowsAffected: [1] });
            const result = await (0, userManagementService_1.updateUserLastLogin)(userId);
            expect(result).toBe(true);
            expect(db_1.executeQuery).toHaveBeenCalledWith(expect.stringContaining('UPDATE Users SET LastLoginDate'), { UserID: userId });
            expect(logger_1.logger.info).toHaveBeenCalledWith(expect.stringContaining('Updating LastLoginDate'));
        });
        it('should return false if DB error occurs', async () => {
            const dbError = new Error('Update failed');
            db_1.executeQuery.mockRejectedValue(dbError);
            const result = await (0, userManagementService_1.updateUserLastLogin)(userId);
            expect(result).toBe(false);
            expect(logger_1.logger.error).toHaveBeenCalledWith(expect.stringContaining('Error updating LastLoginDate'), dbError);
        });
    });
});
//# sourceMappingURL=userManagementService.test.js.map