import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';

export async function FixCurrentChangeRequests(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    try {
        context.log('FixCurrentChangeRequests function triggered');

        // Get database connection
        const pool: ConnectionPool = await getPool();

        // First, fix missing CompanyID for recent change requests
        const fixCompanyIdQuery = `
            UPDATE ChangeRequests 
            SET CompanyID = 4
            WHERE CompanyID IS NULL 
            AND RequestedBy = 1  -- <PERSON><PERSON><PERSON>'s user ID
            AND CreatedDate >= '2024-01-01';
        `;

        const companyResult = await pool.request().query(fixCompanyIdQuery);
        context.log(`Fixed CompanyID for ${companyResult.rowsAffected[0]} change requests`);

        // Second, add deployment dates based on RequestedCompletionDate for records that don't have them
        const fixDeploymentDateQuery = `
            UPDATE ChangeRequests 
            SET DeploymentDate = RequestedCompletionDate
            WHERE DeploymentDate IS NULL 
            AND RequestedCompletionDate IS NOT NULL
            AND CompanyID = 4;
        `;

        const deploymentResult = await pool.request().query(fixDeploymentDateQuery);
        context.log(`Fixed DeploymentDate for ${deploymentResult.rowsAffected[0]} change requests`);

        // Get updated results to show what was fixed
        const selectQuery = `
            SELECT 
                RequestID, 
                Title, 
                Status, 
                Priority, 
                CompanyID,
                DeploymentDate,
                RequestedCompletionDate,
                CreatedDate
            FROM ChangeRequests 
            WHERE CompanyID = 4
            ORDER BY CreatedDate DESC;
        `;

        const selectResult = await pool.request().query(selectQuery);

        return {
            status: 200,
            jsonBody: { 
                success: true,
                message: `Fixed ${companyResult.rowsAffected[0]} CompanyID issues and ${deploymentResult.rowsAffected[0]} DeploymentDate issues`,
                companyIdFixed: companyResult.rowsAffected[0],
                deploymentDateFixed: deploymentResult.rowsAffected[0],
                changeRequests: selectResult.recordset
            }
        };

    } catch (error) {
        context.log('Error in FixCurrentChangeRequests:', error);
        return {
            status: 500,
            jsonBody: { 
                error: 'Failed to fix change requests',
                details: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}

// Register the function
app.http('FixCurrentChangeRequests', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'fix-change-requests',
    handler: FixCurrentChangeRequests
}); 