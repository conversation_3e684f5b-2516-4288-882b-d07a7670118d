"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRole = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = __importStar(require("mssql"));
async function updateRole(request, context) {
    const roleId = parseInt(request.params.roleId, 10);
    context.log(`Http function processed request for UpdateRole: ${roleId}`);
    if (isNaN(roleId)) {
        return {
            status: 400,
            jsonBody: { error: "Invalid RoleID parameter." }
        };
    }
    let body;
    try {
        body = await request.json();
    }
    catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }
    if (!body || (body.RoleName === undefined && body.RoleDescription === undefined && body.IsActive === undefined)) {
        return {
            status: 400,
            jsonBody: { message: "No update data provided (RoleName, RoleDescription, or IsActive required)." }
        };
    }
    if (body.RoleName !== undefined && !body.RoleName.trim()) {
        return {
            status: 400,
            jsonBody: { message: "RoleName cannot be empty." }
        };
    }
    try {
        const getCurrentRoleQuery = 'SELECT RoleID, RoleName, IsSystemRole FROM Roles WHERE RoleID = @RoleID';
        const getCurrentRoleParams = [
            { name: 'RoleID', type: sql.Int, value: roleId }
        ];
        const currentRoleResult = await (0, db_1.executeQuery)(getCurrentRoleQuery, getCurrentRoleParams);
        if (currentRoleResult.recordset.length === 0) {
            logger_1.logger.warn(`UpdateRole: Role with ID ${roleId} not found.`);
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found.` }
            };
        }
        const currentRole = currentRoleResult.recordset[0];
        if (currentRole.RoleName === 'User' && body.RoleName && body.RoleName.trim() !== 'User') {
            return {
                status: 403,
                jsonBody: { message: "Cannot rename the default 'User' role." }
            };
        }
        if (currentRole.RoleName === 'Administrator' && body.RoleName && body.RoleName.trim() !== 'Administrator') {
            return {
                status: 403,
                jsonBody: { message: "Cannot rename the 'Administrator' role." }
            };
        }
        if (body.RoleName && body.RoleName.trim().toLowerCase() !== currentRole.RoleName.toLowerCase()) {
            const checkNameQuery = 'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName) AND RoleID != @RoleID';
            const checkNameParams = [
                { name: 'RoleName', type: sql.NVarChar, value: body.RoleName.trim() },
                { name: 'RoleID', type: sql.Int, value: roleId }
            ];
            const checkResult = await (0, db_1.executeQuery)(checkNameQuery, checkNameParams);
            if (checkResult.recordset[0].Count > 0) {
                logger_1.logger.warn(`UpdateRole: Role name '${body.RoleName.trim()}' already exists.`);
                return {
                    status: 409,
                    jsonBody: { message: `Role with name '${body.RoleName.trim()}' already exists.` }
                };
            }
        }
        const modifiedByUserId = 1;
        let query = 'UPDATE Roles SET ';
        const updateFields = [];
        const updateParams = [];
        if (body.RoleName && body.RoleName.trim() !== currentRole.RoleName.trim()) {
            updateFields.push('RoleName = @RoleName');
            updateParams.push({ name: 'RoleName', type: sql.NVarChar, value: body.RoleName.trim() });
        }
        if (body.RoleDescription !== undefined) {
            updateFields.push('RoleDescription = @RoleDescription');
            updateParams.push({ name: 'RoleDescription', type: sql.NVarChar, value: body.RoleDescription.trim() });
        }
        if (typeof body.IsActive === 'boolean') {
            updateFields.push('IsActive = @IsActive');
            updateParams.push({ name: 'IsActive', type: sql.Bit, value: body.IsActive });
        }
        if (updateFields.length === 0) {
            return {
                status: 400,
                jsonBody: { error: "No valid fields provided for update." }
            };
        }
        updateFields.push('ModifiedBy = @ModifiedBy');
        updateFields.push('ModifiedDate = GETUTCDATE()');
        updateParams.push({ name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId });
        query += updateFields.join(', ');
        query += ' OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription, INSERTED.IsActive ';
        query += ' WHERE RoleID = @RoleID';
        updateParams.push({ name: 'RoleID', type: sql.Int, value: roleId });
        const result = await (0, db_1.executeQuery)(query, updateParams);
        if (result.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found (update check).` }
            };
        }
        const dbUpdatedRole = result.recordset[0];
        const updatedRole = {
            RoleID: dbUpdatedRole.RoleID,
            RoleName: dbUpdatedRole.RoleName,
            Description: dbUpdatedRole.RoleDescription
        };
        return {
            status: 200,
            jsonBody: updatedRole
        };
    }
    catch (error) {
        context.error(`Error updating role ${roleId}: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: `Error updating role ${roleId}.`,
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}
exports.updateRole = updateRole;
functions_1.app.http('UpdateRole', {
    methods: ['PUT'],
    authLevel: 'function',
    route: 'roles/{roleId:int}',
    handler: updateRole
});
//# sourceMappingURL=UpdateRole.js.map