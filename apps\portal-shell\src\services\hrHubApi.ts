// Employee interface for HR Hub
interface Employee {
    id: number;
    entraId: string | null;
    name: string;
    email: string;
    firstName: string;
    lastName: string;
    employeeId: string | null;
    jobTitle?: string;
    department: string | null;
    location: string | null;
    company: string;
    companyId: number;
    contactNumber: string | null;
    profileImageURL: string | null;
    managerId: number | null;
    managerName: string | null;
    dateOfJoining: string | null;
    lastLoginDate: string | null;
    isActive: boolean;
}

// Department interface
interface Department {
    id: number;
    name: string;
    companyId: number;
    companyName: string;
    isActive: boolean;
    employeeCount?: number;
}

// Location interface
interface Location {
    id: number;
    name: string;
    companyId: number;
    companyName: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
    isActive: boolean;
    employeeCount?: number;
}

// Employee search parameters
interface EmployeeSearchParams {
    search?: string;
    department?: string;
    location?: string;
    company?: string;
    status?: 'all' | 'active' | 'inactive';
    limit?: number;
    offset?: number;
}

// API response interface
interface EmployeesResponse {
    employees: Employee[];
    totalCount: number;
    hasMore: boolean;
    searchParams: EmployeeSearchParams;
}

interface DepartmentsResponse {
    departments: Department[];
}

interface LocationsResponse {
    locations: Location[];
}

class HRHubAPI {
    private static baseURL = '/api';

    private static async makeRequest<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const url = `${this.baseURL}${endpoint}`;
        
        const defaultHeaders = {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
        };

        const response = await fetch(url, {
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers,
            },
        });

        if (!response.ok) {
            throw new Error(`HR Hub API Error: ${response.status} ${response.statusText}`);
        }

        return response.json();
    }

    // Get employees with search and filtering
    static async getEmployees(params: EmployeeSearchParams = {}): Promise<EmployeesResponse> {
        const queryParams = new URLSearchParams();
        
        if (params.search) queryParams.append('search', params.search);
        if (params.department) queryParams.append('department', params.department);
        if (params.location) queryParams.append('location', params.location);
        if (params.company) queryParams.append('company', params.company);
        if (params.status) queryParams.append('status', params.status);
        if (params.limit) queryParams.append('limit', params.limit.toString());
        if (params.offset) queryParams.append('offset', params.offset.toString());

        const endpoint = `/employees?${queryParams.toString()}`;
        return this.makeRequest<EmployeesResponse>(endpoint);
    }

    // Get departments for filtering
    static async getDepartments(): Promise<DepartmentsResponse> {
        return this.makeRequest<DepartmentsResponse>('/departments');
    }

    // Get locations for filtering
    static async getLocations(): Promise<LocationsResponse> {
        return this.makeRequest<LocationsResponse>('/locations');
    }

    // Get employee by ID
    static async getEmployeeById(employeeId: number): Promise<Employee> {
        return this.makeRequest<Employee>(`/employees/${employeeId}`);
    }
}

export default HRHubAPI;
export type { Employee, Department, Location, EmployeeSearchParams, EmployeesResponse, DepartmentsResponse, LocationsResponse }; 