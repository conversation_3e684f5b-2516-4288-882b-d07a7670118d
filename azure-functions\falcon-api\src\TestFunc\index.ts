import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";

export async function testFunc(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function TestFunc processed request for url "${request.url}"`);
    return {
        status: 200,
        jsonBody: {
            message: "TestFunc executed successfully!"
        }
    };
}

app.http('TestFunc', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'test-func',
    handler: testFunc
}); 