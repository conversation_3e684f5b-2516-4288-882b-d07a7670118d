"use client"
// import { Link, useNavigate } from "react-router-dom" // Unused imports

import {
  Tool,
  Clipboard,
  CheckCircle,
  FileText,
  Settings,
  Database,
  // AlertCircle, // Unused import
  Truck,
  Archive,
  BarChart2,
  Activity,
  Shield,
  // ArrowRight, // Unused import
  // ExternalLink, // Unused import
  // Home, // Unused import
} from "feather-icons-react"
// import React, { useState } from "react" // Unused imports

function ProductionPage() {

const host_var = "http://**************/"//"http://**************/";http://localhost/
const handleToolClick = (tool) => {
  const reactBaseURL = window.location.origin;
  const token  = localStorage.getItem("authToken");
  if (!token) {
    alert("You must be logged in to access this tool.");
    return;
  }
  if (tool.type === "internal") {
      const targetURL = `${tool.link}?react_url=${encodeURIComponent(reactBaseURL)}&portal_name=${tool.name}&token=${encodeURIComponent(token)}`;
      //  alert(targetURL);

      window.location.href = targetURL;
  }
};//alert(reactBaseURL);
  const toolsList = [
    {
      label: "Serial Number Tracker",
      name: "training_server",
      link: host_var +"sn_tracker/index.php",
      desc: "Track and manage product serial numbers across production",
      icon: <Clipboard />,
      category: "Production",
      priority: "high",
      type: "internal",
    },
    {
      label: "Digital PCR",
      name:"fesil_pcr",
      link: host_var +"fesil_pcr/index.php",
      desc: "Digital Product Change Request management system",
      icon: <CheckCircle />,
      category: "Production",
      priority: "high",
      type: "internal",
    },
    {
      label: "Work Instructions",
      name:"pcr_work_instructions",
      link: host_var +"wi_folder/index.php",
      desc: "Create, document and manage work instructions",
      icon: <FileText />,
      category: "Production",
      priority: "high",
      type: "internal",
    },
    {
      label: "Competency Matrix",
      name:"competency_matrix_portal",
      link: host_var +"competency_matrix_portal/index.php",
      desc: "Track and manage employee skill competencies",
      icon: <BarChart2 />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Tool Issue Tracker",
      name:"tool_issue_tracker",
      link: host_var +"tool_issue_tracker/index.php",
      desc: "Monitor and track tools issued to employees",
      icon: <Tool />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Component Tool Database",
      name:"tool_database",
      link: host_var +"tool_database/index.php",
      desc: "Comprehensive database of component-specific tools",
      icon: <Database />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Non-Calibrated Tools",
      name:"non_calibrated_tools",
      link: host_var +"non_calibrated_tools/index.php",
      desc: "Track and manage tools awaiting calibration",
      icon: <Settings />,
      category: "Production",
      priority: "high",
      type: "internal",
    },
    {
      label: "ETL Test Request",
      name:"etl_test_request",
      link: host_var +"etl_test_request/index.php",
      desc: "Submit and manage ETL testing requests",
      icon: <Activity />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Packing Slip Barcode",
      name:"packing_slip_barcode",
      link: host_var +"packing_slip_barcode/index.php",
      desc: "Generate and track packing slip barcodes",
      icon: <Archive />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
    {
      label: "Safety PPE Requests",
      name:"safety_equipments_request",
      link: host_var +"safety_equipments_request/index.php",
      desc: "Request Personal Protective Equipment (PPE)",
      icon: <Shield />,
      category: "Production",
      priority: "high",
      type: "internal",
    },
    {
      label: "Tool Transfer",
      name:"tool_transfer_tracker",
      link: host_var +"tool_transfer_tracker/index.php",
      desc: "Track tool transfers between locations and departments",
      icon: <Truck />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },

    {
      label: "FG Reference Photo",
      name:"fg_reference_photos",
      link: host_var +"fg_reference_photos/index.php",
      desc: "View finished goods reference photos and specifications",
      icon: <FileText />,
      category: "Production",
      priority: "medium",
      type: "internal",
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">Production Tools</h1>
              <p className="text-gray-600 text-lg">Comprehensive production management and tracking systems</p>
            </div>

          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-6">

        {/* Tools Grid
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool) => renderTool(tool))}
        </div>*/}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {toolsList.map((tool, index) => (
            <div
              key={index}
              onClick={() => handleToolClick(tool)}
              className="cursor-pointer rounded-xl bg-white p-5 border border-transparent shadow-md
                        transition-all duration-300 ease-in-out
                        hover:border-blue-500 hover:shadow-[0_4px_20px_rgba(59,130,246,0.3)] hover:scale-[1.02]"
            >
              <div className="text-2xl mb-3 text-blue-600">{tool.icon}</div>
              <h3 className="text-lg font-bold mb-1 text-gray-800">{tool.label}</h3>
              <p className="text-gray-600 text-sm">{tool.desc}</p>
            </div>
          ))}
        </div>

        {/* System Status Footer */}

      </div>
    </div>
  )
}

export default ProductionPage