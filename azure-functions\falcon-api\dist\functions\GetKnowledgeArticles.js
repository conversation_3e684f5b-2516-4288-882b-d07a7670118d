"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getKnowledgeArticles = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function getKnowledgeArticles(request, context) {
    context.log(`GetKnowledgeArticles function invoked.`);
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!isDevelopment && !userId) {
            return { status: 401, jsonBody: { error: "User not found" } };
        }
        // Parse query parameters
        const url = new URL(request.url);
        const search = url.searchParams.get('search') || '';
        const categoryId = url.searchParams.get('categoryId');
        const companyId = url.searchParams.get('companyId');
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '20');
        const offset = (page - 1) * limit;
        // Build query with parameters
        let whereConditions = ['ka.IsActive = 1', 'ka.IsPublished = 1'];
        const queryParams = [
            { name: 'offset', type: sql.Int, value: offset },
            { name: 'limit', type: sql.Int, value: limit }
        ];
        // Add search filter
        if (search) {
            whereConditions.push(`(ka.Title LIKE @search OR ka.Summary LIKE @search OR ka.Content LIKE @search OR EXISTS (
                SELECT 1 FROM KnowledgeArticleTags kat 
                JOIN DocumentTags dt ON kat.TagID = dt.TagID 
                WHERE kat.ArticleID = ka.ArticleID AND dt.TagName LIKE @search
            ))`);
            queryParams.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }
        // Add category filter
        if (categoryId && categoryId !== 'all') {
            whereConditions.push('ka.CategoryID = @categoryId');
            queryParams.push({ name: 'categoryId', type: sql.Int, value: parseInt(categoryId) });
        }
        // Add company filter
        if (companyId && companyId !== 'all') {
            whereConditions.push('ka.CompanyID = @companyId');
            queryParams.push({ name: 'companyId', type: sql.Int, value: parseInt(companyId) });
        }
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        const query = `
            WITH ArticlesWithTags AS (
                SELECT 
                    ka.ArticleID,
                    ka.Title,
                    ka.Summary,
                    ka.Content,
                    ka.CategoryID,
                    dc.CategoryName,
                    ka.CompanyID,
                    c.CompanyName,
                    ka.IsPublished,
                    ka.ViewCount,
                    ka.CreatedBy,
                    u.FirstName + ' ' + u.LastName as CreatedByName,
                    u.Email as CreatedByEmail,
                    ka.CreatedDate,
                    ka.ModifiedDate,
                    STRING_AGG(dt.TagName, ',') WITHIN GROUP (ORDER BY dt.TagName) as Tags,
                    COALESCE(AVG(CAST(kaf.Rating as FLOAT)), 0) as AverageRating
                FROM KnowledgeArticles ka
                LEFT JOIN DocumentCategories dc ON ka.CategoryID = dc.CategoryID
                LEFT JOIN Companies c ON ka.CompanyID = c.CompanyID
                LEFT JOIN Users u ON ka.CreatedBy = u.UserID
                LEFT JOIN KnowledgeArticleTags kat ON ka.ArticleID = kat.ArticleID
                LEFT JOIN DocumentTags dt ON kat.TagID = dt.TagID
                LEFT JOIN KnowledgeArticleFeedback kaf ON ka.ArticleID = kaf.ArticleID
                ${whereClause}
                GROUP BY 
                    ka.ArticleID, ka.Title, ka.Summary, ka.Content, ka.CategoryID, 
                    dc.CategoryName, ka.CompanyID, c.CompanyName, ka.IsPublished, 
                    ka.ViewCount, ka.CreatedBy, u.FirstName, u.LastName, u.Email, 
                    ka.CreatedDate, ka.ModifiedDate
            )
            SELECT * FROM ArticlesWithTags
            ORDER BY CreatedDate DESC
            OFFSET @offset ROWS
            FETCH NEXT @limit ROWS ONLY;

            -- Get total count for pagination
            SELECT COUNT(*) as TotalCount
            FROM KnowledgeArticles ka
            LEFT JOIN DocumentCategories dc ON ka.CategoryID = dc.CategoryID
            LEFT JOIN Companies c ON ka.CompanyID = c.CompanyID
            ${whereClause};
        `;
        const result = await (0, db_1.executeQuery)(query, queryParams);
        // Access recordsets properly
        const articlesData = Array.isArray(result.recordsets) ? result.recordsets[0] : result.recordset;
        const countData = Array.isArray(result.recordsets) ? result.recordsets[1] : null;
        const articles = articlesData.map((row) => ({
            id: row.ArticleID.toString(),
            title: row.Title,
            summary: row.Summary || '',
            content: row.Content,
            category: {
                id: row.CategoryID?.toString() || '',
                name: row.CategoryName || 'Uncategorized'
            },
            company: row.CompanyName || 'Unknown',
            isPublished: row.IsPublished,
            viewCount: row.ViewCount,
            rating: Math.round((row.AverageRating || 0) * 10) / 10,
            createdBy: {
                name: row.CreatedByName || 'Unknown',
                email: row.CreatedByEmail || ''
            },
            createdDate: row.CreatedDate,
            lastModified: row.ModifiedDate,
            tags: row.Tags ? row.Tags.split(',') : []
        }));
        const totalCount = countData?.[0]?.TotalCount || 0;
        const totalPages = Math.ceil(totalCount / limit);
        logger_1.logger.info(`GetKnowledgeArticles: Retrieved ${articles.length} articles for user ${userId || 'dev-user'}`);
        return {
            status: 200,
            jsonBody: {
                articles,
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetKnowledgeArticles: Error retrieving articles:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                message: "Error retrieving knowledge articles.",
                error: errorMessage
            }
        };
    }
}
exports.getKnowledgeArticles = getKnowledgeArticles;
// Register the function
functions_1.app.http('GetKnowledgeArticles', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'knowledge/articles',
    handler: getKnowledgeArticles
});
//# sourceMappingURL=GetKnowledgeArticles.js.map