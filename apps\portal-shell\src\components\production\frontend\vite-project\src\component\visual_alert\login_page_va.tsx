"use client"

import { useState, useEffect } from "react"
import { User } from "lucide-react"
import { useNavigate } from "react-router-dom"
// import * as FeatherIcons from 'feather-icons-react'; // Unused import


interface LoginPageVAProps {
  onLoginSuccess?: () => void
}

function LoginPageVA({ onLoginSuccess }: LoginPageVAProps) {
  const navigate = useNavigate()
  const [employee_id, setEmployeeId] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Check if user is already logged in
    const userData = sessionStorage.getItem("userData")
    if (userData && userData !== "undefined") {
      try {
        const parsedData = JSON.parse(userData)
        if (parsedData && parsedData.employee_id) {
          // User is already logged in, redirect to dashboard
          if (onLoginSuccess) {
            onLoginSuccess()
          } else {
            navigate("/visual_alert/create", { replace: true })
          }
        }
      } catch (error) {
        console.error("Error parsing stored user data:", error)
        // Clear invalid data
        sessionStorage.removeItem("userData")
        localStorage.removeItem("isLoggedIn")
      }
    }
  }, [navigate, onLoginSuccess])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    if (!employee_id || !password) {
      setError("Employee ID and password are required")
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch("http://localhost:3000/api/va_login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ employee_id, password }),
        credentials: "include",
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Login failed")
      }

      // The backend returns user data directly (not nested under 'user')
      const userData = {
        employee_id: data.employee_id,
        employee_name: data.employee_name,
        employee_mail: data.employee_mail,
        company_name: data.company_name,
        employee_dept: data.employee_dept || "",
        employee_reporting: data.employee_reporting || "",

      }

      // Store user data in sessionStorage
      sessionStorage.setItem("userData", JSON.stringify(userData))

      // Also store in localStorage for persistence if needed
      localStorage.setItem("isLoggedIn", "true")
      localStorage.setItem("emp_id", userData.employee_id || "")
      localStorage.setItem("emp_name", userData.employee_name || "")
      localStorage.setItem("employee_mail", userData.employee_mail || "")

      // Either call the onLoginSuccess prop or navigate
      if (onLoginSuccess) {
        onLoginSuccess()
      } else {
        navigate("/visual_alert/create", { replace: true })
      }
    } catch (error: any) {
      console.error("Login error:", error)
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setError("Server is unavailable. Please check if the server is running.")
      } else {
        setError(error.message || "An error occurred during login")
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="p-10 w-full max-w-md bg-white rounded-xl shadow-lg border border-gray-200">
        <div className="flex flex-col items-center mb-6">
          <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center text-white mb-4">
            <User size={32} />
          </div>
          <h1 className="text-2xl font-bold text-gray-800">Welcome Back</h1>
          <p className="text-gray-600 mt-1">Please sign in to continue</p>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
            <p>{error}</p>
          </div>
        )}

        <form className="space-y-6" onSubmit={handleLogin}>
          <div className="space-y-2">
            <label htmlFor="employee_id" className="text-sm font-medium text-gray-700">
              Employee ID
            </label>
            <input
              id="employee_id"
              type="text"
              placeholder="Enter your Employee ID"
              value={employee_id}
              onChange={(e) => setEmployeeId(e.target.value)}
              disabled={isLoading}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition duration-200 font-medium"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Logging in...
              </span>
            ) : (
              "Sign In"
            )}
          </button>
        </form>
      </div>
    </div>
  )
}

export default LoginPageVA