## 6. Enhanced Rich Content Form with Image Support

### 6.1 Rich Content Editor Component

```jsx
// RichContentEditor.jsx
import React, { useState, useRef, useCallback } from 'react';
import {
  Box, Typography, Paper, IconButton, Tooltip, Dialog, DialogTitle,
  DialogContent, DialogActions, Button, TextField, Alert, LinearProgress,
  Menu, MenuItem, Divider
} from '@mui/material';
import {
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
  FormatListBulleted as BulletIcon,
  FormatListNumbered as NumberedIcon,
  Image as ImageIcon,
  Code as CodeIcon,
  Title as HeadingIcon,
  Undo as UndoIcon,
  Redo as RedoIcon
} from '@mui/icons-material';
import { compressImage } from '../utils/imageCompression';

const RichContentEditor = ({ value, onChange, placeholder, maxImageSize = 2 * 1024 * 1024 }) => {
  const [content, setContent] = useState(value || []);
  const [selectedBlock, setSelectedBlock] = useState(null);
  const [imageDialog, setImageDialog] = useState({ open: false, file: null, caption: '', alt: '' });
  const [uploading, setUploading] = useState(false);
  const [headingMenu, setHeadingMenu] = useState({ anchor: null, open: false });
  const fileInputRef = useRef(null);
  const editorRef = useRef(null);

  // Update parent component when content changes
  const updateContent = useCallback((newContent) => {
    setContent(newContent);
    onChange(newContent);
  }, [onChange]);

  // Add new text block
  const addTextBlock = (type = 'paragraph') => {
    const newBlock = {
      id: Date.now() + Math.random(),
      type: type,
      content: '',
      timestamp: new Date().toISOString()
    };
    const newContent = [...content, newBlock];
    updateContent(newContent);
    setSelectedBlock(newBlock.id);
  };

  // Add image block
  const addImageBlock = (imageData, caption = '', alt = '') => {
    const newBlock = {
      id: Date.now() + Math.random(),
      type: 'image',
      content: imageData.compressedDataUrl,
      originalSize: imageData.originalSize,
      compressedSize: imageData.compressedSize,
      compressionRatio: imageData.compressionRatio,
      caption: caption,
      alt: alt,
      timestamp: new Date().toISOString()
    };
    const newContent = [...content, newBlock];
    updateContent(newContent);
  };

  // Update block content
  const updateBlock = (blockId, newContent) => {
    const newContentArray = content.map(block => 
      block.id === blockId ? { ...block, content: newContent } : block
    );
    updateContent(newContentArray);
  };

  // Delete block
  const deleteBlock = (blockId) => {
    const newContent = content.filter(block => block.id !== blockId);
    updateContent(newContent);
  };

  // Move block up/down
  const moveBlock = (blockId, direction) => {
    const currentIndex = content.findIndex(block => block.id === blockId);
    if (
      (direction === 'up' && currentIndex > 0) ||
      (direction === 'down' && currentIndex < content.length - 1)
    ) {
      const newContent = [...content];
      const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      [newContent[currentIndex], newContent[targetIndex]] = [newContent[targetIndex], newContent[currentIndex]];
      updateContent(newContent);
    }
  };

  // Handle image upload
  const handleImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    if (file.size > maxImageSize) {
      alert(`Image size should be less than ${maxImageSize / (1024 * 1024)}MB`);
      return;
    }

    setImageDialog({ open: true, file, caption: '', alt: '' });
  };

  const processAndAddImage = async () => {
    if (!imageDialog.file) return;

    setUploading(true);
    try {
      const compressedImage = await compressImage(imageDialog.file, {
        maxWidth: 1200,
        maxHeight: 800,
        quality: 0.8,
        format: 'JPEG'
      });

      addImageBlock(compressedImage, imageDialog.caption, imageDialog.alt);
      setImageDialog({ open: false, file: null, caption: '', alt: '' });
    } catch (error) {
      console.error('Error processing image:', error);
      alert('Error processing image. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Text formatting functions
  const formatText = (command, value = null) => {
    document.execCommand(command, false, value);
  };

  const handleHeadingSelect = (level) => {
    addTextBlock(`h${level}`);
    setHeadingMenu({ anchor: null, open: false });
  };

  // Render block based on type
  const renderBlock = (block, index) => {
    const isSelected = selectedBlock === block.id;

    switch (block.type) {
      case 'paragraph':
        return (
          <Box
            key={block.id}
            sx={{
              border: isSelected ? '2px solid #1976d2' : '1px solid transparent',
              borderRadius: 1,
              p: 1,
              mb: 1,
              position: 'relative',
              '&:hover': {
                border: '1px solid #ccc'
              }
            }}
            onClick={() => setSelectedBlock(block.id)}
          >
            <div
              contentEditable
              suppressContentEditableWarning
              onBlur={(e) => updateBlock(block.id, e.target.innerHTML)}
              onInput={(e) => updateBlock(block.id, e.target.innerHTML)}
              style={{
                minHeight: '24px',
                outline: 'none',
                fontSize: '16px',
                lineHeight: '1.5'
              }}
              dangerouslySetInnerHTML={{ __html: block.content }}
              placeholder="Type your text here..."
            />
            {isSelected && (
              <BlockControls
                block={block}
                index={index}
                totalBlocks={content.length}
                onDelete={() => deleteBlock(block.id)}
                onMoveUp={() => moveBlock(block.id, 'up')}
                onMoveDown={() => moveBlock(block.id, 'down')}
              />
            )}
          </Box>
        );

      case 'h1':
      case 'h2':
      case 'h3':
        return (
          <Box
            key={block.id}
            sx={{
              border: isSelected ? '2px solid #1976d2' : '1px solid transparent',
              borderRadius: 1,
              p: 1,
              mb: 1,
              position: 'relative'
            }}
            onClick={() => setSelectedBlock(block.id)}
          >
            <Typography
              variant={block.type}
              component="div"
              contentEditable
              suppressContentEditableWarning
              onBlur={(e) => updateBlock(block.id, e.target.textContent)}
              onInput={(e) => updateBlock(block.id, e.target.textContent)}
              sx={{
                outline: 'none',
                minHeight: '32px'
              }}
            >
              {block.content || `Heading ${block.type.slice(1)}`}
            </Typography>
            {isSelected && (
              <BlockControls
                block={block}
                index={index}
                totalBlocks={content.length}
                onDelete={() => deleteBlock(block.id)}
                onMoveUp={() => moveBlock(block.id, 'up')}
                onMoveDown={() => moveBlock(block.id, 'down')}
              />
            )}
          </Box>
        );

      case 'image':
        return (
          <Box
            key={block.id}
            sx={{
              border: isSelected ? '2px solid #1976d2' : '1px solid transparent',
              borderRadius: 1,
              p: 1,
              mb: 2,
              position: 'relative'
            }}
            onClick={() => setSelectedBlock(block.id)}
          >
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={block.content}
                alt={block.alt || 'Uploaded image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  borderRadius: '4px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              />
              {block.caption && (
                <Typography variant="caption" display="block" sx={{ mt: 1, fontStyle: 'italic' }}>
                  {block.caption}
                </Typography>
              )}
              <Typography variant="caption" display="block" sx={{ mt: 0.5, color: 'text.secondary' }}>
                Original: {(block.originalSize / 1024).toFixed(1)}KB → 
                Compressed: {(block.compressedSize / 1024).toFixed(1)}KB 
                ({Math.round(block.compressionRatio * 100)}% reduction)
              </Typography>
            </Box>
            {isSelected && (
              <BlockControls
                block={block}
                index={index}
                totalBlocks={content.length}
                onDelete={() => deleteBlock(block.id)}
                onMoveUp={() => moveBlock(block.id, 'up')}
                onMoveDown={() => moveBlock(block.id, 'down')}
              />
            )}
          </Box>
        );

      case 'code':
        return (
          <Box
            key={block.id}
            sx={{
              border: isSelected ? '2px solid #1976d2' : '1px solid transparent',
              borderRadius: 1,
              p: 1,
              mb: 1,
              position: 'relative',
              backgroundColor: '#f5f5f5'
            }}
            onClick={() => setSelectedBlock(block.id)}
          >
            <pre
              contentEditable
              suppressContentEditableWarning
              onBlur={(e) => updateBlock(block.id, e.target.textContent)}
              onInput={(e) => updateBlock(block.id, e.target.textContent)}
              style={{
                outline: 'none',
                fontFamily: 'monospace',
                fontSize: '14px',
                margin: 0,
                whiteSpace: 'pre-wrap'
              }}
            >
              {block.content || '// Enter your code here'}
            </pre>
            {isSelected && (
              <BlockControls
                block={block}
                index={index}
                totalBlocks={content.length}
                onDelete={() => deleteBlock(block.id)}
                onMoveUp={() => moveBlock(block.id, 'up')}
                onMoveDown={() => moveBlock(block.id, 'down')}
              />
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      {/* Toolbar */}
      <Paper sx={{ p: 1, mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Tooltip title="Add Heading">
          <IconButton
            size="small"
            onClick={(e) => setHeadingMenu({ anchor: e.currentTarget, open: true })}
          >
            <HeadingIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Add Paragraph">
          <IconButton size="small" onClick={() => addTextBlock('paragraph')}>
            <BoldIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Add Code Block">
          <IconButton size="small" onClick={() => addTextBlock('code')}>
            <CodeIcon />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem />

        <Tooltip title="Add Image">
          <IconButton size="small" onClick={handleImageUpload}>
            <ImageIcon />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem />

        <Tooltip title="Bold">
          <IconButton size="small" onClick={() => formatText('bold')}>
            <BoldIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Italic">
          <IconButton size="small" onClick={() => formatText('italic')}>
            <ItalicIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Underline">
          <IconButton size="small" onClick={() => formatText('underline')}>
            <UnderlineIcon />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem />

        <Tooltip title="Bullet List">
          <IconButton size="small" onClick={() => formatText('insertUnorderedList')}>
            <BulletIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Numbered List">
          <IconButton size="small" onClick={() => formatText('insertOrderedList')}>
            <NumberedIcon />
          </IconButton>
        </Tooltip>

        <Divider orientation="vertical" flexItem />

        <Tooltip title="Undo">
          <IconButton size="small" onClick={() => formatText('undo')}>
            <UndoIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Redo">
          <IconButton size="small" onClick={() => formatText('redo')}>
            <RedoIcon />
          </IconButton>
        </Tooltip>
      </Paper>

      {/* Content Editor Area */}
      <Paper
        ref={editorRef}
        sx={{
          minHeight: 400,
          p: 2,
          border: '1px solid #ddd',
          borderRadius: 1
        }}
      >
        {content.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 200,
              color: 'text.secondary'
            }}
          >
            <Typography variant="h6" gutterBottom>
              Start creating your change request
            </Typography>
            <Typography variant="body2" gutterBottom>
              Add text, images, and code blocks to explain your requirements
            </Typography>
            <Button
              variant="outlined"
              onClick={() => addTextBlock('paragraph')}
              sx={{ mt: 2 }}
            >
              Add Your First Paragraph
            </Button>
          </Box>
        ) : (
          <Box>
            {content.map((block, index) => renderBlock(block, index))}
          </Box>
        )}

        {content.length > 0 && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => addTextBlock('paragraph')}
              sx={{ mr: 1 }}
            >
              Add Text
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={handleImageUpload}
            >
              Add Image
            </Button>
          </Box>
        )}
      </Paper>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleFileSelect}
      />

      {/* Heading Menu */}
      <Menu
        anchorEl={headingMenu.anchor}
        open={headingMenu.open}
        onClose={() => setHeadingMenu({ anchor: null, open: false })}
      >
        <MenuItem onClick={() => handleHeadingSelect(1)}>
          <Typography variant="h4">Heading 1</Typography>
        </MenuItem>
        <MenuItem onClick={() => handleHeadingSelect(2)}>
          <Typography variant="h5">Heading 2</Typography>
        </MenuItem>
        <MenuItem onClick={() => handleHeadingSelect(3)}>
          <Typography variant="h6">Heading 3</Typography>
        </MenuItem>
      </Menu>

      {/* Image Upload Dialog */}
      <Dialog 
        open={imageDialog.open} 
        onClose={() => setImageDialog({ open: false, file: null, caption: '', alt: '' })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Image</DialogTitle>
        <DialogContent>
          {imageDialog.file && (
            <Box sx={{ mb: 2 }}>
              <img
                src={URL.createObjectURL(imageDialog.file)}
                alt="Preview"
                style={{ maxWidth: '100%', height: 'auto', borderRadius: '4px' }}
              />
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Original size: {(imageDialog.file.size / 1024).toFixed(1)}KB
              </Typography>
            </Box>
          )}

          <TextField
            fullWidth
            label="Caption (optional)"
            value={imageDialog.caption}
            onChange={(e) => setImageDialog({ ...imageDialog, caption: e.target.value })}
            sx={{ mb: 2 }}
            placeholder="Add a caption to describe this image"
          />

          <TextField
            fullWidth
            label="Alt text (optional)"
            value={imageDialog.alt}
            onChange={(e) => setImageDialog({ ...imageDialog, alt: e.target.value })}
            placeholder="Describe the image for accessibility"
          />

          {uploading && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Compressing and processing image...
              </Typography>
              <LinearProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageDialog({ open: false, file: null, caption: '', alt: '' })}>
            Cancel
          </Button>
          <Button 
            onClick={processAndAddImage} 
            variant="contained"
            disabled={uploading}
          >
            Add Image
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Block Controls Component
const BlockControls = ({ block, index, totalBlocks, onDelete, onMoveUp, onMoveDown }) => (
  <Box
    sx={{
      position: 'absolute',
      top: -8,
      right: -8,
      display: 'flex',
      gap: 0.5,
      backgroundColor: 'white',
      border: '1px solid #ddd',
      borderRadius: 1,
      p: 0.5
    }}
  >
    {index > 0 && (
      <IconButton size="small" onClick={onMoveUp}>
        ↑
      </IconButton>
    )}
    {index < totalBlocks - 1 && (
      <IconButton size="small" onClick={onMoveDown}>
        ↓
      </IconButton>
    )}
    <IconButton size="small" onClick={onDelete} color="error">
      ×
    </IconButton>
  </Box>
);

export default RichContentEditor;
```

### 6.2 Image Compression Utility

```javascript
// src/utils/imageCompression.js

/**
 * Compress an image file while maintaining quality
 * @param {File} file - The image file to compress
 * @param {Object} options - Compression options
 * @returns {Promise<Object>} Compressed image data
 */
export const compressImage = async (file, options = {}) => {
  const {
    maxWidth = 1200,
    maxHeight = 800,
    quality = 0.8,
    format = 'JPEG'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress image
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert to desired format
      const mimeType = format === 'PNG' ? 'image/png' : 'image/jpeg';
      const compressedDataUrl = canvas.toDataURL(mimeType, quality);
      
      // Calculate file sizes
      const originalSize = file.size;
      const compressedSize = Math.round((compressedDataUrl.length - 22) * 3 / 4); // Estimate base64 size
      const compressionRatio = (originalSize - compressedSize) / originalSize;

      resolve({
        compressedDataUrl,
        originalSize,
        compressedSize,
        compressionRatio,
        width,
        height,
        format: mimeType
      });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Convert data URL to Blob for uploading
 * @param {string} dataUrl - The data URL to convert
 * @returns {Blob} Blob object
 */
export const dataUrlToBlob = (dataUrl) => {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
};

/**
 * Get image dimensions from file
 * @param {File} file - Image file
 * @returns {Promise<Object>} Width and height
 */
export const getImageDimensions = (file) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Progressive image compression with multiple quality levels
 * @param {File} file - Image file
 * @param {number} targetSize - Target size in bytes
 * @returns {Promise<Object>} Best compressed result
 */
export const progressiveCompress = async (file, targetSize = 500 * 1024) => {
  const qualities = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3];
  let bestResult = null;
  
  for (const quality of qualities) {
    const result = await compressImage(file, { quality });
    
    if (result.compressedSize <= targetSize) {
      bestResult = result;
      break;
    }
    
    bestResult = result; // Keep the last result as fallback
  }
  
  return bestResult;
};
```

### 6.3 Enhanced Change Request Form with Rich Content

```jsx
// EnhancedChangeRequestForm.jsx
import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, TextField, FormControl, InputLabel,
  Select, MenuItem, Button, Stepper, Step, StepLabel, Card, CardContent,
  Alert, CircularProgress, Chip, Accordion, AccordionSummary, AccordionDetails
} from '@mui/material';
import { Save as SaveIcon, Send as SendIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import RichContentEditor from './RichContentEditor';
import DynamicForm from './DynamicForm';

const EnhancedChangeRequestForm = ({ requestId, isEdit = false }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [requestTypes, setRequestTypes] = useState([]);
  const [selectedType, setSelectedType] = useState(null);
  const [formData, setFormData] = useState({});
  const [richContent, setRichContent] = useState([]);
  const [basicInfo, setBasicInfo] = useState({
    title: '',
    description: '',
    businessJustification: '',
    expectedBenefit: '',
    priority: 'Medium',
    requestedCompletionDate: ''
  });
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'info' });
  
  const steps = ['Basic Information', 'Request Type', 'Detailed Requirements', 'Review & Submit'];

  useEffect(() => {
    fetchRequestTypes();
    if (isEdit && requestId) {
      fetchExistingRequest();
    }
  }, [isEdit, requestId]);

  const fetchRequestTypes = async () => {
    try {
      const response = await fetch('/api/v1/change-request-types');
      const data = await response.json();
      setRequestTypes(data);
    } catch (error) {
      console.error('Error fetching request types:', error);
      showAlert('Failed to load request types', 'error');
    }
  };

  const fetchExistingRequest = async () => {
    try {
      const response = await fetch(`/api/v1/change-requests/${requestId}`);
      const data = await response.json();
      
      if (response.ok) {
        setBasicInfo({
          title: data.Title,
          description: data.Description,
          businessJustification: data.BusinessJustification || '',
          expectedBenefit: data.ExpectedBenefit || '',
          priority: data.Priority,
          requestedCompletionDate: data.RequestedCompletionDate || ''
        });
        
        if (data.TypeID) {
          const type = requestTypes.find(t => t.TypeID === data.TypeID);
          setSelectedType(type);
        }
        
        if (data.FormData) {
          const parsedFormData = JSON.parse(data.FormData);
          setFormData(parsedFormData.structuredData || {});
          setRichContent(parsedFormData.richContent || []);
        }
      }
    } catch (error) {
      console.error('Error fetching existing request:', error);
      showAlert('Failed to load request details', 'error');
    }
  };

  const showAlert = (message, severity = 'info') => {
    setAlert({ open: true, message, severity });
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateCurrentStep = () => {
    switch (activeStep) {
      case 0:
        if (!basicInfo.title.trim() || !basicInfo.description.trim()) {
          showAlert('Please fill in all required fields', 'error');
          return false;
        }
        return true;
      case 1:
        if (!selectedType) {
          showAlert('Please select a request type', 'error');
          return false;
        }
        return true;
      case 2:
        if (richContent.length === 0) {
          showAlert('Please provide detailed requirements using the rich content editor', 'error');
          return false;
        }
        return true;
      default:
        return true;
    }
  };

  const handleFormDataChange = (fieldName, value) => {
    setFormData({
      ...formData,
      [fieldName]: value
    });
  };

  const saveRequest = async (status) => {
    setLoading(true);
    try {
      const requestData = {
        ...basicInfo,
        TypeID: selectedType?.TypeID,
        Status: status,
        FormData: JSON.stringify({
          structuredData: formData,
          richContent: richContent
        })
      };
      
      const url = isEdit ? `/api/v1/change-requests/${requestId}` : '/api/v1/change-requests';
      const method = isEdit ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      if (response.ok) {
        const data = await response.json();
        showAlert(`Request ${status.toLowerCase()} successfully`, 'success');
        
        if (!isEdit) {
          setTimeout(() => {
            window.location.href = `/it-hub/change-requests/${data.RequestID}`;
          }, 2000);
        }
      } else {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to save request');
      }
    } catch (error) {
      console.error('Error saving request:', error);
      showAlert(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Basic Request Information
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Business Justification"
                  value={basicInfo.businessJustification}
                  onChange={(e) => setBasicInfo({ ...basicInfo, businessJustification: e.target.value })}
                  placeholder="Explain why this request is needed for the business"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Expected Benefit"
                  value={basicInfo.expectedBenefit}
                  onChange={(e) => setBasicInfo({ ...basicInfo, expectedBenefit: e.target.value })}
                  placeholder="Describe the expected benefits and outcomes"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={basicInfo.priority}
                    onChange={(e) => setBasicInfo({ ...basicInfo, priority: e.target.value })}
                    label="Priority"
                  >
                    <MenuItem value="Low">Low</MenuItem>
                    <MenuItem value="Medium">Medium</MenuItem>
                    <MenuItem value="High">High</MenuItem>
                    <MenuItem value="Critical">Critical</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Requested Completion Date"
                  value={basicInfo.requestedCompletionDate}
                  onChange={(e) => setBasicInfo({ ...basicInfo, requestedCompletionDate: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
          </Paper>
        );
      
      case 1:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Select Request Type
            </Typography>
            
            <Grid container spacing={2}>
              {requestTypes.map((type) => (
                <Grid item xs={12} sm={6} md={4} key={type.TypeID}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: selectedType?.TypeID === type.TypeID ? 2 : 1,
                      borderColor: selectedType?.TypeID === type.TypeID ? 'primary.main' : 'grey.300',
                      '&:hover': {
                        borderColor: 'primary.main',
                        boxShadow: 2
                      }
                    }}
                    onClick={() => setSelectedType(type)}
                  >
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {type.TypeName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {type.Description}
                      </Typography>
                      {type.EstimatedDays && (
                        <Chip
                          label={`~${type.EstimatedDays} days`}
                          size="small"
                          color="info"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        );
      
      case 2:
        return (
          <Box>
            {/* Rich Content Editor for detailed requirements */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Detailed Requirements *
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Use the editor below to create detailed requirements. You can mix text and images to explain workflows, 
                show current vs desired states, include screenshots, diagrams, or any visual content that helps explain your needs.
              </Typography>
              
              <RichContentEditor
                value={richContent}
                onChange={setRichContent}
                placeholder="Start by describing your requirements in detail..."
                maxImageSize={5 * 1024 * 1024} // 5MB limit
              />
            </Paper>

            {/* Dynamic form for structured data if available */}
            {selectedType?.FormSchema && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6">
                    Additional Structured Information
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Please fill out the following structured information specific to this request type:
                  </Typography>
                  <DynamicForm
                    schema={JSON.parse(selectedType.FormSchema)}
                    formData={formData}
                    onChange={handleFormDataChange}
                    onSubmit={() => {}} // No submit action needed here
                    submitLabel=""
                  />
                </AccordionDetails>
              </Accordion>
            )}
          </Box>
        );
      
      case 3:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Review Your Request
            </Typography>
            
            <Grid container spacing={3}>
              {/* Basic Information Review */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Basic Information
                    </Typography>
                    <Typography><strong>Title:</strong> {basicInfo.title}</Typography>
                    <Typography><strong>Type:</strong> {selectedType?.TypeName}</Typography>
                    <Typography><strong>Priority:</strong> {basicInfo.priority}</Typography>
                    <Typography><strong>Brief Description:</strong> {basicInfo.description}</Typography>
                    {basicInfo.businessJustification && (
                      <Typography><strong>Business Justification:</strong> {basicInfo.businessJustification}</Typography>
                    )}
                    {basicInfo.expectedBenefit && (
                      <Typography><strong>Expected Benefit:</strong> {basicInfo.expectedBenefit}</Typography>
                    )}
                    {basicInfo.requestedCompletionDate && (
                      <Typography><strong>Requested Completion:</strong> {basicInfo.requestedCompletionDate}</Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Rich Content Preview */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Detailed Requirements ({richContent.length} sections)
                    </Typography>
                    <Box sx={{ maxHeight: 400, overflow: 'auto', border: '1px solid #ddd', borderRadius: 1, p: 2 }}>
                      {richContent.map((block, index) => (
                        <Box key={block.id} sx={{ mb: 2 }}>
                          {block.type === 'image' ? (
                            <Box>
                              <img 
                                src={block.content} 
                                alt={block.alt || 'Content image'} 
                                style={{ maxWidth: '200px', height: 'auto', borderRadius: '4px' }}
                              />
                              {block.caption && (
                                <Typography variant="caption" display="block" sx={{ fontStyle: 'italic' }}>
                                  {block.caption}
                                </Typography>
                              )}
                            </Box>
                          ) : (
                            <Typography 
                              variant={block.type === 'paragraph' ? 'body2' : block.type}
                              dangerouslySetInnerHTML={{ __html: block.content }}
                            />
                          )}
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Structured Data Review */}
              {Object.keys(formData).length > 0 && (
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        Additional Structured Information
                      </Typography>
                      {Object.entries(formData).map(([key, value]) => (
                        <Typography key={key}>
                          <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong>{' '}
                          {Array.isArray(value) ? value.join(', ') : value}
                        </Typography>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>
              )}
              
              {/* Action Buttons */}
              <Grid item xs={12}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Save as Draft:</strong> Save your progress and continue editing later<br/>
                    <strong>Submit Request:</strong> Submit for review and approval (you won't be able to edit after submission)
                  </Typography>
                </Alert>
                
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    onClick={() => saveRequest('Draft')}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => saveRequest('Submitted')}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                  >
                    Submit Request
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        );
      
      default:
        return null;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {isEdit ? 'Edit Change Request' : 'New Change Request'}
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          This form allows you to create rich, detailed change requests with text, images, and structured data. 
          Images will be automatically compressed to maintain quality while reducing file size.
        </Typography>
      </Alert>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      {renderStepContent()}
      
      {activeStep < steps.length - 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={handleNext}
          >
            Next
          </Button>
        </Box>
      )}
      
      {alert.open && (
        <Alert
          severity={alert.severity}
          onClose={() => setAlert({ open: false, message: '', severity: 'info' })}
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 9999 }}
        >
          {alert.message}
        </Alert>
      )}
    </Box>
  );
};

export default EnhancedChangeRequestForm;
```

### 6.4 Rich Content Viewer Component

```jsx
// RichContentViewer.jsx
import React from 'react';
import { Box, Typography, Paper, Divider } from '@mui/material';

const RichContentViewer = ({ content, title = "Request Details" }) => {
  if (!content || content.length === 0) {
    return (
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Typography color="text.secondary">
          No detailed content provided.
        </Typography>
      </Paper>
    );
  }

  const renderBlock = (block) => {
    switch (block.type) {
      case 'paragraph':
        return (
          <Typography
            variant="body1"
            sx={{ mb: 2, lineHeight: 1.6 }}
            dangerouslySetInnerHTML={{ __html: block.content }}
          />
        );

      case 'h1':
        return (
          <Typography variant="h4" sx={{ mb: 2, mt: 3, fontWeight: 'bold' }}>
            {block.content}
          </Typography>
        );

      case 'h2':
        return (
          <Typography variant="h5" sx={{ mb: 2, mt: 2, fontWeight: 'bold' }}>
            {block.content}
          </Typography>
        );

      case 'h3':
        return (
          <Typography variant="h6" sx={{ mb: 1, mt: 2, fontWeight: 'bold' }}>
            {block.content}
          </Typography>
        );

      case 'image':
        return (
          <Box sx={{ my: 3, textAlign: 'center' }}>
            <Box
              sx={{
                display: 'inline-block',
                boxShadow: 2,
                borderRadius: 1,
                overflow: 'hidden',
                maxWidth: '100%'
              }}
            >
              <img
                src={block.content}
                alt={block.alt || 'Request image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  display: 'block'
                }}
              />
            </Box>
            {block.caption && (
              <Typography 
                variant="caption" 
                display="block" 
                sx={{ mt: 1, fontStyle: 'italic', color: 'text.secondary' }}
              >
                {block.caption}
              </Typography>
            )}
          </Box>
        );

      case 'code':
        return (
          <Paper
            sx={{
              p: 2,
              my: 2,
              backgroundColor: '#f5f5f5',
              border: '1px solid #e0e0e0'
            }}
          >
            <pre
              style={{
                margin: 0,
                fontFamily: 'monospace',
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {block.content}
            </pre>
          </Paper>
        );

      default:
        return (
          <Typography variant="body1" sx={{ mb: 2 }}>
            {block.content}
          </Typography>
        );
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      <Divider sx={{ mb: 3 }} />
      
      <Box>
        {content.map((block, index) => (
          <Box key={block.id || index}>
            {renderBlock(block)}
          </Box>
        ))}
      </Box>
      
      {/* Metadata */}
      <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid #e0e0e0' }}>
        <Typography variant="caption" color="text.secondary">
          Content includes {content.filter(b => b.type === 'image').length} image(s) and{' '}
          {content.filter(b => b.type !== 'image').length} text section(s)
        </Typography>
      </Box>
    </Paper>
  );
};

export default RichContentViewer;
```

### 6.5 Backend Storage Service Enhancement

```typescript
// src/services/richContentService.ts
import { BlobStorageService } from './blobStorageService';
import { logger } from '../utils/logger';
import { dataUrlToBlob } from '../utils/imageCompression';

export class RichContentService {
  private blobService: BlobStorageService;

  constructor() {
    this.blobService = new BlobStorageService('change-requests-content');
  }

  /**
   * Process and store rich content, uploading images to blob storage
   * @param content Rich content array
   * @param requestId Change request ID
   * @returns Processed content with blob URLs
   */
  async processAndStoreContent(content: any[], requestId: number): Promise<any[]> {
    const processedContent = [];
    
    for (const block of content) {
      if (block.type === 'image' && block.content.startsWith('data:')) {
        try {
          // Convert data URL to blob
          const blob = dataUrlToBlob(block.content);
          
          // Generate unique filename
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const fileName = `cr-${requestId}/image-${block.id}-${timestamp}.jpg`;
          
          // Upload to blob storage
          const blobUrl = await this.blobService.uploadFile(
            fileName,
            Buffer.from(await blob.arrayBuffer()),
            blob.type
          );
          
          // Update block with blob URL
          processedContent.push({
            ...block,
            content: blobUrl,
            originalDataUrl: block.content, // Keep for fallback
            storagePath: fileName
          });
          
          logger.info(`Image uploaded for change request ${requestId}: ${fileName}`);
        } catch (error) {
          logger.error(`Error uploading image for change request ${requestId}:`, error);
          // Keep original data URL as fallback
          processedContent.push(block);
        }
      } else {
        processedContent.push(block);
      }
    }
    
    return processedContent;
  }

  /**
   * Clean up images when a change request is deleted
   * @param content Rich content array
   */
  async cleanupContent(content: any[]): Promise<void> {
    for (const block of content) {
      if (block.type === 'image' && block.storagePath) {
        try {
          await this.blobService.deleteFile(block.storagePath);
          logger.info(`Deleted image: ${block.storagePath}`);
        } catch (error) {
          logger.error(`Error deleting image ${block.storagePath}:`, error);
        }
      }
    }
  }

  /**
   * Optimize content for email notifications (smaller images)
   * @param content Rich content array
   * @returns Optimized content for email
   */
  optimizeForEmail(content: any[]): string {
    let emailContent = '';
    
    for (const block of content) {
      switch (block.type) {
        case 'paragraph':
          emailContent += `<p>${block.content}</p>\n`;
          break;
        case 'h1':
          emailContent += `<h1>${block.content}</h1>\n`;
          break;
        case 'h2':
          emailContent += `<h2>${block.content}</h2>\n`;
          break;
        case 'h3':
          emailContent += `<h3>${block.content}</h3>\n`;
          break;
        case 'image':
          emailContent += `<div style="text-align: center; margin: 20px 0;">
            <img src="${block.content}" alt="${block.alt || 'Request image'}" 
                 style="max-width: 600px; height: auto; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" />
            ${block.caption ? `<br><em style="color: #666; font-size: 14px;">${block.caption}</em>` : ''}
          </div>\n`;
          break;
        case 'code':
          emailContent += `<pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px; overflow-x: auto;">${block.content}</pre>\n`;
          break;
      }
    }
    
    return emailContent;
  }
}

export const richContentService = new RichContentService();
```

### 6.6 Updated Database Schema for Rich Content

```sql
-- Update the ChangeRequests table to support rich content
ALTER TABLE ChangeRequests 
ADD RichContent NVARCHAR(MAX) NULL; -- JSON array of rich content blocks

-- Add table for tracking image assets
CREATE TABLE ChangeRequestAssets (
    AssetID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    AssetType NVARCHAR(50) NOT NULL, -- 'image', 'file', etc.
    OriginalFileName NVARCHAR(255) NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    BlobURL NVARCHAR(1000) NOT NULL,
    FileSize INT NOT NULL,
    MimeType NVARCHAR(100) NOT NULL,
    CompressedSize INT NULL,
    CompressionRatio DECIMAL(5,4) NULL,
    BlockId NVARCHAR(100) NULL, -- References the block in rich content
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ChangeRequestAssets_ChangeRequests FOREIGN KEY (RequestID) REFERENCES ChangeRequests(RequestID)
);
```

## Key Benefits of This Enhanced Solution:

### 1. **Rich Sequential Content**
- Users can create content that flows naturally with text and images inline
- Support for headings, paragraphs, code blocks, and images in any order
- Visual workflow explanations with screenshots and diagrams

### 2. **Automatic Image Compression**
- Images are compressed to maintain quality while reducing file size
- Progressive compression ensures optimal file sizes
- Users see compression statistics (original vs compressed size)
- Support for multiple image formats with intelligent optimization

### 3. **Professional Presentation**
- Clean, structured content that's easy to read and understand
- Proper image captions and alt text for accessibility
- Code blocks for technical specifications
- Consistent formatting across all requests

### 4. **Storage Efficiency**
- Images stored in Azure Blob Storage with CDN delivery
- Automatic cleanup of orphaned images
- Optimized content for email notifications

### 5. **Enhanced User Experience**
- Intuitive block-based editor similar to modern content platforms
- Drag-and-drop reordering of content blocks
- Real-time preview and editing
- Mobile-responsive interface

This solution addresses the need for users to create comprehensive, visual change requests that effectively communicate complex workflows and requirements while maintaining efficient storage and excellent performance.
                <TextField
                  fullWidth
                  label="Request Title *"
                  value={basicInfo.title}
                  onChange={(e) => setBasicInfo({ ...basicInfo, title: e.target.value })}
                  placeholder="Provide a clear, descriptive title for your request"
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Brief Description *"
                  value={basicInfo.description}
                  onChange={(e) => setBasicInfo({ ...basicInfo, description: e.target.value })}
                  placeholder="Provide a brief overview - detailed requirements will be captured in the next steps"
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                # IT Hub Change Management Module - Complete Specification

## 1. Overview

The Change Management Module will be integrated into the IT Hub to allow users to submit change requests and new application development requests. It provides transparency through a public queue view and approval workflows for authorized personnel.

## 2. Key Features

- **Change Request Submission**: Users can submit various types of change requests
- **Dynamic Form Builder**: Instead of file attachments, use dynamic forms based on change type
- **Public Queue**: All users can view pending changes for transparency
- **Approval Workflow**: Admin and Change Manager roles can approve requests
- **Email Notifications**: Automated notifications to development teams
- **Status Tracking**: Real-time status updates and history
- **Attachment Support**: Optional file attachments for supporting documents

## 3. Database Schema

### 3.1 Change Request Types Table

```sql
CREATE TABLE ChangeRequestTypes (
    TypeID INT PRIMARY KEY IDENTITY(1,1),
    TypeName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    FormSchema NVARCHAR(MAX) NULL, -- JSON schema for dynamic form
    RequiresApproval BIT NOT NULL DEFAULT 1,
    ApprovalLevel NVARCHAR(50) NOT NULL, -- 'Manager', 'Admin', 'ChangeManager'
    EstimatedDays INT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL
);
```

### 3.2 Change Requests Table

```sql
CREATE TABLE ChangeRequests (
    RequestID INT PRIMARY KEY IDENTITY(1,1),
    TypeID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX) NOT NULL,
    BusinessJustification NVARCHAR(MAX) NULL,
    ExpectedBenefit NVARCHAR(MAX) NULL,
    RequestedBy INT NOT NULL,
    CompanyID INT NOT NULL,
    DepartmentID INT NULL,
    Priority NVARCHAR(20) NOT NULL, -- 'Low', 'Medium', 'High', 'Critical'
    Status NVARCHAR(30) NOT NULL, -- 'Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'InProgress', 'Completed', 'Cancelled'
    FormData NVARCHAR(MAX) NULL, -- JSON data from dynamic form
    EstimatedEffort NVARCHAR(100) NULL,
    ProposedImplementationDate DATE NULL,
    RequestedCompletionDate DATE NULL,
    ActualCompletionDate DATE NULL,
    RejectionReason NVARCHAR(1000) NULL,
    ApprovedBy INT NULL,
    ApprovedDate DATETIME NULL,
    AssignedTo INT NULL, -- Development team lead
    AssignedDate DATETIME NULL,
    PubliclyVisible BIT NOT NULL DEFAULT 1,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ModifiedBy INT NULL,
    ModifiedDate DATETIME NULL,
    
    CONSTRAINT FK_ChangeRequests_ChangeRequestTypes FOREIGN KEY (TypeID) REFERENCES ChangeRequestTypes(TypeID),
    CONSTRAINT FK_ChangeRequests_Users_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Users(UserID),
    CONSTRAINT FK_ChangeRequests_Companies FOREIGN KEY (CompanyID) REFERENCES Companies(CompanyID),
    CONSTRAINT FK_ChangeRequests_Departments FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID),
    CONSTRAINT FK_ChangeRequests_Users_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID),
    CONSTRAINT FK_ChangeRequests_Users_AssignedTo FOREIGN KEY (AssignedTo) REFERENCES Users(UserID)
);
```

### 3.3 Change Request Attachments Table

```sql
CREATE TABLE ChangeRequestAttachments (
    AttachmentID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    OriginalFileName NVARCHAR(255) NOT NULL,
    StoragePath NVARCHAR(1000) NOT NULL,
    FileSize INT NOT NULL,
    MimeType NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ChangeRequestAttachments_ChangeRequests FOREIGN KEY (RequestID) REFERENCES ChangeRequests(RequestID)
);
```

### 3.4 Change Request Comments Table

```sql
CREATE TABLE ChangeRequestComments (
    CommentID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    CommentText NVARCHAR(MAX) NOT NULL,
    CommentType NVARCHAR(50) NOT NULL, -- 'General', 'StatusUpdate', 'ApprovalNote', 'RejectionNote'
    IsInternal BIT NOT NULL DEFAULT 0, -- Internal comments only visible to approvers
    CreatedBy INT NOT NULL,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ChangeRequestComments_ChangeRequests FOREIGN KEY (RequestID) REFERENCES ChangeRequests(RequestID),
    CONSTRAINT FK_ChangeRequestComments_Users FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);
```

### 3.5 Change Request History Table

```sql
CREATE TABLE ChangeRequestHistory (
    HistoryID INT PRIMARY KEY IDENTITY(1,1),
    RequestID INT NOT NULL,
    StatusFrom NVARCHAR(30) NULL,
    StatusTo NVARCHAR(30) NOT NULL,
    ChangedBy INT NOT NULL,
    ChangeDate DATETIME NOT NULL DEFAULT GETDATE(),
    Comments NVARCHAR(1000) NULL,
    
    CONSTRAINT FK_ChangeRequestHistory_ChangeRequests FOREIGN KEY (RequestID) REFERENCES ChangeRequests(RequestID),
    CONSTRAINT FK_ChangeRequestHistory_Users FOREIGN KEY (ChangedBy) REFERENCES Users(UserID)
);
```

## 4. API Implementation

### 4.1 Change Request Service

```typescript
// src/services/changeRequestService.ts
import { Op } from 'sequelize';
import { ChangeRequest, ChangeRequestType, ChangeRequestComment, ChangeRequestHistory, User, Company, Department } from '../models';
import { BlobStorageService } from './blobStorageService';
import { EmailNotificationService } from './emailNotificationService';
import { logger } from '../utils/logger';
import sequelize from '../config/database';

interface ChangeRequestFilters {
  status?: string[];
  priority?: string[];
  typeId?: number;
  companyId?: number;
  requestedBy?: number;
  assignedTo?: number;
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

export const listChangeRequests = async (
  filters: ChangeRequestFilters,
  page: number,
  limit: number,
  sortField: string = 'CreatedDate',
  sortOrder: 'asc' | 'desc' = 'desc',
  userId?: number
) => {
  const offset = (page - 1) * limit;
  
  // Build where clause
  const whereClause: any = { 
    IsActive: true,
    PubliclyVisible: true // Only show publicly visible requests
  };
  
  if (filters.status && filters.status.length > 0) {
    whereClause.Status = { [Op.in]: filters.status };
  }
  
  if (filters.priority && filters.priority.length > 0) {
    whereClause.Priority = { [Op.in]: filters.priority };
  }
  
  if (filters.typeId) {
    whereClause.TypeID = filters.typeId;
  }
  
  if (filters.companyId) {
    whereClause.CompanyID = filters.companyId;
  }
  
  if (filters.requestedBy) {
    whereClause.RequestedBy = filters.requestedBy;
  }
  
  if (filters.assignedTo) {
    whereClause.AssignedTo = filters.assignedTo;
  }
  
  if (filters.search) {
    whereClause[Op.or] = [
      { Title: { [Op.like]: `%${filters.search}%` } },
      { Description: { [Op.like]: `%${filters.search}%` } }
    ];
  }
  
  if (filters.dateFrom || filters.dateTo) {
    whereClause.CreatedDate = {};
    if (filters.dateFrom) {
      whereClause.CreatedDate[Op.gte] = filters.dateFrom;
    }
    if (filters.dateTo) {
      whereClause.CreatedDate[Op.lte] = filters.dateTo;
    }
  }
  
  try {
    const totalCount = await ChangeRequest.count({ where: whereClause });
    
    const requests = await ChangeRequest.findAll({
      where: whereClause,
      include: [
        { 
          model: ChangeRequestType, 
          attributes: ['TypeID', 'TypeName', 'Description'] 
        },
        { 
          model: User, 
          as: 'Requester', 
          attributes: ['UserID', 'FirstName', 'LastName', 'Email'] 
        },
        { 
          model: User, 
          as: 'Approver', 
          attributes: ['UserID', 'FirstName', 'LastName'] 
        },
        { 
          model: User, 
          as: 'Assignee', 
          attributes: ['UserID', 'FirstName', 'LastName'] 
        },
        { 
          model: Company, 
          attributes: ['CompanyID', 'CompanyName'] 
        },
        { 
          model: Department, 
          attributes: ['DepartmentID', 'DepartmentName'] 
        }
      ],
      limit,
      offset,
      order: [[sortField, sortOrder]]
    });
    
    return {
      requests,
      totalCount
    };
  } catch (error) {
    logger.error('Error in listChangeRequests service', error);
    throw error;
  }
};

export const getChangeRequestById = async (requestId: number, userId?: number) => {
  try {
    const request = await ChangeRequest.findOne({
      where: {
        RequestID: requestId,
        IsActive: true
      },
      include: [
        { 
          model: ChangeRequestType, 
          attributes: ['TypeID', 'TypeName', 'Description', 'FormSchema'] 
        },
        { 
          model: User, 
          as: 'Requester', 
          attributes: ['UserID', 'FirstName', 'LastName', 'Email'] 
        },
        { 
          model: User, 
          as: 'Approver', 
          attributes: ['UserID', 'FirstName', 'LastName'] 
        },
        { 
          model: User, 
          as: 'Assignee', 
          attributes: ['UserID', 'FirstName', 'LastName'] 
        },
        { 
          model: Company, 
          attributes: ['CompanyID', 'CompanyName'] 
        },
        { 
          model: Department, 
          attributes: ['DepartmentID', 'DepartmentName'] 
        }
      ]
    });
    
    return request;
  } catch (error) {
    logger.error(`Error in getChangeRequestById service for ID ${requestId}`, error);
    throw error;
  }
};

export const createChangeRequest = async (requestData: any, userId: number) => {
  const transaction = await sequelize.transaction();
  
  try {
    // Create the change request
    const request = await ChangeRequest.create({
      ...requestData,
      RequestedBy: userId,
      Status: 'Draft',
      CreatedBy: userId,
      CreatedDate: new Date()
    }, { transaction });
    
    // Add initial history entry
    await ChangeRequestHistory.create({
      RequestID: request.RequestID,
      StatusFrom: null,
      StatusTo: 'Draft',
      ChangedBy: userId,
      ChangeDate: new Date(),
      Comments: 'Change request created'
    }, { transaction });
    
    await transaction.commit();
    return request;
  } catch (error) {
    await transaction.rollback();
    logger.error('Error in createChangeRequest service', error);
    throw error;
  }
};

export const updateChangeRequest = async (requestId: number, requestData: any, userId: number) => {
  const transaction = await sequelize.transaction();
  
  try {
    const request = await ChangeRequest.findByPk(requestId);
    
    if (!request) {
      throw new Error('Change request not found');
    }
    
    // Check if user can update this request
    if (request.RequestedBy !== userId && !['Admin', 'ChangeManager'].includes(userId)) {
      throw new Error('You do not have permission to update this request');
    }
    
    const oldStatus = request.Status;
    
    // Update the request
    await request.update({
      ...requestData,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    }, { transaction });
    
    // If status changed, add history entry
    if (requestData.Status && requestData.Status !== oldStatus) {
      await ChangeRequestHistory.create({
        RequestID: requestId,
        StatusFrom: oldStatus,
        StatusTo: requestData.Status,
        ChangedBy: userId,
        ChangeDate: new Date(),
        Comments: requestData.StatusComment || `Status changed from ${oldStatus} to ${requestData.Status}`
      }, { transaction });
      
      // Send notifications based on status
      await handleStatusChangeNotifications(request, oldStatus, requestData.Status);
    }
    
    await transaction.commit();
    return await getChangeRequestById(requestId);
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error in updateChangeRequest service for ID ${requestId}`, error);
    throw error;
  }
};

export const submitChangeRequest = async (requestId: number, userId: number) => {
  return await updateChangeRequest(requestId, { Status: 'Submitted' }, userId);
};

export const approveChangeRequest = async (requestId: number, userId: number, comments?: string) => {
  const transaction = await sequelize.transaction();
  
  try {
    const request = await ChangeRequest.findByPk(requestId);
    
    if (!request) {
      throw new Error('Change request not found');
    }
    
    if (request.Status !== 'Submitted' && request.Status !== 'UnderReview') {
      throw new Error('Request cannot be approved in its current status');
    }
    
    // Update request
    await request.update({
      Status: 'Approved',
      ApprovedBy: userId,
      ApprovedDate: new Date(),
      ModifiedBy: userId,
      ModifiedDate: new Date()
    }, { transaction });
    
    // Add history entry
    await ChangeRequestHistory.create({
      RequestID: requestId,
      StatusFrom: request.Status,
      StatusTo: 'Approved',
      ChangedBy: userId,
      ChangeDate: new Date(),
      Comments: comments || 'Change request approved'
    }, { transaction });
    
    // Add approval comment if provided
    if (comments) {
      await ChangeRequestComment.create({
        RequestID: requestId,
        CommentText: comments,
        CommentType: 'ApprovalNote',
        IsInternal: false,
        CreatedBy: userId
      }, { transaction });
    }
    
    await transaction.commit();
    
    // Send notification to development team
    await EmailNotificationService.sendChangeRequestApprovalNotification(request);
    
    return await getChangeRequestById(requestId);
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error in approveChangeRequest service for ID ${requestId}`, error);
    throw error;
  }
};

export const rejectChangeRequest = async (requestId: number, userId: number, reason: string) => {
  const transaction = await sequelize.transaction();
  
  try {
    const request = await ChangeRequest.findByPk(requestId);
    
    if (!request) {
      throw new Error('Change request not found');
    }
    
    if (request.Status !== 'Submitted' && request.Status !== 'UnderReview') {
      throw new Error('Request cannot be rejected in its current status');
    }
    
    // Update request
    await request.update({
      Status: 'Rejected',
      RejectionReason: reason,
      ModifiedBy: userId,
      ModifiedDate: new Date()
    }, { transaction });
    
    // Add history entry
    await ChangeRequestHistory.create({
      RequestID: requestId,
      StatusFrom: request.Status,
      StatusTo: 'Rejected',
      ChangedBy: userId,
      ChangeDate: new Date(),
      Comments: `Request rejected: ${reason}`
    }, { transaction });
    
    // Add rejection comment
    await ChangeRequestComment.create({
      RequestID: requestId,
      CommentText: reason,
      CommentType: 'RejectionNote',
      IsInternal: false,
      CreatedBy: userId
    }, { transaction });
    
    await transaction.commit();
    
    // Send notification to requester
    await EmailNotificationService.sendChangeRequestRejectionNotification(request, reason);
    
    return await getChangeRequestById(requestId);
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error in rejectChangeRequest service for ID ${requestId}`, error);
    throw error;
  }
};

const handleStatusChangeNotifications = async (request: any, oldStatus: string, newStatus: string) => {
  try {
    switch (newStatus) {
      case 'Submitted':
        await EmailNotificationService.sendChangeRequestSubmittedNotification(request);
        break;
      case 'InProgress':
        await EmailNotificationService.sendChangeRequestInProgressNotification(request);
        break;
      case 'Completed':
        await EmailNotificationService.sendChangeRequestCompletedNotification(request);
        break;
    }
  } catch (error) {
    logger.error('Error sending status change notifications', error);
    // Don't throw error as this shouldn't block the main operation
  }
};
```

### 4.2 Change Request Controller

```typescript
// src/controllers/changeRequestController.ts
import { Request, Response } from 'express';
import { 
  listChangeRequests, 
  getChangeRequestById, 
  createChangeRequest, 
  updateChangeRequest,
  submitChangeRequest,
  approveChangeRequest,
  rejectChangeRequest
} from '../services/changeRequestService';
import { paginationParams } from '../utils/pagination';
import { logger } from '../utils/logger';

export const getChangeRequests = async (req: Request, res: Response) => {
  try {
    const { page, limit, sortField, sortOrder } = paginationParams(req);
    const filters = {
      status: req.query.status ? (req.query.status as string).split(',') : undefined,
      priority: req.query.priority ? (req.query.priority as string).split(',') : undefined,
      typeId: req.query.typeId ? Number(req.query.typeId) : undefined,
      companyId: req.query.companyId ? Number(req.query.companyId) : undefined,
      requestedBy: req.query.requestedBy ? Number(req.query.requestedBy) : undefined,
      assignedTo: req.query.assignedTo ? Number(req.query.assignedTo) : undefined,
      search: req.query.search as string,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined
    };
    
    const result = await listChangeRequests(filters, page, limit, sortField, sortOrder, req.user.id);
    
    return res.status(200).json({
      items: result.requests,
      pagination: {
        page,
        limit,
        totalItems: result.totalCount,
        totalPages: Math.ceil(result.totalCount / limit),
        hasNext: page * limit < result.totalCount,
        hasPrevious: page > 1
      }
    });
  } catch (error) {
    logger.error('Error fetching change requests', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while fetching change requests'
      }
    });
  }
};

export const getChangeRequest = async (req: Request, res: Response) => {
  try {
    const requestId = Number(req.params.requestId);
    const request = await getChangeRequestById(requestId, req.user.id);
    
    if (!request) {
      return res.status(404).json({
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'Change request not found'
        }
      });
    }
    
    return res.status(200).json(request);
  } catch (error) {
    logger.error(`Error fetching change request with ID ${req.params.requestId}`, error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while fetching the change request'
      }
    });
  }
};

export const createNewChangeRequest = async (req: Request, res: Response) => {
  try {
    const request = await createChangeRequest(req.body, req.user.id);
    
    return res.status(201).json(request);
  } catch (error) {
    logger.error('Error creating change request', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while creating the change request'
      }
    });
  }
};

export const updateExistingChangeRequest = async (req: Request, res: Response) => {
  try {
    const requestId = Number(req.params.requestId);
    const request = await updateChangeRequest(requestId, req.body, req.user.id);
    
    return res.status(200).json(request);
  } catch (error) {
    logger.error(`Error updating change request with ID ${req.params.requestId}`, error);
    
    if (error.message === 'Change request not found') {
      return res.status(404).json({
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'Change request not found'
        }
      });
    }
    
    if (error.message.includes('permission')) {
      return res.status(403).json({
        error: {
          code: 'FORBIDDEN',
          message: error.message
        }
      });
    }
    
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while updating the change request'
      }
    });
  }
};

export const submitRequest = async (req: Request, res: Response) => {
  try {
    const requestId = Number(req.params.requestId);
    const request = await submitChangeRequest(requestId, req.user.id);
    
    return res.status(200).json(request);
  } catch (error) {
    logger.error(`Error submitting change request with ID ${req.params.requestId}`, error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while submitting the change request'
      }
    });
  }
};

export const approveRequest = async (req: Request, res: Response) => {
  try {
    const requestId = Number(req.params.requestId);
    const { comments } = req.body;
    
    const request = await approveChangeRequest(requestId, req.user.id, comments);
    
    return res.status(200).json(request);
  } catch (error) {
    logger.error(`Error approving change request with ID ${req.params.requestId}`, error);
    
    if (error.message.includes('cannot be approved')) {
      return res.status(400).json({
        error: {
          code: 'INVALID_STATUS',
          message: error.message
        }
      });
    }
    
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while approving the change request'
      }
    });
  }
};

export const rejectRequest = async (req: Request, res: Response) => {
  try {
    const requestId = Number(req.params.requestId);
    const { reason } = req.body;
    
    if (!reason) {
      return res.status(400).json({
        error: {
          code: 'MISSING_PARAMETER',
          message: 'Rejection reason is required'
        }
      });
    }
    
    const request = await rejectChangeRequest(requestId, req.user.id, reason);
    
    return res.status(200).json(request);
  } catch (error) {
    logger.error(`Error rejecting change request with ID ${req.params.requestId}`, error);
    
    if (error.message.includes('cannot be rejected')) {
      return res.status(400).json({
        error: {
          code: 'INVALID_STATUS',
          message: error.message
        }
      });
    }
    
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while rejecting the change request'
      }
    });
  }
};
```

## 5. Dynamic Form Schema

Instead of requiring file attachments, we'll use dynamic forms based on change request types:

### 5.1 Example Form Schemas

```json
{
  "newApplicationRequest": {
    "title": "New Application Development Request",
    "sections": [
      {
        "title": "Application Overview",
        "fields": [
          {
            "name": "applicationName",
            "label": "Proposed Application Name",
            "type": "text",
            "required": true,
            "validation": {
              "minLength": 3,
              "maxLength": 100
            }
          },
          {
            "name": "applicationPurpose",
            "label": "Purpose and Objectives",
            "type": "textarea",
            "required": true,
            "placeholder": "Describe what the application should accomplish"
          },
          {
            "name": "targetUsers",
            "label": "Target Users",
            "type": "select",
            "required": true,
            "options": [
              "Internal Employees",
              "External Customers",
              "Partners/Vendors",
              "Mixed User Base"
            ]
          },
          {
            "name": "estimatedUsers",
            "label": "Estimated Number of Users",
            "type": "number",
            "required": true,
            "validation": {
              "min": 1
            }
          }
        ]
      },
      {
        "title": "Technical Requirements",
        "fields": [
          {
            "name": "platformPreference",
            "label": "Platform Preference",
            "type": "checkbox",
            "options": [
              "Web Application",
              "Mobile App (iOS)",
              "Mobile App (Android)",
              "Desktop Application"
            ]
          },
          {
            "name": "integrationRequirements",
            "label": "Integration Requirements",
            "type": "textarea",
            "placeholder": "List systems that need to be integrated (e.g., ERP, CRM, databases)"
          },
          {
            "name": "dataRequirements",
            "label": "Data Storage Requirements",
            "type": "textarea",
            "placeholder": "Describe data that needs to be stored and processed"
          }
        ]
      },
      {
        "title": "Business Impact",
        "fields": [
          {
            "name": "currentProcess",
            "label": "Current Process Description",
            "type": "textarea",
            "required": true,
            "placeholder": "How is this process currently handled?"
          },
          {
            "name": "problemsWithCurrent",
            "label": "Problems with Current Process",
            "type": "textarea",
            "required": true,
            "placeholder": "What issues or inefficiencies exist?"
          },
          {
            "name": "expectedBenefits",
            "label": "Expected Benefits",
            "type": "textarea",
            "required": true,
            "placeholder": "How will this application improve the process?"
          },
          {
            "name": "urgency",
            "label": "Urgency Level",
            "type": "select",
            "required": true,
            "options": [
              "Low - Can wait 6+ months",
              "Medium - Needed within 3-6 months",
              "High - Needed within 1-3 months",
              "Critical - Needed within 1 month"
            ]
          }
        ]
      }
    ]
  },
  "systemModification": {
    "title": "System Modification Request",
    "sections": [
      {
        "title": "System Information",
        "fields": [
          {
            "name": "systemName",
            "label": "System/Application Name",
            "type": "text",
            "required": true
          },
          {
            "name": "moduleAffected",
            "label": "Module/Feature Affected",
            "type": "text",
            "required": true
          },
          {
            "name": "changeType",
            "label": "Type of Change",
            "type": "select",
            "required": true,
            "options": [
              "New Feature",
              "Enhancement",
              "Bug Fix",
              "Performance Improvement",
              "Security Update",
              "Integration"
            ]
          }
        ]
      },
      {
        "title": "Change Details",
        "fields": [
          {
            "name": "currentBehavior",
            "label": "Current System Behavior",
            "type": "textarea",
            "required": true,
            "placeholder": "Describe how the system currently works"
          },
          {
            "name": "requestedChange",
            "label": "Requested Change",
            "type": "textarea",
            "required": true,
            "placeholder": "Describe exactly what you want changed"
          },
          {
            "name": "expectedBehavior",
            "label": "Expected Behavior After Change",
            "type": "textarea",
            "required": true,
            "placeholder": "Describe how the system should work after the change"
          }
        ]
      }
    ]
  }
}
```

## 6. React Components

### 6.1 Change Request List Component

```jsx
// ChangeRequestList.jsx
import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Card, CardContent, Chip, Button, Grid,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Paper, IconButton, Tooltip, Dialog, DialogTitle, DialogContent,
  DialogActions, TextField, FormControl, InputLabel, Select, MenuItem,
  Pagination, LinearProgress, Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Check as ApproveIcon,
  Close as RejectIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

const ChangeRequestList = ({ userRoles }) => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalPages: 1,
    totalItems: 0
  });
  
  // Filter states
  const [filters, setFilters] = useState({
    status: [],
    priority: [],
    search: '',
    dateFrom: '',
    dateTo: ''
  });
  
  // Dialog states
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    requestId: null,
    comments: ''
  });
  
  const [rejectionDialog, setRejectionDialog] = useState({
    open: false,
    requestId: null,
    reason: ''
  });
  
  const [alert, setAlert] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  const canApprove = userRoles.includes('Admin') || userRoles.includes('ChangeManager');
  
  useEffect(() => {
    fetchChangeRequests();
  }, [pagination.page, filters]);
  
  const fetchChangeRequests = async () => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...filters
      });
      
      const response = await fetch(`/api/v1/change-requests?${queryParams}`);
      const data = await response.json();
      
      if (response.ok) {
        setRequests(data.items);
        setPagination(prev => ({
          ...prev,
          totalPages: data.pagination.totalPages,
          totalItems: data.pagination.totalItems
        }));
      } else {
        throw new Error(data.error?.message || 'Failed to fetch change requests');
      }
    } catch (error) {
      console.error('Error fetching change requests:', error);
      showAlert('Failed to load change requests', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  const showAlert = (message, severity = 'info') => {
    setAlert({ open: true, message, severity });
  };
  
  const getStatusColor = (status) => {
    const colors = {
      'Draft': 'default',
      'Submitted': 'info',
      'UnderReview': 'warning',
      'Approved': 'success',
      'Rejected': 'error',
      'InProgress': 'primary',
      'Completed': 'success',
      'Cancelled': 'default'
    };
    return colors[status] || 'default';
  };
  
  const getPriorityColor = (priority) => {
    const colors = {
      'Low': 'default',
      'Medium': 'info',
      'High': 'warning',
      'Critical': 'error'
    };
    return colors[priority] || 'default';
  };
  
  const handleApprove = async () => {
    try {
      const response = await fetch(`/api/v1/change-requests/${approvalDialog.requestId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          comments: approvalDialog.comments
        })
      });
      
      if (response.ok) {
        showAlert('Change request approved successfully', 'success');
        setApprovalDialog({ open: false, requestId: null, comments: '' });
        fetchChangeRequests();
      } else {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to approve request');
      }
    } catch (error) {
      console.error('Error approving request:', error);
      showAlert(error.message, 'error');
    }
  };
  
  const handleReject = async () => {
    try {
      const response = await fetch(`/api/v1/change-requests/${rejectionDialog.requestId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reason: rejectionDialog.reason
        })
      });
      
      if (response.ok) {
        showAlert('Change request rejected', 'success');
        setRejectionDialog({ open: false, requestId: null, reason: '' });
        fetchChangeRequests();
      } else {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to reject request');
      }
    } catch (error) {
      console.error('Error rejecting request:', error);
      showAlert(error.message, 'error');
    }
  };
  
  const openApprovalDialog = (requestId) => {
    setApprovalDialog({ open: true, requestId, comments: '' });
  };
  
  const openRejectionDialog = (requestId) => {
    setRejectionDialog({ open: true, requestId, reason: '' });
  };
  
  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Change Management</Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          href="/it-hub/change-requests/new"
        >
          New Change Request
        </Button>
      </Box>
      
      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary">
                Total Requests
              </Typography>
              <Typography variant="h4">
                {pagination.totalItems}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary">
                Pending Approval
              </Typography>
              <Typography variant="h4" color="warning.main">
                {requests.filter(r => r.Status === 'Submitted' || r.Status === 'UnderReview').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary">
                In Progress
              </Typography>
              <Typography variant="h4" color="primary.main">
                {requests.filter(r => r.Status === 'InProgress').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary">
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {requests.filter(r => r.Status === 'Completed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                placeholder="Search by title or description"
              />
            </Grid>
            <Grid item xs={12} sm={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  multiple
                  value={filters.status}
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  label="Status"
                >
                  <MenuItem value="Draft">Draft</MenuItem>
                  <MenuItem value="Submitted">Submitted</MenuItem>
                  <MenuItem value="UnderReview">Under Review</MenuItem>
                  <MenuItem value="Approved">Approved</MenuItem>
                  <MenuItem value="Rejected">Rejected</MenuItem>
                  <MenuItem value="InProgress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                  <MenuItem value="Cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  multiple
                  value={filters.priority}
                  onChange={(e) => setFilters({ ...filters, priority: e.target.value })}
                  label="Priority"
                >
                  <MenuItem value="Low">Low</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="High">High</MenuItem>
                  <MenuItem value="Critical">Critical</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <TextField
                fullWidth
                type="date"
                label="From Date"
                value={filters.dateFrom}
                onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={2}>
              <TextField
                fullWidth
                type="date"
                label="To Date"
                value={filters.dateTo}
                onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={1}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => setFilters({
                  status: [],
                  priority: [],
                  search: '',
                  dateFrom: '',
                  dateTo: ''
                })}
                sx={{ height: '56px' }}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* Loading indicator */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}
      
      {/* Change Requests Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Title</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Requested By</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Priority</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created Date</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {requests.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  No change requests found
                </TableCell>
              </TableRow>
            ) : (
              requests.map((request) => (
                <TableRow key={request.RequestID}>
                  <TableCell>CR-{request.RequestID}</TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {request.Title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {request.Description?.substring(0, 100)}...
                    </Typography>
                  </TableCell>
                  <TableCell>{request.ChangeRequestType?.TypeName}</TableCell>
                  <TableCell>
                    {request.Requester?.FirstName} {request.Requester?.LastName}
                  </TableCell>
                  <TableCell>{request.Company?.CompanyName}</TableCell>
                  <TableCell>
                    <Chip
                      label={request.Priority}
                      color={getPriorityColor(request.Priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={request.Status}
                      color={getStatusColor(request.Status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {format(new Date(request.CreatedDate), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          href={`/it-hub/change-requests/${request.RequestID}`}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      
                      {canApprove && (request.Status === 'Submitted' || request.Status === 'UnderReview') && (
                        <>
                          <Tooltip title="Approve">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => openApprovalDialog(request.RequestID)}
                            >
                              <ApproveIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Reject">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => openRejectionDialog(request.RequestID)}
                            >
                              <RejectIcon />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                      
                      {request.Requester?.UserID === 'currentUserId' && 
                       (request.Status === 'Draft' || request.Status === 'Rejected') && (
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            href={`/it-hub/change-requests/${request.RequestID}/edit`}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination
          count={pagination.totalPages}
          page={pagination.page}
          onChange={(event, value) => setPagination({ ...pagination, page: value })}
          color="primary"
        />
      </Box>
      
      {/* Approval Dialog */}
      <Dialog open={approvalDialog.open} onClose={() => setApprovalDialog({ open: false, requestId: null, comments: '' })}>
        <DialogTitle>Approve Change Request</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to approve this change request?
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Approval Comments (Optional)"
            value={approvalDialog.comments}
            onChange={(e) => setApprovalDialog({ ...approvalDialog, comments: e.target.value })}
            placeholder="Add any comments or notes about the approval"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialog({ open: false, requestId: null, comments: '' })}>
            Cancel
          </Button>
          <Button onClick={handleApprove} variant="contained" color="success">
            Approve
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Rejection Dialog */}
      <Dialog open={rejectionDialog.open} onClose={() => setRejectionDialog({ open: false, requestId: null, reason: '' })}>
        <DialogTitle>Reject Change Request</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Please provide a reason for rejecting this change request:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Rejection Reason *"
            value={rejectionDialog.reason}
            onChange={(e) => setRejectionDialog({ ...rejectionDialog, reason: e.target.value })}
            placeholder="Explain why this request is being rejected"
            required
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectionDialog({ open: false, requestId: null, reason: '' })}>
            Cancel
          </Button>
          <Button 
            onClick={handleReject} 
            variant="contained" 
            color="error"
            disabled={!rejectionDialog.reason.trim()}
          >
            Reject
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Alert Snackbar */}
      {alert.open && (
        <Alert
          severity={alert.severity}
          onClose={() => setAlert({ open: false, message: '', severity: 'info' })}
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 9999 }}
        >
          {alert.message}
        </Alert>
      )}
    </Box>
  );
};

export default ChangeRequestList;
```

### 6.2 Dynamic Form Component

```jsx
// DynamicForm.jsx
import React, { useState } from 'react';
import {
  Box, TextField, Typography, FormControl, InputLabel, Select, MenuItem,
  Checkbox, FormControlLabel, FormGroup, Button, Paper, Divider
} from '@mui/material';

const DynamicForm = ({ schema, formData, onChange, onSubmit, submitLabel = "Submit" }) => {
  const [errors, setErrors] = useState({});
  
  const validateField = (field, value) => {
    const fieldErrors = [];
    
    if (field.required && (!value || (Array.isArray(value) && value.length === 0))) {
      fieldErrors.push(`${field.label} is required`);
    }
    
    if (field.validation) {
      const { minLength, maxLength, min, max } = field.validation;
      
      if (minLength && value && value.length < minLength) {
        fieldErrors.push(`${field.label} must be at least ${minLength} characters`);
      }
      
      if (maxLength && value && value.length > maxLength) {
        fieldErrors.push(`${field.label} must be no more than ${maxLength} characters`);
      }
      
      if (min && value && Number(value) < min) {
        fieldErrors.push(`${field.label} must be at least ${min}`);
      }
      
      if (max && value && Number(value) > max) {
        fieldErrors.push(`${field.label} must be no more than ${max}`);
      }
    }
    
    return fieldErrors;
  };
  
  const handleFieldChange = (fieldName, value) => {
    onChange(fieldName, value);
    
    // Clear errors for this field
    if (errors[fieldName]) {
      setErrors({ ...errors, [fieldName]: undefined });
    }
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    let hasErrors = false;
    
    schema.sections.forEach(section => {
      section.fields.forEach(field => {
        const fieldErrors = validateField(field, formData[field.name]);
        if (fieldErrors.length > 0) {
          newErrors[field.name] = fieldErrors;
          hasErrors = true;
        }
      });
    });
    
    setErrors(newErrors);
    
    if (!hasErrors) {
      onSubmit(formData);
    }
  };
  
  const renderField = (field) => {
    const value = formData[field.name] || '';
    const fieldErrors = errors[field.name];
    
    switch (field.type) {
      case 'text':
        return (
          <TextField
            fullWidth
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            error={!!fieldErrors}
            helperText={fieldErrors ? fieldErrors[0] : ''}
          />
        );
      
      case 'textarea':
        return (
          <TextField
            fullWidth
            multiline
            rows={4}
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            error={!!fieldErrors}
            helperText={fieldErrors ? fieldErrors[0] : ''}
          />
        );
      
      case 'number':
        return (
          <TextField
            fullWidth
            type="number"
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            error={!!fieldErrors}
            helperText={fieldErrors ? fieldErrors[0] : ''}
            inputProps={{
              min: field.validation?.min,
              max: field.validation?.max
            }}
          />
        );
      
      case 'select':
        return (
          <FormControl fullWidth error={!!fieldErrors}>
            <InputLabel required={field.required}>{field.label}</InputLabel>
            <Select
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              label={field.label}
            >
              {field.options.map((option, index) => (
                <MenuItem key={index} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
            {fieldErrors && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                {fieldErrors[0]}
              </Typography>
            )}
          </FormControl>
        );
      
      case 'checkbox':
        const checkboxValues = Array.isArray(value) ? value : [];
        return (
          <FormControl component="fieldset" error={!!fieldErrors}>
            <Typography variant="body2" component="legend" gutterBottom>
              {field.label} {field.required && '*'}
            </Typography>
            <FormGroup>
              {field.options.map((option, index) => (
                <FormControlLabel
                  key={index}
                  control={
                    <Checkbox
                      checked={checkboxValues.includes(option)}
                      onChange={(e) => {
                        let newValues;
                        if (e.target.checked) {
                          newValues = [...checkboxValues, option];
                        } else {
                          newValues = checkboxValues.filter(v => v !== option);
                        }
                        handleFieldChange(field.name, newValues);
                      }}
                    />
                  }
                  label={option}
                />
              ))}
            </FormGroup>
            {fieldErrors && (
              <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                {fieldErrors[0]}
              </Typography>
            )}
          </FormControl>
        );
      
      case 'date':
        return (
          <TextField
            fullWidth
            type="date"
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            required={field.required}
            error={!!fieldErrors}
            helperText={fieldErrors ? fieldErrors[0] : ''}
            InputLabelProps={{ shrink: true }}
          />
        );
      
      default:
        return null;
    }
  };
  
  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        {schema.title}
      </Typography>
      
      <form onSubmit={handleSubmit}>
        {schema.sections.map((section, sectionIndex) => (
          <Box key={sectionIndex} sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              {section.title}
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {section.fields.map((field, fieldIndex) => (
                <Box key={fieldIndex}>
                  {renderField(field)}
                </Box>
              ))}
            </Box>
          </Box>
        ))}
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            size="large"
          >
            {submitLabel}
          </Button>
        </Box>
      </form>
    </Paper>
  );
};

export default DynamicForm;
```

### 6.3 Change Request Form Component

```jsx
// ChangeRequestForm.jsx
import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, TextField, FormControl, InputLabel,
  Select, MenuItem, Button, Stepper, Step, StepLabel, Card, CardContent,
  Alert, CircularProgress, Chip
} from '@mui/material';
import { Save as SaveIcon, Send as SendIcon } from '@mui/icons-material';
import DynamicForm from './DynamicForm';

const ChangeRequestForm = ({ requestId, isEdit = false }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [requestTypes, setRequestTypes] = useState([]);
  const [selectedType, setSelectedType] = useState(null);
  const [formData, setFormData] = useState({});
  const [basicInfo, setBasicInfo] = useState({
    title: '',
    description: '',
    businessJustification: '',
    expectedBenefit: '',
    priority: 'Medium',
    requestedCompletionDate: ''
  });
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'info' });
  
  const steps = ['Basic Information', 'Request Type', 'Detailed Information', 'Review & Submit'];
  
  useEffect(() => {
    fetchRequestTypes();
    if (isEdit && requestId) {
      fetchExistingRequest();
    }
  }, [isEdit, requestId]);
  
  const fetchRequestTypes = async () => {
    try {
      const response = await fetch('/api/v1/change-request-types');
      const data = await response.json();
      setRequestTypes(data);
    } catch (error) {
      console.error('Error fetching request types:', error);
      showAlert('Failed to load request types', 'error');
    }
  };
  
  const fetchExistingRequest = async () => {
    try {
      const response = await fetch(`/api/v1/change-requests/${requestId}`);
      const data = await response.json();
      
      if (response.ok) {
        setBasicInfo({
          title: data.Title,
          description: data.Description,
          businessJustification: data.BusinessJustification || '',
          expectedBenefit: data.ExpectedBenefit || '',
          priority: data.Priority,
          requestedCompletionDate: data.RequestedCompletionDate || ''
        });
        
        if (data.TypeID) {
          const type = requestTypes.find(t => t.TypeID === data.TypeID);
          setSelectedType(type);
        }
        
        if (data.FormData) {
          setFormData(JSON.parse(data.FormData));
        }
      }
    } catch (error) {
      console.error('Error fetching existing request:', error);
      showAlert('Failed to load request details', 'error');
    }
  };
  
  const showAlert = (message, severity = 'info') => {
    setAlert({ open: true, message, severity });
  };
  
  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };
  
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  
  const validateCurrentStep = () => {
    switch (activeStep) {
      case 0:
        if (!basicInfo.title.trim() || !basicInfo.description.trim()) {
          showAlert('Please fill in all required fields', 'error');
          return false;
        }
        return true;
      case 1:
        if (!selectedType) {
          showAlert('Please select a request type', 'error');
          return false;
        }
        return true;
      default:
        return true;
    }
  };
  
  const handleFormDataChange = (fieldName, value) => {
    setFormData({
      ...formData,
      [fieldName]: value
    });
  };
  
  const saveAsDraft = async () => {
    await saveRequest('Draft');
  };
  
  const submitRequest = async () => {
    await saveRequest('Submitted');
  };
  
  const saveRequest = async (status) => {
    setLoading(true);
    try {
      const requestData = {
        ...basicInfo,
        TypeID: selectedType?.TypeID,
        Status: status,
        FormData: JSON.stringify(formData)
      };
      
      const url = isEdit ? `/api/v1/change-requests/${requestId}` : '/api/v1/change-requests';
      const method = isEdit ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      if (response.ok) {
        const data = await response.json();
        showAlert(`Request ${status.toLowerCase()} successfully`, 'success');
        
        if (!isEdit) {
          // Redirect to the new request
          setTimeout(() => {
            window.location.href = `/it-hub/change-requests/${data.RequestID}`;
          }, 2000);
        }
      } else {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to save request');
      }
    } catch (error) {
      console.error('Error saving request:', error);
      showAlert(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };
  
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Basic Request Information
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Request Title *"
                  value={basicInfo.title}
                  onChange={(e) => setBasicInfo({ ...basicInfo, title: e.target.value })}
                  placeholder="Provide a clear, descriptive title for your request"
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Description *"
                  value={basicInfo.description}
                  onChange={(e) => setBasicInfo({ ...basicInfo, description: e.target.value })}
                  placeholder="Describe what you need in detail"
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Business Justification"
                  value={basicInfo.businessJustification}
                  onChange={(e) => setBasicInfo({ ...basicInfo, businessJustification: e.target.value })}
                  placeholder="Explain why this request is needed for the business"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Expected Benefit"
                  value={basicInfo.expectedBenefit}
                  onChange={(e) => setBasicInfo({ ...basicInfo, expectedBenefit: e.target.value })}
                  placeholder="Describe the expected benefits and outcomes"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={basicInfo.priority}
                    onChange={(e) => setBasicInfo({ ...basicInfo, priority: e.target.value })}
                    label="Priority"
                  >
                    <MenuItem value="Low">Low</MenuItem>
                    <MenuItem value="Medium">Medium</MenuItem>
                    <MenuItem value="High">High</MenuItem>
                    <MenuItem value="Critical">Critical</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Requested Completion Date"
                  value={basicInfo.requestedCompletionDate}
                  onChange={(e) => setBasicInfo({ ...basicInfo, requestedCompletionDate: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </Grid>
          </Paper>
        );
      
      case 1:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Select Request Type
            </Typography>
            
            <Grid container spacing={2}>
              {requestTypes.map((type) => (
                <Grid item xs={12} sm={6} md={4} key={type.TypeID}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      border: selectedType?.TypeID === type.TypeID ? 2 : 1,
                      borderColor: selectedType?.TypeID === type.TypeID ? 'primary.main' : 'grey.300',
                      '&:hover': {
                        borderColor: 'primary.main',
                        boxShadow: 2
                      }
                    }}
                    onClick={() => setSelectedType(type)}
                  >
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {type.TypeName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {type.Description}
                      </Typography>
                      {type.EstimatedDays && (
                        <Chip
                          label={`~${type.EstimatedDays} days`}
                          size="small"
                          color="info"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        );
      
      case 2:
        return selectedType?.FormSchema ? (
          <DynamicForm
            schema={JSON.parse(selectedType.FormSchema)}
            formData={formData}
            onChange={handleFormDataChange}
            onSubmit={() => handleNext()}
            submitLabel="Continue to Review"
          />
        ) : (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Additional Information
            </Typography>
            <Typography>
              No additional information required for this request type.
            </Typography>
          </Paper>
        );
      
      case 3:
        return (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Review Your Request
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Basic Information
                    </Typography>
                    <Typography><strong>Title:</strong> {basicInfo.title}</Typography>
                    <Typography><strong>Type:</strong> {selectedType?.TypeName}</Typography>
                    <Typography><strong>Priority:</strong> {basicInfo.priority}</Typography>
                    <Typography><strong>Description:</strong> {basicInfo.description}</Typography>
                    {basicInfo.businessJustification && (
                      <Typography><strong>Business Justification:</strong> {basicInfo.businessJustification}</Typography>
                    )}
                    {basicInfo.expectedBenefit && (
                      <Typography><strong>Expected Benefit:</strong> {basicInfo.expectedBenefit}</Typography>
                    )}
                    {basicInfo.requestedCompletionDate && (
                      <Typography><strong>Requested Completion:</strong> {basicInfo.requestedCompletionDate}</Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
              
              {Object.keys(formData).length > 0 && (
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        Additional Details
                      </Typography>
                      {Object.entries(formData).map(([key, value]) => (
                        <Typography key={key}>
                          <strong>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong>{' '}
                          {Array.isArray(value) ? value.join(', ') : value}
                        </Typography>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>
              )}
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    onClick={saveAsDraft}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    Save as Draft
                  </Button>
                  <Button
                    variant="contained"
                    onClick={submitRequest}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                  >
                    Submit Request
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {isEdit ? 'Edit Change Request' : 'New Change Request'}
      </Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      {renderStepContent()}
      
      {activeStep < steps.length - 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={handleNext}
          >
            Next
          </Button>
        </Box>
      )}
      
      {alert.open && (
        <Alert
          severity={alert.severity}
          onClose={() => setAlert({ open: false, message: '', severity: 'info' })}
          sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 9999 }}
        >
          {alert.message}
        </Alert>
      )}
    </Box>
  );
};

export default ChangeRequestForm;
```

## 7. Email Notification Service

```typescript
// src/services/emailNotificationService.ts
import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';
import config from '../config/email';

export class EmailNotificationService {
  private static transporter = nodemailer.createTransporter({
    host: config.smtp.host,
    port: config.smtp.port,
    secure: config.smtp.secure,
    auth: {
      user: config.smtp.user,
      pass: config.smtp.password
    }
  });

  static async sendChangeRequestSubmittedNotification(request: any) {
    try {
      const approvers = await this.getApprovers();
      
      const mailOptions = {
        from: config.fromEmail,
        to: approvers.map(a => a.email).join(', '),
        subject: `New Change Request: ${request.Title} (CR-${request.RequestID})`,
        html: `
          <h2>New Change Request Submitted</h2>
          <p><strong>Request ID:</strong> CR-${request.RequestID}</p>
          <p><strong>Title:</strong> ${request.Title}</p>
          <p><strong>Requested By:</strong> ${request.Requester?.FirstName} ${request.Requester?.LastName}</p>
          <p><strong>Company:</strong> ${request.Company?.CompanyName}</p>
          <p><strong>Priority:</strong> ${request.Priority}</p>
          <p><strong>Description:</strong> ${request.Description}</p>
          
          <p><a href="${config.portalUrl}/it-hub/change-requests/${request.RequestID}">View Request Details</a></p>
          
          <p>Please review and take appropriate action.</p>
        `
      };
      
      await this.transporter.sendMail(mailOptions);
      logger.info(`Change request submission notification sent for CR-${request.RequestID}`);
    } catch (error) {
      logger.error('Error sending change request submission notification', error);
    }
  }

  static async sendChangeRequestApprovalNotification(request: any) {
    try {
      const developmentTeam = await this.getDevelopmentTeam();
      
      const mailOptions = {
        from: config.fromEmail,
        to: developmentTeam.map(d => d.email).join(', '),
        cc: request.Requester?.Email,
        subject: `Change Request Approved: ${request.Title} (CR-${request.RequestID})`,
        html: `
          <h2>Change Request Approved</h2>
          <p><strong>Request ID:</strong> CR-${request.RequestID}</p>
          <p><strong>Title:</strong> ${request.Title}</p>
          <p><strong>Requested By:</strong> ${request.Requester?.FirstName} ${request.Requester?.LastName}</p>
          <p><strong>Company:</strong> ${request.Company?.CompanyName}</p>
          <p><strong>Priority:</strong> ${request.Priority}</p>
          <p><strong>Approved By:</strong> ${request.Approver?.FirstName} ${request.Approver?.LastName}</p>
          <p><strong>Approved Date:</strong> ${new Date(request.ApprovedDate).toLocaleDateString()}</p>
          
          <h3>Description:</h3>
          <p>${request.Description}</p>
          
          ${request.BusinessJustification ? `
            <h3>Business Justification:</h3>
            <p>${request.BusinessJustification}</p>
          ` : ''}
          
          ${request.ExpectedBenefit ? `
            <h3>Expected Benefits:</h3>
            <p>${request.ExpectedBenefit}</p>
          ` : ''}
          
          <p><a href="${config.portalUrl}/it-hub/change-requests/${request.RequestID}">View Full Request Details</a></p>
          
          <p>Please review and begin implementation planning.</p>
        `
      };
      
      await this.transporter.sendMail(mailOptions);
      logger.info(`Change request approval notification sent for CR-${request.RequestID}`);
    } catch (error) {
      logger.error('Error sending change request approval notification', error);
    }
  }

  static async sendChangeRequestRejectionNotification(request: any, reason: string) {
    try {
      const mailOptions = {
        from: config.fromEmail,
        to: request.Requester?.Email,
        subject: `Change Request Rejected: ${request.Title} (CR-${request.RequestID})`,
        html: `
          <h2>Change Request Rejected</h2>
          <p><strong>Request ID:</strong> CR-${request.RequestID}</p>
          <p><strong>Title:</strong> ${request.Title}</p>
          <p><strong>Status:</strong> Rejected</p>
          
          <h3>Rejection Reason:</h3>
          <p>${reason}</p>
          
          <p><a href="${config.portalUrl}/it-hub/change-requests/${request.RequestID}">View Request Details</a></p>
          
          <p>You may revise and resubmit your request if needed.</p>
        `
      };
      
      await this.transporter.sendMail(mailOptions);
      logger.info(`Change request rejection notification sent for CR-${request.RequestID}`);
    } catch (error) {
      logger.error('Error sending change request rejection notification', error);
    }
  }

  private static async getApprovers() {
    // Get users with Admin or ChangeManager roles
    // Implementation depends on your user/role structure
    return [
      { email: '<EMAIL>', name: 'Admin' },
      { email: '<EMAIL>', name: 'Change Manager' }
    ];
  }

  private static async getDevelopmentTeam() {
    // Get development team email addresses
    return [
      { email: '<EMAIL>', name: 'Development Team' },
      { email: '<EMAIL>', name: 'Technical Lead' }
    ];
  }
}
```

## 8. API Routes

```typescript
// src/routes/changeRequestRoutes.ts
import { Router } from 'express';
import {
  getChangeRequests,
  getChangeRequest,
  createNewChangeRequest,
  updateExistingChangeRequest,
  submitRequest,
  approveRequest,
  rejectRequest
} from '../controllers/changeRequestController';
import { authenticateJwt } from '../middlewares/auth';
import { requireRole } from '../middlewares/roleCheck';
import { 
  createChangeRequestValidator,
  updateChangeRequestValidator,
  getChangeRequestsValidator 
} from '../validators/changeRequestValidator';
import { validateRequest } from '../middlewares/validation';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateJwt);

// GET /v1/change-requests - List change requests
router.get(
  '/',
  getChangeRequestsValidator,
  validateRequest,
  getChangeRequests
);

// GET /v1/change-requests/:requestId - Get change request details
router.get(
  '/:requestId',
  validateRequest,
  getChangeRequest
);

// POST /v1/change-requests - Create new change request
router.post(
  '/',
  createChangeRequestValidator,
  validateRequest,
  createNewChangeRequest
);

// PUT /v1/change-requests/:requestId - Update change request
router.put(
  '/:requestId',
  updateChangeRequestValidator,
  validateRequest,
  updateExistingChangeRequest
);

// POST /v1/change-requests/:requestId/submit - Submit change request
router.post(
  '/:requestId/submit',
  submitRequest
);

// POST /v1/change-requests/:requestId/approve - Approve change request (Admin/ChangeManager only)
router.post(
  '/:requestId/approve',
  requireRole(['Admin', 'ChangeManager']),
  approveRequest
);

// POST /v1/change-requests/:requestId/reject - Reject change request (Admin/ChangeManager only)
router.post(
  '/:requestId/reject',
  requireRole(['Admin', 'ChangeManager']),
  rejectRequest
);

export default router;
```

## 9. Database Seed Data

```sql
-- Insert default change request types
INSERT INTO ChangeRequestTypes (TypeName, Description, FormSchema, RequiresApproval, ApprovalLevel, EstimatedDays, IsActive, CreatedBy, CreatedDate)
VALUES 
('New Application Development', 'Request for development of a new application or system', 
 '{"title":"New Application Development Request","sections":[...]}', -- JSON schema from section 5.1
 1, 'Admin', 30, 1, 1, GETDATE()),

('System Modification', 'Request to modify existing system functionality', 
 '{"title":"System Modification Request","sections":[...]}', -- JSON schema from section 5.1
 1, 'ChangeManager', 14, 1, 1, GETDATE()),

('Bug Fix', 'Request to fix a bug or issue in existing system', 
 '{"title":"Bug Fix Request","sections":[...]}', 
 1, 'ChangeManager', 7, 1, 1, GETDATE()),

('Data Request', 'Request for data extraction, reporting, or database changes', 
 '{"title":"Data Request","sections":[...]}', 
 1, 'ChangeManager', 5, 1, 1, GETDATE()),

('Infrastructure Change', 'Request for infrastructure or server changes', 
 '{"title":"Infrastructure Change Request","sections":[...]}', 
 1, 'Admin', 10, 1, 1, GETDATE());

-- Add Change Manager role if it doesn't exist
IF NOT EXISTS (SELECT 1 FROM Roles WHERE RoleName = 'ChangeManager')
BEGIN
    INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate)
    VALUES ('ChangeManager', 'Change Management approval authority', 1, 1, 1, GETDATE());
END
```

## 10. Security and Permissions

### 10.1 Permission Matrix

| Action | Employee | Manager | Admin | Change Manager |
|--------|----------|---------|-------|----------------|
| View Public Queue | ✓ | ✓ | ✓ | ✓ |
| Create Request | ✓ | ✓ | ✓ | ✓ |
| Edit Own Draft | ✓ | ✓ | ✓ | ✓ |
| Submit Request | ✓ | ✓ | ✓ | ✓ |
| Approve Request | ✗ | ✗ | ✓ | ✓ |
| Reject Request | ✗ | ✗ | ✓ | ✓ |
| View All Requests | ✗ | ✗ | ✓ | ✓ |
| Assign to Dev Team | ✗ | ✗ | ✓ | ✓ |

### 10.2 Data Privacy

- Personal information is only visible to the requester and approvers
- Request details are publicly visible for transparency unless marked as confidential
- All actions are logged for audit purposes
- Email notifications only go to authorized recipients

## 11. Integration Points

### 11.1 Falcon Portal Integration

The Change Management module integrates seamlessly with the existing Falcon Portal:

- **Navigation**: Added to IT Hub menu
- **Notifications**: Integrated with portal notification system
- **User Management**: Uses existing user authentication and roles
- **Dashboard**: Summary widgets for pending approvals

### 11.2 Future Enhancements

1. **ITSM Integration**: Connect with external ITSM tools
2. **Project Management**: Link approved changes to project tracking
3. **Reporting Dashboard**: Advanced analytics and reporting
4. **Mobile App**: Native mobile support for approvals
5. **AI Suggestions**: Smart categorization and effort estimation

This comprehensive Change Management Module provides a complete solution for managing IT change requests within the Falcon Portal, offering transparency, proper approval workflows, and automated notifications while maintaining security and audit compliance.