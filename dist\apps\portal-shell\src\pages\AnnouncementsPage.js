"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const dashboardApi_1 = require("../services/dashboardApi");
const date_fns_1 = require("date-fns");
const react_router_dom_1 = require("react-router-dom");
// --- Helper Functions (Similar to Dashboard) ---
// TODO: Move shared helpers to a utility file
const getSeverityBorder = (severity) => {
    switch (severity) {
        case 'high': return 'border-red-500';
        case 'medium': return 'border-yellow-500';
        case 'low': return 'border-gray-400';
        default: return 'border-gray-500';
    }
};
const formatRelativeTime = (isoDate) => {
    try {
        return (0, date_fns_1.formatDistanceToNow)(new Date(isoDate), { addSuffix: true });
    }
    catch (_e) {
        return 'Invalid date';
    }
};
const AnnouncementsPage = () => {
    const [announcements, setAnnouncements] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    const navigate = (0, react_router_dom_1.useNavigate)();
    // --- Mock User Context (Replace with actual context later) ---
    const MOCK_USER_COMPANY_ID = 'SASMOS HET'; // Example
    (0, react_1.useEffect)(() => {
        const loadAnnouncements = () => __awaiter(void 0, void 0, void 0, function* () {
            setLoading(true);
            setError(null);
            try {
                // Pass mock company ID for filtering
                const data = yield (0, dashboardApi_1.fetchAllAnnouncements)(MOCK_USER_COMPANY_ID);
                setAnnouncements(data);
            }
            catch (err) {
                console.error("Failed to load announcements:", err);
                setError("Failed to load announcements.");
            }
            setLoading(false);
        });
        loadAnnouncements();
    }, []); // Fetch once on mount
    const handleDetailsClick = (link) => {
        if (link.startsWith('/')) {
            navigate(link);
        }
        else {
            // Assuming external link
            window.open(link, '_blank', 'noopener,noreferrer');
        }
    };
    return (<div>
      <h1 className="text-2xl font-bold mb-6">Announcements</h1>
      
      {loading && <div className="text-center text-gray-500 py-6">Loading announcements...</div>}
      {error && <div className="text-center text-red-600 py-6">Error: {error}</div>}
      
      {!loading && !error && (<div className="space-y-4">
          {announcements.length === 0 && (<p className="text-gray-600">No announcements found.</p>)}
          {announcements.map(ann => (<div key={ann.id} className={`bg-white p-4 rounded shadow border-l-4 ${getSeverityBorder(ann.severity)}`}>
              <div className="flex justify-between items-start mb-1">
                <h2 className="text-lg font-semibold">{ann.title}</h2>
                <span className="text-xs text-gray-500 whitespace-nowrap ml-4">{formatRelativeTime(ann.publishedAt)}</span>
              </div>
              <p className="text-sm text-gray-700 mb-2">{ann.description}</p>
              <div className="text-xs text-gray-500 flex items-center justify-between">
                {ann.scope !== 'Group-wide' ? (<span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20">
                        {ann.scope}
                    </span>) : (<span className="inline-flex items-center rounded-md bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                         Group-wide
                    </span>)}
                {ann.link && (<button onClick={() => handleDetailsClick(ann.link)} className="text-sm text-blue-600 hover:underline">
                    Details
                  </button>)}
              </div>
            </div>))}
        </div>)}
      {/* TODO: Add pagination controls if needed */}
    </div>);
};
exports.default = AnnouncementsPage;
//# sourceMappingURL=AnnouncementsPage.js.map