"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePortalUser = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const userManagementService_1 = require("../shared/services/userManagementService");
const graphService_1 = require("../shared/services/graphService");
const sql = __importStar(require("mssql"));
// Define required role(s) for authorization
const REQUIRED_ROLE = 'Administrator'; // Users who can update other users
async function updatePortalUser(request, context) {
    context.log = logger_1.logger.info;
    logger_1.logger.info('UpdatePortalUser function invoked.');
    // 1. Authentication & Authorization
    let authenticatedUserId;
    const isLocalDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    if (isLocalDevelopment || process.env.NODE_ENV === 'test') {
        logger_1.logger.warn('UpdatePortalUser: Bypassing authentication check in development/test mode.');
        authenticatedUserId = 1; // Use default user ID for development
    }
    else {
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            logger_1.logger.warn("UpdatePortalUser: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }
        if (!(0, authUtils_1.hasRequiredRole)(principal, [REQUIRED_ROLE])) {
            logger_1.logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted to update user without required role '${REQUIRED_ROLE}'.`);
            return { status: 403, jsonBody: { error: "Forbidden. User does not have the required permissions." } };
        }
        const authUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!authUserId) {
            logger_1.logger.error(`UpdatePortalUser: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
            return { status: 403, jsonBody: { error: "Forbidden. Authenticated user not found or inactive in the portal database." } };
        }
        authenticatedUserId = authUserId;
    }
    logger_1.logger.info(`UpdatePortalUser invoked by UserID: ${authenticatedUserId}`);
    // 2. Extract Entra ID from route parameter
    const entraId = request.params.entraId;
    if (!entraId) {
        logger_1.logger.warn("UpdatePortalUser: Entra ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
    }
    // 3. Parse and validate request body
    let parsedBody;
    try {
        parsedBody = await request.json();
    }
    catch (error) {
        logger_1.logger.error('UpdatePortalUser: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { error: "Invalid JSON in request body." } };
    }
    // Basic validation
    if (!parsedBody || typeof parsedBody !== 'object') {
        return { status: 400, jsonBody: { error: "Request body must be a valid object." } };
    }
    const { roles: requestedRoleNames, isActive: requestedIsActive, name: requestedName, company: requestedCompany, syncFromEntra } = parsedBody;
    try {
        // 4. Get UserID and current status/roles for the target user (by EntraID)
        const getUserInfoQuery = `
            SELECT u.UserID, u.IsActive, u.FirstName, u.LastName, u.CompanyID,
                   STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) as CurrentRoleNames
            FROM Users u
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE u.EntraID = @EntraID
            GROUP BY u.UserID, u.IsActive, u.FirstName, u.LastName, u.CompanyID;
        `;
        const userInfoParams = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const userInfoResult = await (0, db_1.executeQuery)(getUserInfoQuery, userInfoParams);
        if (!userInfoResult.recordset || userInfoResult.recordset.length === 0) {
            logger_1.logger.warn(`UpdatePortalUser: Target user with EntraID ${entraId} not found.`);
            return { status: 404, jsonBody: { error: `User with EntraID ${entraId} not found.` } };
        }
        const userId = userInfoResult.recordset[0].UserID;
        const currentIsActive = userInfoResult.recordset[0].IsActive;
        const currentRoleNames = userInfoResult.recordset[0].CurrentRoleNames
            ? userInfoResult.recordset[0].CurrentRoleNames.split(',').filter((r) => r.trim())
            : [];
        logger_1.logger.info(`Updating UserID: ${userId}. Current Status: ${currentIsActive}, Current Roles: [${currentRoleNames.join(',')}]`);
        logger_1.logger.info(`Requested Status: ${requestedIsActive}, Requested Roles: [${requestedRoleNames?.join(',') || 'no change'}], Sync from Entra: ${syncFromEntra}`);
        // 5. Sync from Entra ID first if requested
        if (syncFromEntra) {
            try {
                logger_1.logger.info(`Syncing user data from Entra ID for UserID ${userId}`);
                const entraData = await graphService_1.graphService.syncUserDataFromEntra(entraId);
                if (entraData) {
                    const syncQuery = `
                        UPDATE Users SET 
                            FirstName = @FirstName,
                            LastName = @LastName,
                            Email = @Email,
                            ContactNumber = @ContactNumber,
                            ModifiedBy = @ModifiedBy,
                            ModifiedDate = GETUTCDATE()
                        WHERE UserID = @UserID;
                    `;
                    const syncParams = [
                        { name: 'UserID', type: sql.Int, value: userId },
                        { name: 'FirstName', type: sql.NVarChar, value: entraData.firstName },
                        { name: 'LastName', type: sql.NVarChar, value: entraData.lastName },
                        { name: 'Email', type: sql.NVarChar, value: entraData.email },
                        { name: 'ContactNumber', type: sql.NVarChar, value: entraData.contactNumber },
                        { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
                    ];
                    await (0, db_1.executeQuery)(syncQuery, syncParams);
                    logger_1.logger.info(`Successfully synced user data from Entra ID for UserID ${userId}`);
                }
                else {
                    logger_1.logger.warn(`Could not retrieve data from Entra ID for user ${entraId}, continuing with manual updates`);
                }
            }
            catch (error) {
                logger_1.logger.error(`Error syncing from Entra ID for user ${entraId}:`, error);
                // Continue with manual updates even if sync fails
                logger_1.logger.info(`Continuing with manual user updates despite Entra sync failure`);
            }
        }
        // 6. Update User Status (IsActive) if provided
        if (typeof requestedIsActive === 'boolean' && requestedIsActive !== currentIsActive) {
            logger_1.logger.info(`Updating IsActive status for UserID ${userId} to ${requestedIsActive}`);
            const updateStatusQuery = `UPDATE Users SET IsActive = @IsActive, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
            const statusParams = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'IsActive', type: sql.Bit, value: requestedIsActive },
                { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
            ];
            await (0, db_1.executeQuery)(updateStatusQuery, statusParams);
            logger_1.logger.info(`Successfully updated IsActive status for UserID ${userId}`);
        }
        // 7. Update User Name (FirstName/LastName) if provided and not synced from Entra
        if (requestedName && typeof requestedName === 'string' && requestedName.trim() && !syncFromEntra) {
            logger_1.logger.info(`Updating name for UserID ${userId} to '${requestedName}'`);
            const nameParts = requestedName.trim().split(/\s+/);
            const firstName = nameParts[0] || '';
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
            const updateNameQuery = `UPDATE Users SET FirstName = @FirstName, LastName = @LastName, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
            const nameParams = [
                { name: 'UserID', type: sql.Int, value: userId },
                { name: 'FirstName', type: sql.NVarChar, value: firstName },
                { name: 'LastName', type: sql.NVarChar, value: lastName },
                { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
            ];
            await (0, db_1.executeQuery)(updateNameQuery, nameParams);
            logger_1.logger.info(`Successfully updated name for UserID ${userId}`);
        }
        // 8. Update User Company (CompanyID) if provided
        if (requestedCompany && typeof requestedCompany === 'string' && requestedCompany.trim()) {
            logger_1.logger.info(`Updating company for UserID ${userId} to '${requestedCompany}'`);
            const getCompanyIdQuery = `SELECT CompanyID FROM Companies WHERE CompanyName = @CompanyName AND IsActive = 1;`;
            const companyParams = [
                { name: 'CompanyName', type: sql.NVarChar, value: requestedCompany.trim() }
            ];
            const companyResult = await (0, db_1.executeQuery)(getCompanyIdQuery, companyParams);
            if (companyResult.recordset && companyResult.recordset.length > 0) {
                const companyId = companyResult.recordset[0].CompanyID;
                const updateCompanyQuery = `UPDATE Users SET CompanyID = @CompanyID, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
                const updateCompanyParams = [
                    { name: 'UserID', type: sql.Int, value: userId },
                    { name: 'CompanyID', type: sql.Int, value: companyId },
                    { name: 'ModifiedBy', type: sql.Int, value: authenticatedUserId }
                ];
                await (0, db_1.executeQuery)(updateCompanyQuery, updateCompanyParams);
                logger_1.logger.info(`Successfully updated CompanyID for UserID ${userId} to ${companyId}`);
            }
            else {
                logger_1.logger.warn(`Company '${requestedCompany}' not found or inactive. Skipping company update for UserID ${userId}.`);
            }
        }
        // 9. Handle Role Updates if provided
        if (requestedRoleNames && Array.isArray(requestedRoleNames)) {
            logger_1.logger.info(`Processing role updates for UserID ${userId}`);
            const desiredRoles = new Set(requestedRoleNames.map(role => String(role).trim()).filter(r => r));
            const currentRoles = new Set(currentRoleNames.map((role) => String(role).trim()).filter((r) => r));
            const rolesToAdd = [...desiredRoles].filter(role => !currentRoles.has(role));
            const rolesToRemove = [...currentRoles].filter(role => !desiredRoles.has(role));
            logger_1.logger.info(`Roles to add: [${rolesToAdd.join(', ')}], Roles to remove: [${rolesToRemove.join(', ')}]`);
            if (rolesToAdd.length > 0 || rolesToRemove.length > 0) {
                const allRoleNames = [...rolesToAdd, ...rolesToRemove];
                if (allRoleNames.length > 0) {
                    // Get role IDs for the role names
                    const getRoleIdsQuery = `SELECT RoleName, RoleID FROM Roles WHERE RoleName IN (${allRoleNames.map((_, i) => `@RoleName${i}`).join(', ')}) AND IsActive = 1`;
                    const roleIdParams = allRoleNames.map((name, i) => ({
                        name: `RoleName${i}`,
                        type: sql.NVarChar,
                        value: name
                    }));
                    const roleIdsResult = await (0, db_1.executeQuery)(getRoleIdsQuery, roleIdParams);
                    const roleIdMap = new Map();
                    roleIdsResult.recordset.forEach((row) => {
                        roleIdMap.set(row.RoleName, row.RoleID);
                    });
                    // Process role removals
                    for (const roleName of rolesToRemove) {
                        const roleId = roleIdMap.get(roleName);
                        if (roleId) {
                            logger_1.logger.info(`Removing role '${roleName}' (ID: ${roleId}) from UserID ${userId}`);
                            await (0, userManagementService_1.removeRoleFromUser)(userId, roleId, authenticatedUserId);
                        }
                        else {
                            logger_1.logger.warn(`Role name '${roleName}' not found in Roles table. Skipping removal.`);
                        }
                    }
                    // Process role additions
                    for (const roleName of rolesToAdd) {
                        const roleId = roleIdMap.get(roleName);
                        if (roleId) {
                            logger_1.logger.info(`Adding role '${roleName}' (ID: ${roleId}) to UserID ${userId}`);
                            await (0, userManagementService_1.assignRoleToUser)(userId, roleId, authenticatedUserId);
                        }
                        else {
                            logger_1.logger.warn(`Role name '${roleName}' not found in Roles table. Skipping addition.`);
                        }
                    }
                }
            }
        }
        logger_1.logger.info(`Successfully updated portal user with UserID ${userId}.`);
        return {
            status: 200,
            jsonBody: {
                message: "User updated successfully.",
                userId: userId,
                syncedFromEntra: syncFromEntra || false
            }
        };
    }
    catch (error) {
        logger_1.logger.error(`Error in UpdatePortalUser function for EntraID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            jsonBody: {
                error: "An unexpected error occurred while updating the user.",
                details: errorMessage
            }
        };
    }
}
exports.updatePortalUser = updatePortalUser;
// Register the function
functions_1.app.http('updatePortalUser', {
    methods: ['PUT'],
    route: 'portal-users/{entraId}',
    authLevel: 'anonymous',
    handler: updatePortalUser
});
//# sourceMappingURL=UpdatePortalUser.js.map