-- =============================================
-- Add Deployment Date Fields to ChangeRequests Table
-- Purpose: Add calendar support to existing change management system
-- =============================================

-- Check if the DeploymentDate column already exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ChangeRequests' AND COLUMN_NAME = 'DeploymentDate')
BEGIN
    PRINT 'Adding deployment date fields to ChangeRequests table...';
    
    -- Add calendar-specific fields to ChangeRequests table
    ALTER TABLE ChangeRequests 
    ADD 
        -- Deployment scheduling fields
        DeploymentDate DATETIME2 NULL,
        DeploymentDuration INT NULL, -- Duration in minutes
        DeploymentLocation NVARCHAR(100) NULL, -- 'Production', 'Staging', 'Development'
        DeploymentType NVARCHAR(50) NULL, -- 'Automatic', 'Manual', 'Emergency'
        
        -- Calendar display fields
        CalendarColor NVARCHAR(7) NULL, -- Hex color for calendar events
        RequiresSystemDowntime BIT DEFAULT 0;

    PRINT 'Deployment date fields added successfully.';
END
ELSE
BEGIN
    PRINT 'Deployment date fields already exist in ChangeRequests table.';
END

-- Add check constraints for new fields
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentLocation')
BEGIN
    ALTER TABLE ChangeRequests 
    ADD CONSTRAINT CK_ChangeRequests_DeploymentLocation 
        CHECK (DeploymentLocation IN ('Development', 'Staging', 'Production', 'All Environments') OR DeploymentLocation IS NULL);
    
    PRINT 'Added deployment location constraint.';
END

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentType')
BEGIN
    ALTER TABLE ChangeRequests 
    ADD CONSTRAINT CK_ChangeRequests_DeploymentType 
        CHECK (DeploymentType IN ('Automatic', 'Manual', 'Emergency', 'Scheduled') OR DeploymentType IS NULL);
    
    PRINT 'Added deployment type constraint.';
END

-- Create indexes for calendar queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChangeRequests_DeploymentDate')
BEGIN
    CREATE NONCLUSTERED INDEX IX_ChangeRequests_DeploymentDate 
        ON ChangeRequests(DeploymentDate) 
        WHERE DeploymentDate IS NOT NULL;
    
    PRINT 'Created deployment date index.';
END

-- Update existing change requests with deployment dates based on RequestedCompletionDate
UPDATE ChangeRequests 
SET 
    DeploymentDate = CASE 
        WHEN RequestedCompletionDate IS NOT NULL THEN 
            CAST(RequestedCompletionDate AS DATETIME2)
        WHEN PlannedCompletionDate IS NOT NULL THEN 
            CAST(PlannedCompletionDate AS DATETIME2)
        ELSE NULL
    END,
    DeploymentDuration = 
        CASE Priority
            WHEN 'Critical' THEN 30
            WHEN 'High' THEN 60
            WHEN 'Medium' THEN 120
            WHEN 'Low' THEN 240
            ELSE 60
        END,
    DeploymentLocation = 'Production',
    DeploymentType = 'Manual',
    RequiresSystemDowntime = 
        CASE 
            WHEN Priority IN ('Critical', 'High') THEN 1
            ELSE 0
        END,
    CalendarColor = 
        CASE Priority
            WHEN 'Critical' THEN '#FF4444'
            WHEN 'High' THEN '#FF8800'
            WHEN 'Medium' THEN '#4488FF'
            WHEN 'Low' THEN '#44AA44'
            ELSE '#666666'
        END
WHERE 
    DeploymentDate IS NULL 
    AND Status IN ('Approved', 'Ready for Deployment', 'Deployed', 'In Development', 'Completed')
    AND (RequestedCompletionDate IS NOT NULL OR PlannedCompletionDate IS NOT NULL);

-- Verify the update
SELECT COUNT(*) as UpdatedRecords
FROM ChangeRequests 
WHERE DeploymentDate IS NOT NULL;

PRINT 'Deployment date fields setup completed successfully!'; 