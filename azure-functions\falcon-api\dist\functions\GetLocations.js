"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLocations = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function getLocations(req, context) {
    logger_1.logger.info("GetLocations: Processing request");
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = principal?.userId || 'dev-user';
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        if (!isDevelopment && principal) {
            const internalUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await (0, db_1.executeQuery)(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        logger_1.logger.info(`GetLocations: Processing request for user: ${userId}, company: ${userCompanyId}`);
        // Query to get locations with employee counts
        const query = `
            SELECT 
                l.LocationID,
                l.LocationName,
                l.CompanyID,
                c.CompanyName,
                l.Address,
                l.City,
                l.State,
                l.Country,
                l.PostalCode,
                l.IsActive,
                COUNT(u.UserID) as EmployeeCount
            FROM Locations l
            LEFT JOIN Companies c ON l.CompanyID = c.CompanyID
            LEFT JOIN Users u ON l.LocationID = u.LocationID AND u.IsActive = 1
            WHERE l.CompanyID = @CompanyID AND l.IsActive = 1
            GROUP BY l.LocationID, l.LocationName, l.CompanyID, c.CompanyName, l.Address, l.City, l.State, l.Country, l.PostalCode, l.IsActive
            ORDER BY l.LocationName;
        `;
        const parameters = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];
        logger_1.logger.info(`GetLocations: Executing query for company ${userCompanyId}`);
        const result = await (0, db_1.executeQuery)(query, parameters);
        if (!result.recordset) {
            logger_1.logger.info(`GetLocations: No locations found`);
            return {
                status: 200,
                jsonBody: {
                    locations: []
                }
            };
        }
        // Map results to Location interface
        const locations = result.recordset.map((row) => ({
            id: row.LocationID,
            name: row.LocationName,
            companyId: row.CompanyID,
            companyName: row.CompanyName,
            address: row.Address,
            city: row.City,
            state: row.State,
            country: row.Country,
            postalCode: row.PostalCode,
            isActive: row.IsActive,
            employeeCount: row.EmployeeCount || 0
        }));
        logger_1.logger.info(`GetLocations: Found ${locations.length} locations`);
        return {
            status: 200,
            jsonBody: {
                locations
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetLocations: Error processing request:", error);
        return {
            status: 500,
            jsonBody: {
                error: "Internal server error",
                details: error instanceof Error ? error.message : String(error)
            }
        };
    }
}
exports.getLocations = getLocations;
// Register the function
functions_1.app.http('GetLocations', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'locations',
    handler: getLocations
});
//# sourceMappingURL=GetLocations.js.map