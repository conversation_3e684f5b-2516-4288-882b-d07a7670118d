"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUser = updateUser;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const userManagementService_1 = require("../shared/services/userManagementService");
const authUtils_1 = require("../shared/authUtils");
const validationSchemas_1 = require("../shared/validationSchemas");
const sql = require("mssql");
// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Portal Admin'; // Or from config/env
function updateUser(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function UpdateUser processed request for url "${request.url}"`);
        logger_1.logger.info('UpdateUser function invoked.');
        // --- Authentication & Authorization --- 
        // Log the environment variable value for debugging
        logger_1.logger.warn(`Current NODE_ENV: [${process.env.NODE_ENV}]`);
        // Bypass auth check for local development where header is not present
        let authenticatedUserId = 1; // Default for development mode
        if (process.env.NODE_ENV !== 'development') {
            const principal = (0, authUtils_1.getClientPrincipal)(request);
            if (!principal) {
                return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
            }
            if (!(0, authUtils_1.hasRequiredRole)(principal, [REQUIRED_ROLE])) {
                logger_1.logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
                return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
            }
            const userIdFromPrincipal = yield (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
            if (!userIdFromPrincipal) {
                logger_1.logger.error(`UpdateUser: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
                return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
            }
            authenticatedUserId = userIdFromPrincipal;
            logger_1.logger.info(`UpdateUser invoked by UserID: ${authenticatedUserId}`);
        }
        else {
            logger_1.logger.warn("UpdateUser: Bypassing authentication check in development mode.");
        }
        // --- End Auth --- 
        // --- Input Validation --- 
        // Validate Route Parameters
        const routeParams = { entraId: request.params.entraId };
        let validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.entraIdRouteParamSchema, routeParams, context, "route parameters");
        if (validationError)
            return validationError;
        // Validation passed, use validated data
        const validatedRouteParams = validationSchemas_1.entraIdRouteParamSchema.parse(routeParams);
        const entraId = validatedRouteParams.entraId; // Already a string
        // Validate Request Body
        let parsedBody;
        try {
            parsedBody = yield request.json();
        }
        catch (error) {
            logger_1.logger.error('UpdateUser: Invalid JSON in request body.', error);
            return { status: 400, jsonBody: { message: "Invalid JSON in request body." } };
        }
        validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.updateUserBodySchema, parsedBody, context, "request body");
        if (validationError)
            return validationError;
        // Validation passed, use validated data
        const validatedBody = validationSchemas_1.updateUserBodySchema.parse(parsedBody);
        const { roles: requestedRoleNames, isActive: requestedIsActive } = validatedBody;
        // --- End Validation --- 
        try {
            // Placeholder for the ID of the user performing the update
            const modifiedByUserId = authenticatedUserId;
            // 1. Get UserID and current status/roles for the target user (by EntraID)
            const getUserInfoQuery = `
            SELECT u.UserID, u.IsActive,
                   STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) as CurrentRoleNames
            FROM Users u
            LEFT JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE u.EntraID = @EntraID
            GROUP BY u.UserID, u.IsActive;
        `;
            const userInfoParams = [
                { name: 'EntraID', type: sql.NVarChar, value: entraId }
            ];
            const userInfoResult = yield (0, db_1.executeQuery)(getUserInfoQuery, userInfoParams);
            if (!userInfoResult.recordset || userInfoResult.recordset.length === 0) {
                logger_1.logger.warn(`UpdateUser: Target user with EntraID ${entraId} not found.`);
                return {
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { message: `User with EntraID ${entraId} not found.` }
                };
            }
            const userId = userInfoResult.recordset[0].UserID;
            const currentIsActive = userInfoResult.recordset[0].IsActive;
            const currentRoleNames = (userInfoResult.recordset[0].CurrentRoleNames || '').split(',').filter((role) => role.trim() !== '');
            logger_1.logger.info(`Updating UserID: ${userId}. Current Status: ${currentIsActive}, Current Roles: [${currentRoleNames.join(',')}]`);
            logger_1.logger.info(`Requested Status: ${requestedIsActive}, Requested Roles: [${requestedRoleNames.join(',')}]`);
            // 2. Update User Status (IsActive) if provided
            if (typeof requestedIsActive === 'boolean' && requestedIsActive !== currentIsActive) {
                logger_1.logger.info(`Updating IsActive status for UserID ${userId} to ${requestedIsActive}`);
                const updateStatusQuery = `UPDATE Users SET IsActive = @IsActive, ModifiedBy = @ModifiedBy, ModifiedDate = GETUTCDATE() WHERE UserID = @UserID;`;
                const statusParams = [
                    { name: 'UserID', type: sql.Int, value: userId },
                    { name: 'IsActive', type: sql.Bit, value: requestedIsActive },
                    { name: 'ModifiedBy', type: sql.Int, value: modifiedByUserId }
                ];
                yield (0, db_1.executeQuery)(updateStatusQuery, statusParams);
            }
            // 3. Handle Role Updates if provided
            if (requestedRoleNames && Array.isArray(requestedRoleNames)) {
                logger_1.logger.info(`Processing role updates for UserID ${userId}`);
                // Create Sets explicitly typed as Set<string>
                const desiredRoles = new Set(requestedRoleNames.map((role) => String(role)));
                const currentRoles = new Set(currentRoleNames.filter((r) => r).map((role) => String(role)));
                // Find roles to add and roles to remove (filter should now infer string type)
                const rolesToAdd = [...desiredRoles].filter(role => !currentRoles.has(role));
                const rolesToRemove = [...currentRoles].filter(role => !desiredRoles.has(role));
                // Avoid removing/adding protected roles directly
                const protectedRoles = ['Administrator', 'Employee'];
                // Filter should infer string type here too
                const finalRolesToAdd = rolesToAdd.filter(role => !protectedRoles.includes(role));
                const finalRolesToRemove = rolesToRemove.filter(role => !protectedRoles.includes(role));
                if (finalRolesToAdd.length > 0 || finalRolesToRemove.length > 0) {
                    const roleNamesToQuery = [...finalRolesToAdd, ...finalRolesToRemove];
                    if (roleNamesToQuery.length > 0) {
                        const getRoleIdsQuery = `SELECT RoleName, RoleID FROM Roles WHERE RoleName IN (${roleNamesToQuery.map((_, i) => `@RoleName${i}`).join(', ')})`;
                        const roleIdParams = roleNamesToQuery.map((name, i) => ({
                            name: `RoleName${i}`,
                            type: sql.NVarChar,
                            value: name // Ensure value is string
                        }));
                        const roleIdsResult = yield (0, db_1.executeQuery)(getRoleIdsQuery, roleIdParams);
                        const roleIdMap = new Map();
                        roleIdsResult.recordset.forEach(row => {
                            roleIdMap.set(row.RoleName, row.RoleID);
                        });
                        // Process removals
                        for (const roleName of finalRolesToRemove) {
                            const roleId = roleIdMap.get(roleName);
                            if (roleId) {
                                logger_1.logger.debug(`Removing role '${roleName}' (ID: ${roleId}) from UserID ${userId}`);
                                // Try calling the exported function directly
                                yield (0, userManagementService_1.removeRoleFromUser)(userId, roleId, modifiedByUserId);
                            }
                            else {
                                logger_1.logger.warn(`Role name '${roleName}' specified for removal not found in Roles table.`);
                            }
                        }
                        // Process additions
                        for (const roleName of finalRolesToAdd) {
                            const roleId = roleIdMap.get(roleName);
                            if (roleId) {
                                logger_1.logger.debug(`Adding role '${roleName}' (ID: ${roleId}) to UserID ${userId}`);
                                // Try calling the exported function directly
                                yield (0, userManagementService_1.assignRoleToUser)(userId, roleId, modifiedByUserId);
                            }
                            else {
                                logger_1.logger.warn(`Role name '${roleName}' specified for addition not found in Roles table.`);
                            }
                        }
                    }
                }
            }
            logger_1.logger.info(`Successfully updated status and roles for UserID ${userId}.`);
            // Return success, maybe return the updated user object?
            // For now, just returning success message. Frontend can re-fetch if needed.
            return {
                status: 200,
                jsonBody: { message: "User updated successfully." }
            };
        }
        catch (error) {
            logger_1.logger.error(`Error in UpdateUser function for EntraID ${entraId}:`, error);
            const errorMessage = (error instanceof Error) ? error.message : String(error);
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "An unexpected error occurred while updating the user.",
                    error: errorMessage
                }
            };
        }
    });
}
functions_1.app.http('UpdateUser', {
    methods: ['PUT'],
    authLevel: 'anonymous', // Handled by provider + code checks
    route: 'portal-users/{entraId}', // Match function.json
    handler: updateUser
});
//# sourceMappingURL=index.js.map