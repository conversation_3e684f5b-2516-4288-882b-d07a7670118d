const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

export interface ImageUploadResponse {
  success: boolean;
  message: string;
  imageUrl?: string;
  blobName?: string;
  error?: string;
}

export interface ImageUploadOptions {
  file: File;
  userId: string;
  requestId?: string;
}

class ImageUploadApi {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  /**
   * Upload an image file to Azure Blob Storage
   */
  async uploadImage(options: ImageUploadOptions): Promise<ImageUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('image', options.file);
      formData.append('userId', options.userId);
      
      if (options.requestId) {
        formData.append('requestId', options.requestId);
      }

      const response = await fetch(`${this.baseUrl}/UploadImage`, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header - let the browser set it with boundary for multipart/form-data
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result: ImageUploadResponse = await response.json();
      return result;

    } catch (error) {
      console.error('Error uploading image:', error);
      return {
        success: false,
        message: 'Failed to upload image',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate image file before upload
   */
  validateImageFile(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File too large. Maximum size is ${maxSize / (1024 * 1024)}MB`
      };
    }

    return { isValid: true };
  }

  /**
   * Generate a preview URL for a file (for immediate display before upload)
   */
  generatePreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  /**
   * Clean up preview URL to prevent memory leaks
   */
  revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url);
  }
}

export const imageUploadApi = new ImageUploadApi(); 