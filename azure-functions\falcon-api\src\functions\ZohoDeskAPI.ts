import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { getClientPrincipal } from "../shared/authUtils";
import { logger } from "../shared/utils/logger";
import { OAuthTokenService } from "../services/OAuthTokenService";

// Zoho Desk API Configuration
const ZOHO_DESK_CONFIG = {
    baseURL: 'https://desk.zoho.in/api/v1',
    orgId: process.env.ZOHO_DESK_ORG_ID || '60040351877', // Using the sharedBy ID from connection details
    serviceName: 'falconhub' // From connection details
};

// Main Zoho Desk API proxy function
async function zohoDeskAPIProxy(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Processing ${req.method} request to ${req.url}`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Processing request for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Extract API endpoint from request path
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        const zohoDeskPath = pathParts.slice(3).join('/'); // Remove /api/zoho-desk prefix
        
        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/${zohoDeskPath}${url.search}`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Prepare request options
        const requestOptions: RequestInit = {
            method: req.method,
            headers: headers
        };

        // Add body for POST/PUT/PATCH requests
        if (req.method !== 'GET' && req.method !== 'DELETE') {
            const bodyText = await req.text();
            if (bodyText) {
                requestOptions.body = bodyText;
            }
        }

        // Make request to Zoho Desk API
        logger.info(`ZohoDeskAPI: Making ${req.method} request to ${zohoDeskURL}`);
        const response = await fetch(zohoDeskURL, requestOptions);
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error processing request:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Helper function to get stored access token
async function getZohoDeskAccessToken(userId: string): Promise<string | null> {
    try {
        // Get token from database persistence service
        const token = await OAuthTokenService.getActiveToken(userId, 'zoho', 'desk');
        
        if (!token) {
            logger.warn(`ZohoDeskAPI: No tokens found for user ${userId}`);
            return null;
        }

        // Check if token is expired
        const now = new Date();
        
        if (token.expiresAt <= now) {
            logger.warn(`ZohoDeskAPI: Token expired for user ${userId}. Expired at: ${token.expiresAt.toISOString()}, Current time: ${now.toISOString()}`);
            return null;
        }

        logger.info(`ZohoDeskAPI: Found valid token for user ${userId}, expires at: ${token.expiresAt.toISOString()}`);
        logger.info(`ZohoDeskAPI: Token object keys: ${Object.keys(token).join(', ')}`);
        logger.info(`ZohoDeskAPI: Access token value: ${token.accessToken ? 'exists' : 'null/undefined'}`);
        return token.accessToken;
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting access token:", error);
        return null;
    }
}

// Specific endpoints for common operations

// Get all tickets
async function getTickets(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting tickets`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting tickets for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Extract user email for filtering tickets by Requested By field
        let userEmail: string;
        
        // Debug: Log authentication state
        logger.info(`ZohoDeskAPI: Authentication debug - principal exists: ${!!principal}, claims length: ${principal?.claims?.length || 0}, userDetails: ${principal?.userDetails}`);
        
        // Try to extract from claims first (works in production with Easy Auth)
        if (principal?.claims && principal.claims.length > 0) {
            const emailClaim = principal.claims.find(claim => 
                claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress' ||
                claim.typ === 'email' ||
                claim.typ === 'preferred_username' ||
                claim.typ === 'upn'
            );
            userEmail = emailClaim?.val || principal.userDetails || `unknown-user-${Date.now()}@company.com`;
            logger.info(`ZohoDeskAPI: Extracted email from principal claims: ${userEmail}`);
        } else {
            // Development mode: Try to extract from JWT token in Authorization header first
            const authHeader = req.headers.get('authorization');
            logger.info(`ZohoDeskAPI: Authorization header exists: ${!!authHeader}, starts with Bearer: ${authHeader?.startsWith('Bearer ')}`);
            
            if (authHeader?.startsWith('Bearer ')) {
                try {
                    const token = authHeader.replace('Bearer ', '');
                    logger.info(`ZohoDeskAPI: Attempting to decode JWT token of length: ${token.length}`);
                    const parts = token.split('.');
                    if (parts.length === 3) {
                        const [, payload] = parts;
                        const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
                        const jsonPayload = Buffer.from(base64, 'base64').toString();
                        const decodedPayload = JSON.parse(jsonPayload);
                        
                        logger.info(`ZohoDeskAPI: JWT payload keys: [${Object.keys(decodedPayload).join(', ')}]`);
                        
                        // Extract email from JWT payload (same logic as GetCurrentUser)
                        const extractedEmail = decodedPayload.preferred_username || decodedPayload.email || decodedPayload.upn;
                        
                        if (extractedEmail) {
                            userEmail = extractedEmail;
                            logger.info(`ZohoDeskAPI: ✅ Extracted email from JWT token: ${userEmail}`);
                        } else {
                            // Use a unique fallback email based on the user's OID or unique identifier
                            const userOid = decodedPayload.oid || decodedPayload.sub || decodedPayload.unique_name;
                            userEmail = userOid ? `${userOid}@company.com` : `unknown-user-${Date.now()}@company.com`;
                            logger.warn(`ZohoDeskAPI: No email found in JWT, using unique fallback: ${userEmail}`);
                        }
                    } else {
                        logger.warn(`ZohoDeskAPI: Invalid JWT format - expected 3 parts, got ${parts.length}`);
                        userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                    }
                } catch (jwtError) {
                    logger.error(`ZohoDeskAPI: Failed to decode JWT token: ${jwtError}`);
                    userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                }
            } else {
                // Fallback to principal userDetails if available, otherwise use unique fallback
                userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                logger.info(`ZohoDeskAPI: Using fallback userEmail: ${userEmail}`);
            }
        }
        
        logger.info(`ZohoDeskAPI: Getting tickets for user email: "${userEmail}"`);

        // Build query parameters - only use supported Zoho Desk API parameters
        const url = new URL(req.url);
        const queryParams = url.searchParams;
        
        // Remove unsupported parameters that cause 422 errors
        // Zoho Desk API doesn't support 'email' or 'searchStr' parameters on tickets endpoint
        queryParams.delete('email');
        queryParams.delete('searchStr');
        
        // Ensure we include contacts, departments, and assignee info for filtering
        const includeParam = queryParams.get('include');
        if (includeParam) {
            if (!includeParam.includes('contacts')) {
                queryParams.set('include', `${includeParam},contacts`);
            }
        } else {
            queryParams.set('include', 'contacts,departments,assignee');
        }
        
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/tickets?${queryParams.toString()}`;
        logger.info(`ZohoDeskAPI: Making filtered request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'GET',
            headers: headers
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk tickets`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk tickets API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        // Filter tickets by user email since Zoho Desk API doesn't support server-side filtering
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
            const allTickets = responseData.data;
            logger.info(`ZohoDeskAPI: Retrieved ${allTickets.length} total tickets from Zoho Desk`);
            
            // Debug: Log the user email we're filtering by
            logger.info(`ZohoDeskAPI: Filtering tickets for user email: "${userEmail}"`);
            
            // Debug: Log a sample ticket structure to understand the data format
            if (allTickets.length > 0) {
                logger.info(`ZohoDeskAPI: Sample ticket structure:`, {
                    ticketNumber: allTickets[0].ticketNumber,
                    email: allTickets[0].email,
                    contactEmail: allTickets[0].contactEmail,
                    contact: allTickets[0].contact,
                    requester: allTickets[0].requester,
                    contactId: allTickets[0].contactId,
                    status: allTickets[0].status,
                    subject: allTickets[0].subject
                });
                
                // Log all available fields in the first ticket for debugging
                logger.info(`ZohoDeskAPI: All fields in first ticket:`, Object.keys(allTickets[0]));
            }
            
            // PRIMARY APPROACH: Try contactId-based filtering first (most accurate per Zoho Desk API docs)
            logger.info(`ZohoDeskAPI: Trying contactId-based filtering first for user: ${userEmail}`);
            
            let finalTickets: any[] = [];
            let filteringMethod = 'unknown';
            
            try {
                // First, try to find the user's contact ID by searching contacts by email
                const contactResponse = await fetch(`${ZOHO_DESK_CONFIG.baseURL}/contacts?email=${encodeURIComponent(userEmail)}`, {
                    method: 'GET',
                    headers: headers
                });
                
                if (contactResponse.ok) {
                    const contactData = await contactResponse.json();
                    logger.info(`ZohoDeskAPI: Contact search response:`, {
                        status: contactResponse.status,
                        dataLength: contactData.data?.length || 0,
                        data: contactData.data
                    });
                    
                    if (contactData.data && contactData.data.length > 0) {
                        const userContactId = contactData.data[0].id;
                        logger.info(`ZohoDeskAPI: Found user contact ID: ${userContactId} for email: ${userEmail}`);
                        
                        // Filter tickets by contactId - this is the most accurate method
                        const contactTickets = allTickets.filter((ticket: any, index: number) => {
                            const matches = ticket.contactId === userContactId || 
                                           ticket.contactId === String(userContactId) || 
                                           String(ticket.contactId) === String(userContactId);
                            
                            // Debug: Log ticket details for first few tickets
                            if (index < 5) {
                                logger.info(`ZohoDeskAPI: Ticket ${ticket.ticketNumber} contactId analysis:`, {
                                    ticketContactId: ticket.contactId,
                                    ticketContactIdType: typeof ticket.contactId,
                                    userContactId: userContactId,
                                    userContactIdType: typeof userContactId,
                                    matches: matches,
                                    status: ticket.status,
                                    subject: ticket.subject
                                });
                            }
                            
                            return matches;
                        });
                        
                        logger.info(`ZohoDeskAPI: ✅ ContactId filtering found ${contactTickets.length} tickets for contact ID ${userContactId}`);
                        finalTickets = contactTickets;
                        filteringMethod = 'contactId';
                    } else {
                        logger.warn(`ZohoDeskAPI: No contact found for email ${userEmail} in Zoho Desk`);
                        throw new Error('No contact found');
                    }
                } else {
                    logger.error(`ZohoDeskAPI: Contact search failed with status ${contactResponse.status}`);
                    throw new Error(`Contact search failed: ${contactResponse.status}`);
                }
            } catch (contactError) {
                const errorMessage = contactError instanceof Error ? contactError.message : String(contactError);
                logger.warn(`ZohoDeskAPI: ContactId filtering failed (${errorMessage}). Falling back to email filtering...`);
                
                // FALLBACK APPROACH: Email-based filtering if contactId approach fails
                const userTickets = allTickets.filter((ticket: any, index: number) => {
                    // Check all possible email fields where user's email might be stored
                    const ticketMainEmail = ticket.email;
                    const ticketContactEmail = ticket.contactEmail;
                    const contactObjEmail = ticket.contact && ticket.contact.email;
                    const requesterObjEmail = ticket.requester && ticket.requester.email;
                
                    // Check each email field separately for better debugging
                    const mainEmailMatches = ticketMainEmail && ticketMainEmail.toLowerCase() === userEmail.toLowerCase();
                    const contactEmailMatches = ticketContactEmail && ticketContactEmail.toLowerCase() === userEmail.toLowerCase();
                    const contactObjEmailMatches = contactObjEmail && contactObjEmail.toLowerCase() === userEmail.toLowerCase();
                    const requesterEmailMatches = requesterObjEmail && requesterObjEmail.toLowerCase() === userEmail.toLowerCase();
                    
                    const matches = mainEmailMatches || contactEmailMatches || contactObjEmailMatches || requesterEmailMatches;
                    
                    // Debug: Log each ticket's email fields for first few tickets
                    if (index < 5) {
                        logger.info(`ZohoDeskAPI: Ticket ${ticket.ticketNumber} email analysis:`, {
                            mainEmail: ticketMainEmail,
                            contactEmail: ticketContactEmail,
                            contactObjEmail: contactObjEmail,
                            requesterEmail: requesterObjEmail,
                            userEmail: userEmail,
                            mainMatches: mainEmailMatches,
                            contactMatches: contactEmailMatches,
                            contactObjMatches: contactObjEmailMatches,
                            requesterMatches: requesterEmailMatches,
                            finalMatch: matches
                        });
                    }
                    
                if (matches) {
                        const matchType = mainEmailMatches ? 'main email' : 
                                        contactEmailMatches ? 'contact email' :
                                        contactObjEmailMatches ? 'contact object email' : 'requester email';
                        logger.info(`ZohoDeskAPI: ✅ Ticket ${ticket.ticketNumber} matches user via ${matchType}`);
                    } else if (index < 5) {
                        logger.info(`ZohoDeskAPI: ❌ Ticket ${ticket.ticketNumber} doesn't match any email fields`);
                }
                    
                return matches;
            });
            
                logger.info(`ZohoDeskAPI: Email filtering found ${userTickets.length} tickets for user ${userEmail}`);
            
                if (userTickets.length > 0) {
                    finalTickets = userTickets;
                    filteringMethod = 'email';
                } else {
                    // No tickets found for this user - return empty result
                    logger.info(`ZohoDeskAPI: No tickets found for user ${userEmail} using any filtering method`);
                    finalTickets = [];
                    filteringMethod = 'no-results';
                }
            }
            
            logger.info(`ZohoDeskAPI: Final result: ${finalTickets.length} tickets found using ${filteringMethod} method`);
            responseData.data = finalTickets;
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting tickets:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Create a new ticket
async function createTicket(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Creating ticket`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Creating ticket for user: ${userId}`);
        
        const ticketData = await req.json() as any;
        
        // Validate required fields
        if (!ticketData.subject || !ticketData.description) {
            return { status: 400, jsonBody: { error: "Subject and description are required" } };
        }

        // Get access token - ONLY real data, no mock data
        const accessToken = await getZohoDeskAccessToken(userId);
        
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return {
                status: 401, 
                jsonBody: { 
                    error: "No Zoho Desk authorization found. Please authorize first.",
                    authUrl: `http://localhost:7071/api/auth/zoho-desk/authorize`
                } 
            };
        }

        // Production Zoho Desk API call
        // Get user email from principal using the same logic as ticket retrieval
        let userEmail: string;
        
        // Debug: Log authentication state
        logger.info(`ZohoDeskAPI: Authentication debug - principal exists: ${!!principal}, claims length: ${principal?.claims?.length || 0}, userDetails: ${principal?.userDetails}`);
        
        // Try to extract from claims first (works in production with Easy Auth)
        if (principal?.claims && principal.claims.length > 0) {
            const emailClaim = principal.claims.find(claim => 
                claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress' ||
                claim.typ === 'email' ||
                claim.typ === 'preferred_username' ||
                claim.typ === 'upn'
            );
            userEmail = emailClaim?.val || principal.userDetails || `unknown-user-${Date.now()}@company.com`;
            logger.info(`ZohoDeskAPI: Extracted email from principal claims: ${userEmail}`);
        } else {
            // Development mode: Try to extract from JWT token in Authorization header first
            const authHeader = req.headers.get('authorization');
            logger.info(`ZohoDeskAPI: Authorization header exists: ${!!authHeader}, starts with Bearer: ${authHeader?.startsWith('Bearer ')}`);
            
            if (authHeader?.startsWith('Bearer ')) {
                try {
                    const token = authHeader.replace('Bearer ', '');
                    logger.info(`ZohoDeskAPI: Attempting to decode JWT token of length: ${token.length}`);
                    const parts = token.split('.');
                    if (parts.length === 3) {
                        const [, payload] = parts;
                        const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
                        const jsonPayload = Buffer.from(base64, 'base64').toString();
                        const decodedPayload = JSON.parse(jsonPayload);
                        
                        logger.info(`ZohoDeskAPI: JWT payload keys: [${Object.keys(decodedPayload).join(', ')}]`);
                        
                        // Extract email from JWT payload (same logic as GetCurrentUser)
                        const extractedEmail = decodedPayload.preferred_username || decodedPayload.email || decodedPayload.upn;
                        
                        if (extractedEmail) {
                            userEmail = extractedEmail;
                            logger.info(`ZohoDeskAPI: ✅ Extracted email from JWT token: ${userEmail}`);
                        } else {
                            // Use a unique fallback email based on the user's OID or unique identifier
                            const userOid = decodedPayload.oid || decodedPayload.sub || decodedPayload.unique_name;
                            userEmail = userOid ? `${userOid}@company.com` : `unknown-user-${Date.now()}@company.com`;
                            logger.warn(`ZohoDeskAPI: No email found in JWT, using unique fallback: ${userEmail}`);
                        }
                    } else {
                        logger.warn(`ZohoDeskAPI: Invalid JWT format - expected 3 parts, got ${parts.length}`);
                        userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                    }
                } catch (jwtError) {
                    logger.error(`ZohoDeskAPI: Failed to decode JWT token: ${jwtError}`);
                    userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                }
            } else {
                // Fallback to principal userDetails if available, otherwise use unique fallback
                userEmail = principal?.userDetails || `unknown-user-${Date.now()}@company.com`;
                logger.info(`ZohoDeskAPI: Using fallback userEmail: ${userEmail}`);
            }
        }
        
        const userName = principal?.userRoles?.[0] || 'Unknown User';
        
        logger.info(`ZohoDeskAPI: Creating ticket for user email: "${userEmail}"`);
        
        // Ensure contactId is set (create contact if needed)
        let contactId = ticketData.contactId;
        if (!contactId || contactId === 'auto-create') {
            // Try to create/find contact
            const foundContactId = await ensureContactExists(userEmail, userName);
            if (foundContactId) {
                contactId = foundContactId;
                logger.info(`ZohoDeskAPI: Found/created contact ID: ${contactId} (type: ${typeof contactId})`);
            } else {
                // Fallback: use a default numeric contact ID for development
                contactId = '1'; // Default to contact ID 1
                logger.warn(`No valid contact ID found, using fallback contact ID: ${contactId}`);
            }
        }

        // Ensure contactId is properly formatted for Zoho Desk API
        // Keep as string to avoid JavaScript number precision loss with large IDs
        let finalContactId: string | number = contactId;
        
        // Only convert to number if it's a small integer (to maintain compatibility)
        if (typeof contactId === 'string' && contactId.length <= 10 && !isNaN(Number(contactId))) {
            finalContactId = Number(contactId);
        }
        
        logger.info(`ZohoDeskAPI: Final contactId: ${contactId} -> ${finalContactId} (type: ${typeof finalContactId})`);

        // Ensure departmentId is valid - get from ZohoDesk if not provided or invalid
        let finalDepartmentId = ticketData.departmentId;
        
        // If no departmentId provided or it's a placeholder value, get valid departments from ZohoDesk
        if (!finalDepartmentId || finalDepartmentId === '1' || finalDepartmentId === 1) {
            logger.info(`ZohoDeskAPI: No valid departmentId provided (${finalDepartmentId}), fetching from ZohoDesk...`);
            
            try {
                // Fetch available departments from ZohoDesk
                const departmentsResponse = await fetch(`${ZOHO_DESK_CONFIG.baseURL}/departments`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Zoho-oauthtoken ${accessToken}`,
                        'orgId': ZOHO_DESK_CONFIG.orgId,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (departmentsResponse.ok) {
                    const departmentsData = await departmentsResponse.json();
                    logger.info(`ZohoDeskAPI: Fetched departments:`, departmentsData);
                    
                    if (departmentsData.data && Array.isArray(departmentsData.data) && departmentsData.data.length > 0) {
                        // Use the first enabled department
                        const firstEnabledDept = departmentsData.data.find((dept: any) => dept.isEnabled !== false);
                        if (firstEnabledDept) {
                            finalDepartmentId = firstEnabledDept.id;
                            logger.info(`ZohoDeskAPI: Using first enabled department: ${finalDepartmentId} (${firstEnabledDept.name})`);
                        } else {
                            // Use first department if none are explicitly enabled
                            finalDepartmentId = departmentsData.data[0].id;
                            logger.info(`ZohoDeskAPI: Using first available department: ${finalDepartmentId} (${departmentsData.data[0].name})`);
                        }
                    } else {
                        logger.warn(`ZohoDeskAPI: No departments found in response, using original departmentId`);
                        finalDepartmentId = ticketData.departmentId || '1';
                    }
                } else {
                    logger.warn(`ZohoDeskAPI: Failed to fetch departments (${departmentsResponse.status}), using original departmentId`);
                    finalDepartmentId = ticketData.departmentId || '1';
                }
            } catch (deptError) {
                logger.error(`ZohoDeskAPI: Error fetching departments:`, deptError);
                finalDepartmentId = ticketData.departmentId || '1';
            }
        }

        // Prepare ticket data according to Zoho Desk API format
        const zohoDeskTicketData: any = {
                subject: ticketData.subject,
                description: ticketData.description,
                priority: ticketData.priority || 'Medium',
                status: 'Open',
                channel: 'WEB',
            contactId: finalContactId, // Keep as string for large IDs to avoid precision loss
            // Use the valid departmentId we determined
            departmentId: typeof finalDepartmentId === 'string' && finalDepartmentId.length > 10 
                ? finalDepartmentId  // Keep as string for large IDs
                : Number(finalDepartmentId), // Convert to number for small IDs
            // Additional fields
            email: userEmail
        };

        // Only add productId and classification if they are provided and valid
        if (ticketData.categoryId && ticketData.categoryId !== '') {
            zohoDeskTicketData.productId = ticketData.categoryId;
        }
        
        if (ticketData.subcategory && ticketData.subcategory !== '') {
            zohoDeskTicketData.classification = ticketData.subcategory;
        }

        logger.info(`ZohoDeskAPI: Prepared ticket data for Zoho Desk:`, {
            subject: zohoDeskTicketData.subject,
            priority: zohoDeskTicketData.priority,
            productId: zohoDeskTicketData.productId || 'not provided',
            classification: zohoDeskTicketData.classification || 'not provided',
            departmentId: zohoDeskTicketData.departmentId,
            departmentIdType: typeof zohoDeskTicketData.departmentId,
            contactId: zohoDeskTicketData.contactId,
            contactIdType: typeof zohoDeskTicketData.contactId,
            originalDepartmentId: ticketData.departmentId,
            finalDepartmentId: finalDepartmentId
        });

        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/tickets`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(zohoDeskTicketData)
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk create ticket`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk create ticket API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error creating ticket:", error);
        return { status: 500, jsonBody: { error: "Failed to create ticket", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Get ticket threads (conversations)
async function getTicketThreads(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting ticket threads`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Extract ticket ID from URL
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        const ticketId = pathParts[pathParts.length - 2]; // Get ticket ID from path
        
        logger.info(`ZohoDeskAPI: Getting threads for ticket ${ticketId}`);
        
        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/tickets/${ticketId}/threads`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'GET',
            headers: headers
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk ticket threads`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk ticket threads API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting ticket threads:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Get departments
async function getDepartments(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting departments`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting departments for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { 
                status: 401, 
                jsonBody: { 
                    error: "No Zoho Desk authorization found. Please authorize first.",
                    authUrl: `http://localhost:7071/api/auth/zoho-desk/authorize`
                } 
            };
        }

        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/departments`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'GET',
            headers: headers
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk departments`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk departments API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting departments:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

export async function getCategories(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting categories`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting categories for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { 
                status: 401, 
                jsonBody: { 
                    error: "No Zoho Desk authorization found. Please authorize first.",
                    message: "Your Zoho Desk authorization has expired or was lost. Please reauthorize to continue.",
                    authUrl: `http://localhost:7071/api/auth/zoho-desk/authorize`,
                    action: "reauthorize"
                } 
            };
        }

        // Try multiple endpoints to get real categories from Zoho Desk
        const endpoints = [
            'products',           // Products can act as categories
            'categories',         // Direct categories endpoint
            'classifications',    // Classifications endpoint
            'customFields'        // Custom fields might have category options
        ];

        let categories: any[] = [];
        let successfulEndpoint = '';

        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Try each endpoint until we find one that works
        for (const endpoint of endpoints) {
            try {
                const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/${endpoint}`;
                logger.info(`ZohoDeskAPI: Trying ${endpoint} endpoint: ${zohoDeskURL}`);
                
                const response = await fetch(zohoDeskURL, {
                    method: 'GET',
                    headers: headers
                });
                
                if (response.ok) {
                    const responseData = await response.json();
                    logger.info(`ZohoDeskAPI: ${endpoint} endpoint response:`, responseData);
                    
                    // Extract data from response
                    let data = [];
                    if (Array.isArray(responseData)) {
                        data = responseData;
                    } else if (responseData.data && Array.isArray(responseData.data)) {
                        data = responseData.data;
                    } else if (responseData.products && Array.isArray(responseData.products)) {
                        data = responseData.products;
                    } else if (responseData.categories && Array.isArray(responseData.categories)) {
                        data = responseData.categories;
                    }
                    
                    if (data.length > 0) {
                        categories = data;
                        successfulEndpoint = endpoint;
                        logger.info(`ZohoDeskAPI: Successfully got ${data.length} items from ${endpoint} endpoint`);
                        break;
                    }
                } else {
                    logger.warn(`ZohoDeskAPI: ${endpoint} endpoint failed: ${response.status}`);
                }
            } catch (endpointError) {
                logger.warn(`ZohoDeskAPI: Error with ${endpoint} endpoint:`, endpointError);
                continue;
            }
        }

        // If we got real data, format it properly
        if (categories.length > 0) {
            const formattedCategories = categories.map((item: any, index: number) => ({
                id: item.id || `category-${index}`,
                name: item.name || item.productName || item.classificationName || `Category ${index + 1}`,
                description: item.description || item.productDescription || '',
                isEnabled: item.isEnabled !== false && item.isActive !== false,
                departmentId: item.departmentId || 'it-support'
            }));
            
            logger.info(`Successfully fetched ${formattedCategories.length} REAL categories from Zoho Desk ${successfulEndpoint} endpoint`);
            logger.info(`Categories details:`, formattedCategories.map(cat => ({ id: cat.id, name: cat.name, departmentId: cat.departmentId })));
        return {
            status: 200,
                jsonBody: formattedCategories
            };
        }

        // If no real categories found, create default categories based on departments
        logger.warn(`ZohoDeskAPI: No categories found from any Zoho Desk endpoint, creating default categories`);
        
        // Get departments to create default categories
        const departmentsResponse = await fetch(`${ZOHO_DESK_CONFIG.baseURL}/departments`, {
            method: 'GET',
            headers: headers
        });
        
        let defaultCategories: any[] = [];
        
        if (departmentsResponse.ok) {
            const departmentsData = await departmentsResponse.json();
            const departments = Array.isArray(departmentsData) ? departmentsData : (departmentsData.data || []);
            
            // Create default categories for each department
            departments.forEach((dept: any, index: number) => {
                const deptId = dept.id || `dept-${index}`;
                const deptName = dept.name || `Department ${index + 1}`;
                
                // Create common IT categories for each department
                const commonCategories = [
                    { suffix: 'Hardware', description: 'Hardware related issues' },
                    { suffix: 'Software', description: 'Software related issues' },
                    { suffix: 'Network', description: 'Network and connectivity issues' },
                    { suffix: 'Access', description: 'Access and permissions issues' },
                    { suffix: 'General', description: 'General support requests' }
                ];
                
                commonCategories.forEach((cat, catIndex) => {
                    defaultCategories.push({
                        id: `${deptId}-${cat.suffix.toLowerCase()}`,
                        name: `${deptName} - ${cat.suffix}`,
                        description: cat.description,
                        isEnabled: true,
                        departmentId: deptId
                    });
                });
            });
        }
        
        // If no departments either, create basic default categories
        if (defaultCategories.length === 0) {
            defaultCategories = [
                {
                    id: 'hardware',
                    name: 'Hardware Issues',
                    description: 'Computer, printer, and hardware problems',
                    isEnabled: true,
                    departmentId: 'it-support'
                },
                {
                    id: 'software',
                    name: 'Software Issues',
                    description: 'Application and software problems',
                    isEnabled: true,
                    departmentId: 'it-support'
                },
                {
                    id: 'network',
                    name: 'Network Issues',
                    description: 'Internet, WiFi, and network connectivity',
                    isEnabled: true,
                    departmentId: 'it-support'
                },
                {
                    id: 'access',
                    name: 'Access Issues',
                    description: 'Login, password, and permission problems',
                    isEnabled: true,
                    departmentId: 'it-support'
                },
                {
                    id: 'general',
                    name: 'General Support',
                    description: 'General IT support requests',
                    isEnabled: true,
                    departmentId: 'it-support'
                }
            ];
        }
        
        logger.info(`ZohoDeskAPI: Created ${defaultCategories.length} default categories`);
        return {
            status: 200,
            jsonBody: defaultCategories
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting categories:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Helper function to ensure contact exists
async function ensureContactExists(email: string, name?: string): Promise<string | null> {
    try {
        // Get access token for API calls
        const accessToken = await getZohoDeskAccessToken('dev-user');
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token available for contact operations`);
            return null;
        }

        // Search for existing contact by email
        const searchURL = `${ZOHO_DESK_CONFIG.baseURL}/contacts/search?email=${encodeURIComponent(email)}`;
        
        const searchHeaders: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        const searchResponse = await fetch(searchURL, {
            method: 'GET',
            headers: searchHeaders
        });

        if (searchResponse.ok) {
            const searchData = await searchResponse.json();
            if (searchData.data && searchData.data.length > 0) {
                logger.info(`ZohoDeskAPI: Found existing contact for ${email}: ${searchData.data[0].id}`);
                return searchData.data[0].id;
            }
        }

        // Contact not found, create new one
        logger.info(`ZohoDeskAPI: Creating new contact for ${email}`);
        
        const [firstName, ...lastNameParts] = (name || email.split('@')[0]).split(' ');
        const lastName = lastNameParts.join(' ') || '';

        const contactData = {
            firstName: firstName,
            lastName: lastName || 'User',
            email: email,
            // You might want to associate with a default account
            // accountId: 'default-account-id'
        };

        const createURL = `${ZOHO_DESK_CONFIG.baseURL}/contacts`;
        const createResponse = await fetch(createURL, {
            method: 'POST',
            headers: searchHeaders,
            body: JSON.stringify(contactData)
        });

        if (createResponse.ok) {
            const createData = await createResponse.json();
            logger.info(`ZohoDeskAPI: Created new contact for ${email}: ${createData.id}`);
            return createData.id;
        } else {
            const errorText = await createResponse.text();
            logger.error(`ZohoDeskAPI: Failed to create contact: ${createResponse.status} - ${errorText}`);
        return null;
        }
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error in ensureContactExists:", error);
        return null;
    }
}

// Get contacts
async function getContacts(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting contacts`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting contacts for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/contacts`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'GET',
            headers: headers
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk contacts`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk contacts API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting contacts:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Get agents
async function getAgents(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting agents`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting agents for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Build Zoho Desk API URL
        const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/agents`;
        logger.info(`ZohoDeskAPI: Making request to: ${zohoDeskURL}`);
        
        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Make request to Zoho Desk API
        const response = await fetch(zohoDeskURL, {
            method: 'GET',
            headers: headers
        });
        
        const responseText = await response.text();
        let responseData;
        
        try {
            responseData = JSON.parse(responseText);
        } catch (e) {
            responseData = responseText;
        }

        // Log response for debugging
        logger.info(`ZohoDeskAPI: Response ${response.status} from Zoho Desk agents`);
        
        if (!response.ok) {
            logger.error(`ZohoDeskAPI: Zoho Desk agents API error: ${response.status} - ${responseText}`);
            return { 
                status: response.status, 
                jsonBody: { 
                    error: "Zoho Desk API error", 
                    details: responseData,
                    status: response.status 
                } 
            };
        }

        return {
            status: response.status,
            jsonBody: responseData
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting agents:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Get products (used for ticket categorization in Zoho Desk)
export async function getProducts(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Getting products/subcategories`);

    try {
        // Check authentication
        const principal = getClientPrincipal(request);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Getting products for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { 
                status: 401, 
                jsonBody: { 
                    error: "No Zoho Desk authorization found. Please authorize first.",
                    authUrl: `http://localhost:7071/api/auth/zoho-desk/authorize`
                } 
            };
        }

        // Try multiple endpoints to get subcategories/products from Zoho Desk
        const endpoints = [
            'products',           // Products endpoint
            'subcategories',      // Direct subcategories endpoint
            'customFields',       // Custom fields might have subcategory options
            'layouts'             // Layouts might contain field definitions
        ];

        let subcategories: any[] = [];
        let successfulEndpoint = '';

        // Prepare headers
        const headers: Record<string, string> = {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'orgId': ZOHO_DESK_CONFIG.orgId,
            'Content-Type': 'application/json'
        };

        // Try each endpoint until we find one that works
        for (const endpoint of endpoints) {
            try {
                const zohoDeskURL = `${ZOHO_DESK_CONFIG.baseURL}/${endpoint}`;
                logger.info(`ZohoDeskAPI: Trying ${endpoint} endpoint: ${zohoDeskURL}`);
                
                const response = await fetch(zohoDeskURL, {
                    method: 'GET',
                    headers: headers
                });
                
                if (response.ok) {
                    const responseData = await response.json();
                    logger.info(`ZohoDeskAPI: ${endpoint} endpoint response:`, responseData);
                    
                    // Extract data from response
                    let data = [];
                    if (Array.isArray(responseData)) {
                        data = responseData;
                    } else if (responseData.data && Array.isArray(responseData.data)) {
                        data = responseData.data;
                    } else if (responseData.products && Array.isArray(responseData.products)) {
                        data = responseData.products;
                    } else if (responseData.subcategories && Array.isArray(responseData.subcategories)) {
                        data = responseData.subcategories;
                    }
                    
                    if (data.length > 0) {
                        subcategories = data;
                        successfulEndpoint = endpoint;
                        logger.info(`ZohoDeskAPI: Successfully got ${data.length} items from ${endpoint} endpoint`);
                        break;
                    }
                } else {
                    logger.warn(`ZohoDeskAPI: ${endpoint} endpoint failed: ${response.status}`);
                }
            } catch (endpointError) {
                logger.warn(`ZohoDeskAPI: Error with ${endpoint} endpoint:`, endpointError);
                continue;
            }
        }

        // If we got real data, format it properly
        if (subcategories.length > 0) {
            const formattedSubcategories = subcategories.map((item: any, index: number) => ({
                id: item.id || `subcategory-${index}`,
                name: item.name || item.productName || item.subcategoryName || `Subcategory ${index + 1}`,
                description: item.description || item.productDescription || '',
                isEnabled: item.isEnabled !== false && item.isActive !== false,
                categoryId: item.categoryId || item.parentId || 'general'
            }));
            
            logger.info(`Successfully fetched ${formattedSubcategories.length} REAL subcategories from Zoho Desk ${successfulEndpoint} endpoint`);
            logger.info(`Subcategories details:`, formattedSubcategories.map(sub => ({ id: sub.id, name: sub.name, categoryId: sub.categoryId })));
            return {
                status: 200,
                jsonBody: formattedSubcategories
            };
        }

        // If no real subcategories found, create default subcategories
        logger.warn(`ZohoDeskAPI: No subcategories found from any Zoho Desk endpoint, creating default subcategories`);
        
        // Create default subcategories for common IT categories
        const defaultSubcategories: any[] = [
            // Hardware subcategories
            { id: 'hardware-desktop', name: 'Desktop Computer', categoryId: 'hardware', isEnabled: true },
            { id: 'hardware-laptop', name: 'Laptop Computer', categoryId: 'hardware', isEnabled: true },
            { id: 'hardware-printer', name: 'Printer', categoryId: 'hardware', isEnabled: true },
            { id: 'hardware-monitor', name: 'Monitor', categoryId: 'hardware', isEnabled: true },
            { id: 'hardware-peripherals', name: 'Peripherals', categoryId: 'hardware', isEnabled: true },
            
            // Software subcategories
            { id: 'software-office', name: 'Microsoft Office', categoryId: 'software', isEnabled: true },
            { id: 'software-email', name: 'Email Client', categoryId: 'software', isEnabled: true },
            { id: 'software-browser', name: 'Web Browser', categoryId: 'software', isEnabled: true },
            { id: 'software-antivirus', name: 'Antivirus', categoryId: 'software', isEnabled: true },
            { id: 'software-custom', name: 'Custom Applications', categoryId: 'software', isEnabled: true },
            
            // Network subcategories
            { id: 'network-wifi', name: 'WiFi Connection', categoryId: 'network', isEnabled: true },
            { id: 'network-ethernet', name: 'Ethernet Connection', categoryId: 'network', isEnabled: true },
            { id: 'network-vpn', name: 'VPN Access', categoryId: 'network', isEnabled: true },
            { id: 'network-internet', name: 'Internet Access', categoryId: 'network', isEnabled: true },
            
            // Access subcategories
            { id: 'access-password', name: 'Password Reset', categoryId: 'access', isEnabled: true },
            { id: 'access-account', name: 'Account Locked', categoryId: 'access', isEnabled: true },
            { id: 'access-permissions', name: 'File Permissions', categoryId: 'access', isEnabled: true },
            { id: 'access-new-user', name: 'New User Setup', categoryId: 'access', isEnabled: true },
            
            // General subcategories
            { id: 'general-training', name: 'Training Request', categoryId: 'general', isEnabled: true },
            { id: 'general-consultation', name: 'IT Consultation', categoryId: 'general', isEnabled: true },
            { id: 'general-other', name: 'Other', categoryId: 'general', isEnabled: true }
        ];
        
        logger.info(`ZohoDeskAPI: Created ${defaultSubcategories.length} default subcategories`);
        return {
            status: 200,
            jsonBody: defaultSubcategories
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error getting products/subcategories:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Test function to discover the correct organization ID
async function testZohoDeskOrgId(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAPI: Testing organization ID discovery`);
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        logger.info(`ZohoDeskAPI: Testing org ID for user: ${userId}`);
        
        // Get access token
        const accessToken = await getZohoDeskAccessToken(userId);
        if (!accessToken) {
            logger.warn(`ZohoDeskAPI: No access token found for user ${userId}`);
            return { status: 401, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Try different approaches to get organization information
        const results: any = {};

        // 1. Try calling profile endpoint (doesn't require orgId)
        try {
            const profileResponse = await fetch('https://desk.zoho.in/api/v1/agents/me', {
                headers: {
                    'Authorization': `Zoho-oauthtoken ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const profileData = await profileResponse.json();
            results.profile = {
                status: profileResponse.status,
                data: profileData
            };
            logger.info(`ZohoDeskAPI: Profile response: ${JSON.stringify(profileData)}`);
        } catch (error) {
            results.profile = { error: error instanceof Error ? error.message : String(error) };
        }

        // 2. Try calling organizations endpoint without orgId
        try {
            const orgResponse = await fetch('https://desk.zoho.in/api/v1/organizations', {
                headers: {
                    'Authorization': `Zoho-oauthtoken ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const orgData = await orgResponse.json();
            results.organizations = {
                status: orgResponse.status,
                data: orgData
            };
            logger.info(`ZohoDeskAPI: Organizations response: ${JSON.stringify(orgData)}`);
        } catch (error) {
            results.organizations = { error: error instanceof Error ? error.message : String(error) };
        }

        // 3. Try departments endpoint with current orgId
        try {
            const currentOrgId = process.env.ZOHO_DESK_ORG_ID || '60040351877';
            const deptResponse = await fetch('https://desk.zoho.in/api/v1/departments', {
                headers: {
                    'Authorization': `Zoho-oauthtoken ${accessToken}`,
                    'orgId': currentOrgId,
                    'Content-Type': 'application/json'
                }
            });
            
            const deptData = await deptResponse.json();
            results.departmentsWithCurrentOrgId = {
                status: deptResponse.status,
                orgIdUsed: currentOrgId,
                data: deptData
            };
            logger.info(`ZohoDeskAPI: Departments with current orgId response: ${JSON.stringify(deptData)}`);
        } catch (error) {
            results.departmentsWithCurrentOrgId = { error: error instanceof Error ? error.message : String(error) };
        }

        return {
            status: 200,
            jsonBody: {
                message: "Organization ID discovery test results",
                currentConfigOrgId: process.env.ZOHO_DESK_ORG_ID,
                fallbackOrgId: '60040351877',
                results: results,
                instructions: "Look for organization ID in profile.data.organizationId or profile.data.organization.id"
            }
        };
        
    } catch (error) {
        logger.error("ZohoDeskAPI: Error in org ID test:", error);
        return { status: 500, jsonBody: { error: "Internal server error", details: error instanceof Error ? error.message : String(error) } };
    }
}

// Register Azure Functions
app.http('ZohoDeskAPIProxy', {
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    authLevel: 'anonymous',
    route: 'zoho-desk/{*path}',
    handler: zohoDeskAPIProxy
});

app.http('GetZohoDeskTickets', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/tickets',
    handler: getTickets
});

app.http('CreateZohoDeskTicket', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'zoho-desk/tickets',
    handler: createTicket
});

app.http('GetZohoDeskTicketThreads', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/tickets/{ticketId}/threads',
    handler: getTicketThreads
});

app.http('GetZohoDeskDepartments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/departments',
    handler: getDepartments
});

app.http('GetZohoDeskCategories', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/categories',
    handler: getCategories
});

app.http('GetZohoDeskSubcategories', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/subcategories',
    handler: getProducts
});

app.http('GetZohoDeskContacts', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/contacts',
    handler: getContacts
});

app.http('GetZohoDeskAgents', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/agents',
    handler: getAgents
});

app.http('TestZohoDeskOrgId', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'zoho-desk/test-org-id',
    handler: testZohoDeskOrgId
}); 



