import { simulateNetworkDelay } from './dashboardApi'; // Use dashboardApi for now
// TODO: Move simulateNetworkDelay to a shared utils/helpers.ts file

// --- Types ---
export interface ITTicketSummary {
  id: string; // Ticket ID (e.g., "INC0012345")
  subject: string;
  status: 'Open' | 'Pending' | 'Resolved' | 'Closed';
  lastUpdated: string; // ISO Timestamp
  link: string; // Link to FreshService ticket
}

export interface ITQuickAction {
  id: string;
  title: string;
  icon: string; // Feather icon name
  description: string;
  link: string;
}

export interface ITAsset {
  id: string; // Asset Tag or unique ID
  name: string; // e.g., "Dell Latitude 7400", "Microsoft Office 365 E3"
  type: 'Hardware' | 'Software' | 'Other';
  serialNumber?: string;
  assignedDate: string; // ISO Timestamp or Date string
}

export interface ITPolicy {
  id: string;
  title: string;
  summary: string;
  link: string; // Link to full policy document
  lastReviewed: string; // ISO Timestamp or Date string
}

// --- Mock Data ---
const MOCK_MY_OPEN_TICKETS: ITTicketSummary[] = [
  {
    id: 'INC0054321',
    subject: 'Cannot connect to VPN from home',
    status: 'Pending',
    lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
    link: 'https://sasmos.freshservice.com/a/tickets/54321' // Example link
  },
  {
    id: 'SR0011223',
    subject: 'Request for new software installation - VS Code',
    status: 'Open',
    lastUpdated: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    link: 'https://sasmos.freshservice.com/a/tickets/11223' 
  },
];

const MOCK_IT_QUICK_ACTIONS: ITQuickAction[] = [
  { 
    id: 'itqa-1', 
    title: 'Report an Issue', 
    icon: 'AlertOctagon', 
    description: 'Submit a new incident ticket.', 
    link: '/it/tickets/new-incident' // Internal route placeholder
  },
  { 
    id: 'itqa-2', 
    title: 'Request Service', 
    icon: 'Briefcase', 
    description: 'Request new hardware, software, or access.', 
    link: '/it/requests/new' // Internal route placeholder
  },
    { 
    id: 'itqa-3', 
    title: 'Reset Password', 
    icon: 'Key', 
    description: 'Use the self-service password reset tool.', 
    link: 'https://passwordreset.microsoftonline.com/' // Example external link
  },
];

const MOCK_MY_ASSETS: ITAsset[] = [
  {
    id: 'SAS-LT-10234',
    name: 'Dell Latitude 7420',
    type: 'Hardware',
    serialNumber: 'DJF87T3',
    assignedDate: '2023-05-15T00:00:00Z'
  },
  {
    id: 'MS-O365-E3-10234',
    name: 'Microsoft 365 E3 License',
    type: 'Software',
    assignedDate: '2023-05-15T00:00:00Z'
  },
    {
    id: 'SAS-MON-05881',
    name: 'Dell U2722DE Monitor',
    type: 'Hardware',
    serialNumber: 'SN-XYZ789',
    assignedDate: '2023-05-15T00:00:00Z'
  },
];

const MOCK_IT_POLICIES: ITPolicy[] = [
  {
    id: 'pol-it-001',
    title: 'Acceptable Use Policy',
    summary: 'Guidelines for using company IT resources responsibly.',
    link: '/knowledge/policies/it-acceptable-use', // Example internal link
    lastReviewed: '2024-01-10T00:00:00Z'
  },
  {
    id: 'pol-it-002',
    title: 'Password Security Policy',
    summary: 'Requirements for creating and managing strong passwords.',
    link: '/knowledge/policies/it-password-security',
    lastReviewed: '2024-02-20T00:00:00Z'
  },
   {
    id: 'pol-it-003',
    title: 'Remote Access Policy',
    summary: 'Procedures and requirements for accessing company resources remotely.',
    link: '/knowledge/policies/it-remote-access',
    lastReviewed: '2023-11-05T00:00:00Z'
  },
];

// --- API Functions ---

export const fetchMyOpenTickets = async (limit = 5): Promise<ITTicketSummary[]> => {
  await simulateNetworkDelay(700);
  // TODO: Implement actual FreshService API call
  // TODO: Filter by current user
  return MOCK_MY_OPEN_TICKETS.slice(0, limit);
};

export const fetchITQuickActions = async (): Promise<ITQuickAction[]> => {
  await simulateNetworkDelay(300);
  // TODO: Potentially filter actions based on user role/permissions
  return MOCK_IT_QUICK_ACTIONS;
};

export const fetchMyAssets = async (): Promise<ITAsset[]> => {
  await simulateNetworkDelay(800);
  // TODO: Implement actual ManageEngine API call
  // TODO: Filter by current user
  return MOCK_MY_ASSETS;
};

export const fetchITPolicies = async (): Promise<ITPolicy[]> => {
  await simulateNetworkDelay(400);
  // TODO: Fetch from Knowledge Hub / Policy store
  return MOCK_IT_POLICIES;
};

// TODO: Add function fetchAllMyTickets(userId, statusFilter, pagination)
// TODO: Add function submitTicket(ticketData)
// TODO: Add function fetchServiceCatalogItems() 