import React, { useState, useEffect } from 'react';
import { Search, Upload, Download, Eye, File, Folder, Tag, Calendar, User } from 'feather-icons-react';
import { 
  fetchDocuments, 
  fetchDocumentCategories, 
  fetchKnowledgeArticles,
  formatFileSize,
  formatDate,
  getMockDocuments,
  getMockCategories,
  getMockArticles,
  uploadDocument,
  downloadDocument,
  type Document,
  type DocumentCategory,
  type KnowledgeArticle,
  type PaginationInfo
} from '../services/knowledgeHubApi';

const KnowledgeHubPage: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState<'documents' | 'articles'>('documents');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [articles, setArticles] = useState<KnowledgeArticle[]>([]);
  const [categories, setCategories] = useState<DocumentCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [documentsPagination, setDocumentsPagination] = useState<PaginationInfo | null>(null);
  const [articlesPagination, setArticlesPagination] = useState<PaginationInfo | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedCompany, setSelectedCompany] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Upload modal state
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [uploadData, setUploadData] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    file: null as File | null
  });
  const [isUploading, setIsUploading] = useState(false);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load data when filters change
  useEffect(() => {
    if (categories.length > 0) {
      setCurrentPage(1);
      loadData();
    }
  }, [activeTab, searchTerm, selectedCategory, selectedCompany]);

  // Load data when page changes
  useEffect(() => {
    if (categories.length > 0) {
      loadData();
    }
  }, [currentPage]);

  const loadInitialData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Load categories first
      const categoriesData = await fetchDocumentCategories();
      setCategories(categoriesData);
      
      // Then load initial documents and articles
      await loadData();
    } catch (err) {
      console.error('Failed to load initial data, falling back to mock data:', err);
      setError('Failed to load data from server. Using sample data.');
      
      // Fallback to mock data
      setCategories(getMockCategories());
      setDocuments(getMockDocuments());
      setArticles(getMockArticles());
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async () => {
    if (categories.length === 0) return;
    
    try {
      const params = {
        search: searchTerm || undefined,
        categoryId: selectedCategory !== 'all' ? selectedCategory : undefined,
        companyId: selectedCompany !== 'all' ? selectedCompany : undefined,
        page: currentPage,
        limit: 20
      };

      if (activeTab === 'documents') {
        const response = await fetchDocuments(params);
        setDocuments(response.documents);
        setDocumentsPagination(response.pagination);
      } else {
        const response = await fetchKnowledgeArticles(params);
        setArticles(response.articles);
        setArticlesPagination(response.pagination);
      }
      
      setError(null);
    } catch (err) {
      console.error(`Failed to load ${activeTab}:`, err);
      setError(`Failed to load ${activeTab}. Please try again.`);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!uploadData.file || !uploadData.title) {
      setError('Please provide a title and select a file.');
      return;
    }

    setIsUploading(true);
    try {
      // Prepare metadata for upload
      const metadata = {
        title: uploadData.title,
        description: uploadData.description || undefined,
        categoryId: uploadData.category || undefined,
        tags: uploadData.tags ? uploadData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : undefined,
        isPublished: true // Default to published
      };

      // Call the upload API
      const result = await uploadDocument(uploadData.file, metadata);
      
      setError(null);
      setIsUploadModalOpen(false);
      setUploadData({ title: '', description: '', category: '', tags: '', file: null });
      
      // Show success message (you could add a success state to show this)
      console.log('Document uploaded successfully:', result.document);
      
      // Reload data to show new document
      await loadData();
    } catch (err) {
      console.error('Failed to upload document:', err);
      setError(err instanceof Error ? err.message : 'Failed to upload document. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handle document download
  const handleDocumentDownload = async (documentId: string) => {
    try {
      await downloadDocument(documentId);
    } catch (error) {
      console.error('Failed to download document:', error);
      setError(error instanceof Error ? error.message : 'Failed to download document');
    }
  };

  // Handle document view (same as download for now)
  const handleDocumentView = async (documentId: string) => {
    try {
      await downloadDocument(documentId);
    } catch (error) {
      console.error('Failed to view document:', error);
      setError(error instanceof Error ? error.message : 'Failed to view document');
    }
  };

  // Get current pagination info
  const currentPagination = activeTab === 'documents' ? documentsPagination : articlesPagination;
  const currentData = activeTab === 'documents' ? documents : articles;

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Knowledge Hub</h1>
        <p className="text-gray-600">Centralized document management and knowledge base for the organization.</p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab('documents')}
            className={`${
              activeTab === 'documents'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
          >
            <File size={16} className="inline mr-2" />
            Documents ({documents.length})
          </button>
          <button
            onClick={() => setActiveTab('articles')}
            className={`${
              activeTab === 'articles'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm`}
          >
            <Eye size={16} className="inline mr-2" />
            Knowledge Articles ({articles.length})
          </button>
        </nav>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search documents and articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.name}</option>
            ))}
          </select>

          {/* Company Filter */}
          <select
            value={selectedCompany}
            onChange={(e) => setSelectedCompany(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Companies</option>
            <option value="Group-wide">Group-wide</option>
            <option value="SASMOS HET">SASMOS HET</option>
            <option value="Avirata Defence Systems">Avirata Defence Systems</option>
          </select>

          {/* View Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-indigo-100 text-indigo-600' : 'text-gray-400'}`}
            >
              <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
              </div>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-indigo-100 text-indigo-600' : 'text-gray-400'}`}
            >
              <div className="w-4 h-4 flex flex-col space-y-0.5">
                <div className="h-1 bg-current rounded"></div>
                <div className="h-1 bg-current rounded"></div>
                <div className="h-1 bg-current rounded"></div>
              </div>
            </button>
          </div>
        </div>

        {/* Action Buttons and Results Info */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {currentPagination ? (
              `${currentPagination.totalCount} ${activeTab} found (Page ${currentPagination.page} of ${currentPagination.totalPages})`
            ) : (
              `${currentData.length} ${activeTab} found`
            )}
          </div>
          <button
            onClick={() => setIsUploadModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Upload size={16} className="mr-2" />
            Upload Document
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading content...</p>
        </div>
      )}

      {/* Content Display */}
      {!isLoading && (
        <>
          {/* Documents Tab */}
          {activeTab === 'documents' && (
            <div>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {documents.map((doc) => (
                    <div key={doc.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg font-medium text-gray-900 truncate mb-1">
                              {doc.title}
                            </h3>
                            <p className="text-sm text-gray-500 line-clamp-2">
                              {doc.description}
                            </p>
                          </div>
                          <File size={20} className="text-gray-400 ml-2 flex-shrink-0" />
                        </div>

                        <div className="mb-4">
                          <div className="flex items-center text-sm text-gray-500 mb-2">
                            <Folder size={14} className="mr-1" />
                            {doc.category.name}
                          </div>
                          <div className="flex items-center text-sm text-gray-500 mb-2">
                            <Calendar size={14} className="mr-1" />
                            {formatDate(doc.createdDate)}
                          </div>
                          <div className="flex items-center text-sm text-gray-500">
                            <Download size={14} className="mr-1" />
                            {doc.downloadCount} downloads • {formatFileSize(doc.fileSizeKB)}
                          </div>
                        </div>

                        <div className="mb-4">
                          <div className="flex flex-wrap gap-1">
                            {doc.tags.slice(0, 3).map((tag, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                <Tag size={10} className="mr-1" />
                                {tag}
                              </span>
                            ))}
                            {doc.tags.length > 3 && (
                              <span className="text-xs text-gray-500">+{doc.tags.length - 3} more</span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="flex items-center text-sm text-gray-500">
                            <User size={14} className="mr-1" />
                            {doc.createdBy.name}
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleDocumentView(doc.id)}
                              className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                              <Eye size={14} className="mr-1" />
                              View
                            </button>
                            <button
                              onClick={() => handleDocumentDownload(doc.id)}
                              className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                              <Download size={14} className="mr-1" />
                              Download
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white shadow rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Downloads</th>
                        <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {documents.map((doc) => (
                        <tr key={doc.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <File size={16} className="text-gray-400 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-gray-900">{doc.title}</div>
                                <div className="text-sm text-gray-500">{doc.fileName}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {doc.category.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatFileSize(doc.fileSizeKB)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(doc.lastModified || doc.createdDate)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {doc.downloadCount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handleDocumentView(doc.id)}
                              className="text-indigo-600 hover:text-indigo-900 mr-3"
                            >
                              View
                            </button>
                            <button
                              onClick={() => handleDocumentDownload(doc.id)}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              Download
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Knowledge Articles Tab */}
          {activeTab === 'articles' && (
            <div className="space-y-4">
              {articles.map((article) => (
                <div key={article.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{article.title}</h3>
                      <p className="text-gray-600 mb-4">{article.summary}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Folder size={14} className="mr-1" />
                          {article.category.name}
                        </div>
                        <div className="flex items-center">
                          <User size={14} className="mr-1" />
                          {article.createdBy.name}
                        </div>
                        <div className="flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(article.createdDate)}
                        </div>
                        <div className="flex items-center">
                          <Eye size={14} className="mr-1" />
                          {article.viewCount} views
                        </div>
                        <div className="flex items-center">
                          ⭐ {article.rating}/5
                        </div>
                      </div>
                    </div>
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                      Read Article
                    </button>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {article.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800"
                      >
                        <Tag size={10} className="mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Empty State */}
          {currentData.length === 0 && (
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                {activeTab === 'documents' ? <File size={48} /> : <Eye size={48} />}
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No {activeTab} found
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search or filters.' : `No ${activeTab} have been uploaded yet.`}
              </p>
            </div>
          )}

          {/* Pagination */}
          {currentPagination && currentPagination.totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(currentPagination.page - 1)}
                  disabled={!currentPagination.hasPrev}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(currentPagination.page + 1)}
                  disabled={!currentPagination.hasNext}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing page <span className="font-medium">{currentPagination.page}</span> of{' '}
                    <span className="font-medium">{currentPagination.totalPages}</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      onClick={() => handlePageChange(currentPagination.page - 1)}
                      disabled={!currentPagination.hasPrev}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPagination.page + 1)}
                      disabled={!currentPagination.hasNext}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white max-w-md">
            <div className="mt-3">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Upload Document
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={uploadData.title}
                    onChange={(e) => setUploadData({ ...uploadData, title: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter document title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={uploadData.description}
                    onChange={(e) => setUploadData({ ...uploadData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter document description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select
                    value={uploadData.category}
                    onChange={(e) => setUploadData({ ...uploadData, category: e.target.value })}
                    className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">Select a category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tags
                  </label>
                  <input
                    type="text"
                    value={uploadData.tags}
                    onChange={(e) => setUploadData({ ...uploadData, tags: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter tags separated by commas"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    File *
                  </label>
                  <input
                    type="file"
                    onChange={(e) => setUploadData({ ...uploadData, file: e.target.files?.[0] || null })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => setIsUploadModalOpen(false)}
                  disabled={isUploading}
                  className="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleFileUpload}
                  disabled={isUploading || !uploadData.file || !uploadData.title}
                  className="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUploading ? 'Uploading...' : 'Upload'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KnowledgeHubPage; 