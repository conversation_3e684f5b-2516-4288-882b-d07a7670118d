"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUser = void 0;
// --- API Call Functions --- 
// --- User Management --- ADDED ---
/**
 * Creates a new portal user.
 * Sends the user's email/UPN to the backend, which handles finding/creating
 * the user in the local DB and assigning default roles.
 * @param userData - Object containing the user's email/UPN.
 * @returns The newly created PortalUser object.
 */
const createUser = (userData) => __awaiter(void 0, void 0, void 0, function* () {
    console.log("API: createUser called", userData);
    try {
        // Assuming callApi handles JSON stringification and Content-Type
        const newUser = yield callApi('/api/users', {
            method: 'POST',
            body: JSON.stringify(userData), // Ensure body is stringified
            headers: {
                'Content-Type': 'application/json' // Explicitly set Content-Type
            }
        });
        console.log("Successfully created user:", newUser);
        return newUser;
    }
    catch (error) {
        console.error("Error in createUser calling backend:", error);
        // Re-throw a more specific error or handle it as needed
        throw new Error(`Failed to create user: ${error.message || 'Unknown error'}`);
    }
});
exports.createUser = createUser;
// --- Role Management --- 
//# sourceMappingURL=adminApi.js.map