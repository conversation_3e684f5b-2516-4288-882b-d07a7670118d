{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/CreateSampleData/index.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AAEzF,qCAAuC;AAEhC,KAAK,UAAU,gBAAgB,CAAC,OAAoB,EAAE,OAA0B;IACnF,IAAI;QACA,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,0BAA0B;QAC1B,MAAM,IAAI,GAAmB,MAAM,IAAA,YAAO,GAAE,CAAC;QAE7C,0FAA0F;QAC1F,MAAM,cAAc,GAAG;YACnB;gBACI,KAAK,EAAE,6CAA6C;gBACpD,WAAW,EAAE,qFAAqF;gBAClG,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,YAAY;gBAC5B,uBAAuB,EAAE,YAAY;aACxC;YACD;gBACI,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,iDAAiD;gBAC9D,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,YAAY;gBAC5B,uBAAuB,EAAE,YAAY;aACxC;YACD;gBACI,KAAK,EAAE,2CAA2C;gBAClD,WAAW,EAAE,yDAAyD;gBACtE,MAAM,EAAE,WAAW;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,YAAY;gBAC5B,uBAAuB,EAAE,YAAY;aACxC;YACD;gBACI,KAAK,EAAE,sCAAsC;gBAC7C,WAAW,EAAE,uDAAuD;gBACpE,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,YAAY;gBAC5B,uBAAuB,EAAE,YAAY;aACxC;YACD;gBACI,KAAK,EAAE,2BAA2B;gBAClC,WAAW,EAAE,mDAAmD;gBAChE,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,CAAC;gBACZ,cAAc,EAAE,YAAY;gBAC5B,uBAAuB,EAAE,YAAY;aACxC;SACJ,CAAC;QAEF,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,MAAM,gBAAgB,GAAG,EAAE,CAAC;QAE5B,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE;YAClC,IAAI;gBACA,MAAM,WAAW,GAAG;;;;;;;;;;;;;iBAanB,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;qBAC9B,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;qBAC7B,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC;qBACzC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC;qBAC/B,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC;qBACnC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC;qBACrC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,wBAAwB;qBAChD,KAAK,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;qBACzD,KAAK,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;qBAC3E,KAAK,CAAC,oBAAoB,EAAE,GAAG,CAAC;qBAChC,KAAK,CAAC,oBAAoB,EAAE,YAAY,CAAC;qBACzC,KAAK,CAAC,gBAAgB,EAAE,WAAW,CAAC;qBACpC,KAAK,CAAC,wBAAwB,EAAE,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBACxE,KAAK,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBAC/C,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;wBACzC,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;qBAC3E,KAAK,CAAC,WAAW,CAAC,CAAC;gBAExB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChD,gBAAgB,CAAC,IAAI,CAAC;oBAClB,SAAS;oBACT,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,cAAc,EAAE,OAAO,CAAC,cAAc;iBACzC,CAAC,CAAC;gBAEH,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,KAAK,SAAS,SAAS,GAAG,CAAC,CAAC;aAChE;YAAC,OAAO,WAAW,EAAE;gBAClB,MAAM,YAAY,GAAG,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC1F,OAAO,CAAC,GAAG,CAAC,mBAAmB,OAAO,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC,CAAC;aACpE;SACJ;QAED,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,wBAAwB,aAAa,qEAAqE;gBACnH,aAAa;gBACb,gBAAgB;aACnB;SACJ,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC;QACxD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,8BAA8B;gBACrC,OAAO,EAAE,YAAY;aACxB;SACJ,CAAC;KACL;AACL,CAAC;AAlID,4CAkIC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC"}