import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import sql from 'mssql';

export async function updateChangeRequestCompletionDate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('UpdateChangeRequestCompletionDate function invoked.');
    
    try {
        // Set CORS headers
        const headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Content-Type': 'application/json'
        };

        // Handle preflight request
        if (request.method === 'OPTIONS') {
            return {
                status: 200,
                headers
            };
        }

        const requestId = request.params.requestId;
        
        if (!requestId) {
            return {
                status: 400,
                headers,
                jsonBody: {
                    error: {
                        code: 'MISSING_REQUEST_ID',
                        message: 'Request ID is required'
                    }
                }
            };
        }

        // Parse request body
        const body = await request.json() as {
            requestedCompletionDate: string;
            reason?: string;
            userId: number;
        };

        if (!body.requestedCompletionDate) {
            return {
                status: 400,
                headers,
                jsonBody: {
                    error: {
                        code: 'MISSING_COMPLETION_DATE',
                        message: 'Requested completion date is required'
                    }
                }
            };
        }

        // Update the requested completion date and deployment date
        const updateQuery = `
            UPDATE ChangeRequests 
            SET RequestedCompletionDate = @requestedCompletionDate,
                DeploymentDate = @requestedCompletionDate
            WHERE RequestID = @requestId
        `;

        const updateParams: QueryParameter[] = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'requestedCompletionDate', type: sql.Date, value: new Date(body.requestedCompletionDate) }
        ];

        await executeQuery(updateQuery, updateParams);

        // Add a comment if reason was provided
        if (body.reason && body.reason.trim()) {
            const commentQuery = `
                INSERT INTO ChangeRequestComments (
                    RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
                )
                VALUES (
                    @requestId, @commentText, @commentType, @isInternal, @userId, GETDATE()
                )
            `;

            const commentParams: QueryParameter[] = [
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
                { name: 'commentText', type: sql.NVarChar, value: `[DATE_OVERRIDE] ${body.reason.trim()}` },
                { name: 'commentType', type: sql.NVarChar, value: 'General' },
                { name: 'isInternal', type: sql.Bit, value: false },
                { name: 'userId', type: sql.Int, value: body.userId || 1 }
            ];

            await executeQuery(commentQuery, commentParams);
        }

        context.log(`Successfully updated completion date for change request ${requestId}`);

        return {
            status: 200,
            headers,
            jsonBody: {
                success: true,
                message: 'Completion date updated successfully',
                data: {
                    requestId: parseInt(requestId),
                    newCompletionDate: body.requestedCompletionDate
                }
            }
        };

    } catch (error) {
        context.error('Error in UpdateChangeRequestCompletionDate:', error);
        return {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Content-Type': 'application/json'
            },
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while updating the completion date'
                }
            }
        };
    }
}

app.http('UpdateChangeRequestCompletionDate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/update-completion-date',
    handler: updateChangeRequestCompletionDate
}); 