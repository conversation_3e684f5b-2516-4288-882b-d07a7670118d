"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthTokenService = void 0;
const logger_1 = require("../shared/utils/logger");
const db_1 = require("../shared/db");
const mssql_1 = __importDefault(require("mssql"));
class OAuthTokenService {
    /**
     * Get active OAuth token for a user and service
     * Replaces global.zohoTokens[userId] lookup
     */
    static async getActiveToken(userId, serviceProvider = 'zoho', serviceType = 'desk') {
        try {
            logger_1.logger.info(`OAuthTokenService: Getting active token for user ${userId}, service ${serviceProvider}/${serviceType}`);
            const pool = await (0, db_1.getPool)();
            const result = await pool.request()
                .input('UserId', mssql_1.default.NVarChar(255), userId)
                .input('ServiceProvider', mssql_1.default.NVarChar(100), serviceProvider)
                .input('ServiceType', mssql_1.default.NVarChar(100), serviceType)
                .execute('GetActiveOAuthToken');
            if (result.recordset && result.recordset.length > 0) {
                const token = result.recordset[0];
                logger_1.logger.info(`OAuthTokenService: Found active token for user ${userId}, expires at ${token.ExpiresAt}`);
                return {
                    tokenId: token.TokenId,
                    userId: userId,
                    serviceProvider: serviceProvider,
                    serviceType: serviceType,
                    accessToken: token.AccessToken,
                    refreshToken: token.RefreshToken,
                    expiresAt: new Date(token.ExpiresAt),
                    scope: token.Scope,
                    tokenType: token.TokenType || 'Bearer',
                    isActive: true
                };
            }
            logger_1.logger.warn(`OAuthTokenService: No active token found for user ${userId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error getting active token:', error);
            return null;
        }
    }
    /**
     * Save or update OAuth token for a user
     * Replaces global.zohoTokens[userId] = token assignment
     */
    static async saveToken(userId, accessToken, refreshToken, expiresIn = 3600, scope, serviceProvider = 'zoho', serviceType = 'desk', tokenType = 'Bearer') {
        try {
            logger_1.logger.info(`OAuthTokenService: Saving token for user ${userId}, expires in ${expiresIn} seconds`);
            const pool = await (0, db_1.getPool)();
            const result = await pool.request()
                .input('UserId', mssql_1.default.NVarChar(255), userId)
                .input('ServiceProvider', mssql_1.default.NVarChar(100), serviceProvider)
                .input('ServiceType', mssql_1.default.NVarChar(100), serviceType)
                .input('AccessToken', mssql_1.default.NVarChar(mssql_1.default.MAX), accessToken)
                .input('RefreshToken', mssql_1.default.NVarChar(mssql_1.default.MAX), refreshToken || null)
                .input('ExpiresIn', mssql_1.default.Int, expiresIn)
                .input('Scope', mssql_1.default.NVarChar(500), scope || null)
                .input('TokenType', mssql_1.default.NVarChar(50), tokenType)
                .execute('SaveOAuthToken');
            if (result.recordset && result.recordset.length > 0) {
                const tokenId = result.recordset[0].TokenId;
                logger_1.logger.info(`OAuthTokenService: Token saved successfully with ID ${tokenId}`);
                return tokenId;
            }
            logger_1.logger.error('OAuthTokenService: Failed to save token - no result returned');
            return null;
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error saving token:', error);
            return null;
        }
    }
    /**
     * Check if token needs refresh (expires in next 5 minutes)
     * Used for proactive token refresh
     */
    static async needsRefresh(userId, serviceProvider = 'zoho', serviceType = 'desk') {
        try {
            const pool = await (0, db_1.getPool)();
            const result = await pool.request()
                .input('UserId', mssql_1.default.NVarChar(255), userId)
                .input('ServiceProvider', mssql_1.default.NVarChar(100), serviceProvider)
                .input('ServiceType', mssql_1.default.NVarChar(100), serviceType)
                .query('SELECT dbo.NeedsTokenRefresh(@UserId, @ServiceProvider, @ServiceType) as NeedsRefresh');
            if (result.recordset && result.recordset.length > 0) {
                return result.recordset[0].NeedsRefresh === 1;
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error checking if token needs refresh:', error);
            return true; // Assume needs refresh on error for safety
        }
    }
    /**
     * Get OAuth configuration value
     * Used for client credentials, redirect URIs, etc.
     */
    static async getConfig(configKey, serviceProvider = 'zoho', serviceType = 'desk') {
        try {
            const pool = await (0, db_1.getPool)();
            const result = await pool.request()
                .input('ServiceProvider', mssql_1.default.NVarChar(100), serviceProvider)
                .input('ServiceType', mssql_1.default.NVarChar(100), serviceType)
                .input('ConfigKey', mssql_1.default.NVarChar(255), configKey)
                .query(`
                    SELECT ConfigValue, IsEncrypted 
                    FROM OAuthConfigurations 
                    WHERE ServiceProvider = @ServiceProvider 
                        AND ServiceType = @ServiceType 
                        AND ConfigKey = @ConfigKey 
                        AND IsActive = 1
                `);
            if (result.recordset && result.recordset.length > 0) {
                const config = result.recordset[0];
                // TODO: Implement decryption for encrypted values in production
                if (config.IsEncrypted) {
                    logger_1.logger.warn(`OAuthTokenService: Config ${configKey} is encrypted, returning as-is (implement decryption for production)`);
                }
                return config.ConfigValue;
            }
            logger_1.logger.warn(`OAuthTokenService: Config not found: ${configKey}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error getting configuration:', error);
            return null;
        }
    }
    /**
     * Revoke/deactivate token for user
     * Used when user logs out or token is compromised
     */
    static async revokeToken(userId, serviceProvider = 'zoho', serviceType = 'desk') {
        try {
            logger_1.logger.info(`OAuthTokenService: Revoking token for user ${userId}`);
            const pool = await (0, db_1.getPool)();
            await pool.request()
                .input('UserId', mssql_1.default.NVarChar(255), userId)
                .input('ServiceProvider', mssql_1.default.NVarChar(100), serviceProvider)
                .input('ServiceType', mssql_1.default.NVarChar(100), serviceType)
                .query(`
                    UPDATE OAuthTokens 
                    SET IsActive = 0, UpdatedAt = GETDATE()
                    WHERE UserId = @UserId 
                        AND ServiceProvider = @ServiceProvider 
                        AND ServiceType = @ServiceType
                        AND IsActive = 1
                `);
            logger_1.logger.info(`OAuthTokenService: Token revoked for user ${userId}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error revoking token:', error);
            return false;
        }
    }
    /**
     * Log token usage for audit purposes
     */
    static async logUsage(tokenId, requestEndpoint, responseStatus, errorMessage) {
        try {
            const pool = await (0, db_1.getPool)();
            await pool.request()
                .input('TokenId', mssql_1.default.Int, tokenId)
                .input('RequestEndpoint', mssql_1.default.NVarChar(500), requestEndpoint || null)
                .input('ResponseStatus', mssql_1.default.Int, responseStatus || null)
                .input('ErrorMessage', mssql_1.default.NVarChar(mssql_1.default.MAX), errorMessage || null)
                .query(`
                    INSERT INTO OAuthTokenUsage (TokenId, RequestEndpoint, ResponseStatus, ErrorMessage)
                    VALUES (@TokenId, @RequestEndpoint, @ResponseStatus, @ErrorMessage)
                `);
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error logging token usage:', {
                tokenId,
                requestEndpoint,
                errorMessage: error.message
            });
        }
    }
    /**
     * Retrieves all active tokens.
     * This method could be used for background jobs or administrative tasks.
     */
    static async getAllActiveTokens() {
        try {
            const pool = await (0, db_1.getPool)();
            const result = await pool.request()
                .query(`
                    SELECT TokenId, UserId, ServiceProvider, ServiceType, 
                           ExpiresAt, Scope, TokenType, CreatedAt, UpdatedAt
                    FROM OAuthTokens 
                    WHERE IsActive = 1 
                    ORDER BY CreatedAt DESC
                `);
            return result.recordset.map(row => ({
                tokenId: row.TokenId,
                userId: row.UserId,
                serviceProvider: row.ServiceProvider,
                serviceType: row.ServiceType,
                accessToken: '[HIDDEN]',
                expiresAt: new Date(row.ExpiresAt),
                scope: row.Scope,
                tokenType: row.TokenType,
                isActive: true,
                createdAt: row.CreatedAt,
                updatedAt: row.UpdatedAt
            }));
        }
        catch (error) {
            logger_1.logger.error('OAuthTokenService: Error getting all active tokens:', error);
            return [];
        }
    }
}
exports.OAuthTokenService = OAuthTokenService;
//# sourceMappingURL=OAuthTokenService.js.map