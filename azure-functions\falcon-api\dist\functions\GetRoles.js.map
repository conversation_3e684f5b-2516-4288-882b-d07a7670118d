{"version": 3, "file": "GetRoles.js", "sourceRoot": "", "sources": ["../../src/functions/GetRoles.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAEzC,KAAK,UAAU,QAAQ,CAAC,OAAoB,EAAE,OAA0B;IAC3E,eAAM,CAAC,IAAI,CAAC,sDAAsD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;IAElF,qCAAqC;IACrC,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;IACrD,eAAM,CAAC,IAAI,CAAC,qDAAqD,aAAa,EAAE,CAAC,CAAC;IAElF,IAAI;QACA,uCAAuC;QACvC,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,oGAAoG,CAAC,CAAC;QAExI,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3F,uFAAuF;QACvF,MAAM,KAAK,GAAqB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC5D,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,WAAW,EAAE,MAAM,CAAC,eAAe,CAAC,sCAAsC;YAC1E,oEAAoE;YACpE,qCAAqC;YACrC,4BAA4B;SAC/B,CAAC,CAAC,CAAC;QAEJ,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,KAAK;SAClB,CAAC;KACL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;QAElG,kFAAkF;QAClF,IAAI,aAAa,IAAI,KAAK,YAAY,KAAK;YACvC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACpC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACtC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAChC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAC,CAAC,EAAE;YACxD,eAAM,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;YAEnG,MAAM,SAAS,GAAqB;gBAChC;oBACI,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,eAAe;oBACzB,WAAW,EAAE,kDAAkD;iBAClE;gBACD;oBACI,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,UAAU;oBACpB,WAAW,EAAE,6CAA6C;iBAC7D;gBACD;oBACI,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,mCAAmC;iBACnD;gBACD;oBACI,MAAM,EAAE,CAAC;oBACT,QAAQ,EAAE,YAAY;oBACtB,WAAW,EAAE,wCAAwC;iBACxD;aACJ,CAAC;YAEF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,SAAS;aACtB,CAAC;SACL;QAED,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,uBAAuB;gBAChC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;aAC/E;SACJ,CAAC;KACL;AACL,CAAC;AA3ED,4BA2EC;AAED,eAAG,CAAC,IAAI,CAAC,UAAU,EAAE;IACjB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,QAAQ;CACpB,CAAC,CAAC"}