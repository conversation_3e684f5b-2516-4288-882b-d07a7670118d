{"version": 3, "file": "UpdatePortalUser.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/UpdatePortalUser.ts"], "names": [], "mappings": ";;;;;;;;;;;AAoBA,4CAgQC;AApRD,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAkG;AAClG,oFAAgG;AAChG,kEAA+D;AAC/D,6BAA6B;AAW7B,4CAA4C;AAC5C,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,mCAAmC;AAE1E,SAAsB,gBAAgB,CAAC,OAAoB,EAAE,OAA0B;;QACnF,OAAO,CAAC,GAAG,GAAG,eAAM,CAAC,IAAI,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAElD,oCAAoC;QACpC,IAAI,mBAA2B,CAAC;QAEhC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC5E,eAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;YAC1F,mBAAmB,GAAG,CAAC,CAAC,CAAC,sCAAsC;QACnE,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBACjE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,CAAC;YAC3F,CAAC;YAED,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;gBAC/C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,qDAAqD,aAAa,IAAI,CAAC,CAAC;gBAC1I,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yDAAyD,EAAE,EAAE,CAAC;YAC3G,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,eAAM,CAAC,KAAK,CAAC,mFAAmF,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC/I,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,6EAA6E,EAAE,EAAE,CAAC;YAC/H,CAAC;YACD,mBAAmB,GAAG,UAAU,CAAC;QACrC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,uCAAuC,mBAAmB,EAAE,CAAC,CAAC;QAE1E,2CAA2C;QAC3C,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,eAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;YAC3E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;QAC1F,CAAC;QAED,qCAAqC;QACrC,IAAI,UAAmC,CAAC;QACxC,IAAI,CAAC;YACD,UAAU,IAAG,MAAM,OAAO,CAAC,IAAI,EAA6B,CAAA,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;QACjF,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YAChD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE,EAAE,CAAC;QACxF,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,QAAQ,EAAE,iBAAiB,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,UAAU,CAAC;QAE7I,IAAI,CAAC;YACD,0EAA0E;YAC1E,MAAM,gBAAgB,GAAG;;;;;;;;SAQxB,CAAC;YACF,MAAM,cAAc,GAAqB;gBACrC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;aAC1D,CAAC;YACF,MAAM,cAAc,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;YAE5E,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrE,eAAM,CAAC,IAAI,CAAC,8CAA8C,OAAO,aAAa,CAAC,CAAC;gBAChF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,qBAAqB,OAAO,aAAa,EAAE,EAAE,CAAC;YAC3F,CAAC;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAClD,MAAM,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC7D,MAAM,gBAAgB,GAAa,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB;gBAC3E,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBACzF,CAAC,CAAC,EAAE,CAAC;YAET,eAAM,CAAC,IAAI,CAAC,oBAAoB,MAAM,qBAAqB,eAAe,qBAAqB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC9H,eAAM,CAAC,IAAI,CAAC,qBAAqB,iBAAiB,uBAAuB,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,IAAI,CAAC,GAAG,CAAC,KAAI,WAAW,uBAAuB,aAAa,EAAE,CAAC,CAAC;YAE7J,2CAA2C;YAC3C,IAAI,aAAa,EAAE,CAAC;gBAChB,IAAI,CAAC;oBACD,eAAM,CAAC,IAAI,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;oBACpE,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;oBAEpE,IAAI,SAAS,EAAE,CAAC;wBACZ,MAAM,SAAS,GAAG;;;;;;;;;qBASjB,CAAC;wBAEF,MAAM,UAAU,GAAqB;4BACjC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;4BAChD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE;4BACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE;4BACnE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE;4BAC7D,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE;4BAC7E,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE;yBACpE,CAAC;wBAEF,MAAM,IAAA,iBAAY,EAAC,SAAS,EAAE,UAAU,CAAC,CAAC;wBAC1C,eAAM,CAAC,IAAI,CAAC,0DAA0D,MAAM,EAAE,CAAC,CAAC;oBACpF,CAAC;yBAAM,CAAC;wBACJ,eAAM,CAAC,IAAI,CAAC,kDAAkD,OAAO,kCAAkC,CAAC,CAAC;oBAC7G,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,eAAM,CAAC,KAAK,CAAC,wCAAwC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;oBACxE,kDAAkD;oBAClD,eAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;gBAClF,CAAC;YACL,CAAC;YAED,+CAA+C;YAC/C,IAAI,OAAO,iBAAiB,KAAK,SAAS,IAAI,iBAAiB,KAAK,eAAe,EAAE,CAAC;gBAClF,eAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,OAAO,iBAAiB,EAAE,CAAC,CAAC;gBACrF,MAAM,iBAAiB,GAAG,sHAAsH,CAAC;gBACjJ,MAAM,YAAY,GAAqB;oBACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;oBAChD,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,iBAAiB,EAAE;oBAC7D,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE;iBACpE,CAAC;gBACF,MAAM,IAAA,iBAAY,EAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,iFAAiF;YACjF,IAAI,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC/F,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,QAAQ,aAAa,GAAG,CAAC,CAAC;gBACxE,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAE1E,MAAM,eAAe,GAAG,8IAA8I,CAAC;gBACvK,MAAM,UAAU,GAAqB;oBACjC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;oBAChD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;oBAC3D,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;oBACzD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE;iBACpE,CAAC;gBACF,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,UAAU,CAAC,CAAC;gBAChD,eAAM,CAAC,IAAI,CAAC,wCAAwC,MAAM,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,iDAAiD;YACjD,IAAI,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,IAAI,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtF,eAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,QAAQ,gBAAgB,GAAG,CAAC,CAAC;gBAC9E,MAAM,iBAAiB,GAAG,oFAAoF,CAAC;gBAC/G,MAAM,aAAa,GAAqB;oBACpC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,gBAAgB,CAAC,IAAI,EAAE,EAAE;iBAC9E,CAAC;gBACF,MAAM,aAAa,GAAG,MAAM,IAAA,iBAAY,EAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;gBAE3E,IAAI,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChE,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;oBACvD,MAAM,kBAAkB,GAAG,wHAAwH,CAAC;oBACpJ,MAAM,mBAAmB,GAAqB;wBAC1C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;wBAChD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE;wBACtD,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,mBAAmB,EAAE;qBACpE,CAAC;oBACF,MAAM,IAAA,iBAAY,EAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;oBAC5D,eAAM,CAAC,IAAI,CAAC,6CAA6C,MAAM,OAAO,SAAS,EAAE,CAAC,CAAC;gBACvF,CAAC;qBAAM,CAAC;oBACJ,eAAM,CAAC,IAAI,CAAC,YAAY,gBAAgB,+DAA+D,MAAM,GAAG,CAAC,CAAC;gBACtH,CAAC;YACL,CAAC;YAED,qCAAqC;YACrC,IAAI,kBAAkB,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC1D,eAAM,CAAC,IAAI,CAAC,sCAAsC,MAAM,EAAE,CAAC,CAAC;gBAE5D,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjG,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnH,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC7E,MAAM,aAAa,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEhF,eAAM,CAAC,IAAI,CAAC,kBAAkB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAExG,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACpD,MAAM,YAAY,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,aAAa,CAAC,CAAC;oBAEvD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1B,kCAAkC;wBAClC,MAAM,eAAe,GAAG,yDAAyD,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;wBAC5J,MAAM,YAAY,GAAqB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;4BAClE,IAAI,EAAE,WAAW,CAAC,EAAE;4BACpB,IAAI,EAAE,GAAG,CAAC,QAAQ;4BAClB,KAAK,EAAE,IAAI;yBACd,CAAC,CAAC,CAAC;wBAEJ,MAAM,aAAa,GAAG,MAAM,IAAA,iBAAY,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;wBACxE,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB,CAAC;wBAC5C,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;4BACzC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,QAAkB,EAAE,GAAG,CAAC,MAAgB,CAAC,CAAC;wBAChE,CAAC,CAAC,CAAC;wBAEH,wBAAwB;wBACxB,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE,CAAC;4BACnC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAkB,CAAC,CAAC;4BACjD,IAAI,MAAM,EAAE,CAAC;gCACT,eAAM,CAAC,IAAI,CAAC,kBAAkB,QAAQ,UAAU,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;gCACjF,MAAM,IAAA,0CAAkB,EAAC,MAAM,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;4BAClE,CAAC;iCAAM,CAAC;gCACJ,eAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,+CAA+C,CAAC,CAAC;4BACvF,CAAC;wBACL,CAAC;wBAED,yBAAyB;wBACzB,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;4BAChC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,QAAkB,CAAC,CAAC;4BACjD,IAAI,MAAM,EAAE,CAAC;gCACT,eAAM,CAAC,IAAI,CAAC,gBAAgB,QAAQ,UAAU,MAAM,eAAe,MAAM,EAAE,CAAC,CAAC;gCAC7E,MAAM,IAAA,wCAAgB,EAAC,MAAM,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;4BAChE,CAAC;iCAAM,CAAC;gCACJ,eAAM,CAAC,IAAI,CAAC,cAAc,QAAQ,gDAAgD,CAAC,CAAC;4BACxF,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gDAAgD,MAAM,GAAG,CAAC,CAAC;YAEvE,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,4BAA4B;oBACrC,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,aAAa,IAAI,KAAK;iBAC1C;aACJ,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kDAAkD,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,uDAAuD;oBAC9D,OAAO,EAAE,YAAY;iBACxB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IACzB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,KAAK,EAAE,wBAAwB;IAC/B,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,gBAAgB;CAC5B,CAAC,CAAC"}