-- Diagnostic Script: Check Current Users Table Schema
-- Purpose: Verify current state of Users table before applying multi-tenant updates
-- Date: January 2025

-- Check all columns in Users table
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
ORDER BY ORDINAL_POSITION;

-- Check current indexes on Users table
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    STRING_AGG(c.name, ', ') AS Columns
FROM sys.indexes i
JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.Users')
    AND i.name IS NOT NULL
GROUP BY i.name, i.type_desc, i.is_unique
ORDER BY i.name;

-- Check if TenantID column already exists
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
    PRINT 'TenantID column already exists in Users table'
ELSE
    PRINT 'TenantID column does not exist in Users table';

-- Check if composite index already exists
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
    PRINT 'Composite index IX_Users_EntraID_TenantID already exists'
ELSE
    PRINT 'Composite index IX_Users_EntraID_TenantID does not exist';

-- Check if TenantID index already exists  
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
    PRINT 'TenantID index IX_Users_TenantID already exists'
ELSE
    PRINT 'TenantID index IX_Users_TenantID does not exist';

-- Count current users and their TenantID values
SELECT 
    COUNT(*) as TotalUsers,
    COUNT(TenantID) as UsersWithTenantID,
    COUNT(DISTINCT TenantID) as UniqueTenantIDs
FROM dbo.Users;

-- Show sample of current users (first 5)
SELECT TOP 5
    UserID,
    Email,
    EntraID,
    TenantID,
    CompanyID,
    IsActive
FROM dbo.Users
ORDER BY UserID; 