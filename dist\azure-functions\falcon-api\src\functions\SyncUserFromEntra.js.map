{"version": 3, "file": "SyncUserFromEntra.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/SyncUserFromEntra.ts"], "names": [], "mappings": ";;;;;;;;;;;AAgBA,8CAwEC;AAxFD,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAkG;AAClG,kEAA+D;AAC/D,6BAA6B;AAQ7B,4CAA4C;AAC5C,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,+BAA+B;AAEtE,SAAsB,iBAAiB,CAAC,OAAoB,EAAE,OAA0B;;QACpF,OAAO,CAAC,GAAG,CAAC,8DAA8D,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAC1F,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAEnD,oCAAoC;QACpC,IAAI,mBAA2B,CAAC;QAEhC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC5E,eAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;YAC3F,mBAAmB,GAAG,CAAC,CAAC,CAAC,sCAAsC;QACnE,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBAClE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,CAAC;YAC3F,CAAC;YAED,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;gBAC/C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,oDAAoD,aAAa,IAAI,CAAC,CAAC;gBACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yDAAyD,EAAE,EAAE,CAAC;YAC3G,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACpE,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,eAAM,CAAC,KAAK,CAAC,oFAAoF,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;gBAChJ,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,6EAA6E,EAAE,EAAE,CAAC;YAC/H,CAAC;YACD,mBAAmB,GAAG,UAAU,CAAC;QACrC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wCAAwC,mBAAmB,EAAE,CAAC,CAAC;QAE3E,qCAAqC;QACrC,IAAI,UAAU,GAAoB,EAAE,CAAC;QAErC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACD,UAAU,IAAG,MAAM,OAAO,CAAC,IAAI,EAAqB,CAAA,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;gBACxE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;YACjF,CAAC;QACL,CAAC;QAED,8DAA8D;QAC9D,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;QAC7D,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC;QAE9C,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wDAAwD,EAAE,EAAE,CAAC;QAC1G,CAAC;QAED,IAAI,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACX,iCAAiC;gBACjC,OAAO,MAAM,YAAY,CAAC,mBAAmB,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACJ,qBAAqB;gBACrB,OAAO,MAAM,cAAc,CAAC,OAAQ,EAAE,mBAAmB,CAAC,CAAC;YAC/D,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,uDAAuD;oBAC9D,OAAO,EAAE,YAAY;iBACxB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,qBAAqB;AACrB,SAAe,cAAc,CAAC,OAAe,EAAE,gBAAwB;;QACnE,eAAM,CAAC,IAAI,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;QAEzD,0CAA0C;QAC1C,MAAM,cAAc,GAAG;;;;KAItB,CAAC;QACF,MAAM,eAAe,GAAqB;YACtC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;SAC1D,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAEvE,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7D,eAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,yBAAyB,CAAC,CAAC;YACpE,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,sBAAsB,OAAO,mEAAmE;iBAC1G;aACJ,CAAC;QACN,CAAC;QAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvC,eAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,MAAM,mBAAmB,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAErH,kCAAkC;QAClC,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,KAAK,EAAE,sBAAsB,OAAO,mCAAmC;iBAC1E;aACJ,CAAC;QACN,CAAC;QAED,kCAAkC;QAClC,MAAM,WAAW,GAAG;;;;;;;;;KASnB,CAAC;QAEF,MAAM,YAAY,GAAqB;YACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;YACvD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE;YACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE;YACnE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE;YAC7D,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE;YAC7E,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;SACjE,CAAC;QAEF,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAE9C,eAAM,CAAC,IAAI,CAAC,4CAA4C,MAAM,CAAC,MAAM,gBAAgB,CAAC,CAAC;QAEvF,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,8CAA8C;gBACvD,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,aAAa,EAAE;oBACX,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;oBAC5B,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,aAAa,EAAE,SAAS,CAAC,aAAa;iBACzC;aACJ;SACJ,CAAC;IACN,CAAC;CAAA;AAED,oCAAoC;AACpC,SAAe,YAAY,CAAC,gBAAwB;;QAChD,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;QAE/D,kCAAkC;QAClC,MAAM,gBAAgB,GAAG;;;;KAIxB,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAE7D,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/D,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,wCAAwC;oBACjD,WAAW,EAAE,CAAC;iBACjB;aACJ,CAAC;QACN,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;QAChD,eAAM,CAAC,IAAI,CAAC,SAAS,UAAU,gBAAgB,CAAC,CAAC;QAEjD,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,oBAAoB;QACpB,KAAK,MAAM,MAAM,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC;gBACD,eAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,MAAM,eAAe,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;gBAE3E,+BAA+B;gBAC/B,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC3E,IAAI,CAAC,SAAS,EAAE,CAAC;oBACb,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,OAAO,uCAAuC,CAAC,CAAC;oBAC3E,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,OAAO,kCAAkC,CAAC,CAAC;oBACtE,UAAU,EAAE,CAAC;oBACb,SAAS;gBACb,CAAC;gBAED,+BAA+B;gBAC/B,MAAM,WAAW,GAAG;;;;;;;;;aASnB,CAAC;gBAEF,MAAM,YAAY,GAAqB;oBACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;oBACvD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE;oBACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE;oBACnE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE;oBAC7D,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE;oBAC7E,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;iBACjE,CAAC;gBAEF,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;gBAC9C,WAAW,EAAE,CAAC;gBAEd,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAE/D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,eAAM,CAAC,KAAK,CAAC,sBAAsB,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,IAAI,EAAE,KAAK,CAAC,CAAC;gBAChF,MAAM,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAChH,UAAU,EAAE,CAAC;YACjB,CAAC;QACL,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,aAAa,UAAU,YAAY,UAAU,EAAE,CAAC,CAAC;QAExG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,wBAAwB,WAAW,6BAA6B;gBACzE,UAAU,EAAE,UAAU;gBACtB,WAAW,EAAE,WAAW;gBACxB,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aACjD;SACJ,CAAC;IACN,CAAC;CAAA;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE;IAC1B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IACxB,KAAK,EAAE,iCAAiC;IACxC,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,iBAAiB;CAC7B,CAAC,CAAC"}