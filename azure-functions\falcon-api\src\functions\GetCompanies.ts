import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { logger } from "../shared/utils/logger";

export async function getCompanies(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log = logger.info;
    logger.info(`Http function processed request for url "${request.url}"`);

    // 1. Authentication & Authorization Check
    const isLocalDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    if (!isLocalDevelopment) {
        // Add auth check for production
        logger.warn("GetCompanies: Authentication check bypassed in production mode - TODO: Add proper auth.");
    } else {
        logger.warn("GetCompanies: Bypassing authentication check in development mode.");
    }

    try {
        // 2. Fetch Companies from Database
        const query = `
            SELECT CompanyID, CompanyName, CompanyCode, IsActive
            FROM Companies 
            WHERE IsActive = 1
            ORDER BY CompanyName
        `;

        const result = await executeQuery(query, []);

        // 3. Return Response
        return {
            status: 200,
            jsonBody: result.recordset
        };

    } catch (err) {
        logger.error(`Error fetching companies: ${err}`);
        const error = err as Error;
        return {
            status: 500,
            jsonBody: {
                error: "Failed to fetch companies",
                details: error.message
            }
        };
    }
}

app.http('GetCompanies', {
    methods: ['GET'],
    route: 'companies',
    authLevel: 'anonymous',
    handler: getCompanies
}); 