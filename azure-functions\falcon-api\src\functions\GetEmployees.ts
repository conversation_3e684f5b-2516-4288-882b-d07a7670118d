import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

// Employee interface for HR Hub
interface Employee {
    id: number;
    entraId: string | null;
    name: string;
    email: string;
    firstName: string;
    lastName: string;
    employeeId: string | null;
    jobTitle?: string;
    department: string | null;
    location: string | null;
    company: string;
    companyId: number;
    contactNumber: string | null;
    profileImageURL: string | null;
    managerId: number | null;
    managerName: string | null;
    dateOfJoining: string | null;
    lastLoginDate: string | null;
    isActive: boolean;
}

// Employee search/filter parameters
interface EmployeeSearchParams {
    search?: string;
    department?: string;
    location?: string;
    company?: string;
    status?: 'all' | 'active' | 'inactive';
    limit?: number;
    offset?: number;
}

async function getEmployees(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info("GetEmployees: Processing request");
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        
        if (!isDevelopment && principal) {
            const internalUserId = await getUserIdFromPrincipal(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams: QueryParameter[] = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await executeQuery(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        
        logger.info(`GetEmployees: Processing request for user: ${userId}, company: ${userCompanyId}`);
        
        // Parse query parameters
        const url = new URL(req.url);
        const searchParams: EmployeeSearchParams = {
            search: url.searchParams.get('search') || undefined,
            department: url.searchParams.get('department') || undefined,
            location: url.searchParams.get('location') || undefined,
            company: url.searchParams.get('company') || undefined,
            status: (url.searchParams.get('status') as 'all' | 'active' | 'inactive') || 'active',
            limit: parseInt(url.searchParams.get('limit') || '50'),
            offset: parseInt(url.searchParams.get('offset') || '0')
        };

        // Build dynamic query
        let whereClause = 'WHERE u.CompanyID = @CompanyID';
        const parameters: QueryParameter[] = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];

        // Add status filter
        if (searchParams.status === 'active') {
            whereClause += ' AND u.IsActive = 1';
        } else if (searchParams.status === 'inactive') {
            whereClause += ' AND u.IsActive = 0';
        }

        // Add search filter
        if (searchParams.search) {
            whereClause += ` AND (
                u.FirstName LIKE @Search 
                OR u.LastName LIKE @Search 
                OR u.Email LIKE @Search 
                OR u.EmployeeID LIKE @Search
                OR (u.FirstName + ' ' + u.LastName) LIKE @Search
            )`;
            parameters.push({
                name: 'Search',
                type: sql.NVarChar,
                value: `%${searchParams.search}%`
            });
        }

        // Add department filter
        if (searchParams.department) {
            whereClause += ' AND d.DepartmentName = @Department';
            parameters.push({
                name: 'Department',
                type: sql.NVarChar,
                value: searchParams.department
            });
        }

        // Add location filter
        if (searchParams.location) {
            whereClause += ' AND l.LocationName = @Location';
            parameters.push({
                name: 'Location',
                type: sql.NVarChar,
                value: searchParams.location
            });
        }

        // Main query to get employees with their details
        const query = `
            WITH EmployeeData AS (
                SELECT 
                    u.UserID,
                    u.EntraID,
                    u.FirstName,
                    u.LastName,
                    (u.FirstName + ' ' + u.LastName) as FullName,
                    u.Email,
                    u.EmployeeID,
                    u.ContactNumber,
                    u.ProfileImageURL,
                    u.ManagerID,
                    u.DateOfJoining,
                    u.LastLoginDate,
                    u.IsActive,
                    u.CompanyID,
                    c.CompanyName,
                    d.DepartmentName,
                    l.LocationName,
                    m.FirstName + ' ' + m.LastName as ManagerName
                FROM Users u
                LEFT JOIN Companies c ON u.CompanyID = c.CompanyID
                LEFT JOIN Departments d ON u.DepartmentID = d.DepartmentID
                LEFT JOIN Locations l ON u.LocationID = l.LocationID
                LEFT JOIN Users m ON u.ManagerID = m.UserID
                ${whereClause}
            ),
            TotalCount AS (
                SELECT COUNT(*) as Total FROM EmployeeData
            )
            SELECT 
                e.*,
                t.Total as TotalCount
            FROM EmployeeData e
            CROSS JOIN TotalCount t
            ORDER BY e.FullName
            OFFSET @Offset ROWS
            FETCH NEXT @Limit ROWS ONLY;
        `;

        parameters.push(
            { name: 'Offset', type: sql.Int, value: searchParams.offset || 0 },
            { name: 'Limit', type: sql.Int, value: Math.min(searchParams.limit || 50, 100) } // Max 100 records
        );

        logger.info(`GetEmployees: Executing query with parameters:`, { searchParams, whereClause });
        
        const result = await executeQuery(query, parameters);
        
        if (!result.recordset || result.recordset.length === 0) {
            logger.info(`GetEmployees: No employees found`);
            return {
                status: 200,
                jsonBody: {
                    employees: [],
                    totalCount: 0,
                    hasMore: false,
                    searchParams
                }
            };
        }

        const totalCount = result.recordset[0]?.TotalCount || 0;
        const hasMore = (searchParams.offset || 0) + result.recordset.length < totalCount;

        // Map results to Employee interface
        const employees: Employee[] = result.recordset.map((row: any) => ({
            id: row.UserID,
            entraId: row.EntraID,
            name: row.FullName,
            email: row.Email,
            firstName: row.FirstName,
            lastName: row.LastName,
            employeeId: row.EmployeeID,
            department: row.DepartmentName,
            location: row.LocationName,
            company: row.CompanyName,
            companyId: row.CompanyID,
            contactNumber: row.ContactNumber,
            profileImageURL: row.ProfileImageURL,
            managerId: row.ManagerID,
            managerName: row.ManagerName,
            dateOfJoining: row.DateOfJoining ? new Date(row.DateOfJoining).toISOString().split('T')[0] : null,
            lastLoginDate: row.LastLoginDate ? new Date(row.LastLoginDate).toISOString() : null,
            isActive: row.IsActive
        }));

        logger.info(`GetEmployees: Found ${employees.length} employees (${totalCount} total)`);

        return {
            status: 200,
            jsonBody: {
                employees,
                totalCount,
                hasMore,
                searchParams
            }
        };

    } catch (error) {
        logger.error("GetEmployees: Error processing request:", error);
        return { 
            status: 500, 
            jsonBody: { 
                error: "Internal server error", 
                details: error instanceof Error ? error.message : String(error) 
            } 
        };
    }
}

// Register the function
app.http('GetEmployees', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'employees',
    handler: getEmployees
});

export { getEmployees }; 