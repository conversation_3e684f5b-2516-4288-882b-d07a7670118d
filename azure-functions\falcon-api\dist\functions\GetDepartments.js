"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDepartments = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const sql = __importStar(require("mssql"));
async function getDepartments(req, context) {
    logger_1.logger.info("GetDepartments: Processing request");
    try {
        // Check authentication
        const principal = (0, authUtils_1.getClientPrincipal)(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        const userId = principal?.userId || 'dev-user';
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        if (!isDevelopment && principal) {
            const internalUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await (0, db_1.executeQuery)(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        logger_1.logger.info(`GetDepartments: Processing request for user: ${userId}, company: ${userCompanyId}`);
        // Query to get departments with employee counts
        const query = `
            SELECT 
                d.DepartmentID,
                d.DepartmentName,
                d.CompanyID,
                c.CompanyName,
                d.IsActive,
                COUNT(u.UserID) as EmployeeCount
            FROM Departments d
            LEFT JOIN Companies c ON d.CompanyID = c.CompanyID
            LEFT JOIN Users u ON d.DepartmentID = u.DepartmentID AND u.IsActive = 1
            WHERE d.CompanyID = @CompanyID AND d.IsActive = 1
            GROUP BY d.DepartmentID, d.DepartmentName, d.CompanyID, c.CompanyName, d.IsActive
            ORDER BY d.DepartmentName;
        `;
        const parameters = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];
        logger_1.logger.info(`GetDepartments: Executing query for company ${userCompanyId}`);
        const result = await (0, db_1.executeQuery)(query, parameters);
        if (!result.recordset) {
            logger_1.logger.info(`GetDepartments: No departments found`);
            return {
                status: 200,
                jsonBody: {
                    departments: []
                }
            };
        }
        // Map results to Department interface
        const departments = result.recordset.map((row) => ({
            id: row.DepartmentID,
            name: row.DepartmentName,
            companyId: row.CompanyID,
            companyName: row.CompanyName,
            isActive: row.IsActive,
            employeeCount: row.EmployeeCount || 0
        }));
        logger_1.logger.info(`GetDepartments: Found ${departments.length} departments`);
        return {
            status: 200,
            jsonBody: {
                departments
            }
        };
    }
    catch (error) {
        logger_1.logger.error("GetDepartments: Error processing request:", error);
        return {
            status: 500,
            jsonBody: {
                error: "Internal server error",
                details: error instanceof Error ? error.message : String(error)
            }
        };
    }
}
exports.getDepartments = getDepartments;
// Register the function
functions_1.app.http('GetDepartments', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'departments',
    handler: getDepartments
});
//# sourceMappingURL=GetDepartments.js.map