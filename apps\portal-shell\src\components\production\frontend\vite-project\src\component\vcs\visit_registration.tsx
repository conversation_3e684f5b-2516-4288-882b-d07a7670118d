import { useState, useEffect } from 'react';

const VisitorControlSystem = () => {


  function generateTimes(start = 7, end = 23) {
    const times = [];
    for (let h = start; h <= end; h++) {
      const hour12 = h > 12 ? h - 12 : h === 0 ? 12 : h;
      const ampm = h < 12 ? 'AM' : 'PM';
      times.push(`${hour12}:00 ${ampm}`);
      times.push(`${hour12}:30 ${ampm}`);
    }
    return times;
  }

  const timeOptions = generateTimes();

  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [purpose, setPurpose] = useState('');
  const [place, setPlace] = useState('');
  const [selectedValue, setSelectedValue] = useState('');
  const [selectedRestrictedArea, setSelectedRestrictedArea] = useState('');
  const [isRestrictedDisabled, setIsRestrictedDisabled] = useState(true);
  const [selectedVisitor, setSelectedVisitors] = useState([]);
  const [submitTrigger, setSubmitTrigger] = useState(false);
  const [visitorData,setvisitorData] = useState([]);
useEffect(() => {
  fetch(`http://localhost:3000/api/get_visitor_names`)
    .then(response => response.json())
    .then(data => {
      console.log("Fetched visitor data:", data); // Add this
        setvisitorData(data.result)
    })
    .catch(err => console.log("Fetch error:", err));
}, []);
  const company_visit_array = ["SASMOS", "FE-SIL", "SASMOS-SEZ", "ADS-WHITEFIELD"];
  const general_area_array = ["OFFICE AREA", "SHOP FLOOR", "EOU", "DTA", "STORES", "RESTRICTED AREA"];
  const restricted_area_array = ["BOEING", "FESIL", "ISRO", "ENGINEERING", "TESTING", "LOCKHEED MARTIN"];

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSelectedValue(value);

    if (value === "RESTRICTED AREA") {
      setIsRestrictedDisabled(false);
    } else {
      setIsRestrictedDisabled(true);
      setSelectedRestrictedArea('');
    }
  };

  const handleRestrictedAreaChange = (e) => {
    setSelectedRestrictedArea(e.target.value);
  };

  const handleCheckboxChange = (id) => {
    setSelectedVisitors(prev =>
      prev.includes(id) ? prev.filter(vid => vid !== id) : [...prev, id]
    );
  };

  const handleReset = () => {
    setFromDate('');
    setToDate('');
    setStartTime('');
    setEndTime('');
    setPurpose('');
    setPlace('');
    setSelectedValue('');
    setSelectedRestrictedArea('');
    setSelectedVisitors([]);
    setIsRestrictedDisabled(true);
  };

  useEffect(() => {
    if (!submitTrigger) return;

    // Validation
    if (selectedVisitor.length === 0) {
      alert("Select at least one visitor");
      setSubmitTrigger(false);
      return;
    }

    if (!fromDate || !toDate || !startTime || !endTime || !purpose || !place) {
      alert("Please fill all required fields");
      setSubmitTrigger(false);
      return;
    }

    const payload = {
      visitorIds: selectedVisitor,
      fromDate: fromDate,
      toDate: toDate,
      startTime: startTime,
      endTime: endTime,
      purpose: purpose,
      place: place,
      areaAccess: selectedValue,
      restrictedArea: selectedRestrictedArea
    };

    console.log('Submitting payload:', payload);

    fetch("http://localhost:3000/api/submit_visit_registration", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    })
      .then(res => res.json())
      .then(result => {
        console.log('Server response:', result);
        alert(result.message || "Submitted successfully");
        if (result.message && !result.message.includes('failed')) {
          handleReset();
        }
        setSubmitTrigger(false);
      })
      .catch(error => {
        console.error("Submit error:", error);
        alert("Error submitting data. Please try again.");
        setSubmitTrigger(false);
      });

  }, [submitTrigger, selectedVisitor, fromDate, toDate, startTime, endTime, purpose, place, selectedValue, selectedRestrictedArea]);

  const startIndex = timeOptions.indexOf(startTime);
  const endOptions = startIndex >= 0 ? timeOptions.slice(startIndex + 1) : timeOptions;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {/* Visitor Invite Form */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-center text-lg font-bold mb-6 text-red-600">VISITOR INVITE FORM</h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {/* Date Fields */}
            <div>
              <label className="block text-sm font-medium mb-2">FROM DATE *</label>
              <input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded mt-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={startTime}
                onChange={(e) => {
                  setStartTime(e.target.value);
                  setEndTime('');
                }}
                required
              >
                <option value="">-- Start Time --</option>
                {timeOptions.map((t, i) => <option key={i} value={t}>{t}</option>)}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">TO DATE *</label>
              <input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded mt-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                required
              >
                <option value="">-- End Time --</option>
                {endOptions.map((t, i) => <option key={i} value={t}>{t}</option>)}
              </select>
            </div>

            {/* Purpose of Visit */}
            <div>
              <label className="block text-sm font-medium mb-2">PURPOSE OF VISIT *</label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded h-20 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter purpose of visit..."
                value={purpose}
                onChange={(e) => setPurpose(e.target.value)}
                required
              />
            </div>

            {/* Place and Access Fields */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">PLACE OF VISIT *</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={place}
                  onChange={(e) => setPlace(e.target.value)}
                  required
                >
                  <option value="">-SELECT PLACE-</option>
                  {company_visit_array.map((item, index) => (
                    <option key={index} value={item}>{item}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">AREA ACCESS</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={selectedValue}
                  onChange={handleInputChange}
                >
                  <option value="">-SELECT AREA-</option>
                  {general_area_array.map((item, index) => {
                    const bgColor = item === "RESTRICTED AREA" ? "bg-yellow-200" : "";
                    return (
                      <option key={index} value={item} className={bgColor}>
                        {item}
                      </option>
                    );
                  })}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">RESTRICTED AREA NAME</label>
                <select
                  className={`w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    isRestrictedDisabled ? 'bg-gray-100 cursor-not-allowed' : ''
                  }`}
                  value={selectedRestrictedArea}
                  onChange={handleRestrictedAreaChange}
                  disabled={isRestrictedDisabled}
                >
                  <option value="">NONE</option>
                  {restricted_area_array.map((item, index) => (
                    <option key={index} value={item}>
                      {item}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Visitor Data Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-blue-100">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">
                    SELECT
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">NAME</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">ORGANISATION</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">NATIONALITY</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">LOCATION</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">CONTACT #</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">MAIL ID</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {visitorData.map((visitor, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedVisitor.includes(visitor.id)}
                        onChange={() => handleCheckboxChange(visitor.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      {visitor.visitor_name}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {visitor.visitor_org}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {visitor.visitor_nation}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {visitor.visitor_loc}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {visitor.visitor_contact}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-600">
                      {visitor.visitor_mail}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {selectedVisitor.length > 0 && (
            <div className="p-4 bg-blue-50 border-t">
              <p className="text-sm text-blue-600">
                Selected visitors: {selectedVisitor.length}
              </p>
            </div>
          )}
        </div>

        {/* Form Buttons */}
        <div className="flex justify-center space-x-4 mt-6">
          <button
            className="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            onClick={handleReset}
          >
            RESET
          </button>
          <button
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            onClick={() => setSubmitTrigger(true)}
          >
            SUBMIT
          </button>
        </div>
      </div>
    </div>
  );
};

export default VisitorControlSystem;