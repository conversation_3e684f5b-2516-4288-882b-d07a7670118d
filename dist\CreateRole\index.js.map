{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../CreateRole/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAgBA,gCAwFC;AAxGD,gDAAyF;AACzF,qCAA4C,CAAC,iDAAiD;AAe9F,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;;QAC7E,OAAO,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,GAAG,qBAAqB,CAAC,CAAC;QAE1F,IAAI,IAA2B,CAAC;QAChC,IAAI,CAAC;YACD,iCAAiC;YACjC,IAAI,IAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAA,CAAC;QACzD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACzD,CAAC;QACN,CAAC;QAED,MAAM,QAAQ,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,0CAAE,IAAI,EAAE,CAAC;QACpC,MAAM,WAAW,GAAG,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,0CAAE,IAAI,EAAE,KAAI,IAAI,CAAC,CAAC,iCAAiC;QAExF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,8CAA8C,EAAE;aACxE,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,qDAAqD;YACrD,MAAM,UAAU,GAAG,kFAAkF,CAAC;YACtG,MAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAEhE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,6BAA6B,QAAQ,mBAAmB,CAAC,CAAC;gBACtE,OAAO;oBACH,MAAM,EAAE,GAAG,EAAE,WAAW;oBACxB,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,QAAQ,mBAAmB,EAAE;iBACxE,CAAC;YACN,CAAC;YAED,yBAAyB;YACzB,mFAAmF;YACnF,MAAM,WAAW,GAAG;;;;SAInB,CAAC;YACF,MAAM,YAAY,GAAG;gBACjB,aAAa,EAAE,QAAQ;gBACvB,gBAAgB,EAAE,WAAW;gBAC7B,gEAAgE;aACnE,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAE7D,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,OAAO,GAAmB;oBAC5B,EAAE,EAAE,SAAS,CAAC,MAAM;oBACpB,IAAI,EAAE,SAAS,CAAC,QAAQ;oBACxB,WAAW,EAAE,SAAS,CAAC,WAAW;iBACrC,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,cAAc,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC9E,OAAO;oBACH,MAAM,EAAE,GAAG,EAAE,UAAU;oBACvB,QAAQ,EAAE;wBACN,OAAO,EAAE,SAAS,QAAQ,yBAAyB;wBACnD,IAAI,EAAE,OAAO;qBAChB;iBACJ,CAAC;YACN,CAAC;iBAAM,CAAC;gBACH,OAAO,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;gBACzE,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,4DAA4D,EAAE;iBACtF,CAAC;YACP,CAAC;QAEL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,wBAAwB,QAAQ,IAAI,EAAE,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9F,iHAAiH;YACjH,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,8CAA8C;oBACvD,KAAK,EAAE,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,mCAAmC;iBAClG;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,4EAA4E;AAC5E,eAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE;IAC3B,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,UAAU,EAAE,qDAAqD;IAC5E,KAAK,EAAE,OAAO,EAAE,kCAAkC;IAClD,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}