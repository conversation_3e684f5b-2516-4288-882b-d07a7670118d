import React from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search } from 'feather-icons-react';
import { useCurrentUser } from '../services/userContext';

const hasElevatedAccess = (roles: string[] = []) =>
  roles.includes('Administrator') || roles.includes('IT Admin');

const mockDocuments = [
  {
    id: 1,
    title: 'IT Security Policy',
    type: 'IT Policy',
    description: 'Guidelines for information security and access control.',
    lastUpdated: '2024-07-01',
    version: 'v1.2',
  },
  {
    id: 2,
    title: 'Password Management',
    type: 'Guideline',
    description: 'Best practices for password creation and management.',
    lastUpdated: '2024-06-15',
    version: 'v1.0',
  },
];

const DocumentsPage: React.FC = () => {
  const { user } = useCurrentUser();
  return (
    <div className="p-8 min-h-screen bg-white">
      {/* Breadcrumbs */}
      <nav className="text-sm text-gray-500 mb-6">
        <Link to="/it" className="hover:underline">IT Hub</Link> <span className="mx-2">/</span> <span className="text-gray-700 font-semibold">Policies</span>
      </nav>

      {/* Header and Upload */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
        <h1 className="text-2xl font-bold text-gray-900">IT Policies & Documents</h1>
        {hasElevatedAccess(user?.roles) && (
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition-colors">
            <Plus size={18} className="mr-2" /> Upload Document
          </button>
        )}
      </div>

      {/* Search Bar */}
      <div className="mb-8 max-w-xl">
        <div className="relative">
          <span className="absolute left-3 top-2.5 text-gray-400">
            <Search size={18} />
          </span>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:ring-2 focus:ring-blue-200 focus:outline-none text-gray-900 bg-gray-50"
            placeholder="Search IT policies and documents..."
            // TODO: Add search logic
          />
        </div>
      </div>

      {/* Document Card Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockDocuments.map(doc => (
          <div key={doc.id} className="bg-white rounded-xl shadow p-6 flex flex-col">
            <div className="flex items-center mb-2">
              <span className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-700 font-medium mr-2">{doc.type}</span>
              <span className="text-xs text-gray-400 ml-auto">{doc.version}</span>
            </div>
            <div className="font-semibold text-lg text-gray-900 truncate mb-1">{doc.title}</div>
            <div className="text-sm text-gray-600 mb-3 truncate">{doc.description}</div>
            <div className="text-xs text-gray-400 mb-4">Last updated: {doc.lastUpdated}</div>
            <div className="mt-auto flex gap-2">
              <button className="px-3 py-1 bg-blue-50 text-blue-700 rounded hover:bg-blue-100 text-xs font-medium">View</button>
              <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-xs font-medium">Download</button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocumentsPage; 