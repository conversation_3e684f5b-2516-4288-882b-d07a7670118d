"use strict";
// main.ts - Single source of truth for function registration
Object.defineProperty(exports, "__esModule", { value: true });
// --- Core & System ---
require("./functions/GetCompanies");
require("./functions/ServeImage");
require("./UploadImage/index");
// --- Role Management ---
require("./functions/GetRoles");
require("./functions/CreateRole");
require("./functions/UpdateRole");
require("./functions/DeleteRole");
// --- User Management ---
require("./functions/GetPortalUsers");
require("./functions/GetPortalUserById");
require("./functions/CreatePortalUser");
require("./functions/UpdatePortalUser");
require("./functions/DeletePortalUser");
require("./functions/GetCurrentUser");
require("./GetUserOverrides/index");
// --- Change Management ---
require("./GetChangeRequests/index");
require("./CreateChangeRequest/index");
require("./GetChangeRequestById/index");
require("./UpdateChangeRequest/index");
require("./SubmitChangeRequest/index");
require("./ApproveChangeRequest/index");
require("./RejectChangeRequest/index");
require("./GetChangeRequestComments/index");
require("./AddChangeRequestComment/index");
require("./GetChangeRequestDashboardStats/index");
require("./AssignChangeRequest/index");
require("./RequestMoreInfoChangeRequest/index");
require("./UpdateRequestedCompletionDate/index");
// --- Draft Management ---
require("./GetChangeRequestDrafts/index");
require("./SaveChangeRequestDraft/index");
require("./DeleteChangeRequestDraft/index");
// --- Zoho Desk Integration ---
require("./functions/ZohoDeskAuth");
require("./functions/ZohoDeskAPI");
// --- Test Functions ---
require("./TestSimple/index");
require("./SimpleTest");
//# sourceMappingURL=main.js.map