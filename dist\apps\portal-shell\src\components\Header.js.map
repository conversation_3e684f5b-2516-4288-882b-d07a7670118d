{"version": 3, "file": "Header.js", "sourceRoot": "", "sources": ["../../../../../apps/portal-shell/src/components/Header.tsx"], "names": [], "mappings": ";;AAAA,iCAA2D;AAC3D,kDAA4C;AAC5C,6DAAwF;AAExF,uDAAuD;AACvD,0EAA0E;AAE1E,MAAM,MAAM,GAAa,GAAG,EAAE;;IAC5B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAA,oBAAO,GAAE,CAAC;IACzC,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClE,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAChE,MAAM,gBAAgB,GAAG,IAAA,cAAM,EAAiB,IAAI,CAAC,CAAC;IACtD,MAAM,eAAe,GAAG,IAAA,cAAM,EAAiB,IAAI,CAAC,CAAC;IAErD,MAAM,QAAQ,GAAG,MAAA,QAAQ,CAAC,CAAC,CAAC,0CAAE,IAAI,CAAC,CAAC,mCAAmC;IACvE,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAEtE,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,QAAQ,CAAC,cAAc,CAAC,EAAE,qBAAqB,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC;IAEF,wCAAwC;IACxC,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,kBAAkB,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC/C,IAAI,gBAAgB,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,CAAC;gBACzF,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,eAAe,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,CAAC;gBACvF,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;QAEF,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAC3D,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAChE,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CACL,CAAC,MAAM,CAAC,SAAS,CAAC,sCAAsC,CACtD;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,uCAAuC,CACpD;QAAA,CAAC,gBAAgB,CACjB;QAAA,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAC9B;UAAA,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CACvB;YAAA,CAAC,IAAI,CAAC,SAAS,CAAC,kDAAkD,CAChE;cAAA,CAAC,4BAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,eAAe,EAC7C;YAAA,EAAE,IAAI,CACN;YAAA,CAAC,KAAK,CACJ,IAAI,CAAC,MAAM,CACX,WAAW,CAAC,yBAAyB,CACrC,SAAS,CAAC,oHAAoH,EAElI;UAAA,EAAE,GAAG,CACP;QAAA,EAAE,GAAG,CAEL;;QAAA,CAAC,wBAAwB,CACzB;QAAA,CAAC,GAAG,CAAC,SAAS,CAAC,6BAA6B,CAC1C;UAAA,CAAC,mBAAmB,CACpB;UAAA,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC,CAC9C;YAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACzF,SAAS,CAAC,4CAA4C,CACtD,UAAU,CAAC,eAAe,CAE1B;cAAA,CAAC,0BAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACf;cAAA,CAAC,8CAA8C,CAC/C;cAAA,CAAC,IAAI,CAAC,SAAS,CAAC,wDAAwD,CAAC,EAAE,IAAI,CACjF;YAAA,EAAE,MAAM,CAER;;YAAA,CAAC,iBAAiB,IAAI,CACpB,CAAC,GAAG,CAAC,SAAS,CAAC,oFAAoF,CACjG;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB,CACjC;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,qBAAqB,CAAC,aAAa,EAAE,GAAG,CACzD;gBAAA,EAAE,GAAG,CACL;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,0BAA0B,CACvC;kBAAA,CAAC,8CAA8C,CAC/C;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,4DAA4D,CACzE;oBAAA,CAAC,GAAG,CAAC,qCAAqC,EAAE,GAAG,CAC/C;oBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CAAC,WAAW,EAAE,GAAG,CAC9D;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,4DAA4D,CACzE;oBAAA,CAAC,GAAG,CAAC,2CAA2C,EAAE,GAAG,CACrD;oBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CAAC,SAAS,EAAE,GAAG,CAC5D;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,gCAAgC,CACnC;gBAAA,EAAE,GAAG,CACL;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,gCAAgC,CAC7C;kBAAA,CAAC,MAAM,CAAC,SAAS,CAAC,2CAA2C,CAC3D;;kBACF,EAAE,MAAM,CACV;gBAAA,EAAE,GAAG,CACP;cAAA,EAAE,GAAG,CAAC,CACP,CACH;UAAA,EAAE,GAAG,CAEL;;UAAA,CAAC,eAAe,CAChB;UAAA,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,CAC7C;YAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,mBAAmB,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CACxF,SAAS,CAAC,+DAA+D,CACzE,UAAU,CAAC,WAAW,CAEtB;cAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yFAAyF,CACtG;gBAAA,CAAC,WAAW,CACd;cAAA,EAAE,GAAG,CACL;cAAA,CAAC,iCAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACxB;YAAA,EAAE,MAAM,CAER;;YAAA,CAAC,gBAAgB,IAAI,CACnB,CAAC,GAAG,CAAC,SAAS,CAAC,oFAAoF,CACjG;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,EAAE,GAAG,CACvE;kBAAA,CAAC,+BAA+B,CAChC;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,gCAAgC,CAAC,gBAAgB,EAAE,GAAG,CACvE;gBAAA,EAAE,GAAG,CACL;gBAAA,CAAC,MAAM,CAAC,SAAS,CAAC,sFAAsF,CACtG;kBAAA,CAAC,0BAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,EAC9C;kBAAA,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CACxB;gBAAA,EAAE,MAAM,CACR;gBAAA,CAAC,MAAM,CAAC,SAAS,CAAC,sFAAsF,CACtG;kBAAA,CAAC,8BAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,oBAAoB,EAClD;kBAAA,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CACtB;gBAAA,EAAE,MAAM,CACR;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,GAAG,CACpC;gBAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,YAAY,CAAC,CACtB,SAAS,CAAC,mFAAmF,CAE7F;kBAAA,CAAC,4BAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,EAClC;kBAAA,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CACtB;gBAAA,EAAE,MAAM,CACV;cAAA,EAAE,GAAG,CAAC,CACP,CACH;UAAA,EAAE,GAAG,CACP;QAAA,EAAE,GAAG,CACP;MAAA,EAAE,GAAG,CACP;IAAA,EAAE,MAAM,CAAC,CACV,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,MAAM,CAAC"}