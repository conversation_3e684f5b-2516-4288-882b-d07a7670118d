{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/RemoveRole/index.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,oFAA8E;AAC9E,mDAAgD;AAChD,mDAAkG;AAClG,mEAAyF;AAEzF,0BAA0B;AAC1B,mHAAmH;AACnH,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,yCAAyC;AAEzE,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,OAAO,CAAC,GAAG,CAAC,uDAAuD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;IACnF,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAE5C,0CAA0C;IAC1C,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;KAC5F;IACD,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE;QAC7C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,6CAA6C,aAAa,IAAI,CAAC,CAAC;QAClI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yDAAyD,EAAE,EAAE,CAAC;KAC7G;IACD,MAAM,mBAAmB,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7E,IAAI,CAAC,mBAAmB,EAAE;QACtB,eAAM,CAAC,KAAK,CAAC,6EAA6E,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;QACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,6EAA6E,EAAE,EAAE,CAAC;KAChI;IACD,+EAA+E;IAC/E,6EAA6E;IAC7E,6EAA6E;IAC7E,eAAM,CAAC,IAAI,CAAC,iCAAiC,mBAAmB,EAAE,CAAC,CAAC;IACpE,oBAAoB;IAEpB,4BAA4B;IAC5B,MAAM,WAAW,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;QAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM;KAChC,CAAC,CAAC,yBAAyB;IAE5B,MAAM,eAAe,GAAG,IAAA,mCAAe,EAAC,6CAAyB,EAAE,WAAW,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;IAC7G,IAAI,eAAe;QAAE,OAAO,eAAe,CAAC;IAE5C,wCAAwC;IACxC,MAAM,oBAAoB,GAAG,6CAAyB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,8BAA8B;IACzG,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,mBAAmB;IAC/D,MAAM,MAAM,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,mBAAmB;IAC/D,0BAA0B;IAE1B,IAAI;QACA,MAAM,OAAO,GAAG,MAAM,IAAA,0CAAkB,EAAC,MAAM,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAE9E,IAAI,OAAO,EAAE;YACT,qDAAqD;YACrD,uDAAuD;YACvD,OAAO;gBACH,MAAM,EAAE,GAAG;aACd,CAAC;SACL;aAAM;YACH,wDAAwD;YACxD,6CAA6C;YAC7C,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE;aAClD,CAAC;SACL;KAEJ;IAAC,OAAO,KAAK,EAAE;QACZ,sEAAsE;QACtE,eAAM,CAAC,KAAK,CAAC,oDAAoD,MAAM,UAAU,MAAM,cAAc,mBAAmB,GAAG,EAAE,KAAK,CAAC,CAAC;QACpI,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACN,OAAO,EAAE,uDAAuD;gBAChE,KAAK,EAAE,YAAY;aACtB;SACJ,CAAC;KACL;AACL,CAAC;AAvED,gCAuEC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,+BAA+B;IACtC,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}