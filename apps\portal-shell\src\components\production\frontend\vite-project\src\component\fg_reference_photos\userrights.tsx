"use client"

import { useState, useEffect } from "react"
import Select from "react-select"
import axios from "axios"
// import { useNavigate } from "react-router-dom" // Unused import

const rightsList = ["ASSIGN RIGHTS", "CREATE", "EDIT", "MASTER USER"]

function UserRightsForm() {
  const [selectedUser, setSelectedUser] = useState(null)
  const [selectedRights, setSelectedRights] = useState([])
  const [employees, setEmployees] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasAccess, setHasAccess] = useState(false)
  // const navigate = useNavigate() // Unused hook

  const logged_emp_name = localStorage.getItem("emp_name") || "Employee"
  const logged_emp_id = localStorage.getItem("emp_id") || "S0268"


 useEffect(() => {
  const checkAccess = async () => {

    let userRights = localStorage.getItem("user_rights")
    const empId = localStorage.getItem("emp_id") // make sure this is stored at login
    //alert("empId: " + empId)


    if (!userRights && empId) {
      try {
        const res = await fetch(`http://localhost:3000/api/get_current_userrights?emp_id=${empId}`)
        const data = await res.json()

        if (res.ok && data.user_rights) {
          userRights = JSON.stringify(data.user_rights)
          localStorage.setItem("user_rights", userRights)
        } else {
          console.warn("No user rights found for this user.")
        }
      } catch (error) {
        console.error("Failed to fetch user rights:", error)
      }
    }

    let rights = []
    try {
      rights = JSON.parse(userRights || "[]")
    } catch (e) {
      rights = [userRights]
    }

    //alert(JSON.stringify(rights))
    const authorized =
      rights.includes("MASTER USER") ||
      rights.includes("ASSIGN RIGHTS")

    setHasAccess(authorized)
  }

  checkAccess()
}, [])


  useEffect(() => {
    if (!hasAccess) return;

    const fetchEmployees = async () => {
      try {
        setIsLoading(true)
        // Fixed: Changed to get active employees endpoint
        const res = await axios.get("http://localhost:3000/api/get_active_employees")

        if (res.data && Array.isArray(res.data)) {
          const formatted = res.data.map((emp) => ({
            value: `${emp.employee_id}|${emp.employee_name}`,
            label: `${emp.employee_name} - ${emp.employee_id}`,
          }))
          setEmployees(formatted)
        } else {
          console.error("Unexpected response format:", res.data)
          alert("Error: Unexpected response format from server")
        }
      } catch (error) {
        console.error("Error fetching employees:", error)
        alert("Error fetching employees: " + (error.response?.data?.message || error.message))
      } finally {
        setIsLoading(false)
      }
    }

    fetchEmployees()
  }, [hasAccess])

  // Fetch user rights when a user is selected
  useEffect(() => {
    if (!hasAccess || !selectedUser) return;

    const fetchUserRights = async () => {
      try {
        const [emp_id] = selectedUser.value.split("|")
        const res = await axios.get(`http://localhost:3000/api/get_current_userrights?emp_id=${emp_id}`)

        if (res.data && res.data.user_rights) {
          setSelectedRights(res.data.user_rights)
        } else {
          // New user with no rights yet
          setSelectedRights([])
        }
      } catch (error) {
        if (error.response?.status === 404) {
          // User has no rights yet, this is normal
          setSelectedRights([])
        } else {
          console.error("Error fetching user rights:", error)
          alert("Error fetching user rights: " + (error.response?.data?.message || error.message))
        }
      }
    }

    fetchUserRights()
  }, [selectedUser, hasAccess])

  const handleCheckboxChange = (e) => {
    const value = e.target.value
    setSelectedRights((prev) => (prev.includes(value) ? prev.filter((r) => r !== value) : [...prev, value]))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!selectedUser) {
      alert("Please select a user")
      return
    }

    setIsLoading(true)
    const [emp_id, emp_name] = selectedUser.value.split("|")

    try {
      // Fixed: Changed to the correct endpoint
      const res = await axios.post("http://localhost:3000/api/assign_user_rights", {
        employee_id: emp_id,
        employee_name: emp_name,
        user_rights: selectedRights,
        logged_emp_name: logged_emp_name,
        logged_emp_id: logged_emp_id,
      })

      alert(res.data.message)
      window.location.reload()
      //navigate("/home_page")
    } catch (error) {
      alert("Error: " + (error.response?.data?.message || error.message))
    } finally {
      setIsLoading(false)
    }
  }

  // If the user doesn't have access, show the unauthorized message
  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen p-6">
        <div className="bg-red-50 border border-red-400 text-red-800 rounded-xl p-8 shadow-lg text-center">
          <h1 className="text-3xl font-bold mb-4">**YOU ARE NOT AUTHORISED TO USE THIS FORM!!**</h1>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen p-6">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6 bg-blue-600 text-white">
          <h2 className="text-2xl font-bold text-center">User Rights Management</h2>
          <p className="text-center text-blue-100 mt-1">
            Assign system permissions to users
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 uppercase tracking-wide">
              Select User
            </label>
            <Select
              options={employees}
              value={selectedUser}
              onChange={setSelectedUser}
              placeholder="Search and select user..."
              isLoading={isLoading}
              className="text-sm"
              classNamePrefix="react-select"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 uppercase tracking-wide">
              User Rights
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
              {rightsList.map((right) => (
                <div key={right} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 bg-gray-50 hover:bg-gray-100 transition-colors">
                  <input
                    type="checkbox"
                    id={right}
                    value={right}
                    checked={selectedRights.includes(right)}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                  />
                  <label
                    htmlFor={right}
                    className="text-sm font-medium text-gray-700 cursor-pointer"
                  >
                    {right}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <button
            type="submit"
            className="w-full py-3 mt-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : "Assign Rights"}
          </button>
        </form>
      </div>
    </div>
  )
}

export default UserRightsForm