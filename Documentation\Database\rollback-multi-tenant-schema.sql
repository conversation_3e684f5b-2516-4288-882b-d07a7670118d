-- <PERSON><PERSON><PERSON><PERSON><PERSON>K SCRIPT: Multi-Tenant Identity Management Database Schema
-- Purpose: Rollback the TenantID column addition and restore original schema
-- Date: January 2025
-- Author: Falcon Portal Development Team
-- 
-- ⚠️ WARNING: This script will remove the TenantID column and associated indexes
-- ⚠️ This should only be used if there are critical issues with the multi-tenant implementation
-- ⚠️ Always backup your database before running this script

USE [FalconPortal];
GO

PRINT 'Starting rollback of multi-tenant schema changes...';
PRINT 'This will remove TenantID column and restore original unique constraints.';
GO

-- Step 1: Drop the composite unique index on EntraID + TenantID
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    PRINT 'Dropping composite index IX_Users_EntraID_TenantID...';
    DROP INDEX [IX_Users_EntraID_TenantID] ON [dbo].[Users];
    PRINT 'Composite index dropped successfully.';
END
ELSE
BEGIN
    PRINT 'Composite index IX_Users_EntraID_TenantID not found, skipping...';
END
GO

-- Step 2: Drop the TenantID index
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_TenantID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    PRINT 'Dropping TenantID index IX_Users_TenantID...';
    DROP INDEX [IX_Users_TenantID] ON [dbo].[Users];
    PRINT 'TenantID index dropped successfully.';
END
ELSE
BEGIN
    PRINT 'TenantID index IX_Users_TenantID not found, skipping...';
END
GO

-- Step 3: Remove extended property on TenantID column
IF EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID('dbo.Users') AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Users') AND name = 'TenantID'))
BEGIN
    PRINT 'Removing extended property from TenantID column...';
    EXEC sp_dropextendedproperty 
        @name = N'MS_Description', 
        @level0type = N'Schema', @level0name = 'dbo', 
        @level1type = N'Table', @level1name = 'Users', 
        @level2type = N'Column', @level2name = 'TenantID';
    PRINT 'Extended property removed successfully.';
END
ELSE
BEGIN
    PRINT 'Extended property on TenantID column not found, skipping...';
END
GO

-- Step 4: Drop the TenantID column
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'TenantID')
BEGIN
    PRINT 'Dropping TenantID column from Users table...';
    ALTER TABLE [dbo].[Users] DROP COLUMN [TenantID];
    PRINT 'TenantID column dropped successfully.';
END
ELSE
BEGIN
    PRINT 'TenantID column not found in Users table, skipping...';
END
GO

-- Step 5: Restore the original unique index on EntraID only
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_EntraID' AND object_id = OBJECT_ID('dbo.Users'))
BEGIN
    PRINT 'Restoring original unique index on EntraID...';
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_EntraID] 
    ON [dbo].[Users] ([EntraID]) 
    WHERE [EntraID] IS NOT NULL;
    PRINT 'Original EntraID index restored successfully.';
END
ELSE
BEGIN
    PRINT 'Original EntraID index already exists, skipping...';
END
GO

-- Step 6: Verify the rollback
PRINT 'Verifying rollback...';
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
    AND COLUMN_NAME IN ('EntraID', 'TenantID', 'Email', 'CompanyID')
ORDER BY COLUMN_NAME;
GO

-- Step 7: Display current indexes on Users table
PRINT 'Current indexes on Users table:';
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    STRING_AGG(c.name, ', ') AS Columns
FROM sys.indexes i
JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('dbo.Users')
    AND i.name IS NOT NULL
GROUP BY i.name, i.type_desc, i.is_unique
ORDER BY i.name;
GO

PRINT 'Rollback of multi-tenant schema changes completed successfully!';
PRINT 'The database schema has been restored to its original state.';
PRINT 'Next steps:';
PRINT '1. Update backend code to remove composite key lookups';
PRINT '2. Restart the application to use original authentication logic';
PRINT '3. Test that existing users can still log in normally'; 