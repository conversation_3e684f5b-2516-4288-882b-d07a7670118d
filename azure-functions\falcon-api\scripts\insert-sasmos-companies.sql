-- Insert SASMOS Group Companies into Database
-- This script adds all the companies that need to be supported by the Falcon Portal

USE [FalconPortal]; -- Adjust database name as needed
GO

-- Check if Companies table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Companies')
BEGIN
    PRINT 'Companies table does not exist. Please run the main schema script first.';
    RETURN;
END

-- Insert companies if they don't already exist
PRINT 'Inserting SASMOS Group companies...';

-- 1. Avirata Defence Systems (already exists in some deployments)
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'Avirata Defence Systems')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('Avirata Defence Systems', 'ADS', 1, 1, GETDATE());
    PRINT 'Inserted: Avirata Defence Systems';
END
ELSE
BEGIN
    PRINT 'Already exists: Avirata Defence Systems';
END

-- 2. SASMOS HET
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'SASMOS HET')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('SASMOS HET', 'SASMOS', 1, 1, GETDATE());
    PRINT 'Inserted: SASMOS HET';
END
ELSE
BEGIN
    PRINT 'Already exists: SASMOS HET';
END

-- 3. SASMOS Group (parent company)
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'SASMOS Group')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('SASMOS Group', 'SG', 1, 1, GETDATE());
    PRINT 'Inserted: SASMOS Group';
END
ELSE
BEGIN
    PRINT 'Already exists: SASMOS Group';
END

-- 4. FE-SIL
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'FE-SIL')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('FE-SIL', 'FESIL', 1, 1, GETDATE());
    PRINT 'Inserted: FE-SIL';
END
ELSE
BEGIN
    PRINT 'Already exists: FE-SIL';
END

-- 5. Glodesi
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'Glodesi')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('Glodesi', 'GLO', 1, 1, GETDATE());
    PRINT 'Inserted: Glodesi';
END
ELSE
BEGIN
    PRINT 'Already exists: Glodesi';
END

-- 6. Hanuka
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'Hanuka')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('Hanuka', 'HAN', 1, 1, GETDATE());
    PRINT 'Inserted: Hanuka';
END
ELSE
BEGIN
    PRINT 'Already exists: Hanuka';
END

-- 7. West Wire Harnessing
IF NOT EXISTS (SELECT 1 FROM Companies WHERE CompanyName = 'West Wire Harnessing')
BEGIN
    INSERT INTO Companies (CompanyName, CompanyCode, IsActive, CreatedBy, CreatedDate)
    VALUES ('West Wire Harnessing', 'WWH', 1, 1, GETDATE());
    PRINT 'Inserted: West Wire Harnessing';
END
ELSE
BEGIN
    PRINT 'Already exists: West Wire Harnessing';
END

-- Display all companies
PRINT '';
PRINT 'Current companies in database:';
SELECT CompanyID, CompanyName, CompanyCode, IsActive, CreatedDate 
FROM Companies 
ORDER BY CompanyName;

PRINT '';
PRINT 'SASMOS Group companies setup complete!';
PRINT 'Next steps:';
PRINT '1. Add corresponding domains to Azure Entra ID';
PRINT '2. Verify authentication works for all domains';
PRINT '3. Test company filtering in portal'; 