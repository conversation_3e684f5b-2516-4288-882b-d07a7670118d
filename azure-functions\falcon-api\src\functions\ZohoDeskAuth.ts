import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { getClientPrincipal } from "../shared/authUtils";
import { logger } from "../shared/utils/logger";
import { OAuthTokenService } from "../services/OAuthTokenService";

// Zoho Desk OAuth Configuration
interface ZohoDeskConfig {
    clientId: string;
    clientSecret: string;
    redirectUri: string;
    baseUrl: string;
    scopes: string[];
}

interface ZohoDeskTokens {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
}

// Get Zoho Desk configuration from environment variables
function getZohoDeskConfig(): ZohoDeskConfig {
    // Fix region to 'in' for Indian Zoho instance (was 'com')
    const region = process.env.ZOHO_DESK_REGION || 'in'; // Changed from 'com' to 'in'
    const baseUrl = `https://accounts.zoho.${region}/oauth/v2`;

    return {
        clientId: process.env.ZOHO_DESK_CLIENT_ID || '',
        clientSecret: process.env.ZOHO_DESK_CLIENT_SECRET || '',
        // Fix port from 7075 to 7071 to match actual Azure Functions port
        redirectUri: process.env.ZOHO_DESK_REDIRECT_URI || 'http://localhost:7071/api/auth/zoho-desk/callback',
        baseUrl: baseUrl,
        scopes: [
            'Desk.tickets.ALL',
            'Desk.contacts.ALL',
            'Desk.basic.READ'
        ]
    };
}

// Function to initiate OAuth authorization
async function authorizeZohoDesk(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskAuth: Starting OAuth authorization flow`);

    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;

        if (!isDevelopment && !principal) {
            logger.warn("ZohoDeskAuth: Unauthorized access attempt");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const config = getZohoDeskConfig();

        if (!config.clientId || !config.clientSecret) {
            logger.error("ZohoDeskAuth: Missing Zoho Desk OAuth configuration");
            return { status: 500, jsonBody: { error: "OAuth configuration not found" } };
        }

        // Generate OAuth authorization URL
        const authUrl = new URL(`${config.baseUrl}/auth`);
        authUrl.searchParams.append('response_type', 'code');
        authUrl.searchParams.append('client_id', config.clientId);
        authUrl.searchParams.append('redirect_uri', config.redirectUri);
        authUrl.searchParams.append('scope', config.scopes.join(','));
        authUrl.searchParams.append('access_type', 'offline');
        authUrl.searchParams.append('prompt', 'consent');

        // Optional: Add state parameter for security
        const state = Buffer.from(JSON.stringify({
            userId: principal?.userId || 'dev-user',
            timestamp: Date.now()
        })).toString('base64');
        authUrl.searchParams.append('state', state);

        logger.info(`ZohoDeskAuth: Generated authorization URL for user ${principal?.userId || 'dev-user'}`);

        return {
            status: 200,
            jsonBody: {
                authUrl: authUrl.toString(),
                message: 'Visit the authorization URL to grant access to Zoho Desk'
            }
        };

    } catch (error) {
        logger.error("ZohoDeskAuth: Error generating authorization URL:", error);
        return { status: 500, jsonBody: { error: "Failed to generate authorization URL" } };
    }
}

// Function to handle OAuth callback and exchange code for tokens
async function zohoDeskCallback(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`ZohoDeskCallback: Processing OAuth callback`);

    try {
        const url = new URL(req.url);
        const code = url.searchParams.get('code');
        const state = url.searchParams.get('state');
        const error = url.searchParams.get('error');

        if (error) {
            logger.error(`ZohoDeskCallback: OAuth error: ${error}`);
            return { status: 400, jsonBody: { error: `OAuth authorization failed: ${error}` } };
        }

        if (!code) {
            logger.error("ZohoDeskCallback: No authorization code received");
            return { status: 400, jsonBody: { error: "Authorization code not found" } };
        }

        const config = getZohoDeskConfig();

        // Exchange authorization code for access token
        const tokenRequestBody = new URLSearchParams({
            grant_type: 'authorization_code',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            redirect_uri: config.redirectUri,
            code: code
        });

        // Debug: Log the exact request being made
        logger.info(`ZohoDeskCallback: Token exchange request URL: ${config.baseUrl}/token`);
        logger.info(`ZohoDeskCallback: Token exchange request body: ${tokenRequestBody.toString()}`);
        logger.info(`ZohoDeskCallback: Using redirect_uri: ${config.redirectUri}`);
        logger.info(`ZohoDeskCallback: Using client_id: ${config.clientId}`);

        const tokenResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenRequestBody
        });

        if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            logger.error(`ZohoDeskCallback: Token exchange failed with status ${tokenResponse.status}: ${errorText}`);
            return {
                status: 400,
                jsonBody: {
                    error: "Failed to exchange authorization code for tokens",
                    statusCode: tokenResponse.status,
                    response: errorText,
                    url: `${config.baseUrl}/token`,
                    requestBody: tokenRequestBody.toString()
                }
            };
        }

        const tokens: any = await tokenResponse.json();

        // Debug: Log what Zoho actually returned
        logger.info(`ZohoDeskCallback: Raw token response from Zoho:`, JSON.stringify(tokens, null, 2));
        logger.info(`ZohoDeskCallback: Token response keys: ${Object.keys(tokens).join(', ')}`);

        // Check if Zoho returned an error
        if (tokens.error) {
            logger.error(`ZohoDeskCallback: Zoho OAuth error: ${tokens.error}`);
            logger.error(`ZohoDeskCallback: Error description: ${tokens.error_description}`);
            return {
                status: 400,
                jsonBody: {
                    error: `Zoho OAuth error: ${tokens.error}`,
                    error_description: tokens.error_description,
                    debug: tokens
                }
            };
        }

        // Check if we received a valid access token
        if (!tokens.access_token) {
            logger.error(`ZohoDeskCallback: No access_token in response`);
            return {
                status: 400,
                jsonBody: {
                    error: "No access token received from Zoho",
                    debug: tokens
                }
            };
        }

        logger.info(`ZohoDeskCallback: access_token field: ${tokens.access_token ? 'exists' : 'null/undefined'}`);

        // Parse state to get user information
        let userId = 'unknown';
        if (state) {
            try {
                const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
                userId = stateData.userId;
            } catch (err) {
                logger.warn("ZohoDeskCallback: Failed to parse state parameter");
            }
        }

        // Cast to proper type after validation
        const validTokens = tokens as ZohoDeskTokens;

        // Store tokens securely
        // In production, store in Azure Key Vault or encrypted database
        // For now, we'll return them for development purposes
        await storeZohoDeskTokens(userId, validTokens);

        logger.info(`ZohoDeskCallback: Successfully obtained tokens for user ${userId}`);

        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Zoho Desk authorization completed successfully',
                expiresIn: validTokens.expires_in || 3600,
                scope: validTokens.scope,
                accessToken: 'received',
                tokenKeys: Object.keys(tokens),
                userId: userId
            }
        };

    } catch (error) {
        logger.error("ZohoDeskCallback: Error processing callback:", error);
        return { status: 500, jsonBody: { error: "Failed to process OAuth callback" } };
    }
}

// Function to get current access token (with refresh if needed)
async function getZohoDeskToken(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info(`GetZohoDeskToken: Getting access token`);

    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;

        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';

        // Get stored tokens
        const tokens = await getStoredZohoDeskTokens(userId);

        if (!tokens) {
            logger.warn(`GetZohoDeskToken: No tokens found for user ${userId}`);
            return { status: 404, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        // Check if token needs refresh (if expires within next 5 minutes)
        // Use the database token directly instead of buggy timestamp calculation
        const token = await OAuthTokenService.getActiveToken(userId, 'zoho', 'desk');
        if (!token) {
            return { status: 404, jsonBody: { error: "No Zoho Desk authorization found. Please authorize first." } };
        }

        const now = new Date();
        const fiveMinutesFromNow = new Date(now.getTime() + (5 * 60 * 1000));

        if (token.expiresAt <= fiveMinutesFromNow) {
            // Refresh token
            const refreshedTokens = await refreshZohoDeskToken(token.refreshToken);
            if (refreshedTokens) {
                await storeZohoDeskTokens(userId, refreshedTokens);
                return { status: 200, jsonBody: { accessToken: refreshedTokens.access_token } };
            } else {
                return { status: 401, jsonBody: { error: "Failed to refresh token. Please reauthorize." } };
            }
        }

        return { status: 200, jsonBody: { accessToken: token.accessToken } };

    } catch (error) {
        logger.error("GetZohoDeskToken: Error getting token:", error);
        return { status: 500, jsonBody: { error: "Failed to get access token" } };
    }
}

// Helper function to refresh access token
async function refreshZohoDeskToken(refreshToken: string): Promise<ZohoDeskTokens | null> {
    try {
        const config = getZohoDeskConfig();

        const refreshRequestBody = new URLSearchParams({
            grant_type: 'refresh_token',
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: refreshToken
        });

        const refreshResponse = await fetch(`${config.baseUrl}/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: refreshRequestBody
        });

        if (!refreshResponse.ok) {
            logger.error("Failed to refresh Zoho Desk token");
            return null;
        }

        const newTokens: ZohoDeskTokens = await refreshResponse.json();
        return newTokens;

    } catch (error) {
        logger.error("Error refreshing Zoho Desk token:", error);
        return null;
    }
}

// Helper functions for token storage (implement based on your storage strategy)
async function storeZohoDeskTokens(userId: string, tokens: ZohoDeskTokens): Promise<void> {
    try {
    // Handle potential field name variations from Zoho (cast to any to avoid TS errors)
    const tokensAny = tokens as any;
    const accessToken = tokens.access_token || tokensAny.accessToken || tokensAny.token;
    const refreshToken = tokens.refresh_token || tokensAny.refreshToken;
    const expiresIn = tokens.expires_in || tokensAny.expiresIn || 3600;
    const tokenType = tokens.token_type || tokensAny.tokenType || 'Bearer';
        const scope = tokens.scope;

    logger.info(`Storing Zoho Desk tokens for user ${userId} (expires in ${expiresIn} seconds)`);
    logger.info(`ZohoDeskTokens: Field mapping - access_token: ${accessToken ? 'found' : 'null'}, refresh_token: ${refreshToken ? 'found' : 'null'}`);

        // Use OAuthTokenService instead of global storage
        await OAuthTokenService.saveToken(
            userId,
            accessToken,
            refreshToken,
            expiresIn,
            scope,
            'zoho',
            'desk',
            tokenType
        );

        logger.info(`ZohoDeskTokens: Successfully stored tokens for user ${userId} in database`);
    } catch (error) {
        logger.error(`ZohoDeskTokens: Error storing tokens for user ${userId}:`, error);
        throw error;
    }
}

async function getStoredZohoDeskTokens(userId: string): Promise<any> {
    try {
        const token = await OAuthTokenService.getActiveToken(userId, 'zoho', 'desk');

        if (!token) {
            return null;
        }

        // Convert to the format expected by existing code
        const now = Date.now();
        const expiresInSeconds = Math.floor((token.expiresAt.getTime() - now) / 1000);

        return {
            access_token: token.accessToken,
            refresh_token: token.refreshToken,
            expires_in: Math.max(0, expiresInSeconds), // Ensure non-negative
            token_type: token.tokenType || 'Bearer',
            scope: token.scope,
            timestamp: now // Use current time as timestamp for consistency
        };
    } catch (error) {
        logger.error(`ZohoDeskTokens: Error retrieving tokens for user ${userId}:`, error);
        return null;
    }
}

// Register Azure Functions
app.http('ZohoDeskAuthorize', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'auth/zoho-desk/authorize',
    handler: authorizeZohoDesk
});

app.http('ZohoDeskCallback', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'auth/zoho-desk/callback',
    handler: zohoDeskCallback
});

app.http('GetZohoDeskToken', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'auth/zoho-desk/token',
    handler: getZohoDeskToken
});