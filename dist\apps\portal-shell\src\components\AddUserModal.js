"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddUserModal = void 0;
const react_1 = require("react");
require("./Modal.css");
const AddUserModal = ({ isOpen, onClose, onAddUser }) => {
    const [formData, setFormData] = (0, react_1.useState)({
        email: '',
        selectedRoleId: 0
    });
    const [roles, setRoles] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [loadingRoles, setLoadingRoles] = (0, react_1.useState)(false);
    // Load roles when modal opens
    (0, react_1.useEffect)(() => {
        if (isOpen) {
            loadRoles();
        }
    }, [isOpen]);
    const loadRoles = () => __awaiter(void 0, void 0, void 0, function* () {
        setLoadingRoles(true);
        try {
            const response = yield fetch('/api/roles');
            if (!response.ok) {
                throw new Error(`Failed to load roles. Status: ${response.status}`);
            }
            const rolesData = yield response.json();
            setRoles(rolesData);
            // Set default role to first available role
            if (rolesData.length > 0) {
                setFormData(prev => (Object.assign(Object.assign({}, prev), { selectedRoleId: rolesData[0].RoleID })));
            }
        }
        catch (error) {
            console.error('Error loading roles:', error);
            setError('Failed to load available roles');
        }
        finally {
            setLoadingRoles(false);
        }
    });
    const handleSubmit = (e) => __awaiter(void 0, void 0, void 0, function* () {
        e.preventDefault();
        if (!formData.email.trim()) {
            setError('Email is required');
            return;
        }
        if (!formData.selectedRoleId) {
            setError('Please select a role');
            return;
        }
        setLoading(true);
        setError(null);
        try {
            yield onAddUser({
                email: formData.email.trim(),
                roleId: formData.selectedRoleId
            });
            // Reset form and close modal on success
            setFormData({ email: '', selectedRoleId: 0 });
            onClose();
        }
        catch (error) {
            console.error('Error adding user:', error);
            setError(error instanceof Error ? error.message : 'Failed to add user');
        }
        finally {
            setLoading(false);
        }
    });
    const handleClose = () => {
        if (!loading) {
            setFormData({ email: '', selectedRoleId: 0 });
            setError(null);
            onClose();
        }
    };
    if (!isOpen)
        return null;
    return (<div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Add New User to Portal</h2>
          <button className="modal-close" onClick={handleClose} disabled={loading}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {error && (<div className="error-message" style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#fee', border: '1px solid #fcc', borderRadius: '4px', color: '#c00' }}>
              {error}
            </div>)}

          <div className="form-group">
            <label htmlFor="email">Email Address *</label>
            <input type="email" id="email" value={formData.email} onChange={(e) => setFormData(prev => (Object.assign(Object.assign({}, prev), { email: e.target.value })))} placeholder="<EMAIL>" required disabled={loading} style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}/>
            <small style={{ color: '#666', fontSize: '0.9em' }}>
              Enter the email address of the user you want to add to the portal
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="role">Role *</label>
            {loadingRoles ? (<div style={{ padding: '8px', color: '#666' }}>Loading roles...</div>) : (<select id="role" value={formData.selectedRoleId} onChange={(e) => setFormData(prev => (Object.assign(Object.assign({}, prev), { selectedRoleId: parseInt(e.target.value) })))} required disabled={loading} style={{ width: '100%', padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}>
                <option value={0}>Select a role...</option>
                {roles.map(role => (<option key={role.RoleID} value={role.RoleID}>
                    {role.RoleName} {role.Description && `- ${role.Description}`}
                  </option>))}
              </select>)}
          </div>

          <div className="modal-footer">
            <button type="button" onClick={handleClose} disabled={loading} style={{
            padding: '8px 16px',
            marginRight: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            backgroundColor: '#f5f5f5',
            cursor: loading ? 'not-allowed' : 'pointer'
        }}>
              Cancel
            </button>
            <button type="submit" disabled={loading || loadingRoles} style={{
            padding: '8px 16px',
            border: 'none',
            borderRadius: '4px',
            backgroundColor: loading ? '#ccc' : '#007bff',
            color: 'white',
            cursor: loading ? 'not-allowed' : 'pointer'
        }}>
              {loading ? 'Adding User...' : 'Add User'}
            </button>
          </div>
        </form>
      </div>
    </div>);
};
exports.AddUserModal = AddUserModal;
//# sourceMappingURL=AddUserModal.js.map