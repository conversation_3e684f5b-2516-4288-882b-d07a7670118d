import { ClientSecretCredential } from '@azure/identity';
import { Client } from '@microsoft/microsoft-graph-client'; 
// Do NOT import graphService here initially
// import { graphService } from './graphService'; 
import { logger } from '../utils/logger';
import { GraphService } from './graphService'; // Import type if needed

// Mock dependencies that graphService uses internally
jest.mock('@azure/identity', () => ({
    ClientSecretCredential: jest.fn().mockImplementation(() => ({}))
}));

jest.mock('../utils/logger', () => ({
    logger: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn(),
    }
}));

// --- Test Suite --- 

describe('Graph Service', () => {

    // Declare variables for the service instance and mocks
    let graphServiceInstance: GraphService;
    let mockFluentAPI: { // To hold the mock API object created by initWithMiddleware
        api: jest.Mock;
        select: jest.Mock;
        filter: jest.Mock;
        top: jest.Mock;
        get: jest.Mock;
    };
    let mockInitWithMiddleware: jest.Mock; // To hold the mock static method

    beforeAll(() => {
        // Use jest.doMock BEFORE the module is required for the first time in tests
        jest.doMock('@microsoft/microsoft-graph-client', () => {
            // Define the mocks within the factory
            const fluentAPI = {
                api: jest.fn().mockReturnThis(),
                select: jest.fn().mockReturnThis(),
                filter: jest.fn().mockReturnThis(),
                top: jest.fn().mockReturnThis(),
                get: jest.fn().mockResolvedValue({ value: [] }), // Default success
            };
            const initWithMiddleware = jest.fn(() => fluentAPI);
            
            // Store references to the mocks so tests can access them
            mockFluentAPI = fluentAPI;
            mockInitWithMiddleware = initWithMiddleware;

            return {
                Client: {
                    initWithMiddleware: initWithMiddleware
                }
            };
        });
    });

    beforeEach(async () => {
        // Reset mocks between tests
        jest.clearAllMocks();
        // Need to reset the fluent API mocks as well
        mockFluentAPI.api.mockClear().mockReturnThis();
        mockFluentAPI.select.mockClear().mockReturnThis();
        mockFluentAPI.filter.mockClear().mockReturnThis();
        mockFluentAPI.top.mockClear().mockReturnThis();
        mockFluentAPI.get.mockClear().mockResolvedValue({ value: [] }); // Reset to default

        // Dynamically import/require the service *after* mocks are set up
        // Use require for potentially easier handling with Jest mocks
        const serviceModule = require('./graphService'); 
        graphServiceInstance = serviceModule.graphService;

        // Now, graphServiceInstance should have been initialized using the mocked Client
        // Verify initialization mocks were called (optional, but good check)
        expect(mockInitWithMiddleware).toHaveBeenCalled();
        expect(ClientSecretCredential).toHaveBeenCalledWith(process.env.ENTRA_TENANT_ID, process.env.ENTRA_CLIENT_ID, process.env.ENTRA_CLIENT_SECRET);
    });

    // REMOVED Initialization tests

    describe('searchUsers', () => {
        const searchQuery = 'testUser';
        const limit = 5;
        const mockUsersResponse = {
            value: [
                { id: '1', displayName: 'Test User One', /* ... other fields */ },
                { id: '2', displayName: 'Test User Two', /* ... other fields */ }
            ]
        };

        it('should call Graph API with correct parameters and return users', async () => {
            // Set the mock response for this specific test
            mockFluentAPI.get.mockResolvedValue(mockUsersResponse); 
            
            const users = await graphServiceInstance.searchUsers(searchQuery, limit);

            expect(users).toEqual(mockUsersResponse.value);
            // Check the mocks defined in the doMock factory
            expect(mockFluentAPI.api).toHaveBeenCalledWith('/users');
            expect(mockFluentAPI.select).toHaveBeenCalledWith(expect.arrayContaining(['id', 'displayName'])); // Check subset
            expect(mockFluentAPI.filter).toHaveBeenCalledWith(expect.stringContaining(`startswith(displayName,'${searchQuery}')`));
            expect(mockFluentAPI.top).toHaveBeenCalledWith(limit);
            expect(mockFluentAPI.get).toHaveBeenCalledTimes(1);
        });

        it('should handle empty search results', async () => {
            // Default mock is empty results, no need to change mockFluentAPI.get
            const users = await graphServiceInstance.searchUsers(searchQuery, limit);
            expect(users).toEqual([]);
            expect(mockFluentAPI.get).toHaveBeenCalledTimes(1);
            expect(mockFluentAPI.api).toHaveBeenCalledWith('/users'); 
        });

        it('should handle Graph API errors', async () => {
            const apiError = new Error('Graph API Error');
            mockFluentAPI.get.mockRejectedValue(apiError); 
            
            await expect(graphServiceInstance.searchUsers(searchQuery, limit)).rejects.toThrow('Error searching Graph API users: Graph API Error');
            expect(logger.error).toHaveBeenCalledWith('Error searching Graph API users:', apiError);
            expect(mockFluentAPI.api).toHaveBeenCalledWith('/users');
        });
    });

    describe('getUserById', () => {
        const userId = 'user-guid-123';
        const mockUserResponse = { id: userId, displayName: 'Specific User', /* ... */ };

        it('should call Graph API with correct parameters and return user', async () => {
            mockFluentAPI.get.mockResolvedValue(mockUserResponse);
            const user = await graphServiceInstance.getUserById(userId);
            expect(user).toEqual(mockUserResponse);
            expect(mockFluentAPI.api).toHaveBeenCalledWith(`/users/${userId}`);
            expect(mockFluentAPI.select).toHaveBeenCalledWith(expect.arrayContaining(['id', 'displayName']));
            expect(mockFluentAPI.get).toHaveBeenCalledTimes(1);
        });
        
        it('should return null if user is not found (404 error)', async () => {
             const notFoundError = new Error('User not found');
             (notFoundError as any).statusCode = 404; 
             mockFluentAPI.get.mockRejectedValue(notFoundError);
             const user = await graphServiceInstance.getUserById(userId);
             expect(user).toBeNull();
             expect(logger.warn).toHaveBeenCalledWith(`User with ID ${userId} not found in Graph API.`);
             expect(mockFluentAPI.api).toHaveBeenCalledWith(`/users/${userId}`);
        });
        
        it('should re-throw non-404 Graph API errors', async () => {
            const apiError = new Error('Some other Graph error');
             (apiError as any).statusCode = 500; 
            mockFluentAPI.get.mockRejectedValue(apiError);
            await expect(graphServiceInstance.getUserById(userId)).rejects.toThrow('Error fetching user by ID from Graph API: Some other Graph error');
            expect(logger.error).toHaveBeenCalledWith(`Error fetching user ${userId} from Graph API:`, apiError);
            expect(mockFluentAPI.api).toHaveBeenCalledWith(`/users/${userId}`);
        });
    });
}); 