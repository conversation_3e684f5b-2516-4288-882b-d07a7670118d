export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-ms-client-principal',
  'Access-Control-Max-Age': '86400'
};

export function addCorsHeaders(response: any) {
  return {
    ...response,
    headers: {
      ...response.headers,
      ...corsHeaders
    }
  };
} 