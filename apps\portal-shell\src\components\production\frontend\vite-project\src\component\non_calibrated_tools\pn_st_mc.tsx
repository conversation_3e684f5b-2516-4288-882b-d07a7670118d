import { useState } from 'react';
import axios from 'axios';

function PnStMc() {
  const [partNumber, setPartNumber] = useState('');
  const [fileType, setFileType] = useState('');
  const [file, setFile] = useState(null);
  const [message, setMessage] = useState('');

  const userId = ""; // Replace with actual user ID
  const username = ""; // Replace with actual username

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!partNumber || !fileType || !file) {
      setMessage('All fields are required');
      return;
    }

    const formData = new FormData();
    formData.append('partNumber', partNumber.toUpperCase());
    formData.append('fileType', fileType);
    formData.append('file', file);

    axios.post('http://localhost:3000/api/upload', formData, {
      headers: {
        'user-id': userId,
        'user-name': username,
        'Content-Type': 'multipart/form-data'
      }
    })
      .then(res => {
        setMessage(res.data.message);
        setTimeout(() => window.location.reload(), 2000);
      })
      .catch(err => setMessage(err.response?.data?.error || 'Upload failed'));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-xl">
        <h1 className="text-3xl font-semibold text-center text-blue-700 mb-6">
          Upload Serviceable Tag / COC
        </h1>

        {message && (
          <div
            className={`p-4 mb-4 rounded text-center font-medium transition-all ${
              message.toLowerCase().includes('success')
                ? 'bg-green-100 text-green-800 border border-green-300'
                : 'bg-red-100 text-red-800 border border-red-300'
            }`}
          >
            {message}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="text-sm font-medium text-gray-700 mb-1 block">Part Number</label>
            <input
              type="text"
              value={partNumber}
              onChange={(e) => setPartNumber(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none"
              placeholder="Enter Part Number"
              required
            />
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">File Type</label>
            <div className="flex items-center gap-4">
              <span className={`text-sm font-semibold ${fileType === 'FOAM BOARD' ? 'text-gray-500' : 'text-blue-600'}`}>FOAM BOARD</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={fileType === 'MC'}
                  onChange={(e) => setFileType(e.target.checked ? 'MC' : 'FOAM BOARD')}
                />
                <div className="w-16 h-8 bg-gray-300 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer dark:bg-gray-400 peer-checked:after:translate-x-8 after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
              <span className={`text-sm font-semibold ${fileType === 'MC' ? 'text-blue-600' : 'text-gray-500'}`}>MC</span>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700 mb-1 block">Upload File</label>
            <input
              type="file"
              onChange={(e) => setFile(e.target.files[0])}
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              required
            />
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-all shadow-md"
          >
            Submit
          </button>
        </form>
      </div>
    </div>
  );
}

export default PnStMc;
