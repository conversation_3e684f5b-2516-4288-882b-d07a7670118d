import React, { useCallback, useEffect, useRef, useState } from 'react';
import { 
  Bold, 
  Italic, 
  Code, 
  List, 
  Type,
  Save,
  Image as ImageIcon,
  Plus
} from 'feather-icons-react';
import { imageUploadApi } from '../../services/imageUploadApi';
import { getAuthenticatedImageUrl } from '../../utils/imageUtils';

// ContentBlock interface for content structure
export interface ContentBlock {
  id: string;
  type: 'paragraph' | 'heading1' | 'heading2' | 'heading3' | 'list' | 'code' | 'image';
  content: string;
  imageUrl?: string;
  imageCaption?: string;
  imageAlt?: string;
  listItems?: string[];
}

interface RichContentEditorProps {
  initialContent?: ContentBlock[];
  onChange: (content: ContentBlock[]) => void;
  onAutoSave?: (content: ContentBlock[]) => void;
  autoSaveInterval?: number;
  placeholder?: string;
  className?: string;
}

const RichContentEditor: React.FC<RichContentEditorProps> = ({
  initialContent = [],
  onChange,
  onAutoSave,
  autoSaveInterval = 30000,
  placeholder = "Start typing your detailed description...",
  className = ""
}) => {
  const [content, setContent] = useState<ContentBlock[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [uploading, setUploading] = useState(false);
  const [selectedBlockIndex, setSelectedBlockIndex] = useState(0);
  const autoSaveTimerRef = useRef<number | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const blockRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  // Generate unique IDs
  const generateId = (): string => {
    return Math.random().toString(36).substr(2, 9);
  };

  // Initialize content
  useEffect(() => {
    console.log('🔄 Loading initial content:', initialContent);
    if (initialContent && initialContent.length > 0) {
      setContent(initialContent);
    } else {
      setContent([{ id: generateId(), type: 'paragraph', content: '' }]);
    }
  }, [initialContent]);

  // Auto-save functionality
  const performAutoSave = useCallback(() => {
    if (hasUnsavedChanges && onAutoSave) {
      onAutoSave(content);
      setHasUnsavedChanges(false);
      setLastSaved(new Date());
    }
  }, [hasUnsavedChanges, onAutoSave, content]);

  // Set up auto-save timer
  useEffect(() => {
    if (autoSaveTimerRef.current) {
      clearInterval(autoSaveTimerRef.current);
    }

    if (onAutoSave) {
      autoSaveTimerRef.current = window.setInterval(performAutoSave, autoSaveInterval);
    }

    return () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }
    };
  }, [performAutoSave, autoSaveInterval, onAutoSave]);

  // Handle content changes
  const handleContentChange = useCallback((newContent: ContentBlock[]) => {
    console.log('📝 Content changed:', newContent);
    setContent(newContent);
    onChange(newContent);
    setHasUnsavedChanges(true);
  }, [onChange]);

  // Update block content
  const updateBlockContent = useCallback((index: number, newContent: string, newType?: ContentBlock['type']) => {
    const updatedContent = [...content];
    if (updatedContent[index]) {
      updatedContent[index] = {
        ...updatedContent[index],
        content: newContent,
        type: newType || updatedContent[index].type
      };
      handleContentChange(updatedContent);
    }
  }, [content, handleContentChange]);

  // Add new block with focus management
  const addNewBlock = useCallback((afterIndex: number, type: ContentBlock['type'] = 'paragraph') => {
    const newBlock: ContentBlock = {
      id: generateId(),
      type,
      content: ''
    };
    
    const updatedContent = [...content];
    updatedContent.splice(afterIndex + 1, 0, newBlock);
    handleContentChange(updatedContent);
    
    // Focus the new block
    setTimeout(() => {
      const newIndex = afterIndex + 1;
      setSelectedBlockIndex(newIndex);
      const element = blockRefs.current[newBlock.id];
      if (element) {
        element.focus();
        // Place cursor at the beginning
        const range = document.createRange();
        const selection = window.getSelection();
        range.setStart(element, 0);
        range.collapse(true);
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    }, 10);
  }, [content, handleContentChange]);

  // Remove block
  const removeBlock = useCallback((index: number) => {
    if (content.length <= 1) return; // Keep at least one block
    
    const updatedContent = content.filter((_, i) => i !== index);
    handleContentChange(updatedContent);
    
    // Focus previous block
    setTimeout(() => {
      const newIndex = Math.max(0, index - 1);
      setSelectedBlockIndex(newIndex);
      if (updatedContent[newIndex]) {
        const element = blockRefs.current[updatedContent[newIndex].id];
        if (element) {
          element.focus();
          // Place cursor at the end
          const range = document.createRange();
          const selection = window.getSelection();
          range.selectNodeContents(element);
          range.collapse(false);
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    }, 10);
  }, [content, handleContentChange]);

  // Handle image upload from file input
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate image file
    const validation = imageUploadApi.validateImageFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    try {
      setUploading(true);
      console.log('🖼️ Uploading image:', file.name);
      
      const response = await imageUploadApi.uploadImage({ 
        file, 
        userId: 'current-user' 
      });
      
      console.log('📷 Image upload response:', response);
      
      if (response.success && response.imageUrl) {
        const imageBlock: ContentBlock = {
          id: generateId(),
          type: 'image',
          content: '',
          imageUrl: response.imageUrl,
          imageAlt: file.name,
          imageCaption: file.name.split('.')[0] // Remove extension for caption
        };

        const updatedContent = [...content, imageBlock];
        handleContentChange(updatedContent);
        console.log('✅ Image block added:', imageBlock);
      } else {
        throw new Error(response.message || 'Failed to upload image');
      }
      
    } catch (error) {
      console.error('❌ Failed to upload image:', error);
      alert(error instanceof Error ? error.message : 'Failed to upload image');
    } finally {
      setUploading(false);
      if (e.target) {
        e.target.value = '';
      }
    }
  };

  // Handle paste events for image support - FIXED to not clear text
  const handlePaste = useCallback(async (e: React.ClipboardEvent, blockIndex: number) => {
    const items = Array.from(e.clipboardData.items);
    const imageItem = items.find(item => item.type.startsWith('image/'));
    
    if (imageItem) {
      e.preventDefault();
      const file = imageItem.getAsFile();
      if (file) {
        console.log('📋 Pasting image:', file.name || 'clipboard-image');
        
        try {
          setUploading(true);
          
          const response = await imageUploadApi.uploadImage({ 
            file, 
            userId: 'current-user' 
          });
          
          console.log('📷 Image upload response:', response);
          
          if (response.success && response.imageUrl) {
            const imageBlock: ContentBlock = {
              id: generateId(),
              type: 'image',
              content: '',
              imageUrl: response.imageUrl,
              imageAlt: file.name || 'pasted-image',
              imageCaption: 'Pasted image'
            };

            // Insert image block after current block, don't replace content
            const updatedContent = [...content];
            updatedContent.splice(blockIndex + 1, 0, imageBlock);
            handleContentChange(updatedContent);
            console.log('✅ Image block added via paste:', imageBlock);
          }
        } catch (error) {
          console.error('❌ Failed to upload pasted image:', error);
          alert('Failed to upload pasted image');
        } finally {
          setUploading(false);
        }
      }
    }
  }, [content, handleContentChange]);

  // Handle toolbar actions
  const handleToolbarAction = useCallback((action: string) => {
    const currentBlock = content[selectedBlockIndex];
    if (!currentBlock) return;

    switch (action) {
      case 'bold': {
        // For now, just add ** markers (could be enhanced with proper formatting)
        const boldText = `**${currentBlock.content}**`;
        updateBlockContent(selectedBlockIndex, boldText);
        break;
      }
      case 'italic': {
        const italicText = `*${currentBlock.content}*`;
        updateBlockContent(selectedBlockIndex, italicText);
        break;
      }
      case 'h1':
        updateBlockContent(selectedBlockIndex, currentBlock.content, 'heading1');
        break;
      case 'h2':
        updateBlockContent(selectedBlockIndex, currentBlock.content, 'heading2');
        break;
      case 'paragraph':
        updateBlockContent(selectedBlockIndex, currentBlock.content, 'paragraph');
        break;
      case 'code':
        updateBlockContent(selectedBlockIndex, currentBlock.content, 'code');
        break;
      case 'list':
        updateBlockContent(selectedBlockIndex, currentBlock.content, 'list');
        break;
    }
  }, [content, selectedBlockIndex, updateBlockContent]);

  // Render content block - FIXED to avoid text reversal
  const renderContentBlock = useCallback((block: ContentBlock, index: number) => {
    const isSelected = index === selectedBlockIndex;
    const baseClasses = `min-h-[24px] p-2 rounded border-2 transition-colors ${
      isSelected ? 'border-blue-300 bg-blue-50' : 'border-transparent hover:border-gray-200'
    }`;

    const handleBlockClick = () => setSelectedBlockIndex(index);
    
    // FIXED: Use onBlur to avoid cursor jumping during typing
    const handleBlockChange = (e: React.FormEvent<HTMLDivElement>) => {
      const newContent = e.currentTarget.textContent || '';
      updateBlockContent(index, newContent);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        // Get current content before creating new block
        const currentContent = e.currentTarget.textContent || '';
        if (currentContent !== block.content) {
          updateBlockContent(index, currentContent);
        }
        addNewBlock(index);
      } else if (e.key === 'Backspace' && block.content === '' && content.length > 1) {
        e.preventDefault();
        removeBlock(index);
      }
    };

    // Store ref for this block
    const setBlockRef = (element: HTMLDivElement | null) => {
      blockRefs.current[block.id] = element;
    };

    switch (block.type) {
      case 'heading1':
        return (
          <h1
            key={block.id}
            ref={setBlockRef}
            className={`${baseClasses} text-2xl font-bold`}
            contentEditable
            suppressContentEditableWarning
            onClick={handleBlockClick}
            onBlur={handleBlockChange}
            onKeyDown={handleKeyDown}
            onPaste={(e) => handlePaste(e, index)}
            style={{ outline: 'none' }}
            data-placeholder={isSelected && !block.content ? 'Heading 1' : ''}
          >
            {block.content}
          </h1>
        );

      case 'heading2':
        return (
          <h2
            key={block.id}
            ref={setBlockRef}
            className={`${baseClasses} text-xl font-semibold`}
            contentEditable
            suppressContentEditableWarning
            onClick={handleBlockClick}
            onBlur={handleBlockChange}
            onKeyDown={handleKeyDown}
            onPaste={(e) => handlePaste(e, index)}
            style={{ outline: 'none' }}
            data-placeholder={isSelected && !block.content ? 'Heading 2' : ''}
          >
            {block.content}
          </h2>
        );

      case 'code':
        return (
          <pre
            key={block.id}
            ref={(element: HTMLPreElement | null) => {
              blockRefs.current[block.id] = element;
            }}
            className={`${baseClasses} font-mono text-sm bg-gray-100 overflow-x-auto`}
            contentEditable
            suppressContentEditableWarning
            onClick={handleBlockClick}
            onBlur={(e: React.FocusEvent<HTMLPreElement>) => {
              const newContent = e.currentTarget.textContent || '';
              updateBlockContent(index, newContent);
            }}
            onKeyDown={handleKeyDown}
            onPaste={(e) => handlePaste(e as React.ClipboardEvent, index)}
            style={{ outline: 'none' }}
            data-placeholder={isSelected && !block.content ? 'Code block' : ''}
          >
            {block.content}
          </pre>
        );

      case 'image':
        return (
          <div key={block.id} className="relative group">
            <div className={`${baseClasses} text-center`} onClick={handleBlockClick}>
              {block.imageUrl ? (
                <div className="space-y-2">
                  <img
                    src={getAuthenticatedImageUrl(block.imageUrl)}
                    alt={block.imageAlt || 'Uploaded image'}
                    className="max-w-full h-auto mx-auto rounded shadow-sm"
                    style={{ maxHeight: '400px' }}
                  />
                  {block.imageCaption && (
                    <p className="text-sm text-gray-600 italic">{block.imageCaption}</p>
                  )}
                  <div className="flex justify-center space-x-2">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeBlock(index);
                      }}
                      className="text-xs text-red-600 hover:text-red-800 bg-red-100 px-2 py-1 rounded"
                    >
                      Remove Image
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-500 p-4">
                  <ImageIcon size={24} className="mx-auto mb-2" />
                  <p>Image failed to load</p>
                </div>
              )}
            </div>
            {/* Add New Block Button for Images */}
            <div className="flex justify-center mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  addNewBlock(index);
                }}
                className="flex items-center space-x-1 text-xs text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
              >
                <Plus size={12} />
                <span>Add Block Below</span>
              </button>
            </div>
          </div>
        );

      default: // paragraph
        return (
          <div
            key={block.id}
            ref={setBlockRef}
            className={`${baseClasses}`}
            contentEditable
            suppressContentEditableWarning
            onClick={handleBlockClick}
            onBlur={handleBlockChange}
            onKeyDown={handleKeyDown}
            onPaste={(e) => handlePaste(e, index)}
            style={{ 
              outline: 'none'
            }}
            data-placeholder={isSelected && !block.content ? placeholder : ''}
          >
            {block.content}
          </div>
        );
    }
  }, [content, selectedBlockIndex, placeholder, updateBlockContent, addNewBlock, removeBlock, handlePaste]);

  return (
    <div className={`relative border rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="border-b bg-gray-50 px-3 py-2">
        <div className="flex flex-wrap items-center gap-1">
          <div className="flex items-center border-r border-gray-300 pr-2 mr-2">
            <button
              type="button"
              onClick={() => handleToolbarAction('paragraph')}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200"
              title="Paragraph"
            >
              <Type size={16} />
            </button>
            <button
              type="button"
              onClick={() => handleToolbarAction('h1')}
              className="px-2 py-1.5 rounded text-sm font-semibold text-gray-600 hover:bg-gray-200"
              title="Heading 1"
            >
              H1
            </button>
            <button
              type="button"
              onClick={() => handleToolbarAction('h2')}
              className="px-2 py-1.5 rounded text-sm font-semibold text-gray-600 hover:bg-gray-200"
              title="Heading 2"
            >
              H2
            </button>
          </div>

          <div className="flex items-center border-r border-gray-300 pr-2 mr-2">
            <button
              type="button"
              onClick={() => handleToolbarAction('bold')}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200"
              title="Bold"
            >
              <Bold size={16} />
            </button>
            <button
              type="button"
              onClick={() => handleToolbarAction('italic')}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200"
              title="Italic"
            >
              <Italic size={16} />
            </button>
          </div>

          <div className="flex items-center border-r border-gray-300 pr-2 mr-2">
            <button
              type="button"
              onClick={() => handleToolbarAction('list')}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200"
              title="List"
            >
              <List size={16} />
            </button>
            <button
              type="button"
              onClick={() => handleToolbarAction('code')}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200"
              title="Code Block"
            >
              <Code size={16} />
            </button>
          </div>

          <div className="flex items-center">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              disabled={uploading}
            />
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              className="p-1.5 rounded text-gray-600 hover:bg-gray-200 disabled:opacity-50"
              title="Add Image"
            >
              {uploading ? (
                <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <ImageIcon size={16} />
              )}
            </button>
          </div>

          {lastSaved && (
            <div className="ml-auto flex items-center text-xs text-green-600">
              <Save size={12} className="mr-1" />
              Auto-saved at {lastSaved.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>

      {/* Editor Content */}
      <div 
        ref={editorRef}
        className="prose prose-sm max-w-none min-h-[300px] p-4 space-y-2"
      >
        {content.map((block, index) => renderContentBlock(block, index))}
      </div>

      {/* Upload overlay */}
      {uploading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
          <div className="bg-white p-4 rounded-lg flex items-center space-x-3">
            <div className="w-6 h-6 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-gray-700">Uploading image...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RichContentEditor;