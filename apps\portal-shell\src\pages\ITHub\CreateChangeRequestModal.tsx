import React, { useState, useCallback, useEffect } from 'react';
import { X, AlertCircle, CheckCircle, Save } from 'feather-icons-react';
import { changeManagementApi, type CreateChangeRequestData } from '../../services/changeManagementApi';
import { draftManagementApi, type ChangeRequestDraft } from '../../services/draftManagementApi';
import { RichContentEditor, type ContentBlock } from '../../components/RichContentEditor';
import { useCurrentUser } from '../../services/userContext';

interface CreateChangeRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  initialDraft?: ChangeRequestDraft | null;
}

const CreateChangeRequestModal: React.FC<CreateChangeRequestModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  initialDraft
}) => {
  const { user } = useCurrentUser();
  
  const [formData, setFormData] = useState<CreateChangeRequestData>({
    title: '',
    description: '',
    typeId: 1,
    priority: 'Medium',
    businessJustification: '',
    expectedBenefit: '',
    requestedCompletionDate: '',
    requestedBy: 1, // Will be updated when user context loads
    companyId: undefined // Will be updated when user context loads
  });

  const [richContent, setRichContent] = useState<ContentBlock[]>([]);
  const [isDraft, setIsDraft] = useState(false);
  const [currentDraftId, setCurrentDraftId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [lastAutoSave, setLastAutoSave] = useState<Date | null>(null);
  const [autoSaving, setAutoSaving] = useState(false);

  // Auto-save functionality
  const handleAutoSave = useCallback(async (content: ContentBlock[]) => {
    console.log('💾 Auto-save triggered with content:', {
      contentLength: content.length,
      content: content,
      contentBlocks: content.map((block, i) => ({
        index: i,
        id: block.id,
        type: block.type,
        content: block.content,
        contentLength: block.content?.length || 0
      })),
      formData
    });

    try {
      if (!formData.title.trim()) {
        console.log('⚠️ Auto-save skipped: No title');
        return;
      }

      console.log('💾 Auto-save triggered with content:', {
        contentLength: content.length,
        content: content,
        formData: formData
      });

      setAutoSaving(true);
      setError(null);

      const draftData = {
        draftId: currentDraftId || undefined,
        title: formData.title,
        description: formData.description,
        typeId: formData.typeId,
        priority: formData.priority,
        businessJustification: formData.businessJustification,
        expectedBenefit: formData.expectedBenefit,
        requestedCompletionDate: formData.requestedCompletionDate,
        requestedBy: formData.requestedBy,
        richContent: content
      };

      console.log('📤 Sending draft data:', draftData);
      console.log('📤 Rich content being saved:', {
        richContentLength: content.length,
        richContentBlocks: content.map((block, i) => ({
          index: i,
          id: block.id,
          type: block.type,
          content: block.content,
          hasContent: !!block.content,
          contentPreview: block.content?.substring(0, 50) + (block.content?.length > 50 ? '...' : '')
        }))
      });
      
      const response = await draftManagementApi.saveDraft(draftData);
      
      console.log('✅ Auto-save successful:', response);
      
      if (response.draftId && !currentDraftId) {
        setCurrentDraftId(response.draftId);
        setIsDraft(true);
      }
      
      setLastAutoSave(new Date());
      
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
      setError('Failed to save draft');
    } finally {
      setAutoSaving(false);
    }
  }, [formData, currentDraftId]);

  // Handle rich content changes
  const handleRichContentChange = useCallback((content: ContentBlock[]) => {
    console.log('📝 Rich content changed:', {
      contentLength: content.length,
      content: content
    });
    
    setRichContent(content);
    
    // Convert rich content to plain text for description field (backward compatibility)
    const plainTextDescription = content
      .map(block => {
        if (block.type === 'paragraph' || block.type.startsWith('heading')) {
          return block.content;
        } else if (block.type === 'list') {
          return block.listItems?.map(item => `• ${item}`).join('\n') || '';
        } else if (block.type === 'code') {
          return `\`\`\`\n${block.content}\n\`\`\``;
        }
        return block.content;
      })
      .filter(text => text.trim())
      .join('\n\n');
    
    console.log('📄 Generated plain text description:', plainTextDescription);
    
    setFormData(prev => ({ ...prev, description: plainTextDescription }));
  }, []);

  // Manual save as draft
  const handleSaveAsDraft = useCallback(async () => {
    try {
      if (!formData.title.trim()) {
        setError('Title is required to save as draft');
        return;
      }

      setLoading(true);
      await handleAutoSave(richContent);
      // Success feedback is handled by auto-save
    } catch {
      setError('Failed to save draft');
    } finally {
      setLoading(false);
    }
  }, [formData.title, richContent, handleAutoSave]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      draftManagementApi.clearAllAutoSave();
    };
  }, []);

  // Update requestedBy and companyId when user context changes
  useEffect(() => {
    if (user?.id) {
      console.log('🔐 User authenticated:', { 
        entraId: user.id, 
        email: user.email, 
        name: user.name,
        company: user.company 
      });
      
      // TODO: This is a critical issue - we need to map Entra ID to internal UserID
      // For now, we'll use a hardcoded value but this must be fixed
      // The backend should accept Entra ID and handle the user lookup internally
      console.warn('⚠️ FIXME: Using hardcoded UserID instead of mapping Entra ID to internal UserID');
      
      // Map company name to company ID (same logic as calendar)
      const companyId = user.company === 'Avirata Defence Systems' ? 4 : 1;
      
      // Temporary workaround: All users get UserID 1
      // This is the root cause of the "Chetan for all users" issue
      setFormData(prev => ({ 
        ...prev, 
        requestedBy: 1,
        companyId: companyId
      }));
    }
  }, [user]);

  // Load initial draft data
  useEffect(() => {
    if (initialDraft) {
      console.log('🔄 Loading initial draft:', {
        draftId: initialDraft.draftId,
        title: initialDraft.title,
        requestedCompletionDate: initialDraft.requestedCompletionDate,
        requestedCompletionDateType: typeof initialDraft.requestedCompletionDate,
        richContent: initialDraft.richContent,
        richContentLength: initialDraft.richContent?.length || 0
      });

      // Format date for HTML date input (YYYY-MM-DD)
      let formattedDate = '';
      if (initialDraft.requestedCompletionDate) {
        try {
          const date = new Date(initialDraft.requestedCompletionDate);
          if (!isNaN(date.getTime())) {
            formattedDate = date.toISOString().split('T')[0];
            console.log('📅 Formatted date:', {
              original: initialDraft.requestedCompletionDate,
              formatted: formattedDate
            });
          } else {
            console.warn('⚠️ Invalid date from draft:', initialDraft.requestedCompletionDate);
          }
        } catch (error) {
          console.error('❌ Date parsing error:', error);
        }
      }

      setFormData({
        title: initialDraft.title,
        description: initialDraft.description,
        typeId: initialDraft.typeId,
        priority: initialDraft.priority as 'Low' | 'Medium' | 'High' | 'Critical',
        businessJustification: initialDraft.businessJustification || '',
        expectedBenefit: initialDraft.expectedBenefit || '',
        requestedCompletionDate: formattedDate,
        requestedBy: initialDraft.requestedBy
      });
      
      console.log('📋 Setting rich content from draft:', initialDraft.richContent || []);
      console.log('📋 Detailed rich content from draft:', {
        richContentLength: initialDraft.richContent?.length || 0,
        richContentBlocks: (initialDraft.richContent || []).map((block, i) => ({
          index: i,
          id: block.id,
          type: block.type,
          content: block.content,
          hasContent: !!block.content,
          contentLength: block.content?.length || 0,
          contentPreview: block.content?.substring(0, 50) + (block.content?.length > 50 ? '...' : ''),
          listItems: block.listItems,
          imageUrl: block.imageUrl
        }))
      });
      setRichContent(initialDraft.richContent || []);
      setCurrentDraftId(initialDraft.draftId);
      setIsDraft(true);
    }
  }, [initialDraft]);

  const requestTypes = [
    { id: 1, name: 'New Application Development', description: 'Request for development of a completely new application or system' },
    { id: 2, name: 'System Enhancement', description: 'Request to modify or enhance existing system functionality' },
    { id: 3, name: 'Bug Fix Request', description: 'Request to fix a bug or issue in existing system' },
    { id: 4, name: 'Data & Reporting Request', description: 'Request for data extraction, reporting, or database changes' },
    { id: 5, name: 'Infrastructure Change', description: 'Request for infrastructure, server, or environment changes' }
  ];

  const priorities = [
    { value: 'Low', color: 'bg-green-100 text-green-800', description: 'Can wait 3+ months' },
    { value: 'Medium', color: 'bg-yellow-100 text-yellow-800', description: 'Needed within 1-3 months' },
    { value: 'High', color: 'bg-orange-100 text-orange-800', description: 'Needed within 2-4 weeks' },
    { value: 'Critical', color: 'bg-red-100 text-red-800', description: 'Urgent - needed within 1 week' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.description.trim()) {
      setError('Title and description are required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      // Convert rich content to the format expected by the backend
      const contentBlocks = richContent.map((block) => {
        // Map frontend types to backend types
        let backendType: 'text' | 'heading' | 'image' | 'code' | 'list' = 'text';
        switch (block.type) {
          case 'paragraph':
            backendType = 'text';
            break;
          case 'heading1':
          case 'heading2':
          case 'heading3':
            backendType = 'heading';
            break;
          case 'image':
            backendType = 'image';
            break;
          case 'code':
            backendType = 'code';
            break;
          case 'list':
            backendType = 'list';
            break;
          default:
            backendType = 'text';
        }

        return {
          type: backendType,
          data: block.content || '',
          content: block.content || '',
          imageUrl: block.imageUrl,
          imageCaption: block.imageCaption,
          imageAltText: block.imageAlt
        };
      });

      console.log('🚀 Submitting change request with content:', {
        formData,
        contentBlocks,
        richContentLength: richContent.length
      });

      // Include rich content in the request
      const requestData = {
        ...formData,
        content: contentBlocks,
        submitImmediately: true // This will make it submit to Under Review status instead of Draft
      };
      
      await changeManagementApi.createChangeRequest(requestData);
      
      setSuccess(true);
      setTimeout(() => {
        onSuccess();
        handleClose();
      }, 1500);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create change request');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        title: '',
        description: '',
        typeId: 1,
        priority: 'Medium',
        businessJustification: '',
        expectedBenefit: '',
        requestedCompletionDate: '',
        requestedBy: 1,
        companyId: undefined
      });
      setRichContent([]);
      setIsDraft(false);
      setCurrentDraftId(null);
      setLastAutoSave(null);
      setAutoSaving(false);
      setError(null);
      setSuccess(false);
      draftManagementApi.clearAllAutoSave();
      onClose();
    }
  };

  const handleInputChange = (field: keyof CreateChangeRequestData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!isOpen) return null;

  // Debug logging for rich content editor
  console.log('🎨 Rendering RichContentEditor with:', {
    richContentLength: richContent.length,
    richContent: richContent,
    isDraft: isDraft,
    currentDraftId: currentDraftId
  });

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header - Fixed */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900">
            {initialDraft ? 'Edit Draft Change Request' : 'Create New Change Request'}
          </h2>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content - Scrollable */}
        <div className="p-6 overflow-y-auto flex-1">
          {success ? (
            <div className="text-center py-8">
              <CheckCircle size={48} className="mx-auto text-green-600 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Request Created Successfully!</h3>
              <p className="text-gray-600">Your change request has been submitted and is now visible in the queue.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertCircle size={20} className="text-red-600" />
                    <p className="text-red-700">{error}</p>
                  </div>
                </div>
              )}

              {/* Basic Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Title */}
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Request Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Provide a clear, descriptive title for your request"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                {/* Request Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Request Type *
                  </label>
                  <select
                    value={formData.typeId}
                    onChange={(e) => handleInputChange('typeId', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  >
                    {requestTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    {requestTypes.find(t => t.id === formData.typeId)?.description}
                  </p>
                </div>

                {/* Priority */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority *
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  >
                    {priorities.map(priority => (
                      <option key={priority.value} value={priority.value}>
                        {priority.value}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    {priorities.find(p => p.value === formData.priority)?.description}
                  </p>
                </div>

                {/* Requested Completion Date */}
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Requested Completion Date
                  </label>
                  <input
                    type="date"
                    value={formData.requestedCompletionDate}
                    onChange={(e) => handleInputChange('requestedCompletionDate', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    When would you ideally like this request to be completed?
                  </p>
                </div>
              </div>

              {/* Rich Content Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Detailed Description *
                </label>
                <RichContentEditor
                  key={isDraft ? currentDraftId : 'new-form'}
                  initialContent={richContent}
                  onChange={handleRichContentChange}
                  onAutoSave={handleAutoSave}
                  placeholder="Provide a detailed description of what you need. Use the toolbar to format text, add headings, lists, code blocks, and images..."
                  className="min-h-[300px]"
                />
                <p className="mt-2 text-sm text-gray-500">
                  Use the rich editor to create comprehensive requirements documentation. You can add formatted text, headings, lists, code examples, and images to clearly communicate your needs.
                </p>
                {lastAutoSave && (
                  <p className="mt-1 text-xs text-green-600">
                    <Save size={12} className="inline mr-1" />
                    Auto-saved at {lastAutoSave.toLocaleTimeString()}
                  </p>
                )}
              </div>

              {/* Business Justification */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Justification
                </label>
                <textarea
                  value={formData.businessJustification}
                  onChange={(e) => handleInputChange('businessJustification', e.target.value)}
                  placeholder="Explain why this change is needed from a business perspective..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Explain the business case, current problems, or opportunities this request addresses.
                </p>
              </div>

              {/* Expected Benefits */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Expected Benefits
                </label>
                <textarea
                  value={formData.expectedBenefit}
                  onChange={(e) => handleInputChange('expectedBenefit', e.target.value)}
                  placeholder="Describe the expected benefits and outcomes..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="mt-1 text-sm text-gray-500">
                  What benefits do you expect from this change? Include efficiency gains, cost savings, or user experience improvements.
                </p>
              </div>
            </form>
          )}
        </div>

        {/* Footer - Fixed */}
        {!success && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200 flex-shrink-0 bg-white">
            {/* Draft Status */}
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {autoSaving && (
                <span className="flex items-center text-blue-600">
                  <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Auto-saving...
                </span>
              )}
              {isDraft && !autoSaving && (
                <span className="flex items-center text-green-600">
                  <Save size={14} className="mr-1" />
                  Draft saved
                </span>
              )}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSaveAsDraft}
                disabled={loading || autoSaving}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                {autoSaving ? 'Saving...' : 'Save as Draft'}
              </button>
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={loading || !formData.title.trim() || richContent.length === 0}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Submitting...</span>
                  </div>
                ) : (
                  'Submit Request'
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateChangeRequestModal; 