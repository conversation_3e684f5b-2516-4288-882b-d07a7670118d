"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const vite_1 = require("vite");
const plugin_react_1 = require("@vitejs/plugin-react");
// https://vite.dev/config/
exports.default = (0, vite_1.defineConfig)({
    plugins: [(0, plugin_react_1.default)()],
    server: {
        proxy: {
            '/api': {
                target: 'http://127.0.0.1:7072',
                changeOrigin: true,
                // rewrite: (path) => path.replace(/^\/api/, '') // Only if your Azure Function routes don't include /api
            }
        }
    }
});
//# sourceMappingURL=vite.config.js.map