"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPortalUserById = void 0;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const authUtils_1 = require("../shared/authUtils");
const logger_1 = require("../shared/utils/logger");
async function getPortalUserById(request, context) {
    context.log = logger_1.logger.info;
    logger_1.logger.info(`Http function processed request for url "${request.url}" to get user by ID.`);
    // 1. Authentication (basic check, adapt as needed)
    // For local development, you might want to bypass this if headers are not easily emulated.
    const isLocalDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    if (!isLocalDevelopment && process.env.NODE_ENV !== 'test') {
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            logger_1.logger.warn("GetPortalUserById: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        // TODO: Add role checks if necessary, e.g., only admins or the user themselves
        // Example: if (!hasRequiredRole(principal, ['PortalAdmin']) && principal.userId !== entraId) { return { status: 403, ... }}
    }
    else {
        logger_1.logger.warn("GetPortalUserById: Bypassing auth in development/test mode.");
    }
    // 2. Extract user ID from route parameter
    const userIdParam = request.params.entraId; // Keep the same parameter name for route consistency
    if (!userIdParam) {
        logger_1.logger.warn("GetPortalUserById: User ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "User ID must be provided in the path." } };
    }
    try {
        let user = null;
        // 3. Determine if the parameter is a numeric UserID or an EntraID
        if (/^\d+$/.test(userIdParam)) {
            // Parameter is numeric, treat as UserID
            const userId = parseInt(userIdParam, 10);
            logger_1.logger.info(`Fetching user by UserID: ${userId}`);
            user = await userManagementService_1.userManagementService.getPortalUserByUserId(userId);
        }
        else {
            // Parameter is not numeric, treat as EntraID
            logger_1.logger.info(`Fetching user by EntraID: ${userIdParam}`);
            user = await userManagementService_1.userManagementService.getPortalUserByEntraId(userIdParam);
        }
        // 4. Return Response
        if (user) {
            return { status: 200, jsonBody: user };
        }
        else {
            logger_1.logger.warn(`GetPortalUserById: User with ID ${userIdParam} not found.`);
            return { status: 404, jsonBody: { error: "User not found" } };
        }
    }
    catch (err) {
        logger_1.logger.error(`Error in GetPortalUserById for ID ${userIdParam}:`, err);
        const error = err;
        // Check if the error message is one that should be propagated or a generic one
        const message = error.message.startsWith("Database error while fetching user") ? error.message : "Failed to fetch user due to an internal error.";
        return { status: 500, jsonBody: { error: message } };
    }
}
exports.getPortalUserById = getPortalUserById;
functions_1.app.http('GetPortalUserById', {
    methods: ['GET'],
    route: 'portal-users/{entraId}',
    authLevel: 'anonymous',
    handler: getPortalUserById
});
//# sourceMappingURL=GetPortalUserById.js.map