// import React from 'react'; // Unused import
import { useNavigate } from 'react-router-dom';
import {
  // Home, // Unused import
  Book,
  Briefcase,
  Heart,
  Settings,
  MessageSquare,
  Users,
  // ChevronDown, // Unused import
  // ChevronRight, // Unused import
  Shield,
  // UserCheck, // Unused import
  Globe,
  ArrowRight,
} from 'feather-icons-react';

function Enterprises_section() {
  const navigate = useNavigate();

  const navlinks_portal = [
    {
      id: 'EXECUTION MANAGEMENT',
      label: 'Execution Management',
      icon: <Settings />,
      link: '/execution_mgnt',
      desc: 'Engineering operations and project oversight',
      category: 'Operations',
    },
    {
      id: 'QUALITY',
      label: 'Quality Assurance',
      icon: <Shield />,
      link: '/quality',
      desc: 'Quality control and compliance management',
      category: 'Operations',
    },
    {
      id: 'PRODUCTION',
      label: 'Production',
      icon: <Heart />,
      link: '/production',
      desc: 'Manufacturing and production workflows',
      category: 'Operations',
    },
    {
      id: 'SALES',
      label: 'Sales',
      icon: <Globe />,
      link: '/sales',
      desc: 'Sales management and customer relations',
      category: 'Business',
    },
    {
      id: 'SUPPLY CHAIN',
      label: 'Supply Chain',
      icon: <Briefcase />,
      link: '/scm',
      desc: 'Supply chain management and logistics',
      category: 'Operations',
    },
    {
      id: 'STORES',
      label: 'Inventory',
      icon: <Book />,
      link: '/stores',
      desc: 'Warehouse and inventory management',
      category: 'Operations',
    },
    {
      id: 'HR',
      label: 'Human Resources',
      icon: <Users />,
      link: '/employee_advance',
      desc: 'Employee management and HR processes',
      category: 'Business',
    },
  ];

  const handleActionClick = (link) => {
    navigate(link);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">


      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-light text-gray-900 mb-2">Enterprise Hub</h1>
              <p className="text-gray-600 text-lg">Centralized access to all business operations</p>
            </div>
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full">
                {navlinks_portal.length} Modules Active
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {navlinks_portal.map((item) => (
            <div
              key={item.id}
              onClick={() => handleActionClick(item.link)}
              className="group relative bg-white rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer overflow-hidden hover:shadow-xl"
            >
              {/* Hover gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Content */}
              <div className="relative p-8">
                {/* Category badge */}
                <div className="flex items-center justify-between mb-6">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {item.category}
                  </span>
                  <div className="w-12 h-12 bg-gray-50 rounded-xl flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-300">
                    <div className="text-gray-600 group-hover:text-blue-600 transition-colors duration-300">
                      {item.icon}
                    </div>
                  </div>
                </div>

                {/* Title and description */}
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-900 transition-colors duration-300">
                    {item.label}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {item.desc}
                  </p>
                </div>

                {/* Arrow indicator */}
                <div className="flex items-center justify-between">
                  <div className="flex-1" />
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center group-hover:bg-blue-100 transition-all duration-300 group-hover:scale-110">
                    <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-blue-600 transition-colors duration-300" />
                  </div>
                </div>
              </div>

              {/* Bottom border accent */}
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
            </div>
          ))}
        </div>

        {/* Additional info section */}
        <div className="mt-16 bg-white rounded-2xl border border-gray-200 p-8">
          <div className="flex items-start space-x-6">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
              <p className="text-gray-600 mb-4">
                Access our comprehensive documentation and support resources to get the most out of your enterprise tools.
              </p>
              <button className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center space-x-1 transition-colors duration-200">
                <span>View Documentation</span>
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Enterprises_section;