import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { changeManagementApi, type CalendarEvent } from '../../services/changeManagementApi';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock, ExternalLink } from 'feather-icons-react';

interface CalendarProps {
  className?: string;
}

const ChangeManagementCalendar: React.FC<CalendarProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [hoveredDate, setHoveredDate] = useState<string | null>(null);

  useEffect(() => {
    const loadEvents = async () => {
      setLoading(true);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      
      try {
        console.log(`Loading calendar events for ${year}-${month}`);
        const calendarEvents = await changeManagementApi.getChangeCalendar(year, month);
        console.log('Calendar events loaded:', calendarEvents);
        setEvents(calendarEvents || []);
      } catch (error) {
        console.error('Failed to load calendar events:', error);
        setEvents([]);
      }
      setLoading(false);
    };

    loadEvents();
  }, [currentDate]);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }
    
    return days;
  };

  const getEventsForDate = (day: number) => {
    const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return events.filter(event => {
      const eventDate = event.startDate.split('T')[0];
      return eventDate === dateStr;
    });
  };

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
    setSelectedDate(null); // Clear selection when navigating
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return {
        bg: 'bg-red-50',
        border: 'border-red-300',
        dot: 'bg-red-500',
        text: 'text-red-800',
        ring: 'ring-red-100',
        cardBg: 'bg-red-100',
        cardBorder: 'border-red-400',
        badge: 'bg-red-600 text-white'
      };
      case 'high': return {
        bg: 'bg-amber-50',
        border: 'border-amber-300',
        dot: 'bg-amber-500',
        text: 'text-amber-800',
        ring: 'ring-amber-100',
        cardBg: 'bg-amber-100',
        cardBorder: 'border-amber-400',
        badge: 'bg-amber-600 text-white'
      };
      case 'medium': return {
        bg: 'bg-blue-50',
        border: 'border-blue-300',
        dot: 'bg-blue-500',
        text: 'text-blue-800',
        ring: 'ring-blue-100',
        cardBg: 'bg-blue-100',
        cardBorder: 'border-blue-400',
        badge: 'bg-blue-600 text-white'
      };
      case 'low': return {
        bg: 'bg-emerald-50',
        border: 'border-emerald-300',
        dot: 'bg-emerald-500',
        text: 'text-emerald-800',
        ring: 'ring-emerald-100',
        cardBg: 'bg-emerald-100',
        cardBorder: 'border-emerald-400',
        badge: 'bg-emerald-600 text-white'
      };
      default: return {
        bg: 'bg-gray-50',
        border: 'border-gray-300',
        dot: 'bg-gray-500',
        text: 'text-gray-800',
        ring: 'ring-gray-100',
        cardBg: 'bg-gray-100',
        cardBorder: 'border-gray-400',
        badge: 'bg-gray-600 text-white'
      };
    }
  };

  const handleDateClick = (day: number) => {
    const dateKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const dayEvents = getEventsForDate(day);
    
    if (dayEvents.length > 0) {
      setSelectedDate(selectedDate === dateKey ? null : dateKey);
    }
  };

  const isToday = (day: number) => {
    const today = new Date();
    return (
      day === today.getDate() &&
      currentDate.getMonth() === today.getMonth() &&
      currentDate.getFullYear() === today.getFullYear()
    );
  };

  const days = getDaysInMonth(currentDate);
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden ${className}`}>
      {/* Modern Header */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white rounded-lg shadow-sm">
              <CalendarIcon size={20} className="text-slate-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Release Calendar</h3>
              <p className="text-sm text-gray-500">Upcoming deployments & releases</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-1 bg-white rounded-lg shadow-sm p-1">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-2 rounded-md hover:bg-gray-50 transition-colors duration-200 group"
            >
              <ChevronLeft size={18} className="text-gray-600 group-hover:text-gray-900" />
            </button>
            <div className="px-4 py-2">
              <span className="text-sm font-medium text-gray-900 min-w-[140px] text-center block">
                {formatMonth(currentDate)}
              </span>
            </div>
            <button
              onClick={() => navigateMonth('next')}
              className="p-2 rounded-md hover:bg-gray-50 transition-colors duration-200 group"
            >
              <ChevronRight size={18} className="text-gray-600 group-hover:text-gray-900" />
            </button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="relative">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-200"></div>
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent absolute top-0 left-0"></div>
          </div>
          <p className="text-sm text-gray-500 mt-3">Loading calendar events...</p>
        </div>
      ) : (
        <div className="p-6">
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-4">
            {/* Day headers */}
            {dayNames.map(day => (
              <div key={day} className="py-3 text-xs font-medium text-gray-500 text-center uppercase tracking-wider">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {days.map((day, index) => {
              if (day === null) {
                return <div key={index} className="h-12"></div>;
              }
              
              const dayEvents = getEventsForDate(day);
              const hasEvents = dayEvents.length > 0;
              const dateKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
              const isSelected = selectedDate === dateKey;
              const isTodayDate = isToday(day);
              
              // Consistent right-aligned tooltip positioning for all days
              const getTooltipPositioning = () => {
                return "right-0 transform translate-x-0";
              };
              
              return (
                <div key={dateKey} className="relative">
                  <button
                    onClick={() => handleDateClick(day)}
                    onMouseEnter={() => hasEvents && setHoveredDate(dateKey)}
                    onMouseLeave={() => setHoveredDate(null)}
                    className={`
                      w-full h-12 flex items-center justify-center text-sm rounded-lg transition-all duration-200 relative
                      ${isTodayDate 
                        ? 'bg-blue-500 text-white font-semibold shadow-md' 
                        : hasEvents 
                          ? 'bg-gray-50 hover:bg-gray-100 text-gray-900 font-medium cursor-pointer border border-gray-200 hover:border-gray-300' 
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }
                      ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50' : ''}
                    `}
                  >
                    {day}
                    
                    {/* Event indicators */}
                    {hasEvents && !isTodayDate && (
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 flex space-x-0.5">
                        {dayEvents.slice(0, 3).map((event, idx) => {
                          const colors = getPriorityColor(event.priority);
                          return (
                            <div
                              key={`indicator-${day}-${event.id}-${idx}`}
                              className={`w-1.5 h-1.5 rounded-full ${colors.dot}`}
                            />
                          );
                        })}
                        {dayEvents.length > 3 && (
                          <div className="w-1.5 h-1.5 rounded-full bg-gray-400" />
                        )}
                      </div>
                    )}
                  </button>
                  
                  {/* Enhanced hover tooltip with smart positioning */}
                  {hoveredDate === dateKey && dayEvents.length > 0 && !isSelected && (
                    <div className={`absolute z-20 top-full ${getTooltipPositioning()} mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[250px] max-w-[280px] opacity-0 animate-fade-in`}>
                      <div className="flex items-center space-x-2 mb-2">
                        <Clock size={14} className="text-gray-500" />
                        <span className="text-sm font-medium text-gray-900">
                          {dayEvents.length} Release{dayEvents.length > 1 ? 's' : ''} Scheduled
                        </span>
                      </div>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {dayEvents.map((event, idx) => {
                          const colors = getPriorityColor(event.priority);
                          return (
                            <div key={`hover-${hoveredDate}-${event.id}-${idx}`} className="flex items-center space-x-2 text-sm">
                              <div className={`w-2 h-2 rounded-full ${colors.dot} flex-shrink-0`} />
                              <span className="truncate font-medium">{event.title}</span>
                              <span className={`text-xs px-2 py-1 rounded-full font-semibold ${colors.badge}`}>
                                {event.priority}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {/* Selected Date Details */}
          {selectedDate && (
            <div className="mt-6 p-5 bg-gradient-to-br from-slate-50 to-gray-100 rounded-xl border-2 border-slate-200 shadow-md transform transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white rounded-lg shadow-sm">
                    <CalendarIcon size={16} className="text-slate-600" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-slate-900">
                      Releases on {new Date(selectedDate).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </h4>
                    <p className="text-sm text-slate-600">
                      {getEventsForDate(parseInt(selectedDate.split('-')[2])).length} release{getEventsForDate(parseInt(selectedDate.split('-')[2])).length > 1 ? 's' : ''} scheduled
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedDate(null)}
                  className="p-2 text-slate-400 hover:text-slate-600 hover:bg-white rounded-lg transition-all duration-200"
                  title="Close details"
                >
                  ×
                </button>
              </div>
              <div className="space-y-3">
                {getEventsForDate(parseInt(selectedDate.split('-')[2])).map((event, idx) => {
                  const colors = getPriorityColor(event.priority);
                  return (
                    <div key={`selected-${selectedDate}-${event.id}-${idx}`} className={`p-4 rounded-xl border-2 ${colors.cardBorder} ${colors.cardBg} transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <div className={`w-3 h-3 rounded-full ${colors.dot} shadow-sm`} />
                            <h5 className="font-bold text-slate-900 text-base group-hover:text-slate-800">{event.title}</h5>
                            <span className={`text-xs px-3 py-1 rounded-full font-semibold ${colors.badge} shadow-sm`}>
                              {event.priority}
                            </span>
                          </div>
                          <div className="ml-6">
                            <p className="text-sm font-medium text-slate-700 mb-1">Status: <span className="font-normal">{event.status}</span></p>
                            <p className="text-xs text-slate-600">Request ID: CR-{String(event.id).padStart(5, '0')}</p>
                          </div>
                        </div>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/it/change-management?search=CR-${String(event.id).padStart(5, '0')}`);
                          }}
                          className="p-2 text-slate-400 hover:text-blue-600 hover:bg-white rounded-lg transition-all duration-200 shadow-sm"
                          title={`Find change request CR-${String(event.id).padStart(5, '0')}`}
                        >
                          <ExternalLink size={16} />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          
          {/* Modern Legend */}
          <div className="mt-6 pt-4 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-500 font-medium uppercase tracking-wider">Priority Levels</div>
              <div className="flex items-center space-x-4 text-xs">
                <div className="flex items-center space-x-1.5">
                  <div className="w-2 h-2 rounded-full bg-red-500" />
                  <span className="text-gray-600">Critical</span>
                </div>
                <div className="flex items-center space-x-1.5">
                  <div className="w-2 h-2 rounded-full bg-amber-500" />
                  <span className="text-gray-600">High</span>
                </div>
                <div className="flex items-center space-x-1.5">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <span className="text-gray-600">Medium</span>
                </div>
                <div className="flex items-center space-x-1.5">
                  <div className="w-2 h-2 rounded-full bg-emerald-500" />
                  <span className="text-gray-600">Low</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChangeManagementCalendar; 