import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';
import { addCorsHeaders } from '../shared/cors';

interface ChangeRequestDraft {
  draftId: string;
  title: string;
  description: string;
  typeId: number;
  typeName: string;
  priority: string;
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  richContent: any[];
  requestedBy: number;
  requestedByName: string;
  createdDate: string;
  lastModified: string;
}

export async function GetChangeRequestDrafts(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  context.log('GetChangeRequestDrafts function processed a request.');

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      body: ''
    });
  }

  try {
    // Get user ID from query parameters
    const userId = request.query.get('userId');
    
    if (!userId) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { 
          success: false, 
          message: 'User ID is required' 
        }
      });
    }

    // Get database connection
    const pool: ConnectionPool = await getPool();

    // Simple query to get user's drafts without joins for now
    const query = `
      SELECT 
        d.DraftID as draftId,
        d.Title as title,
        d.Description as description,
        d.TypeID as typeId,
        'General' as typeName,
        d.Priority as priority,
        d.BusinessJustification as businessJustification,
        d.ExpectedBenefit as expectedBenefit,
        d.RequestedCompletionDate as requestedCompletionDate,
        d.RichContent as richContent,
        d.RequestedBy as requestedBy,
        'User' as requestedByName,
        d.CreatedDate as createdDate,
        d.LastModified as lastModified
      FROM ChangeRequestDrafts d
      WHERE d.RequestedBy = @userId
      ORDER BY d.LastModified DESC
    `;

    const result = await pool.request()
      .input('userId', parseInt(userId))
      .query(query);

    // Process the results
    const drafts: ChangeRequestDraft[] = result.recordset.map(row => ({
      draftId: row.draftId,
      title: row.title,
      description: row.description,
      typeId: row.typeId,
      typeName: row.typeName,
      priority: row.priority,
      businessJustification: row.businessJustification,
      expectedBenefit: row.expectedBenefit,
      requestedCompletionDate: row.requestedCompletionDate,
      richContent: row.richContent ? JSON.parse(row.richContent) : [],
      requestedBy: row.requestedBy,
      requestedByName: row.requestedByName,
      createdDate: row.createdDate,
      lastModified: row.lastModified
    }));

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        drafts: drafts,
        count: drafts.length
      }
    });

  } catch (error) {
    context.log('Error fetching drafts:', error);
    
    return addCorsHeaders({
      status: 500,
      jsonBody: {
        success: false,
        message: 'Internal server error while fetching drafts',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });
  }
}

app.http('GetChangeRequestDrafts', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  handler: GetChangeRequestDrafts
}); 