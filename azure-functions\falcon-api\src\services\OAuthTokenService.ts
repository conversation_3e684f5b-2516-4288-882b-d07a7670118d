import { logger } from '../shared/utils/logger';
import { getPool } from '../shared/db';
import sql from 'mssql';

export interface OAuthToken {
    tokenId?: number;
    userId: string;
    serviceProvider: string;
    serviceType: string;
    accessToken: string;
    refreshToken?: string;
    expiresAt: Date;
    scope?: string;
    tokenType: string;
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface OAuthConfiguration {
    configId?: number;
    serviceProvider: string;
    serviceType: string;
    configKey: string;
    configValue: string;
    isEncrypted: boolean;
    isActive: boolean;
}

export class OAuthTokenService {
    
    /**
     * Get active OAuth token for a user and service
     * Replaces global.zohoTokens[userId] lookup
     */
    static async getActiveToken(
        userId: string, 
        serviceProvider: string = 'zoho', 
        serviceType: string = 'desk'
    ): Promise<OAuthToken | null> {
        try {
            logger.info(`OAuthTokenService: Getting active token for user ${userId}, service ${serviceProvider}/${serviceType}`);
            
            const pool = await getPool();
            const result = await pool.request()
                .input('UserId', sql.NVarChar(255), userId)
                .input('ServiceProvider', sql.NVarChar(100), serviceProvider)
                .input('ServiceType', sql.NVarChar(100), serviceType)
                .execute('GetActiveOAuthToken');
            
            if (result.recordset && result.recordset.length > 0) {
                const token = result.recordset[0];
                logger.info(`OAuthTokenService: Found active token for user ${userId}, expires at ${token.ExpiresAt}`);
                
                return {
                    tokenId: token.TokenId,
                    userId: userId,
                    serviceProvider: serviceProvider,
                    serviceType: serviceType,
                    accessToken: token.AccessToken,
                    refreshToken: token.RefreshToken,
                    expiresAt: new Date(token.ExpiresAt),
                    scope: token.Scope,
                    tokenType: token.TokenType || 'Bearer',
                    isActive: true
                };
            }
            
            logger.warn(`OAuthTokenService: No active token found for user ${userId}`);
            return null;
            
        } catch (error) {
            logger.error('OAuthTokenService: Error getting active token:', error);
            return null;
        }
    }
    
    /**
     * Save or update OAuth token for a user
     * Replaces global.zohoTokens[userId] = token assignment
     */
    static async saveToken(
        userId: string,
        accessToken: string,
        refreshToken?: string,
        expiresIn: number = 3600,
        scope?: string,
        serviceProvider: string = 'zoho',
        serviceType: string = 'desk',
        tokenType: string = 'Bearer'
    ): Promise<number | null> {
        try {
            logger.info(`OAuthTokenService: Saving token for user ${userId}, expires in ${expiresIn} seconds`);
            
            const pool = await getPool();
            const result = await pool.request()
                .input('UserId', sql.NVarChar(255), userId)
                .input('ServiceProvider', sql.NVarChar(100), serviceProvider)
                .input('ServiceType', sql.NVarChar(100), serviceType)
                .input('AccessToken', sql.NVarChar(sql.MAX), accessToken)
                .input('RefreshToken', sql.NVarChar(sql.MAX), refreshToken || null)
                .input('ExpiresIn', sql.Int, expiresIn)
                .input('Scope', sql.NVarChar(500), scope || null)
                .input('TokenType', sql.NVarChar(50), tokenType)
                .execute('SaveOAuthToken');
            
            if (result.recordset && result.recordset.length > 0) {
                const tokenId = result.recordset[0].TokenId;
                logger.info(`OAuthTokenService: Token saved successfully with ID ${tokenId}`);
                return tokenId;
            }
            
            logger.error('OAuthTokenService: Failed to save token - no result returned');
            return null;
            
        } catch (error) {
            logger.error('OAuthTokenService: Error saving token:', error);
            return null;
        }
    }
    
    /**
     * Check if token needs refresh (expires in next 5 minutes)
     * Used for proactive token refresh
     */
    static async needsRefresh(
        userId: string,
        serviceProvider: string = 'zoho',
        serviceType: string = 'desk'
    ): Promise<boolean> {
        try {
            const pool = await getPool();
            const result = await pool.request()
                .input('UserId', sql.NVarChar(255), userId)
                .input('ServiceProvider', sql.NVarChar(100), serviceProvider)
                .input('ServiceType', sql.NVarChar(100), serviceType)
                .query('SELECT dbo.NeedsTokenRefresh(@UserId, @ServiceProvider, @ServiceType) as NeedsRefresh');
            
            if (result.recordset && result.recordset.length > 0) {
                return result.recordset[0].NeedsRefresh === 1;
            }
            
            return false;
            
        } catch (error) {
            logger.error('OAuthTokenService: Error checking if token needs refresh:', error);
            return true; // Assume needs refresh on error for safety
        }
    }
    
    /**
     * Get OAuth configuration value
     * Used for client credentials, redirect URIs, etc.
     */
    static async getConfig(
        configKey: string,
        serviceProvider: string = 'zoho',
        serviceType: string = 'desk'
    ): Promise<string | null> {
        try {
            const pool = await getPool();
            const result = await pool.request()
                .input('ServiceProvider', sql.NVarChar(100), serviceProvider)
                .input('ServiceType', sql.NVarChar(100), serviceType)
                .input('ConfigKey', sql.NVarChar(255), configKey)
                .query(`
                    SELECT ConfigValue, IsEncrypted 
                    FROM OAuthConfigurations 
                    WHERE ServiceProvider = @ServiceProvider 
                        AND ServiceType = @ServiceType 
                        AND ConfigKey = @ConfigKey 
                        AND IsActive = 1
                `);
            
            if (result.recordset && result.recordset.length > 0) {
                const config = result.recordset[0];
                
                // TODO: Implement decryption for encrypted values in production
                if (config.IsEncrypted) {
                    logger.warn(`OAuthTokenService: Config ${configKey} is encrypted, returning as-is (implement decryption for production)`);
                }
                
                return config.ConfigValue;
            }
            
            logger.warn(`OAuthTokenService: Config not found: ${configKey}`);
            return null;
            
        } catch (error) {
            logger.error('OAuthTokenService: Error getting configuration:', error);
            return null;
        }
    }
    
    /**
     * Revoke/deactivate token for user
     * Used when user logs out or token is compromised
     */
    static async revokeToken(
        userId: string,
        serviceProvider: string = 'zoho',
        serviceType: string = 'desk'
    ): Promise<boolean> {
        try {
            logger.info(`OAuthTokenService: Revoking token for user ${userId}`);
            
            const pool = await getPool();
            await pool.request()
                .input('UserId', sql.NVarChar(255), userId)
                .input('ServiceProvider', sql.NVarChar(100), serviceProvider)
                .input('ServiceType', sql.NVarChar(100), serviceType)
                .query(`
                    UPDATE OAuthTokens 
                    SET IsActive = 0, UpdatedAt = GETDATE()
                    WHERE UserId = @UserId 
                        AND ServiceProvider = @ServiceProvider 
                        AND ServiceType = @ServiceType
                        AND IsActive = 1
                `);
            
            logger.info(`OAuthTokenService: Token revoked for user ${userId}`);
            return true;
            
        } catch (error) {
            logger.error('OAuthTokenService: Error revoking token:', error);
            return false;
        }
    }
    
    /**
     * Log token usage for audit purposes
     */
    static async logUsage(
        tokenId: number,
        requestEndpoint?: string,
        responseStatus?: number,
        errorMessage?: string
    ): Promise<void> {
        try {
            const pool = await getPool();
            await pool.request()
                .input('TokenId', sql.Int, tokenId)
                .input('RequestEndpoint', sql.NVarChar(500), requestEndpoint || null)
                .input('ResponseStatus', sql.Int, responseStatus || null)
                .input('ErrorMessage', sql.NVarChar(sql.MAX), errorMessage || null)
                .query(`
                    INSERT INTO OAuthTokenUsage (TokenId, RequestEndpoint, ResponseStatus, ErrorMessage)
                    VALUES (@TokenId, @RequestEndpoint, @ResponseStatus, @ErrorMessage)
                `);
                
        } catch (error) {
            logger.error('OAuthTokenService: Error logging token usage:', { 
                tokenId, 
                requestEndpoint, 
                errorMessage: (error as Error).message 
            });
        }
    }
    
    /**
     * Retrieves all active tokens.
     * This method could be used for background jobs or administrative tasks.
     */
    static async getAllActiveTokens(): Promise<OAuthToken[]> {
        try {
            const pool = await getPool();
            const result = await pool.request()
                .query(`
                    SELECT TokenId, UserId, ServiceProvider, ServiceType, 
                           ExpiresAt, Scope, TokenType, CreatedAt, UpdatedAt
                    FROM OAuthTokens 
                    WHERE IsActive = 1 
                    ORDER BY CreatedAt DESC
                `);
            
            return result.recordset.map(row => ({
                tokenId: row.TokenId,
                userId: row.UserId,
                serviceProvider: row.ServiceProvider,
                serviceType: row.ServiceType,
                accessToken: '[HIDDEN]', // Don't expose access tokens in admin view
                expiresAt: new Date(row.ExpiresAt),
                scope: row.Scope,
                tokenType: row.TokenType,
                isActive: true,
                createdAt: row.CreatedAt,
                updatedAt: row.UpdatedAt
            }));
            
        } catch (error) {
            logger.error('OAuthTokenService: Error getting all active tokens:', error);
            return [];
        }
    }
} 