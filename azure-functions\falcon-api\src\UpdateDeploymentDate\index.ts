import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';

export async function UpdateDeploymentDate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    try {
        context.log('UpdateDeploymentDate function triggered');

        // Get database connection
        const pool: ConnectionPool = await getPool();

        // Update specific change requests with deployment dates
        const updateQuery = `
            UPDATE ChangeRequests 
            SET DeploymentDate = CASE 
                WHEN RequestID = 32 THEN '2024-06-15'  -- Database Server Upgrade
                WHEN RequestID = 33 THEN '2024-06-20'  -- Security Patch Deployment  
                WHEN RequestID = 34 THEN '2024-06-10'  -- Network Infrastructure Update
                WHEN RequestID = 35 THEN '2024-06-25'  -- Application Performance Optimization
                WHEN RequestID = 36 THEN '2024-06-28'  -- Backup System Enhancement
                ELSE DeploymentDate
            END
            WHERE RequestID IN (32, 33, 34, 35, 36);
        `;

        const updateResult = await pool.request().query(updateQuery);
        context.log(`Updated ${updateResult.rowsAffected[0]} change requests with deployment dates`);

        // Get the updated results
        const selectQuery = `
            SELECT 
                RequestID, 
                Title, 
                Status, 
                Priority, 
                DeploymentDate,
                RequestedCompletionDate
            FROM ChangeRequests 
            WHERE RequestID IN (32, 33, 34, 35, 36)
            ORDER BY DeploymentDate;
        `;

        const selectResult = await pool.request().query(selectQuery);

        return {
            status: 200,
            jsonBody: { 
                success: true,
                message: `Updated ${updateResult.rowsAffected[0]} change requests with June deployment dates`,
                updatedCount: updateResult.rowsAffected[0],
                changeRequests: selectResult.recordset
            }
        };

    } catch (error) {
        context.log('Error in UpdateDeploymentDate:', error);
        return {
            status: 500,
            jsonBody: { 
                error: 'Failed to update deployment dates',
                details: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}

// Register the function
app.http('UpdateDeploymentDate', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'update-deployment-dates',
    handler: UpdateDeploymentDate
});