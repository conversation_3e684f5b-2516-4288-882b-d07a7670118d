"use client"

// import { useState, useEffect } from "react" // Unused imports
import Non_Calibrated_Mfg_Masters from "./component/non_calibrated_tools/non_calibrated_mfg_masters"
import Create_Nct from "./component/non_calibrated_tools/layout1"
import View_List from "./component/non_calibrated_tools/view_list"
import Mis_report_Cal from "./component/non_calibrated_tools/mis_report"
import Pn_St_Mc from  "./component/non_calibrated_tools/pn_st_mc"
import View_St_Coc from "./component/non_calibrated_tools/view_st_coc";

function App() {



  return (
    <div className="flex min-h-screen">
        <main className="flex-grow">
         <Non_Calibrated_Mfg_Masters/>
         <Create_Nct/>
         <View_List/>
         <Mis_report_Cal/>
         <Pn_St_Mc/>
         <View_St_Coc/>
        </main>

      </div>
  )
}

export default App
