import React, { useState, useEffect, useCallback } from "react";
import { fetchPortalUsers, createUser } from "../../../services/adminApi";
import { PortalUser } from "../../../shared/interfaces";
import { CreateUserData } from "../../../services/adminApi";
import UserTable from "../../../components/Admin/UserTable";
import Pagination from "../../../components/Common/Pagination";
import { useToast } from "../../../hooks/useToast";
// Import Button from nextui
import {
  Button,
  Modal, 
  ModalContent, 
  ModalHeader, 
  ModalBody, 
  ModalFooter, 
  useDisclosure,
  Input // Add Input for form fields
} from "@nextui-org/react";

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<PortalUser[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const pageSize = 10; // Or get from configuration/state
  const { addToast } = useToast();
  // Add state for Create User Modal
  const { isOpen: isCreateModalOpen, onOpen: onCreateModalOpen, onClose: onCreateModalClose } = useDisclosure();
  // State for the new user form
  const [newUserEmail, setNewUserEmail] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const loadUsers = useCallback(async (page: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await fetchPortalUsers({ page, pageSize });
      setUsers(result.users);
      setTotalUsers(result.totalCount);
      setTotalPages(Math.ceil(result.totalCount / pageSize));
      setCurrentPage(page);
    } catch (err: any) { // Explicitly type err
      const errorMessage = err.message || "Failed to load users. Please try again.";
      setError(errorMessage);
      addToast({ type: 'error', message: `Error loading users: ${errorMessage}` });
      console.error("Error in loadUsers:", err); // Log the full error
    } finally {
      setIsLoading(false);
    }
  }, [pageSize, addToast]); // Dependencies for useCallback

  useEffect(() => {
    loadUsers(currentPage);
  }, [loadUsers, currentPage]); // Trigger loadUsers when page changes or function definition changes

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      loadUsers(newPage);
    }
  };

  // Ensure this function definition exists
  const handleCreateUserSubmit = async () => {
    if (!newUserEmail) {
      addToast({ type: 'warning', message: 'Please enter an email or UPN.' });
      return;
    }

    setIsSubmitting(true);
    try {
      const userData: CreateUserData = { email: newUserEmail };
      const createdUser = await createUser(userData);
      
      addToast({ type: 'success', message: `User ${createdUser.name || createdUser.email} created successfully.` });
      onCreateModalClose(); // Close modal on success
      setNewUserEmail(''); // Clear input
      loadUsers(1); // Refresh user list (go back to page 1)

    } catch (error: any) {
      addToast({ type: 'error', message: `Failed to create user: ${error.message || 'Unknown error'}` });
      console.error("Create user error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // --- RENDER --- Single return statement starts here
  return (
    <div className="p-4 md:p-6 lg:p-8">
      <h1 className="text-2xl font-semibold mb-4">User Management</h1>

      {/* Action Bar: Filters and Create Button */}
      <div className="flex justify-between items-center mb-4">
        <div>
          {/* Placeholder for Filters */}
          {/* <Input placeholder="Search users..." /> */}
        </div>
        {/* Add onClick handler later - NOW */}
        <Button color="primary" onPress={onCreateModalOpen}>Create User</Button>
      </div>

      {/* Loading and Error States */}
      {isLoading && (
        <div className="text-center py-4">Loading users...</div>
      )}
      {!isLoading && error && (
        <div className="text-center py-4 text-red-500">Error: {error}</div>
      )}
      {!isLoading && !error && users.length === 0 && (
        <div className="text-center py-4">No users found.</div>
      )}

      {/* User Table and Pagination */}
      {!isLoading && !error && users.length > 0 && (
        <>
          <UserTable users={users} />
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            itemsPerPage={pageSize}
            totalItems={totalUsers}
          />
        </>
      )}

      {/* Create User Modal */}
      <Modal isOpen={isCreateModalOpen} onClose={onCreateModalClose} placement="top-center">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">Create New User</ModalHeader>
              <ModalBody>
                {/* Form fields will go here */}
                <Input 
                  autoFocus
                  label="Email / UPN"
                  placeholder="Enter user's email or User Principal Name"
                  variant="bordered"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                />
                {/* Add other fields like FirstName, LastName, Roles later */}
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onCreateModalClose}>
                  Close
                </Button>
                <Button 
                  color="primary" 
                  onPress={handleCreateUserSubmit} 
                  isLoading={isSubmitting}
                  disabled={!newUserEmail || isSubmitting} // Disable if no email or submitting
                >
                  {isSubmitting ? 'Creating...' : 'Create User'}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

    </div>
  ); // Single return statement ends here
};

export default UserManagementPage; 