"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rejectChangeRequest = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const sql = __importStar(require("mssql"));
const emailService_1 = require("../shared/services/emailService");
async function rejectChangeRequest(request, context) {
    context.log('RejectChangeRequest function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json();
        const { reason, userId } = body;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        if (!reason || !reason.trim()) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Rejection reason is required'
                    }
                }
            };
        }
        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required for rejection'
                    }
                }
            };
        }
        // First, check if the request exists and can be rejected
        const checkQuery = `
            SELECT RequestID, Status, RequestedBy, Title
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        const currentRequest = checkResult.recordset[0];
        // Check if request can be rejected
        if (!['Under Review'].includes(currentRequest.Status)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'INVALID_STATUS',
                        message: `Request cannot be rejected in current status: ${currentRequest.Status}`
                    }
                }
            };
        }
        // Update the request to rejected status
        const updateQuery = `
            UPDATE ChangeRequests 
            SET 
                Status = 'Rejected',
                RejectionReason = @reason,
                ModifiedBy = @userId,
                ModifiedDate = GETDATE()
            WHERE RequestID = @requestId
        `;
        const updateParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'reason', type: sql.NVarChar, value: reason.trim() },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(updateQuery, updateParams);
        // Add history entry
        const historyQuery = `
            INSERT INTO ChangeRequestHistory (
                RequestID, StatusFrom, StatusTo, ChangedBy, ChangeDate, Comments
            )
            VALUES (
                @requestId, @statusFrom, 'Rejected', @userId, GETDATE(), @comments
            )
        `;
        const historyParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'statusFrom', type: sql.NVarChar, value: currentRequest.Status },
            { name: 'userId', type: sql.Int, value: parseInt(userId) },
            { name: 'comments', type: sql.NVarChar, value: `Request rejected: ${reason.trim()}` }
        ];
        await (0, db_1.executeQuery)(historyQuery, historyParams);
        // Add rejection comment
        const commentQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, CreatedBy, CreatedDate
            )
            VALUES (
                @requestId, @commentText, 'ApprovalNote', 0, @userId, GETDATE()
            )
        `;
        const commentParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: `[REJECTION] ${reason.trim()}` },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        await (0, db_1.executeQuery)(commentQuery, commentParams);
        // Get comprehensive request details for email notification
        const detailsQuery = `
            SELECT 
                cr.RequestID as requestId,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Status as status,
                cr.Priority as priority,
                cr.CreatedDate as createdDate,
                cr.RequestedCompletionDate as dueDate,
                cr.RejectionReason as rejectionReason,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                c.CompanyName as companyName,
                CONCAT(rejecter.FirstName, ' ', rejecter.LastName) as approverName
            FROM ChangeRequests cr
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Companies c ON requester.CompanyID = c.CompanyID
                LEFT JOIN Users rejecter ON cr.ModifiedBy = rejecter.UserID
            WHERE cr.RequestID = @requestId
        `;
        const detailsResult = await (0, db_1.executeQuery)(detailsQuery, checkParams);
        const requestDetails = detailsResult.recordset[0];
        // Send email notification asynchronously
        try {
            const emailData = {
                requestId: requestDetails.requestId,
                requestNumber: requestDetails.requestNumber,
                title: requestDetails.title,
                description: requestDetails.description || '',
                priority: requestDetails.priority,
                status: requestDetails.status,
                requesterName: requestDetails.requesterName,
                requesterEmail: requestDetails.requesterEmail,
                companyName: requestDetails.companyName || 'Unknown Company',
                comments: reason.trim(),
                approverName: requestDetails.approverName,
                actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/it-hub/change-requests/${requestId}`,
                createdDate: new Date(requestDetails.createdDate),
                dueDate: requestDetails.dueDate ? new Date(requestDetails.dueDate) : undefined
            };
            // Send rejection notification (don't await to avoid blocking the response)
            emailService_1.EmailService.getInstance().sendChangeRequestRejected(emailData).catch((error) => {
                context.error('Failed to send rejection email notification:', error);
            });
            context.log(`Email notification queued for rejected change request ${requestId}`);
        }
        catch (emailError) {
            context.error('Error preparing email notification:', emailError);
            // Don't fail the rejection if email fails
        }
        context.log(`Successfully rejected change request ${requestId}`);
        return {
            status: 200,
            jsonBody: {
                success: true,
                message: 'Change request rejected successfully',
                data: {
                    requestId: requestDetails.requestId,
                    requestNumber: requestDetails.requestNumber,
                    title: requestDetails.title,
                    status: requestDetails.status,
                    priority: requestDetails.priority,
                    rejectionReason: requestDetails.rejectionReason,
                    requesterName: requestDetails.requesterName,
                    requesterEmail: requestDetails.requesterEmail
                }
            }
        };
    }
    catch (error) {
        context.error('Error in RejectChangeRequest:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while rejecting the change request',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.rejectChangeRequest = rejectChangeRequest;
functions_1.app.http('RejectChangeRequest', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/reject',
    handler: rejectChangeRequest
});
//# sourceMappingURL=index.js.map