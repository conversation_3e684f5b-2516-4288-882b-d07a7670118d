import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import { logger } from "../shared/utils/logger";
import { getClientPrincipal, getUserIdFromPrincipal } from "../shared/authUtils";
import * as sql from 'mssql';

// Location interface
interface Location {
    id: number;
    name: string;
    companyId: number;
    companyName: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
    isActive: boolean;
    employeeCount?: number;
}

async function getLocations(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info("GetLocations: Processing request");
    
    try {
        // Check authentication
        const principal = getClientPrincipal(req);
        const isDevelopment = !process.env.WEBSITE_SITE_NAME;
        
        if (!isDevelopment && !principal) {
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }

        const userId = principal?.userId || 'dev-user';
        
        // Get user's company ID from database
        let userCompanyId = 1; // Default to company 1 in development
        
        if (!isDevelopment && principal) {
            const internalUserId = await getUserIdFromPrincipal(principal, context);
            if (internalUserId) {
                // Get user's company ID from database
                const userQuery = `SELECT CompanyID FROM Users WHERE UserID = @UserID`;
                const userParams: QueryParameter[] = [
                    { name: 'UserID', type: sql.Int, value: internalUserId }
                ];
                const userResult = await executeQuery(userQuery, userParams);
                if (userResult.recordset && userResult.recordset.length > 0) {
                    userCompanyId = userResult.recordset[0].CompanyID;
                }
            }
        }
        
        logger.info(`GetLocations: Processing request for user: ${userId}, company: ${userCompanyId}`);
        
        // Query to get locations with employee counts
        const query = `
            SELECT 
                l.LocationID,
                l.LocationName,
                l.CompanyID,
                c.CompanyName,
                l.Address,
                l.City,
                l.State,
                l.Country,
                l.PostalCode,
                l.IsActive,
                COUNT(u.UserID) as EmployeeCount
            FROM Locations l
            LEFT JOIN Companies c ON l.CompanyID = c.CompanyID
            LEFT JOIN Users u ON l.LocationID = u.LocationID AND u.IsActive = 1
            WHERE l.CompanyID = @CompanyID AND l.IsActive = 1
            GROUP BY l.LocationID, l.LocationName, l.CompanyID, c.CompanyName, l.Address, l.City, l.State, l.Country, l.PostalCode, l.IsActive
            ORDER BY l.LocationName;
        `;

        const parameters: QueryParameter[] = [
            { name: 'CompanyID', type: sql.Int, value: userCompanyId }
        ];

        logger.info(`GetLocations: Executing query for company ${userCompanyId}`);
        
        const result = await executeQuery(query, parameters);
        
        if (!result.recordset) {
            logger.info(`GetLocations: No locations found`);
            return {
                status: 200,
                jsonBody: {
                    locations: []
                }
            };
        }

        // Map results to Location interface
        const locations: Location[] = result.recordset.map((row: any) => ({
            id: row.LocationID,
            name: row.LocationName,
            companyId: row.CompanyID,
            companyName: row.CompanyName,
            address: row.Address,
            city: row.City,
            state: row.State,
            country: row.Country,
            postalCode: row.PostalCode,
            isActive: row.IsActive,
            employeeCount: row.EmployeeCount || 0
        }));

        logger.info(`GetLocations: Found ${locations.length} locations`);

        return {
            status: 200,
            jsonBody: {
                locations
            }
        };

    } catch (error) {
        logger.error("GetLocations: Error processing request:", error);
        return { 
            status: 500, 
            jsonBody: { 
                error: "Internal server error", 
                details: error instanceof Error ? error.message : String(error) 
            } 
        };
    }
}

// Register the function
app.http('GetLocations', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'locations',
    handler: getLocations
});

export { getLocations }; 