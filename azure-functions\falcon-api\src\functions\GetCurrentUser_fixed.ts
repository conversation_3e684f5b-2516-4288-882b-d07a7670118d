import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { userManagementService } from '../shared/services/userManagementService';
import { logger } from '../shared/utils/logger';
import { getClientPrincipal } from "../shared/authUtils";
import { PortalUser } from '../shared/interfaces';

// Helper function to extract name from email if not available
function extractNameFromEmail(email: string): string {
    if (!email) return 'User';
    
    const localPart = email.split('@')[0];
    // Handle common email patterns like firstname.lastname
    if (localPart.includes('.')) {
        const nameParts = localPart.split('.');
        return nameParts
            .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
            .join(' ');
    }
    
    // For other patterns, just capitalize the first letter
    return localPart.charAt(0).toUpperCase() + localPart.slice(1).toLowerCase();
}

async function getCurrentUser(req: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    logger.info("GetCurrentUser: Function invoked");

    // Check if we're in development mode (no Azure App Service authentication)
    const isDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    logger.info(`GetCurrentUser: Development mode check - WEBSITE_SITE_NAME: ${process.env.WEBSITE_SITE_NAME}, isDevelopment: ${isDevelopment}`);
    
    if (isDevelopment) {
        // TEMPORARY: For development testing, simulate your user data
        // This will be removed in production where proper Azure Easy Auth is used
        logger.info("GetCurrentUser: Development mode - simulating user authentication for testing");
        
        // Simulate your Entra ID authentication data
        const entraId = "simulated-guid-12345"; // This would be your actual Entra Object ID
        const email = "<EMAIL>";
        const name = "Chetan Pal";
        const tenantId = "ecb4a448-4a99-443b-aaff-063150b6c9ea";
        
        logger.info(`GetCurrentUser: Development simulation - EntraID: ${entraId}, Email: ${email}, Tenant: ${tenantId}`);

        // Try to get existing user from portal database by EntraID first
        let existingUser: PortalUser | null = await userManagementService.getPortalUserByEntraId(entraId);
        
        // If not found by EntraID and we have email, try email as fallback (for legacy data)
        if (!existingUser && email) {
            logger.info(`GetCurrentUser: Development - EntraID lookup failed, trying email fallback: ${email}`);
            existingUser = await userManagementService.getPortalUserByEmail(email);
            
            // If found by email but EntraID doesn't match, update the database with correct EntraID
            if (existingUser && existingUser.internalId) {
                logger.info(`GetCurrentUser: Development - Found user by email, updating EntraID from ${existingUser.id} to ${entraId}`);
                await userManagementService.updateUserEntraId(existingUser.internalId, entraId);
                // Update the user object with correct EntraID
                existingUser.id = entraId;
            }
        }
        
        if (existingUser) {
            // Update user information with latest from Entra ID - roles are already included from the database query
            const updatedUser: PortalUser = {
                ...existingUser,
                name: name || existingUser.name,
                email: email || existingUser.email,
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };

            logger.info(`GetCurrentUser: Development - Found existing user ${existingUser.email} with roles: ${updatedUser.roles.join(', ')}`);
            return { status: 200, jsonBody: updatedUser };
        } else {
            // User not found in portal database - create with default Employee role
            logger.info(`GetCurrentUser: Development - User ${entraId} (${email}) not found in portal database, creating with default Employee role`);
            
            // Determine company from email domain or tenant
            const company = getCompanyFromEmailOrTenant(email, tenantId);
            const companyId = getCompanyIdFromName(company);
            
            // Create a basic user record with Employee role
            const defaultUser: PortalUser = {
                id: entraId,
                internalId: 0, // Will be set when user is properly created in database
                name: name,
                email: email || entraId,
                company: company,
                companyId: companyId,
                roles: ['Employee'], // Default role for all authenticated users
                status: 'Active',
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };
            
            logger.info(`GetCurrentUser: Development - Created default user for ${email} in company ${company}`);
            return { status: 200, jsonBody: defaultUser };
        }
    }

    // Production mode - use Azure App Service Easy Auth
    const principal = getClientPrincipal(req);
    if (!principal) {
        logger.warn("GetCurrentUser: Production mode - no client principal found.");
        return { status: 401, jsonBody: { error: "Unauthorized - Authentication required" } };
    }

    try {
        // Extract user information from Entra ID claims
        const entraId = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.microsoft.com/identity/claims/objectidentifier' || 
            claim.typ === 'oid'
        )?.val || principal.userId;

        const email = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress' ||
            claim.typ === 'email' ||
            claim.typ === 'preferred_username'
        )?.val || principal.userDetails;

        const name = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name' ||
            claim.typ === 'name'
        )?.val || extractNameFromEmail(email || entraId);

        const tenantId = principal.claims?.find((claim: any) => 
            claim.typ === 'http://schemas.microsoft.com/identity/claims/tenantid' ||
            claim.typ === 'tid'
        )?.val;

        logger.info(`GetCurrentUser: Processing user - EntraID: ${entraId}, Email: ${email}, Tenant: ${tenantId}`);

        // Try to get existing user from portal database by EntraID first
        let existingUser: PortalUser | null = await userManagementService.getPortalUserByEntraId(entraId);
        
        // If not found by EntraID and we have email, try email as fallback (for legacy data)
        if (!existingUser && email) {
            logger.info(`GetCurrentUser: EntraID lookup failed, trying email fallback: ${email}`);
            existingUser = await userManagementService.getPortalUserByEmail(email);
            
            // If found by email but EntraID doesn't match, update the database with correct EntraID
            if (existingUser && existingUser.internalId) {
                logger.info(`GetCurrentUser: Found user by email, updating EntraID from ${existingUser.id} to ${entraId}`);
                await userManagementService.updateUserEntraId(existingUser.internalId, entraId);
                // Update the user object with correct EntraID
                existingUser.id = entraId;
            }
        }
        
        if (existingUser) {
            // Update user information with latest from Entra ID - roles are already included from the database query
            const updatedUser: PortalUser = {
                ...existingUser,
                name: name || existingUser.name,
                email: email || existingUser.email,
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };

            logger.info(`GetCurrentUser: Found existing user ${existingUser.email} with roles: ${updatedUser.roles.join(', ')}`);
            return { status: 200, jsonBody: updatedUser };
        } else {
            // User not found in portal database - create with default Employee role
            logger.info(`GetCurrentUser: User ${entraId} (${email}) not found in portal database, creating with default Employee role`);
            
            // Determine company from email domain or tenant
            const company = getCompanyFromEmailOrTenant(email, tenantId);
            const companyId = getCompanyIdFromName(company);
            
            // Create a basic user record with Employee role
            const defaultUser: PortalUser = {
                id: entraId,
                internalId: 0, // Will be set when user is properly created in database
                name: name,
                email: email || entraId,
                company: company,
                companyId: companyId,
                roles: ['Employee'], // Default role for all authenticated users
                status: 'Active',
                tenantId: tenantId,
                lastLogin: new Date().toISOString()
            };
            
            logger.info(`GetCurrentUser: Created default user for ${email} in company ${company}`);
            return { status: 200, jsonBody: defaultUser };
        }
    } catch (error) {
        logger.error(`GetCurrentUser: Error processing user ${principal.userId}:`, error);
        return { status: 500, jsonBody: { error: "Failed to fetch current user information" } };
    }
}

// Helper function to determine company from email domain or tenant ID
function getCompanyFromEmailOrTenant(email: string, tenantId?: string): string {
    if (!email) return 'Unknown Company';
    
    const emailDomain = email.split('@')[1]?.toLowerCase();
    
    // Map email domains to companies
    switch (emailDomain) {
        case 'aviratadefsys.com':
        case 'aviratadefencesystems.onmicrosoft.com':
            return 'Avirata Defence Systems';
        case 'sasmos.com':
        case 'sasmos.onmicrosoft.com':
            return 'SASMOS HET';
        case 'sasmosgroup.com':
            return 'SASMOS Group';
        case 'fe-sil.com':
            return 'FE-SIL';
        case 'glodesi.com':
            return 'Glodesi';
        case 'hanuka.com':
            return 'Hanuka';
        case 'westwireharnessing.co.uk':
            return 'West Wire Harnessing';
        default:
            // Try to map by tenant ID if available
            if (tenantId) {
                switch (tenantId) {
                    case 'ecb4a448-4a99-443b-aaff-063150b6c9ea':
                        return 'Avirata Defence Systems';
                    case '0ebf3a7d-84cf-4f4f-9ada-932ff92d8cd4':
                    case '334d188b-2ac3-43a9-8bad-590957b087c2':
                        return 'SASMOS Group';
                    default:
                        console.warn(`Unknown tenant ID: ${tenantId}`);
                        return 'Unknown Company';
                }
            }
            console.warn(`Unknown email domain: ${emailDomain}`);
            return 'Unknown Company';
    }
}

// Helper function to get company ID from company name
function getCompanyIdFromName(companyName: string): number {
    switch (companyName) {
        case 'Avirata Defence Systems':
            return 1;
        case 'SASMOS HET':
            return 2;
        case 'SASMOS Group':
            return 3;
        case 'FE-SIL':
            return 4;
        case 'Glodesi':
            return 5;
        case 'Hanuka':
            return 6;
        case 'West Wire Harnessing':
            return 7;
        default:
            return 1; // Default to Avirata if unknown
    }
}

app.http('GetCurrentUser', {
    methods: ['GET'],
    authLevel: 'anonymous', // or 'function' if you want to require a key
    route: 'current-user',
    handler: getCurrentUser
}); 