"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const validationSchemas_1 = require("./validationSchemas");
const zod_1 = require("zod");
// Mock InvocationContext for validateRequest
const mockContext = { log: jest.fn() };
describe('Validation Schemas', () => {
    describe('searchQuerySchema', () => {
        it('should validate correct input with defaults', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({ q: 'test' });
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.q).toBe('test');
                expect(result.data.limit).toBe(10); // Default limit
            }
        });
        it('should validate correct input with explicit limit', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({ q: 'search term', limit: '25' });
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.q).toBe('search term');
                expect(result.data.limit).toBe(25); // Parsed limit
            }
        });
        it('should fail validation for missing query', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({});
            expect(result.success).toBe(false);
        });
        it('should fail validation for short query', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({ q: 'a' });
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error.flatten().fieldErrors.q).toContain("Search query parameter 'q' must be at least 2 characters long.");
            }
        });
        it('should fail validation for invalid limit type', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({ q: 'test', limit: 'abc' });
            expect(result.success).toBe(false);
        });
        it('should fail validation for non-positive limit', () => {
            const result = validationSchemas_1.searchQuerySchema.safeParse({ q: 'test', limit: '0' });
            expect(result.success).toBe(false);
        });
    });
    describe('listUsersQuerySchema', () => {
        it('should validate correct input with defaults', () => {
            const result = validationSchemas_1.listUsersQuerySchema.safeParse({});
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.searchTerm).toBeUndefined();
                expect(result.data.companyFilter).toBe('All');
                expect(result.data.roleFilter).toBe('All');
                expect(result.data.statusFilter).toBe('All');
                expect(result.data.page).toBe(1);
                expect(result.data.pageSize).toBe(10);
            }
        });
        it('should validate explicit filters and pagination', () => {
            const input = {
                searchTerm: 'user',
                companyFilter: 'SASMOS',
                roleFilter: 'Admin',
                statusFilter: 'Active',
                page: '2',
                pageSize: '50'
            };
            const result = validationSchemas_1.listUsersQuerySchema.safeParse(input);
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.searchTerm).toBe('user');
                expect(result.data.companyFilter).toBe('SASMOS');
                expect(result.data.roleFilter).toBe('Admin');
                expect(result.data.statusFilter).toBe('Active');
                expect(result.data.page).toBe(2);
                expect(result.data.pageSize).toBe(50);
            }
        });
        it('should allow Inactive status filter', () => {
            const result = validationSchemas_1.listUsersQuerySchema.safeParse({ statusFilter: 'Inactive' });
            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.statusFilter).toBe('Inactive');
            }
        });
        it('should fail for invalid status filter', () => {
            const result = validationSchemas_1.listUsersQuerySchema.safeParse({ statusFilter: 'Pending' });
            expect(result.success).toBe(false);
        });
        it('should fail for invalid page number', () => {
            const result = validationSchemas_1.listUsersQuerySchema.safeParse({ page: '0' });
            expect(result.success).toBe(false);
        });
        it('should fail for invalid pageSize (too large)', () => {
            const result = validationSchemas_1.listUsersQuerySchema.safeParse({ pageSize: '101' });
            expect(result.success).toBe(false);
        });
    });
    describe('entraIdRouteParamSchema', () => {
        it('should validate correct Entra ID', () => {
            expect(validationSchemas_1.entraIdRouteParamSchema.safeParse({ entraId: 'valid-guid-or-id' }).success).toBe(true);
        });
        it('should fail for empty Entra ID', () => {
            expect(validationSchemas_1.entraIdRouteParamSchema.safeParse({ entraId: '' }).success).toBe(false);
        });
        it('should fail for missing Entra ID', () => {
            expect(validationSchemas_1.entraIdRouteParamSchema.safeParse({}).success).toBe(false);
        });
    });
    describe('userIdRouteParamSchema', () => {
        it('should validate correct User ID', () => {
            expect(validationSchemas_1.userIdRouteParamSchema.safeParse({ userId: '123' }).success).toBe(true);
        });
        it('should fail for non-numeric User ID', () => {
            expect(validationSchemas_1.userIdRouteParamSchema.safeParse({ userId: 'abc' }).success).toBe(false);
        });
        it('should fail for non-positive User ID', () => {
            expect(validationSchemas_1.userIdRouteParamSchema.safeParse({ userId: '0' }).success).toBe(false);
        });
        it('should fail for non-integer User ID', () => {
            expect(validationSchemas_1.userIdRouteParamSchema.safeParse({ userId: '1.5' }).success).toBe(false);
        });
    });
    // Note: userRoleRouteParamsSchema tests similar to userIdRouteParamSchema, potentially add if needed
    describe('addUserBodySchema', () => {
        it('should validate correct body', () => {
            expect(validationSchemas_1.addUserBodySchema.safeParse({ entraId: 'user-entra-id' }).success).toBe(true);
        });
        it('should fail for missing entraId', () => {
            expect(validationSchemas_1.addUserBodySchema.safeParse({}).success).toBe(false);
        });
        it('should fail for empty entraId', () => {
            expect(validationSchemas_1.addUserBodySchema.safeParse({ entraId: '' }).success).toBe(false);
        });
    });
    describe('assignRoleBodySchema', () => {
        it('should validate correct body', () => {
            expect(validationSchemas_1.assignRoleBodySchema.safeParse({ roleId: 5 }).success).toBe(true);
        });
        it('should fail for missing roleId', () => {
            expect(validationSchemas_1.assignRoleBodySchema.safeParse({}).success).toBe(false);
        });
        it('should fail for non-positive roleId', () => {
            expect(validationSchemas_1.assignRoleBodySchema.safeParse({ roleId: 0 }).success).toBe(false);
        });
        it('should fail for non-integer roleId', () => {
            expect(validationSchemas_1.assignRoleBodySchema.safeParse({ roleId: 1.2 }).success).toBe(false);
        });
    });
    describe('updateUserBodySchema', () => {
        it('should validate correct body with roles', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ roles: ['Admin', 'User'], isActive: true });
            expect(result.success).toBe(true);
        });
        it('should validate correct body with empty roles array', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ roles: [], isActive: false });
            expect(result.success).toBe(true);
        });
        it('should fail for missing isActive', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ roles: [] });
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error.flatten().fieldErrors.isActive).toBeDefined();
            }
        });
        it('should fail for missing roles', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ isActive: true });
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error.flatten().fieldErrors.roles).toBeDefined();
            }
        });
        it('should fail if roles is not an array', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ roles: 'Admin', isActive: true });
            expect(result.success).toBe(false);
        });
        it('should fail if roles contains non-strings', () => {
            const result = validationSchemas_1.updateUserBodySchema.safeParse({ roles: ['Admin', 123], isActive: true });
            expect(result.success).toBe(false);
        });
    });
    describe('validateRequest function', () => {
        // Use a simple schema for testing the wrapper function
        const testSchema = zod_1.z.object({ name: zod_1.z.string().min(1) });
        it('should return null for valid data', () => {
            const result = (0, validationSchemas_1.validateRequest)(testSchema, { name: 'Valid Name' }, mockContext, 'test data');
            expect(result).toBeNull();
            expect(mockContext.log).toHaveBeenCalledWith('Validation successful for test data.');
        });
        it('should return a 400 response for invalid data', () => {
            const result = (0, validationSchemas_1.validateRequest)(testSchema, { name: '' }, mockContext, 'test data');
            expect(result).not.toBeNull();
            if (result) {
                expect(result.status).toBe(400);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.message).toBe('Invalid test data.');
                expect(result.jsonBody.errors).toBeDefined();
                expect(result.jsonBody.errors.name).toBeDefined(); // Check specific field error
            }
            expect(mockContext.log).toHaveBeenCalledWith(expect.stringContaining('Request validation failed for test data:'), expect.any(String));
        });
        it('should return null and log warning if no schema provided', () => {
            const result = (0, validationSchemas_1.validateRequest)(null, { name: 'Valid Name' }, mockContext, 'test data');
            expect(result).toBeNull();
            expect(mockContext.log).toHaveBeenCalledWith('WARN: No validation schema provided for test data. Skipping validation.');
        });
    });
});
//# sourceMappingURL=validationSchemas.test.js.map