{"version": 3, "file": "DeleteRole.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/DeleteRole.ts"], "names": [], "mappings": ";;;;;;;;;;;AAKA,gCA2HC;AAhID,gDAAyF;AACzF,6BAA6B,CAAC,2BAA2B;AACzD,qCAAuC,CAAC,0BAA0B;AAGlE,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;QAEzE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAChB,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,0BAA0B;iBACtC;aACJ,CAAC;QACN,CAAC;QAED,mCAAmC;QACnC,IAAI,WAAW,GAA2B,IAAI,CAAC;QAC/C,IAAI,CAAC;YACD,0BAA0B;YAC1B,MAAM,IAAI,GAAG,MAAM,IAAA,YAAO,GAAE,CAAC;YAC7B,sBAAsB;YACtB,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC;YAE1B,4EAA4E;YAC5E,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YAC/C,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAClD,MAAM,eAAe,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;YAC1G,IAAI,eAAe,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzC,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;gBACzD,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,OAAO,EAAE,gBAAgB,MAAM,aAAa;qBAC/C;iBACJ,CAAC;YACN,CAAC;YACD,MAAM,QAAQ,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACvD,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnD,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;gBACzD,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE;wBACN,OAAO,EAAE,sCAAsC,QAAQ,EAAE;qBAC5D;iBACJ,CAAC;YACN,CAAC;YAED,2DAA2D;YAC3D,MAAM,kBAAkB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YACjD,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,uEAAuE,CAAC,CAAC;YAC/H,MAAM,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1G,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,2BAA2B;gBACzD,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAClF,CAAC;YAED,uEAAuE;YACvE,MAAM,4BAA4B,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3D,4BAA4B,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,0BAA0B,GAAG;;;;;;;;;;SAUlC,CAAC;YACF,MAAM,mBAAmB,GAAG,MAAM,4BAA4B,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACjG,MAAM,aAAa,GAAG,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAE3E,iEAAiE;YACjE,MAAM,wBAAwB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YACvD,wBAAwB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC1D,MAAM,wBAAwB,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAErF,4FAA4F;YAC5F,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,eAAe,aAAa,CAAC,MAAM,oCAAoC,CAAC,CAAC;gBACrF,oEAAoE;gBACpE,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;oBACjC,MAAM,eAAe,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;oBAC9C,eAAe,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,8CAA8C;oBACrG,eAAe,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;oBACnE,yFAAyF;oBACzF,MAAM,eAAe,CAAC,KAAK,CAAC,yEAAyE,CAAC,CAAC;gBACvG,CAAC;YACL,CAAC;YAEL,8CAA8C;YAC9C,MAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;YAChD,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACnD,MAAM,iBAAiB,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YAE1E,yBAAyB;YACzB,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;YAE3B,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;YACnD,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,aAAa;QAEzC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,GAAG,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,CAAC;YAEhE,8CAA8C;YAC9C,0EAA0E;YAC1E,IAAI,WAAW,EAAE,CAAC;gBACd,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;oBAC7B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;gBACzD,CAAC;gBAAC,OAAO,aAAkB,EAAE,CAAC;oBAC1B,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAC,CAAC;gBAC7E,CAAC;YACL,CAAC;YAED,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;oBACzC,KAAK,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,sCAAsC;iBAC/D;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnB,KAAK,EAAE,oBAAoB;IAC3B,SAAS,EAAE,UAAU;IACrB,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}