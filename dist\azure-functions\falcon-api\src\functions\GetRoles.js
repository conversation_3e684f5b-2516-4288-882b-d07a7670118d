"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoles = getRoles;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
function getRoles(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function processed request for url "${request.url}"`);
        try {
            // Query the correct description column
            const result = yield (0, db_1.executeQuery)('SELECT RoleID, RoleName, RoleDescription, IsActive FROM Roles WHERE IsActive = 1 ORDER BY RoleName');
            context.log("Raw recordset from DB:", JSON.stringify(result.recordset, null, 2));
            // Map the database result to the RoleDefinition interface using correct property names
            const roles = result.recordset.map(dbRole => ({
                RoleID: dbRole.RoleID,
                RoleName: dbRole.RoleName,
                Description: dbRole.RoleDescription // Map Description, assuming it exists
                // Include IsSystemRole and IsActive if they are selected and needed
                // IsSystemRole: dbRole.IsSystemRole,
                // IsActive: dbRole.IsActive
            }));
            return {
                status: 200,
                jsonBody: roles
            };
        }
        catch (error) {
            context.error(`Error fetching roles: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error fetching roles.",
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'roles',
    handler: getRoles
});
//# sourceMappingURL=GetRoles.js.map