<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1746204117232" clover="3.2.0">
  <project timestamp="1746204117233" name="All files">
    <metrics statements="283" coveredstatements="226" conditionals="120" coveredconditionals="87" methods="32" coveredmethods="22" elements="435" coveredelements="335" complexity="0" loc="283" ncloc="283" packages="4" files="5" classes="5"/>
    <package name="DeleteRole">
      <metrics statements="47" coveredstatements="44" conditionals="16" coveredconditionals="11" methods="2" coveredmethods="2"/>
      <file name="index.ts" path="C:\GitHub\FalconHub\FalconHub\azure-functions\falcon-api\DeleteRole\index.ts">
        <metrics statements="47" coveredstatements="44" conditionals="16" coveredconditionals="11" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="6" count="5" type="stmt"/>
        <line num="8" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="9" count="1" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="4" type="stmt"/>
        <line num="17" count="4" type="stmt"/>
        <line num="18" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="1" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="26" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="34" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="35" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="48" count="3" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="51" count="2" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="56" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="61" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="62" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="88" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="92" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="93" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
      </file>
    </package>
    <package name="shared">
      <metrics statements="50" coveredstatements="7" conditionals="22" coveredconditionals="4" methods="4" coveredmethods="0"/>
      <file name="db.ts" path="C:\GitHub\FalconHub\FalconHub\azure-functions\falcon-api\shared\db.ts">
        <metrics statements="50" coveredstatements="7" conditionals="22" coveredconditionals="4" methods="4" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.shared">
      <metrics statements="85" coveredstatements="79" conditionals="38" coveredconditionals="33" methods="22" coveredmethods="16"/>
      <file name="authUtils.ts" path="C:\GitHub\FalconHub\FalconHub\azure-functions\falcon-api\src\shared\authUtils.ts">
        <metrics statements="51" coveredstatements="51" conditionals="18" coveredconditionals="18" methods="7" coveredmethods="7"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="4" type="stmt"/>
        <line num="24" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="29" count="3" type="stmt"/>
        <line num="30" count="3" type="stmt"/>
        <line num="31" count="3" type="stmt"/>
        <line num="33" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="1" type="stmt"/>
        <line num="57" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="2" type="stmt"/>
        <line num="62" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="63" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="64" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="67" count="2" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="6" type="cond" truecount="1" falsecount="0"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="89" count="5" type="stmt"/>
        <line num="90" count="5" type="stmt"/>
        <line num="92" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="98" count="4" type="stmt"/>
        <line num="99" count="4" type="stmt"/>
        <line num="100" count="4" type="stmt"/>
        <line num="102" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="103" count="2" type="stmt"/>
        <line num="106" count="2" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="111" count="2" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
      </file>
      <file name="validationSchemas.ts" path="C:\GitHub\FalconHub\FalconHub\azure-functions\falcon-api\src\shared\validationSchemas.ts">
        <metrics statements="34" coveredstatements="28" conditionals="20" coveredconditionals="15" methods="15" coveredmethods="9"/>
        <line num="2" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="15" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="29" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="35" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="50" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="52" count="4" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="117" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.shared.services">
      <metrics statements="101" coveredstatements="96" conditionals="44" coveredconditionals="39" methods="4" coveredmethods="4"/>
      <file name="userManagementService.ts" path="C:\GitHub\FalconHub\FalconHub\azure-functions\falcon-api\src\shared\services\userManagementService.ts">
        <metrics statements="101" coveredstatements="96" conditionals="44" coveredconditionals="39" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="57" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="6" type="stmt"/>
        <line num="60" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="67" count="6" type="stmt"/>
        <line num="69" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="77" count="4" type="stmt"/>
        <line num="80" count="4" type="stmt"/>
        <line num="81" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="82" count="3" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="84" count="3" type="stmt"/>
        <line num="85" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="86" count="2" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="108" count="3" type="stmt"/>
        <line num="109" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="110" count="3" type="stmt"/>
        <line num="111" count="3" type="stmt"/>
        <line num="112" count="3" type="stmt"/>
        <line num="113" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="114" count="2" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="3" type="stmt"/>
        <line num="137" count="3" type="stmt"/>
        <line num="149" count="3" type="stmt"/>
        <line num="151" count="3" type="cond" truecount="2" falsecount="1"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="3" type="stmt"/>
        <line num="157" count="3" type="stmt"/>
        <line num="160" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="161" count="3" type="stmt"/>
        <line num="162" count="3" type="stmt"/>
        <line num="163" count="3" type="stmt"/>
        <line num="164" count="3" type="stmt"/>
        <line num="165" count="3" type="stmt"/>
        <line num="166" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="167" count="2" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="176" count="2" type="stmt"/>
        <line num="180" count="2" type="stmt"/>
        <line num="181" count="2" type="stmt"/>
        <line num="187" count="2" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="194" count="3" type="stmt"/>
        <line num="198" count="2" type="stmt"/>
        <line num="200" count="2" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="216" count="4" type="stmt"/>
        <line num="218" count="4" type="stmt"/>
        <line num="224" count="4" type="stmt"/>
        <line num="225" count="4" type="stmt"/>
        <line num="227" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="229" count="2" type="stmt"/>
        <line num="230" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="232" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="238" count="1" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="252" count="1" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="261" count="1" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="274" count="1" type="stmt"/>
        <line num="275" count="3" type="stmt"/>
        <line num="277" count="3" type="stmt"/>
        <line num="283" count="3" type="stmt"/>
        <line num="284" count="3" type="stmt"/>
        <line num="291" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="292" count="1" type="stmt"/>
        <line num="293" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="301" count="1" type="stmt"/>
        <line num="302" count="1" type="stmt"/>
        <line num="312" count="1" type="stmt"/>
        <line num="313" count="2" type="stmt"/>
        <line num="314" count="2" type="stmt"/>
        <line num="315" count="2" type="stmt"/>
        <line num="316" count="2" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="320" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
