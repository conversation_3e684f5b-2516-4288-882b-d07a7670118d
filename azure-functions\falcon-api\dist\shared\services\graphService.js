"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.graphService = exports.GraphService = void 0;
const microsoft_graph_client_1 = require("@microsoft/microsoft-graph-client");
const azureTokenCredentials_1 = require("@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials");
const identity_1 = require("@azure/identity");
require("isomorphic-fetch"); // Polyfill for fetch
const logger_1 = require("../utils/logger");
// Configuration (Should ideally come from a dedicated config file or env variables)
// Ensure these environment variables are set in your Azure Function App configuration
const tenantId = process.env.ENTRA_TENANT_ID;
const clientId = process.env.ENTRA_CLIENT_ID;
const clientSecret = process.env.ENTRA_CLIENT_SECRET;
// Check if we're in development mode
const isDevelopment = process.env.AZURE_FUNCTIONS_ENVIRONMENT === 'Development' ||
    process.env.NODE_ENV === 'development' ||
    !process.env.AZURE_FUNCTIONS_ENVIRONMENT;
let graphClient = null;
// Initialize Graph client only if credentials are available
if (tenantId && clientId && clientSecret) {
    try {
        // Create Azure Identity credential
        const credential = new identity_1.ClientSecretCredential(tenantId, clientId, clientSecret);
        // Create Microsoft Graph authentication provider
        const authProvider = new azureTokenCredentials_1.TokenCredentialAuthenticationProvider(credential, {
            scopes: ['https://graph.microsoft.com/.default'] // Use default scope for client credentials flow
        });
        // Initialize Microsoft Graph client
        graphClient = microsoft_graph_client_1.Client.initWithMiddleware({
            authProvider: authProvider,
        });
        logger_1.logger.info('GraphService: Successfully initialized with Entra ID credentials');
    }
    catch (error) {
        logger_1.logger.error('GraphService: Failed to initialize with provided credentials', error);
        graphClient = null;
    }
}
else {
    if (isDevelopment) {
        logger_1.logger.warn('GraphService: Entra ID credentials not configured - running in development mode without Graph API integration');
    }
    else {
        logger_1.logger.error('GraphService: Missing Entra ID configuration (tenantId, clientId, clientSecret) in production environment');
    }
}
class GraphService {
    checkGraphAvailability() {
        if (!graphClient) {
            if (isDevelopment) {
                logger_1.logger.warn('GraphService: Graph API not available in development mode - Entra ID credentials not configured');
            }
            else {
                logger_1.logger.error('GraphService: Graph API not available - Entra ID credentials not configured');
            }
            return false;
        }
        return true;
    }
    // Search for users in Entra ID
    async searchUsers(searchQuery, limit = 10) {
        if (!this.checkGraphAvailability()) {
            if (isDevelopment) {
                logger_1.logger.info('GraphService.searchUsers: Returning empty results - Graph API not configured in development');
                return [];
            }
            else {
                throw new Error('Microsoft Graph API is not configured');
            }
        }
        if (!searchQuery || searchQuery.length < 2) {
            logger_1.logger.warn('GraphService.searchUsers: Search query must be at least 2 characters.');
            // Return empty array or throw error based on desired behavior
            return [];
        }
        logger_1.logger.info(`GraphService: Searching users with query: "${searchQuery}", limit: ${limit}`);
        try {
            // Use filter and select as defined in the guide
            // Filter users based on display name, given name, surname, or email
            // NOTE: Filtering on mail might require advanced query capabilities and permissions.
            // Testing shows startsWith often works well.
            const filter = `startswith(displayName,'${searchQuery}') or startswith(givenName,'${searchQuery}') or startswith(surname,'${searchQuery}') or startswith(userPrincipalName,'${searchQuery}') or startswith(mail,'${searchQuery}')`;
            // Select only needed fields (as defined in EntraUserData interface in userManagementService)
            const select = 'id,userPrincipalName,mail,givenName,surname,companyName,department';
            const response = await graphClient.api('/users')
                .filter(filter)
                .select(select)
                .top(limit)
                // Header required for advanced queries / filter consistency
                // Check if needed for startsWith filters - often required
                .header('ConsistencyLevel', 'eventual')
                .count(true) // Request count for potential future use
                .get();
            // The response structure is { @odata.context: ..., @odata.count: ..., value: [...] }
            logger_1.logger.info(`GraphService: Found ${response['@odata.count'] ?? response.value?.length ?? 0} potential matches for "${searchQuery}". Returning top ${response.value?.length ?? 0}.`);
            return response.value || []; // Return the array of user objects
        }
        catch (error) {
            logger_1.logger.error('GraphService: Error searching users in Graph API', {
                query: searchQuery,
                limit: limit,
                errorMessage: error.message,
                statusCode: error.statusCode, // Graph errors often have statusCode
                // error: error // Avoid logging potentially large error object unless needed
            });
            // Re-throw a more generic error or handle specific graph errors
            if (error.statusCode === 401 || error.statusCode === 403) {
                throw new Error('Authentication or permission error accessing Microsoft Graph API.');
            }
            else {
                throw new Error('Failed to search users in Microsoft Entra ID.');
            }
        }
    }
    // Get a single user by their Entra ID (Object ID)
    async getUserById(userId) {
        if (!this.checkGraphAvailability()) {
            if (isDevelopment) {
                logger_1.logger.info('GraphService.getUserById: Returning null - Graph API not configured in development');
                return null;
            }
            else {
                throw new Error('Microsoft Graph API is not configured');
            }
        }
        if (!userId) {
            logger_1.logger.warn('GraphService.getUserById: userId cannot be empty.');
            return null;
        }
        logger_1.logger.info(`GraphService: Getting user by ID: ${userId}`);
        try {
            // Select only needed fields
            const select = 'id,userPrincipalName,mail,givenName,surname,companyName,department';
            const user = await graphClient.api(`/users/${userId}`)
                .select(select)
                .get();
            logger_1.logger.info(`GraphService: Successfully retrieved user ${userId}`);
            return user; // Return the full user object from Graph
        }
        catch (error) {
            logger_1.logger.error(`GraphService: Error getting user with ID ${userId} from Graph API`, {
                userId: userId,
                errorMessage: error.message,
                statusCode: error.statusCode,
            });
            // Handle specific errors like 404 Not Found
            if (error.statusCode === 404) {
                logger_1.logger.warn(`GraphService: User with ID ${userId} not found.`);
                return null; // Return null if user not found
            }
            // Handle auth errors
            if (error.statusCode === 401 || error.statusCode === 403) {
                throw new Error('Authentication or permission error accessing Microsoft Graph API.');
            }
            else {
                throw new Error('Failed to get user details from Microsoft Entra ID.');
            }
        }
    }
    // Get user by UPN (User Principal Name / Email)
    async getUserByUpn(upn) {
        if (!this.checkGraphAvailability()) {
            if (isDevelopment) {
                logger_1.logger.info('GraphService.getUserByUpn: Returning null - Graph API not configured in development');
                return null;
            }
            else {
                throw new Error('Microsoft Graph API is not configured');
            }
        }
        if (!upn) {
            logger_1.logger.warn('GraphService.getUserByUpn: UPN cannot be empty.');
            return null;
        }
        logger_1.logger.info(`GraphService: Getting user by UPN: ${upn}`);
        try {
            // Select only needed fields including contact information
            const select = 'id,userPrincipalName,mail,givenName,surname,companyName,department,businessPhones,mobilePhone,displayName,jobTitle,officeLocation,employeeId';
            const user = await graphClient.api(`/users/${upn}`)
                .select(select)
                .get();
            logger_1.logger.info(`GraphService: Successfully retrieved user by UPN ${upn}`);
            return user;
        }
        catch (error) {
            logger_1.logger.error(`GraphService: Error getting user with UPN ${upn} from Graph API`, {
                upn: upn,
                errorMessage: error.message,
                statusCode: error.statusCode,
            });
            if (error.statusCode === 404) {
                logger_1.logger.warn(`GraphService: User with UPN ${upn} not found.`);
                return null;
            }
            if (error.statusCode === 401 || error.statusCode === 403) {
                throw new Error('Authentication or permission error accessing Microsoft Graph API.');
            }
            else {
                throw new Error('Failed to get user details from Microsoft Entra ID.');
            }
        }
    }
    // Sync user data from Entra ID
    async syncUserDataFromEntra(entraId) {
        if (!this.checkGraphAvailability()) {
            if (isDevelopment) {
                logger_1.logger.info('GraphService.syncUserDataFromEntra: Returning null - Graph API not configured in development');
                return null;
            }
            else {
                throw new Error('Microsoft Graph API is not configured');
            }
        }
        if (!entraId) {
            logger_1.logger.warn('GraphService.syncUserDataFromEntra: entraId cannot be empty.');
            return null;
        }
        logger_1.logger.info(`GraphService: Syncing user data from Entra ID: ${entraId}`);
        try {
            // Select comprehensive user fields for sync
            const select = 'id,userPrincipalName,mail,givenName,surname,companyName,department,businessPhones,mobilePhone,displayName,jobTitle,officeLocation,employeeId';
            const user = await graphClient.api(`/users/${entraId}`)
                .select(select)
                .get();
            logger_1.logger.info(`GraphService: Successfully synced user data for ${entraId}`);
            // Extract primary phone number (mobile preferred, then business)
            const primaryPhone = this.extractPrimaryPhoneNumber(user);
            return {
                ...user,
                primaryPhone
            };
        }
        catch (error) {
            logger_1.logger.error(`GraphService: Error syncing user data for ${entraId} from Graph API`, {
                entraId: entraId,
                errorMessage: error.message,
                statusCode: error.statusCode,
            });
            if (error.statusCode === 404) {
                logger_1.logger.warn(`GraphService: User with Entra ID ${entraId} not found.`);
                return null;
            }
            if (error.statusCode === 401 || error.statusCode === 403) {
                throw new Error('Authentication or permission error accessing Microsoft Graph API.');
            }
            else {
                throw new Error('Failed to sync user data from Microsoft Entra ID.');
            }
        }
    }
    // Extract primary phone number (mobile preferred, then business)
    extractPrimaryPhoneNumber(user) {
        if (user.mobilePhone) {
            return user.mobilePhone;
        }
        if (user.businessPhones && user.businessPhones.length > 0) {
            return user.businessPhones[0];
        }
        return null;
    }
}
exports.GraphService = GraphService;
// Export a singleton instance of the service
exports.graphService = new GraphService();
//# sourceMappingURL=graphService.js.map