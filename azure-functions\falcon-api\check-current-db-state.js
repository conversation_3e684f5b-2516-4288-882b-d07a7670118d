const sql = require('mssql');

async function checkDatabase() {
    const config = {
        server: 'fp-sql-falcon-dev-cin-001.database.windows.net',
        database: 'fp-sqldb-falcon-dev-cin-001',
        user: 'falconhub_admin',
        password: 'Str!k3ab0ltThor',
        options: {
            encrypt: true,
            trustServerCertificate: false
        }
    };

    try {
        const pool = await sql.connect(config);
        console.log('Connected to database successfully.');
        
        // Check current state of Avirata users
        const result = await pool.request().query(`
            SELECT 
                u.UserID,
                u.Email, 
                u.EntraID,
                u.TenantID,
                c.CompanyName
            FROM Users u
            JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE c.CompanyName = 'Avirata Defence Systems'
            ORDER BY u.Email
        `);
        
        console.log('\nAvirata Defence Systems users:');
        console.log('================================');
        result.recordset.forEach(user => {
            console.log(`Email: ${user.Email}`);
            console.log(`EntraID: ${user.EntraID}`);
            console.log(`TenantID: ${user.TenantID}`);
            console.log(`Company: ${user.CompanyName}`);
            console.log('---');
        });
        
        // Also check Vasuki specifically
        const vasukiResult = await pool.request()
            .input('email', sql.VarChar, '<EMAIL>')
            .query(`
                SELECT 
                    u.UserID,
                    u.Email, 
                    u.EntraID,
                    u.TenantID,
                    c.CompanyName
                FROM Users u
                JOIN Companies c ON u.CompanyID = c.CompanyID
                WHERE u.Email = @email
            `);
        
        console.log('\nVasuki user details:');
        console.log('====================');
        if (vasukiResult.recordset.length > 0) {
            const user = vasukiResult.recordset[0];
            console.log(`Email: ${user.Email}`);
            console.log(`EntraID: ${user.EntraID}`);
            console.log(`TenantID: ${user.TenantID}`);
            console.log(`Company: ${user.CompanyName}`);
        } else {
            console.log('Vasuki user not found');
        }
        
        // Check all users with placeholder EntraIDs
        console.log('\nAll users with placeholder EntraIDs:');
        console.log('=====================================');
        const placeholderUsers = await pool.request().query(`
            SELECT 
                u.UserID,
                u.Email, 
                u.EntraID,
                u.TenantID,
                c.CompanyName
            FROM Users u
            JOIN Companies c ON u.CompanyID = c.CompanyID
            WHERE u.EntraID LIKE '%placeholder%' 
               OR u.EntraID LIKE '%simulated%'
               OR u.EntraID LIKE '00000000-%'
            ORDER BY c.CompanyName, u.Email
        `);
        
        placeholderUsers.recordset.forEach(user => {
            console.log(`${user.CompanyName}: ${user.Email} - ${user.EntraID}`);
        });
        
        await pool.close();
    } catch (error) {
        console.error('Database error:', error.message);
        console.error('Stack:', error.stack);
    }
}

checkDatabase(); 