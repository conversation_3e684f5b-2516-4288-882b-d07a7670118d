"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const msal_react_1 = require("@azure/msal-react");
const feather_icons_react_1 = require("feather-icons-react");
// TODO: Add Header content from falcon-standalone.html
// TODO: Integrate search, notifications, user dropdown, MSAL login/logout
const Header = () => {
    var _a;
    const { instance, accounts } = (0, msal_react_1.useMsal)();
    const [notificationsOpen, setNotificationsOpen] = (0, react_1.useState)(false);
    const [userDropdownOpen, setUserDropdownOpen] = (0, react_1.useState)(false);
    const notificationsRef = (0, react_1.useRef)(null);
    const userDropdownRef = (0, react_1.useRef)(null);
    const userName = (_a = accounts[0]) === null || _a === void 0 ? void 0 : _a.name; // Prefer display name if available
    const userInitial = userName ? userName.charAt(0).toUpperCase() : '?';
    const handleLogout = () => {
        instance.logoutRedirect({ postLogoutRedirectUri: "/" });
    };
    // Close dropdowns when clicking outside
    (0, react_1.useEffect)(() => {
        const handleClickOutside = (event) => {
            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
                setNotificationsOpen(false);
            }
            if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
                setUserDropdownOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    return (<header className="bg-white shadow-sm sticky top-0 z-10">
      <div className="flex items-center justify-between p-4">
        {/* Search Bar */}
        <div className="flex-1 max-w-xl">
          <div className="relative">
            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
              <feather_icons_react_1.Search size={16} className="text-gray-400"/>
            </span>
            <input type="text" placeholder="Search Falcon Portal..." className="w-full py-2 pl-10 pr-4 border rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"/>
          </div>
        </div>
          
        {/* Right Side Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative" ref={notificationsRef}>
            <button onClick={() => { setNotificationsOpen(!notificationsOpen); setUserDropdownOpen(false); }} className="text-gray-600 hover:text-gray-800 relative" aria-label="Notifications">
              <feather_icons_react_1.Bell size={20}/>
              {/* TODO: Add dynamic notification indicator */}
              <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span> 
            </button>
              
            {notificationsOpen && (<div className="dropdown absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-20 border">
                <div className="px-4 py-2 border-b">
                  <div className="text-sm font-medium">Notifications</div>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {/* TODO: Populate with actual notifications */}
                  <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                    <div>Your travel request has been approved</div>
                    <div className="text-xs text-gray-500 mt-1">2 hours ago</div>
                  </div>
                  <div className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b text-sm">
                    <div>New policy document requires acknowledgment</div>
                    <div className="text-xs text-gray-500 mt-1">Yesterday</div>
                  </div>
                  {/* ... more notifications ... */}
                </div>
                <div className="px-4 py-2 border-t text-center">
                  <button className="text-blue-600 text-sm hover:text-blue-800">
                    View All Notifications
                  </button>
                </div>
              </div>)}
          </div>
            
          {/* User menu */}
          <div className="relative" ref={userDropdownRef}>
            <button onClick={() => { setUserDropdownOpen(!userDropdownOpen); setNotificationsOpen(false); }} className="flex items-center space-x-1 text-gray-600 hover:text-gray-800" aria-label="User Menu">
              <div className="bg-gray-200 rounded-full h-8 w-8 flex items-center justify-center text-sm font-semibold">
                {userInitial} 
              </div>
              <feather_icons_react_1.ChevronDown size={16}/>
            </button>
              
            {userDropdownOpen && (<div className="dropdown absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-20 border">
                <div className="px-4 py-2 border-b mb-1">
                  <div className="text-sm font-medium truncate">{userName || 'User'}</div>
                  {/* TODO: Get user role/title */}
                  <div className="text-xs text-gray-500 truncate">Role Placeholder</div> 
                </div>
                <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                  <feather_icons_react_1.User size={16} className="mr-2 text-gray-500"/>
                  <span>My Profile</span>
                </button>
                <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center">
                  <feather_icons_react_1.Settings size={16} className="mr-2 text-gray-500"/>
                  <span>Settings</span>
                </button>
                <div className="border-t my-1"></div>
                <button onClick={handleLogout} className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center">
                  <feather_icons_react_1.LogOut size={16} className="mr-2"/>
                  <span>Sign Out</span>
                </button>
              </div>)}
          </div>
        </div>
      </div>
    </header>);
};
exports.default = Header;
//# sourceMappingURL=Header.js.map