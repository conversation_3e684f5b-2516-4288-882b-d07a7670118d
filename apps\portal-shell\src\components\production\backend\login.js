const express = require("express")
const cors = require("cors")
const mysql = require("mysql2")
const bodyParser = require("body-parser")

const app = express()
const port = 3000

app.use(
  cors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true)

      // Check if origin matches localhost with any port
      if (origin.match(/^http:\/\/localhost:\d+$/)) {
        return callback(null, true)
      }

      callback(new Error("Not allowed by CORS"))
    },
    credentials: true,
  }),
)

app.use(express.json())
app.use(bodyParser.urlencoded({ extended: true }))

// Create connection pool instead of single connection
const pool = mysql.createPool({
  host: "**************",
  user: "mysql_root",
  password: "Sasmos@123$",
  database: "employees_db",
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  idleTimeout: 300000,
  maxIdle: 10
})

// Test the pool connection
let dbConnected = false;
pool.getConnection((err, connection) => {
  if (err) {
    console.error("Database connection failed:", err)
    console.log("⚠️  Continuing without database - some features may not work")
    dbConnected = false;
    return
  }
  dbConnected = true;
  console.log("Connected to database successfully")
  connection.release() // Always release the connection back to the pool
})

// Handle pool errors
pool.on('connection', function (connection) {
  console.log('New connection established as id ' + connection.threadId)
})

pool.on('error', function(err) {
  console.error('Database pool error:', err)
  if(err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('Connection lost, pool will reconnect automatically')
  } else {
    throw err
  }
})

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    database: dbConnected ? "connected" : "disconnected",
    timestamp: new Date().toISOString()
  });
});

// Updated login endpoint with dashboard counts
app.post("/api/employees_login", async (req, res) => {
  const { employee_id, password } = req.body

  // Check if database is connected
  if (!dbConnected) {
    console.log("⚠️  Database not connected - using mock login for development");
    // Return mock user data for development
    return res.json({
      success: true,
      user: {
        employee_id: employee_id,
        employee_name: "Development User",
        company_name: "SASMOS",
        department: "IT",
        designation: "Developer"
      },
      dashboardCounts: {
        expenses: { total: 0 },
        travel: { total: 0 },
        ecn: { total: 0 },
        shelf_life: { total: 0 },
        qa_tracker: { total: 0 },
        issue_control: { total: 0 },
        scrap_note: { total: 0 },
        material_positive_recall: { total: 0 }
      }
    });
  }

  const loginQuery = "SELECT * FROM employees_db.employee_table WHERE employee_id = ? AND user_password = ?"
  const loginParams = [employee_id, password]

  // Use pool.query instead of db.query
  pool.query(loginQuery, [employee_id, password], async (err, results) => {
    if (err) {
      console.error("Error executing login query:", err)
      return res.status(500).json({ error: "Internal server error" })
    }

    if (results.length > 0) {
      const employeeData = results[0]
      console.log("Login successful:", employeeData)

      try {
        const dashboardCounts = await getEmployeeDashboardCounts(employee_id)
        console.log("Dashboard counts for employee:", dashboardCounts)
        return res.json({
          ...employeeData,
          dashboardCounts: dashboardCounts,
        })
      } catch (countError) {
        console.error("Error getting dashboard counts:", countError)
        return res.json({
          ...employeeData,
          dashboardCounts: {
            expenses: { total: 0, roles: [] },
            travel: { total: 0, roles: [] },
            ecn: { total: 0, roles: [] },
            shelf_life: { total: 0, roles: [] },
            qa_tracker: { total: 0, roles: [] },
            issue_control: { total: 0, roles: [] },
            scrap_note: { total: 0, roles: [] },
            material_positive_recall: { total: 0, roles: [] },
            error: "Could not load dashboard counts",
          },
        })
      }
    } else {
      console.log("Login failed: Invalid credentials")
      return res.status(401).json({ error: "Invalid employee ID or password" })
    }
  })
})

// Function to get dashboard counts for an employee
async function getEmployeeDashboardCounts(employee_id) {
  return new Promise((resolve, reject) => {
    const queries = [
      {
        name: "expenses",
        query: `SELECT
                  authorised_by_id,
                  second_level_approve_id,
                  create_userid,
                  second_level_status,
                  reviewers_status,
                  finance_status
                FROM localexpenses_db.expenses_raised_data_table
                WHERE (authorised_by_id = ? OR second_level_approve_id = ? OR create_userid = ?)
                  AND (second_level_status != '5' OR reviewers_status != '5' OR finance_status != '1')`,
        columns: ["authorised_by_id", "second_level_approve_id", "create_userid"],
        params: [employee_id, employee_id, employee_id],
      },
      {
        name: "travel",
        query: `SELECT employee_id, request_status
                FROM finance_db.emp_travel_table
                WHERE employee_id = ? AND request_status = '20'`,
        columns: ["employee_id"],
        params: [employee_id],
      },
      {
        name: "ecn",
        query: `SELECT
                  ecn_reviewer_id,
                  ecn_approver_id,
                  ecn_reviewer_status,
                  ecn_approver_status
                FROM ecn_db.ecn_data_table
                WHERE (ecn_reviewer_id = ? OR ecn_approver_id = ?)
                  AND (ecn_reviewer_status != '1' OR ecn_approver_status != '1')`,
        columns: ["ecn_reviewer_id", "ecn_approver_id"],
        params: [employee_id, employee_id],
      },
      {
        name: "shelf_life",
        query: `SELECT
                  buyer_userid,
                  quality_userid,
                  create_userid,
                  planner_userid,
                  buhead_userid,
                  planner_status,
                  buyer_status,
                  quality_status,
                  buhead_status
                FROM sqa_db.shelf_life_clearence_table
                WHERE (buyer_userid = ? OR quality_userid = ? OR create_userid = ? OR planner_userid = ? OR buhead_userid = ?)
                  AND (planner_status != '1' OR buyer_status != '0' OR quality_status != '1' OR buhead_status != '1')`,
        columns: ["buyer_userid", "quality_userid", "create_userid", "planner_userid", "buhead_userid"],
        params: [employee_id, employee_id, employee_id, employee_id, employee_id],
      },
      {
        name: "qa_tracker",
        query: `SELECT qa_approver_id, qa_status
                FROM partnumber_db.qa_tracker_table
                WHERE qa_approver_id = ? AND qa_approver_status != '1'`,
        columns: ["qa_approver_id"],
        params: [employee_id],
      },
      {
        name: "issue_control",
        query: `SELECT
                  process_userid,
                  quality_userid,
                  production_userid,
                  process_status,
                  quality_status,
                  production_status
                FROM partnumber_db.issue_control_table
                WHERE (process_userid = ? OR quality_userid = ? OR production_userid = ?)
                  AND (process_status != '1' OR quality_status != '1' OR production_status != '1')`,
        columns: ["process_userid", "quality_userid", "production_userid"],
        params: [employee_id, employee_id, employee_id],
      },
      {
        name: "scrap_note",
        query: `SELECT
                  production_initiator_userid,
                  production_approver_userid,
                  process_userid,
                  planning_userid,
                  quality_userid,
                  operational_head_userid,
                  purchase_userid,
                  stores_userid,
                  production_initiator_status,
                  production_approver_status,
                  process_approval_status,
                  planning_status,
                  quality_status,
                  operational_head_status,
                  purchase_approval_status,
                  stores_status
                FROM stores_db.scrap_note_approval_table
                WHERE (production_initiator_userid = ? OR production_approver_userid = ? OR process_userid = ? OR planning_userid = ? OR quality_userid = ? OR operational_head_userid = ? OR purchase_userid = ? OR stores_userid = ?)
                  AND (production_initiator_status != '1' OR production_approver_status != '1' OR process_approval_status != '1' OR planning_status != '1' OR quality_status != '1' OR operational_head_status != '1' OR purchase_approval_status != '1' OR stores_status != '1')`,
        columns: [
          "production_initiator_userid",
          "production_approver_userid",
          "process_userid",
          "planning_userid",
          "quality_userid",
          "operational_head_userid",
          "purchase_userid",
          "stores_userid",
        ],
        params: [
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
        ],
      },
      {
        name: "material_positive_recall",
        query: `SELECT
                  scm_requestor_userid,
                  scm_approver_userid,
                  qa_approver_userid,
                  iqa_approver_userid,
                  store_approver_userid,
                  line_qa_approver_userid,
                  scm_requestor_status,
                  scm_approver_status,
                  qa_approver_status,
                  iqa_approver_status,
                  store_approver_status,
                  line_qa_approver_status
                FROM stores_db.material_positive_recall_table
                WHERE (scm_requestor_userid = ? OR scm_approver_userid = ? OR qa_approver_userid = ? OR iqa_approver_userid = ? OR store_approver_userid = ? OR line_qa_approver_userid = ?)
                  AND (scm_requestor_status != '1' OR scm_approver_status != '1' OR qa_approver_status != '1' OR iqa_approver_status != '1' OR store_approver_status != '1' OR line_qa_approver_status != '1')`,
        columns: [
          "scm_requestor_userid",
          "scm_approver_userid",
          "qa_approver_userid",
          "iqa_approver_userid",
          "store_approver_userid",
          "line_qa_approver_userid",
        ],
        params: [employee_id, employee_id, employee_id, employee_id, employee_id, employee_id],
      },
    ]

    let completedQueries = 0
    const results = {}
    let hasError = false

    queries.forEach((queryObj) => {
      // Use pool.query instead of db.query
      pool.query(queryObj.query, queryObj.params, (err, queryResults) => {
        if (err) {
          console.error(`Error executing ${queryObj.name} query:`, err)
          hasError = true
          results[queryObj.name] = { total: 0, roles: [], error: err.message }
        } else {
          console.log(`${queryObj.name} raw results length:`, queryResults.length)
          const roleCounts = {}
          let totalCount = 0

          queryObj.columns.forEach((column) => {
            const count = queryResults.filter(
              (row) => row[column] && row[column].toString().toLowerCase() === employee_id.toString().toLowerCase(),
            ).length
            if (count > 0) {
              roleCounts[column] = count
              totalCount += count
            }
          })

          results[queryObj.name] = {
            total: totalCount,
            roles: Object.keys(roleCounts).map((role) => ({
              role: role,
              count: roleCounts[role],
            })),
          }

          console.log(`${queryObj.name} counts for employee ${employee_id}:`, results[queryObj.name])
        }

        completedQueries++

        if (completedQueries === queries.length) {
          if (hasError) {
            reject(new Error("Some queries failed"))
          } else {
            resolve(results)
          }
        }
      })
    })
  })
}

//dashboard counts
app.get("/api/employee_dashboard_counts/:employee_id", async (req, res) => {
  const { employee_id } = req.params

  try {
    const dashboardCounts = await getEmployeeDashboardCounts(employee_id)
    res.json({
      employee_id: employee_id,
      counts: dashboardCounts,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Error getting dashboard counts:", error)
    res.status(500).json({
      error: "Failed to get dashboard counts",
      employee_id: employee_id,
    })
  }
})

//detailed breakdown
async function getDetailedEmployeeCounts(employee_id) {
  return new Promise((resolve, reject) => {
    const detailedQueries = [
      {
        name: "expenses_summary",
        query: `
          SELECT
            SUM(CASE WHEN authorised_by_id = ? THEN 1 ELSE 0 END) as authorised_count,
            SUM(CASE WHEN second_level_approve_id = ? THEN 1 ELSE 0 END) as second_level_count,
            SUM(CASE WHEN create_userid = ? THEN 1 ELSE 0 END) as created_count,
            COUNT(*) as total_records
          FROM localexpenses_db.expenses_raised_data_table
          WHERE (authorised_by_id = ? OR second_level_approve_id = ? OR create_userid = ?)
            AND (second_level_status != '5' OR reviewers_status != '5' OR finance_status != '1')
        `,
        params: [employee_id, employee_id, employee_id, employee_id, employee_id, employee_id],
      },
      {
        name: "travel_summary",
        query: `
          SELECT
            COUNT(*) as total_travel_records,
            SUM(CASE WHEN employee_id = ? THEN 1 ELSE 0 END) as employee_travel_count
          FROM finance_db.emp_travel_table
          WHERE employee_id = ? AND request_status = '20'
        `,
        params: [employee_id, employee_id],
      },
      {
        name: "ecn_summary",
        query: `
          SELECT
            SUM(CASE WHEN ecn_reviewer_id = ? THEN 1 ELSE 0 END) as reviewer_count,
            SUM(CASE WHEN ecn_approver_id = ? THEN 1 ELSE 0 END) as approver_count,
            COUNT(*) as total_ecn_records
          FROM ecn_db.ecn_data_table
          WHERE (ecn_reviewer_id = ? OR ecn_approver_id = ?)
            AND (ecn_reviewer_status != '1' OR ecn_approver_status != '1')
        `,
        params: [employee_id, employee_id, employee_id, employee_id],
      },
      {
        name: "shelf_life_summary",
        query: `
          SELECT
            SUM(CASE WHEN buyer_userid = ? THEN 1 ELSE 0 END) as buyer_count,
            SUM(CASE WHEN quality_userid = ? THEN 1 ELSE 0 END) as quality_count,
            SUM(CASE WHEN create_userid = ? THEN 1 ELSE 0 END) as creator_count,
            SUM(CASE WHEN planner_userid = ? THEN 1 ELSE 0 END) as planner_count,
            SUM(CASE WHEN buhead_userid = ? THEN 1 ELSE 0 END) as buhead_count,
            COUNT(*) as total_shelf_life_records
          FROM sqa_db.shelf_life_clearence_table
          WHERE (buyer_userid = ? OR quality_userid = ? OR create_userid = ? OR planner_userid = ? OR buhead_userid = ?)
            AND (planner_status != '1' OR buyer_status != '0' OR quality_status != '1' OR buhead_status != '1')
        `,
        params: [
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
        ],
      },
      {
        name: "qa_tracker_summary",
        query: `
          SELECT
            SUM(CASE WHEN qa_approver_id = ? THEN 1 ELSE 0 END) as qa_approver_count,
            COUNT(*) as total_qa_tracker_records
          FROM partnumber_db.qa_tracker_table
          WHERE qa_approver_id = ? AND qa_status != '1'
        `,
        params: [employee_id, employee_id],
      },
      {
        name: "issue_control_summary",
        query: `
          SELECT
            SUM(CASE WHEN process_userid = ? THEN 1 ELSE 0 END) as process_count,
            SUM(CASE WHEN quality_userid = ? THEN 1 ELSE 0 END) as quality_count,
            SUM(CASE WHEN production_userid = ? THEN 1 ELSE 0 END) as production_count,
            COUNT(*) as total_issue_control_records
          FROM partnumber_db.issue_control_table
          WHERE (process_userid = ? OR quality_userid = ? OR production_userid = ?)
            AND (process_status != '1' OR quality_status != '1' OR production_status != '1')
        `,
        params: [employee_id, employee_id, employee_id, employee_id, employee_id, employee_id],
      },
      {
        name: "scrap_note_summary",
        query: `
          SELECT
            SUM(CASE WHEN production_initiator_userid = ? THEN 1 ELSE 0 END) as production_initiator_count,
            SUM(CASE WHEN production_approver_userid = ? THEN 1 ELSE 0 END) as production_approver_count,
            SUM(CASE WHEN process_userid = ? THEN 1 ELSE 0 END) as process_count,
            SUM(CASE WHEN planning_userid = ? THEN 1 ELSE 0 END) as planning_count,
            SUM(CASE WHEN quality_userid = ? THEN 1 ELSE 0 END) as quality_count,
            SUM(CASE WHEN operational_head_userid = ? THEN 1 ELSE 0 END) as operational_head_count,
            SUM(CASE WHEN purchase_userid = ? THEN 1 ELSE 0 END) as purchase_count,
            SUM(CASE WHEN stores_userid = ? THEN 1 ELSE 0 END) as stores_count,
            COUNT(*) as total_scrap_note_records
          FROM stores_db.scrap_note_approval_table
          WHERE (production_initiator_userid = ? OR production_approver_userid = ? OR process_userid = ? OR planning_userid = ? OR quality_userid = ? OR operational_head_userid = ? OR purchase_userid = ? OR stores_userid = ?)
            AND (production_initiator_status != '1' OR production_approver_status != '1' OR process_approval_status != '1' OR planning_status != '1' OR quality_status != '1' OR operational_head_status != '1' OR purchase_approval_status != '1' OR stores_status != '1')
        `,
        params: [
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
        ],
      },
      {
        name: "material_positive_recall_summary",
        query: `
          SELECT
            SUM(CASE WHEN scm_requestor_userid = ? THEN 1 ELSE 0 END) as scm_requestor_count,
            SUM(CASE WHEN scm_approver_userid = ? THEN 1 ELSE 0 END) as scm_approver_count,
            SUM(CASE WHEN qa_approver_userid = ? THEN 1 ELSE 0 END) as qa_approver_count,
            SUM(CASE WHEN iqa_approver_userid = ? THEN 1 ELSE 0 END) as iqa_approver_count,
            SUM(CASE WHEN store_approver_userid = ? THEN 1 ELSE 0 END) as store_approver_count,
            SUM(CASE WHEN line_qa_approver_userid = ? THEN 1 ELSE 0 END) as line_qa_approver_count,
            COUNT(*) as total_material_recall_records
          FROM stores_db.material_positive_recall_table
          WHERE (scm_requestor_userid = ? OR scm_approver_userid = ? OR qa_approver_userid = ? OR iqa_approver_userid = ? OR store_approver_userid = ? OR line_qa_approver_userid = ?)
            AND (scm_requestor_status != '' OR scm_approver_status != '' OR qa_approver_status != '' OR iqa_approver_status != '' OR store_approver_status != '' OR line_qa_approver_status != '')
        `,
        params: [
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
          employee_id,
        ],
      },
    ]

    let completedQueries = 0
    const results = {}

    detailedQueries.forEach((queryObj) => {
      // Use pool.query instead of db.query
      pool.query(queryObj.query, queryObj.params, (err, queryResults) => {
        if (err) {
          console.error(`Error executing ${queryObj.name}:`, err)
          results[queryObj.name] = { error: err.message }
        } else {
          results[queryObj.name] = queryResults[0] || {}
        }

        completedQueries++

        if (completedQueries === detailedQueries.length) {
          resolve(results)
        }
      })
    })
  })
}

// Endpoint for detailed counts
app.get("/api/employee_detailed_counts/:employee_id", async (req, res) => {
  const { employee_id } = req.params

  try {
    const detailedCounts = await getDetailedEmployeeCounts(employee_id)
    res.json({
      employee_id: employee_id,
      detailed_counts: detailedCounts,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Error getting detailed counts:", error)
    res.status(500).json({
      error: "Failed to get detailed counts",
      employee_id: employee_id,
    })
  }
})

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Received SIGINT, closing database pool...')
  pool.end(() => {
    console.log('Database pool closed.')
    process.exit(0)
  })
})

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, closing database pool...')
  pool.end(() => {
    console.log('Database pool closed.')
    process.exit(0)
  })
})

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`)
})