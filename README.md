# FalconHub Portal

A comprehensive enterprise portal solution for SASMOS Group companies, built with React, Azure Functions, and Azure SQL Database.

## 🚀 Current Version: v0.1.0

### ✅ Completed Features
- **User Management System**: Complete CRUD operations with real-time search and filtering
- **Authentication & Authorization**: Microsoft Entra ID integration with role-based access control
- **Multi-Company Support**: Proper data filtering for Avirata Defence Systems and SASMOS HET
- **Search & Filtering**: Advanced search across user fields with company, role, and status filters
- **Responsive UI**: Modern, mobile-friendly interface with Tailwind CSS
- **Real-time Data**: Direct integration with Azure SQL Database (no mock data)

## 🏗️ Architecture

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Authentication**: MSAL (Microsoft Authentication Library)
- **State Management**: React Context API

### Backend
- **Runtime**: Azure Functions (Node.js)
- **Database**: Azure SQL Database
- **Authentication**: Azure Entra ID
- **API Validation**: Zod schemas

### Infrastructure
- **Cloud Provider**: Microsoft Azure
- **Resource Group**: FalconHub
- **Region**: Central India
- **Naming Convention**: `fp-[service]-[component]-[env]-[region]-[instance]`

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- pnpm 9+
- Azure CLI
- Azure Functions Core Tools

### Installation
```bash
# Clone the repository
git clone https://github.com/Avirata-CP/FalconHub.git
cd FalconHub

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

### Environment Configuration
Create `apps/portal-shell/.env.local`:
```env
VITE_MSAL_CLIENT_ID=your-client-id
VITE_MSAL_AUTHORITY=https://login.microsoftonline.com/your-tenant-id
```

Create `azure-functions/falcon-api/local.settings.json`:
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "",
    "FUNCTIONS_WORKER_RUNTIME": "node",
    "DB_SERVER": "your-sql-server.database.windows.net",
    "DB_DATABASE": "your-database-name",
    "DB_USERNAME": "your-username",
    "DB_PASSWORD": "your-password"
  }
}
```

## 📦 Version Management

This project uses semantic versioning with automatic version management.

### Version Bumping
```bash
# Patch version (0.1.0 → 0.1.1)
pnpm version:patch

# Minor version (0.1.0 → 0.2.0)
pnpm version:minor

# Major version (0.1.0 → 1.0.0)
pnpm version:major
```

### Commit Process
1. Make your changes
2. Bump version: `pnpm version:patch`
3. Stage changes: `git add .`
4. Commit: `git commit -m "v0.1.1: Your feature description"`
5. Push: `git push origin main`

The version number in the sidebar will automatically update from `package.json`.

## 🏢 Multi-Company Structure

### Supported Companies
- **Avirata Defence Systems**: Primary defense contractor
- **SASMOS HET**: Satellite and space technology division

### Data Isolation
- All database queries include company filtering
- User access is restricted to their company's data
- Role-based permissions are company-specific

## 🔐 Authentication & Authorization

### Roles
- **Administrator**: Full system access, user management
- **Employee**: Standard user access to assigned hubs

### Protected Routes
- `/portal-admin/*`: Administrator only
- `/admin-hub/*`: Administrator only
- All other routes: Authenticated users

## 📊 Database Schema

### Core Tables
- `Users`: User profiles and authentication data
- `Companies`: Company information and settings
- `Roles`: Role definitions and permissions
- `UserRoles`: User-role assignments with audit trail
- `Departments`: Organizational structure

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Run frontend tests
pnpm --filter portal-shell test

# Run backend tests
pnpm --filter falcon-api test
```

## 🚀 Deployment

### Azure Resources
- **App Service**: Frontend hosting
- **Function App**: Backend API
- **SQL Database**: Data storage
- **Storage Account**: Static assets and logs

### CI/CD Pipeline
Automated deployment via Azure DevOps (configuration in progress).

## 📁 Project Structure

```
FalconHub/
├── apps/
│   └── portal-shell/          # React frontend application
├── azure-functions/
│   └── falcon-api/            # Azure Functions backend
├── Documentation/
│   ├── progress_tracking/     # Development progress and status
│   ├── Database/             # Database schemas and scripts
│   └── Technical Document/   # Architecture and design docs
├── scripts/                  # Build and deployment scripts
└── package.json             # Root package configuration
```

## 🔄 Development Workflow

### Feature Development
1. Create feature branch: `git checkout -b feature/your-feature`
2. Develop and test locally
3. Update documentation in `Documentation/progress_tracking/`
4. Bump version and commit
5. Create pull request to main

### Release Process
1. Ensure all tests pass
2. Update version with appropriate bump (patch/minor/major)
3. Update `Documentation/progress_tracking/status.md`
4. Commit and push to main
5. Deploy to staging for testing
6. Deploy to production

## 📚 Documentation

- **Progress Tracking**: `Documentation/progress_tracking/`
- **API Documentation**: `Documentation/API/`
- **Database Schema**: `Documentation/Database/`
- **User Guides**: `Documentation/User Management/`

## 🤝 Contributing

1. Follow the established coding standards
2. Write tests for new features
3. Update documentation
4. Use semantic commit messages
5. Ensure all linter checks pass

## 📞 Support

For technical support or questions:
- **Development Team**: Avirata Defence Systems
- **Repository**: https://github.com/Avirata-CP/FalconHub
- **Issues**: Use GitHub Issues for bug reports and feature requests

## 📄 License

This project is proprietary software owned by Avirata Defence Systems. All rights reserved.

---

**Last Updated**: January 29, 2025  
**Current Version**: v0.1.0  
**Next Milestone**: User CRUD operations and role management system