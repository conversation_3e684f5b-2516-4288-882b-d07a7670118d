const fs = require('fs');

// Create test data SQL script
const sqlScript = `
-- Create sample change requests for Company ID 4 (Avirata Defence Systems) with June deployment dates
INSERT INTO ChangeRequests (
    Title, Description, Status, Priority, CompanyID, RequesterID,
    DeploymentDate, RequestedCompletionDate, CreatedDate, ModifiedDate,
    DeploymentDuration, DeploymentLocation, DeploymentType, 
    RequiresSystemDowntime, CalendarColor
) VALUES 
(
    'Database Server Upgrade - Approved for June',
    'Upgrade production database server to latest version - approved for June deployment',
    'Approved', 'High', 4, 1,
    '2024-06-15', '2024-06-30', GETDATE(), GETDATE(),
    120, 'Production', 'Automated', 0, '#FF8800'
),
(
    'Security Patch Deployment',
    'Deploy critical security patches to all servers',
    'In Development', 'Critical', 4, 1,
    '2024-06-20', '2024-06-25', GETDATE(), GETDATE(),
    120, 'Production', 'Automated', 1, '#FF4444'
),
(
    'Network Infrastructure Update - Completed',
    'Update network switches and routers - completed in June',
    'Completed', 'Medium', 4, 1,
    '2024-06-10', '2024-06-15', GETDATE(), GETDATE(),
    120, 'Production', 'Automated', 0, '#4488FF'
),
(
    'Application Performance Optimization',
    'Optimize application performance and database queries',
    'Approved', 'Medium', 4, 1,
    '2024-06-25', '2024-07-05', GETDATE(), GETDATE(),
    120, 'Production', 'Automated', 0, '#4488FF'
),
(
    'Backup System Enhancement',
    'Enhance backup system with new retention policies',
    'Approved', 'Low', 4, 1,
    '2024-06-28', '2024-07-10', GETDATE(), GETDATE(),
    120, 'Production', 'Automated', 0, '#44AA44'
);

-- Verify the data was inserted
SELECT 
    RequestID, Title, Status, Priority, CompanyID, 
    DeploymentDate, DeploymentLocation, CalendarColor
FROM ChangeRequests 
WHERE CompanyID = 4
ORDER BY DeploymentDate;
`;

// Write the SQL script to a file
fs.writeFileSync('sample-data.sql', sqlScript);
console.log('Created sample-data.sql with test change requests for Company ID 4');
console.log('You can run this script against your database to add the test data.'); 