import { useState, useEffect } from 'react';

function ViewList() {
  const [nonCalibratedMfgDetails, setNonCalibratedMfgDetails] = useState([]);

  useEffect(() => {
    fetch('http://localhost:3000/api/non_calibrated_tool_table')
      .then(response => response.json())
      .then(data => setNonCalibratedMfgDetails(data))
      .catch(error => console.error('Error fetching data:', error));
  }, []);

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">📋 View Report</h1>

      <div className="overflow-x-auto rounded-lg shadow">
        <table className="min-w-full bg-white text-sm text-left text-gray-700">
          <thead className="bg-blue-600 text-white">
            <tr>
              <th className="px-4 py-3">SL #</th>
              <th className="px-4 py-3">Tool Category</th>
              <th className="px-4 py-3">Mfg. Part Number</th>
              <th className="px-4 py-3">SubTool Part Number</th>
              <th className="px-4 py-3">BU Name</th>
              <th className="px-4 py-3">IPT Name</th>
              <th className="px-4 py-3">Type</th>
              <th className="px-4 py-3">Qty</th>
              <th className="px-4 py-3">Created By</th>
              <th className="px-4 py-3">Create Date</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {nonCalibratedMfgDetails.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50 transition">
                <td className="px-4 py-2">{index + 1}</td>
                <td className="px-4 py-2">{item.tool_category}</td>
                <td className="px-4 py-2">{item.manufacture_no}</td>
                <td className="px-4 py-2">{item.subtool_no}</td>
                <td className="px-4 py-2">{item.bu_name}</td>
                <td className="px-4 py-2">{item.ipt_name}</td>
                <td className="px-4 py-2">{item.create_type}</td>
                <td className="px-4 py-2">{item.quantity}</td>
                <td className="px-4 py-2">{item.user_name}</td>
                <td className="px-4 py-2">{item.create_date}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default ViewList;
