import { useState, useEffect, useRef } from 'react';
import { Search, X, Users, Calendar, MapPin, Building2 } from 'lucide-react';

function VisitorsListForm() {
    const [visitorlist, setVisitorDetails] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [visitorlogstatus, setLogStatus] = useState({});
    const [showPopup, setShowPopup] = useState(false);
    const [selectedVisitor, setSelectedVisitor] = useState(null);
    const [materialCarried, setMaterialCarried] = useState('');
    const [idProof, setIdProof] = useState('');
    const [companyVisit, setCompanyVisit] = useState('');
    const [companyAreaVisit, setCompanyAreaVisit] = useState('');
    const [capturedImage, setCapturedImage] = useState(null);
    const [idProofFile, setIdProofFile] = useState(null);

    const videoRef = useRef(null);
    const canvasRef = useRef(null);

    // Fetch visitor list
    useEffect(() => {
        fetch(`http://localhost:3000/api/visitor_list_form`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                setVisitorDetails(data.data || []);
            })
            .catch(err => {
                console.log("Error fetching visitor data:", err);
            });
    }, []);

    // Fetch visitor log status for each visitor
    useEffect(() => {
        if (visitorlist.length > 0) {
            const fetchLogStatus = async () => {
                const statusPromises = visitorlist.map(visitor =>
                    fetch(`http://localhost:3000/api/visitor_log_table?visitor_ref_num=${visitor.visitor_ref_num}`)
                        .then(response => response.json())
                        .then(data => ({
                            visitor_ref_num: visitor.visitor_ref_num,
                            log: data.data[0] || {}
                        }))
                        .catch(err => {
                            console.log(`Error fetching log for ${visitor.visitor_ref_num}:`, err);
                            return { visitor_ref_num: visitor.visitor_ref_num, log: {} };
                        })
                );
                const statuses = await Promise.all(statusPromises);
                const statusMap = statuses.reduce((acc, { visitor_ref_num, log }) => ({
                    ...acc,
                    [visitor_ref_num]: log
                }), {});
                setLogStatus(statusMap);
            };
            fetchLogStatus();
        }
    }, [visitorlist]);

    // Initialize webcam when popup opens
    useEffect(() => {
        if (showPopup) {
            const video = videoRef.current;
            navigator.mediaDevices
                .getUserMedia({ video: true })
                .then(stream => {
                    video.srcObject = stream;
                    video.play();
                })
                .catch(err => {
                    console.error("Error accessing webcam:", err);
                    alert("ERROR WITH CAMERA");
                });

            return () => {
                const stream = video.srcObject;
                if (stream) {
                    const tracks = stream.getTracks();
                    tracks.forEach((track: MediaStreamTrack) => track.stop());
                }
            };
        }
    }, [showPopup]);

    const filteredItems = visitorlist.filter((item) =>
        item.visitor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.visitor_org.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.visitor_ref_num.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.visitor_invitee.toLowerCase().includes(searchTerm.toLowerCase())
    );
const clearSearch = () => {
    setSearchTerm('');
};

const getSubmitValue = (visitor_ref_num: string) => {
    const log = visitorlogstatus[visitor_ref_num] || {};
    const check_in_out = log.check_in_out;

    console.log(`Getting submit value for ${visitor_ref_num}:`, { log, check_in_out });

    if (check_in_out === "CHECKIN") {
        return `
            <a title="CHECKOUT" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:orange; cursor:pointer; text-decoration:underline">CHECKOUT</a><br />
            <hr />
            <a title="REPRINT" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:blue; cursor:pointer; text-decoration:underline">REPRINT</a>
        `;
    } else if (check_in_out === "CHECKOUT") {
        return `
            <a title="CHECKIN" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:green; cursor:pointer; text-decoration:underline">CHECKIN</a><br />
            <hr />
            <a title="REPRINT" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:blue; cursor:pointer; text-decoration:underline">REPRINT</a>
        `;
    } else if (check_in_out === "CHECKIN" && !log.log_date) {
        return `
            <a title="LOGOUT ERROR" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:red; cursor:pointer; text-decoration:underline">LOGOUT ERROR</a>
        `;
    }

    // Default case - no log entry exists
    return `
        <a title="CHECKIN" visitor_ref_num="${visitor_ref_num}" class="visit_ref_a" style="color:green; cursor:pointer; text-decoration:underline">CHECKIN</a>
    `;
};

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            clearSearch();
        }
    };

    const handleLinkClick = (e: React.MouseEvent, visitor: any) => {
        const title = (e.target as HTMLElement).getAttribute('title');
        if (title === "CHECKIN") {
            e.preventDefault();
            setSelectedVisitor(visitor);
            setShowPopup(true);
        } else if (title === "CHECKOUT") {
            e.preventDefault();
            handleCheckout(visitor);
        } else if (title === "REPRINT") {
            e.preventDefault();
            handleReprint(visitor);
        }
    };

    const captureImage = () => {
        const video = videoRef.current;
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        const imageData = canvas.toDataURL('image/png');
        setCapturedImage(imageData);

        // Send image to server
        const formData = new FormData();
        formData.append('canvasData', imageData);
        fetch(`http://localhost:3000/api/camera_pic_ajax?a=${selectedVisitor.visitor_name}&visitor_ref_num=${selectedVisitor.visitor_ref_num}`, {
            method: 'POST',
            body: formData,
        })
            .then(response => response.json())
            .then(data => {
                console.log("Image saved as:", data.file_path);
            })
            .catch(err => console.error("Error saving image:", err));
    };

    const refreshLogStatus = async (visitor_ref_num: string) => {
    try {
        console.log('Refreshing log status for:', visitor_ref_num);

        const response = await fetch(`http://localhost:3000/api/visitor_log_table?visitor_ref_num=${visitor_ref_num}`);
        if (!response.ok) throw new Error('Failed to fetch log status');

        const data = await response.json();
        console.log('Fetched log data:', data);

        // Update the state with the latest log information
        setLogStatus(prev => {
            const updated = {
                ...prev,
                [visitor_ref_num]: data.data && data.data.length > 0 ? data.data[0] : {}
            };
            console.log('Updated log status:', updated[visitor_ref_num]);
            return updated;
        });

    } catch (err) {
        console.error(`Error refreshing log for ${visitor_ref_num}:`, err);
    }
};

   const handleCheckout = async (visitor: any) => {
    const commentDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

    try {
        console.log('Starting checkout process for:', visitor.visitor_ref_num);

        // First, log the checkout
        const logResponse = await fetch(`http://localhost:3000/api/visitor_log`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                visitor_ref_num: visitor.visitor_ref_num,
                visitor_name: visitor.visitor_name,
                log_date: commentDate,
                log_time: commentDate,
                check_in_out: 'CHECKOUT',
                company_visit: visitorlogstatus[visitor.visitor_ref_num]?.company_visit || '',
                company_area_visit: visitorlogstatus[visitor.visitor_ref_num]?.company_area_visit || '',
                login_userid: 'USER123',
                login_username: 'Admin',
                create_date: commentDate,
            }),
        });

        if (!logResponse.ok) {
            const errorText = await logResponse.text();
            throw new Error(`Failed to log checkout: ${errorText}`);
        }

        const logData = await logResponse.json();
        console.log('Checkout logged successfully:', logData);

        // Then update the visitor entry status
        const entryResponse = await fetch(`http://localhost:3000/api/visitor_entry`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                visitor_ref_num: visitor.visitor_ref_num,
                visit_status: '3', // 3 = checked out
                pass_number: visitor.pass_number,
                visit_login_time: visitor.visit_login_time,
            }),
        });

        if (!entryResponse.ok) {
            const errorText = await entryResponse.text();
            throw new Error(`Failed to update visitor entry: ${errorText}`);
        }

        const entryData = await entryResponse.json();
        console.log('Visitor entry updated successfully:', entryData);

        // Wait a moment before refreshing status to ensure backend is updated
        setTimeout(async () => {
            await refreshLogStatus(visitor.visitor_ref_num);
            console.log('Log status refreshed for:', visitor.visitor_ref_num);
        }, 500);

        alert('Visitor checked out successfully!');

    } catch (err) {
        console.error("Error during checkout:", err);
        alert(`Failed to process checkout: ${err.message}. Please try again.`);
    }
};

    const handleReprint = (visitor: any) => {
        const printWindow = window.open('', 'printWindow', 'height=950mm,width=1100mm');
        const printContent = `
            <html moznomarginboxes mozdisallowselectionprint>
            <head><title>${visitor.visitor_ref_num}</title></head>
            <body style="width:120%;margin: 0mm 0mm 0mm 0mm">
                <div align="left" style="width:100mm;height:100mm; border:thin hidden red; margin:; float:">
                    <table border="1" cellpadding="0" cellspacing="0" style="width:100%; font-size:10px">
                        <tr>
                            <td colspan="4">
                                <table border="0" cellpadding="0" cellspacing="0" style="width:100%; font-size:12px">
                                    <tr>
                                        <th colspan="2" style="text-align:center;font-size:18px; color:blue">
                                            SASMOS HET TECHNOLOGIES
                                        </th>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">VISITOR PASS # ${visitor.pass_number}</th>
                                        <th rowspan="2">
                                            <img align="right" alt="SASMOS HET" src="/bgimages/logo.png" width="98%">
                                        </th>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">ISSUED ON ${new Date(visitor.visit_login_time).toLocaleString()}</th>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <table border="0" cellpadding="0" cellspacing="0" style="width:100%; font-size:11px">
                                    <tr>
                                        <th style="text-align:left">VISITOR NAME</th>
                                        <td style="font-size:14px; font-weight:bold">${visitor.visitor_name.toUpperCase()}</td>
                                        <th rowspan="9">
                                            <img src="/VISITOR PICS/${visitor.visitor_ref_num}.png" width="100%" height="auto" alt="${visitor.visitor_name}"/>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">ORGANISATION</th>
                                        <td>${visitor.visitor_org.toUpperCase()}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">PERSON TO MEET</th>
                                        <td>${visitor.visitor_invitee.toUpperCase()}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">VALID FROM</th>
                                        <td>${new Date(visitor.visit_start_date).toLocaleDateString()} - ${visitor.visit_start_time}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">VALID TILL</th>
                                        <td>${new Date(visitor.visit_end_date).toLocaleDateString()} - ${visitor.visit_end_time}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">AREA ACCESS</th>
                                        <td>${visitorlogstatus[visitor.visitor_ref_num]?.company_visit || ''} - ${visitorlogstatus[visitor.visitor_ref_num]?.company_area_visit || ''}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">MATERIAL CARRIED</th>
                                        <td>${materialCarried.toUpperCase() || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <th style="text-align:left">ID PROOF</th>
                                        <td>${idProof.toUpperCase() || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="4">
                                            <table border="1" cellpadding="0" cellspacing="0" style="width:100%; font-size:10px">
                                                <tr height="25px">
                                                    <th width="49%"></th>
                                                    <th width="49%">
                                                        <img height="20vh" width="70%" alt="" src="/bgimages/RAI.png"/>
                                                        <br />AUTHORISED SIGN
                                                    </th>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th colspan="4" style="text-align:left">Please return the pass at the Reception / Security at the time of exit.</th>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </body>
            </html>
        `;
        if (printWindow) {
            printWindow.document.open();
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();
            printWindow.close();
        }
    };

    const handleCheckin = async () => {
        if (!companyVisit || !companyAreaVisit || !idProof || !idProofFile) {
            alert("FILL IN ALL REQUIRED FIELDS (Company, Area, ID Proof, and ID Proof Attachment)");
            return;
        }

        const commentDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
        const loginUserid = "USER123";
        const loginUsername = "Admin";
        const passNumber = String(Math.max(...visitorlist.map(v => parseInt(v.pass_number) || 0)) + 1).padStart(7, '0');

        try {
            // Upload ID proof file
            const formData = new FormData();
            formData.append('idProof', idProof);
            formData.append('idProofFile', idProofFile);
            formData.append('visitorName', selectedVisitor.visitor_name);
            formData.append('visitor_ref_num', selectedVisitor.visitor_ref_num);

            const uploadResponse = await fetch(`http://localhost:3000/api/upload`, {
                method: 'POST',
                body: formData,
            });
            if (!uploadResponse.ok) {
                throw new Error(`File upload failed: ${uploadResponse.statusText}`);
            }
            const uploadData = await uploadResponse.json();
            if (!uploadData.message) {
                throw new Error('File upload response invalid');
            }

            // Insert into visitor_log_table
            const logResponse = await fetch(`http://localhost:3000/api/visitor_log`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    visitor_ref_num: selectedVisitor.visitor_ref_num,
                    visitor_name: selectedVisitor.visitor_name,
                    log_date: commentDate,
                    log_time: commentDate,
                    check_in_out: 'CHECKIN',
                    company_visit: companyVisit,
                    company_area_visit: companyAreaVisit,
                    login_userid: loginUserid,
                    login_username: loginUsername,
                    create_date: commentDate,
                }),
            });
            if (!logResponse.ok) {
                throw new Error(`Log insertion failed: ${logResponse.statusText}`);
            }

            // Update visitor_entry_table
            const entryResponse = await fetch(`http://localhost:3000/api/visitor_entry`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    visitor_ref_num: selectedVisitor.visitor_ref_num,
                    visit_status: '2',
                    pass_number: passNumber,
                    visit_login_time: commentDate,
                }),
            });
            if (!entryResponse.ok) {
                throw new Error(`Entry update failed: ${entryResponse.statusText}`);
            }

            // Refresh log status
            await refreshLogStatus(selectedVisitor.visitor_ref_num);

            // Print the pass
            const printWindow = window.open('', 'printWindow', 'height=950mm,width=1100mm');
            const printContent = `
                <html moznomarginboxes mozdisallowselectionprint>
                <head><title>${selectedVisitor.visitor_ref_num}</title></head>
                <body style="width:120%;margin: 0mm 0mm 0mm 0mm">
                    <div align="left" style="width:100mm;height:100mm; border:thin hidden red; margin:; float:">
                        <table border="1" cellpadding="0" cellspacing="0" style="width:100%; font-size:10px">
                            <tr>
                                <td colSpan="4">
                                    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; font-size:12px">
                                        <tr>
                                            <th colSpan="2" style="text-align:center;font-size:18px; color:blue">
                                                SASMOS HET TECHNOLOGIES
                                            </th>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">VISITOR PASS # ${passNumber}</th>
                                            <th rowSpan="2">
                                                <img align="right" alt="SASMOS HET" src="/bgimages/logo.png" width="98%">
                                            </th>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">ISSUED ON ${new Date().toLocaleString()}</th>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td colSpan="4">
                                    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; font-size:11px">
                                        <tr>
                                            <th style="text-align:left">VISITOR NAME</th>
                                            <td style="font-size:14px; font-weight:bold">${selectedVisitor.visitor_name.toUpperCase()}</td>
                                            <th rowSpan="9">
                                                <img src="${capturedImage || ''}" width="100%" height="auto" alt="${selectedVisitor.visitor_name}"/>
                                            </th>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">ORGANISATION</th>
                                            <td>${selectedVisitor.visitor_org.toUpperCase()}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">PERSON TO MEET</th>
                                            <td>${selectedVisitor.visitor_invitee.toUpperCase()}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">VALID FROM</th>
                                            <td>${new Date(selectedVisitor.visit_start_date).toLocaleDateString()} - ${selectedVisitor.visit_start_time}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">VALID TILL</th>
                                            <td>${new Date(selectedVisitor.visit_end_date).toLocaleDateString()} - ${selectedVisitor.visit_end_time}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">AREA ACCESS</th>
                                            <td>${companyVisit} - ${companyAreaVisit}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">MATERIAL CARRIED</th>
                                            <td>${materialCarried.toUpperCase()}</td>
                                        </tr>
                                        <tr>
                                            <th style="text-align:left">ID PROOF</th>
                                            <td>${idProof.toUpperCase()}</td>
                                        </tr>
                                        <tr>
                                            <td colSpan="4">
                                                <table border="1" cellpadding="0" cellspacing="0" style="width:100%; font-size:10px">
                                                    <tr height="25px">
                                                        <th width="49%"></th>
                                                        <th width="49%">
                                                            <img height="20vh" width="70%" alt="" src="/bgimages/RAI.png"/>
                                                            <br />AUTHORISED SIGN
                                                        </th>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th colSpan="4" style="text-align:left">Please return the pass at the Reception / Security at the time of exit.</th>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </div>
                </body>
                </html>
            `;
            if (printWindow) {
                printWindow.document.open();
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
                printWindow.close();
            }

            setShowPopup(false);
            setMaterialCarried('');
            setIdProof('');
            setCompanyVisit('');
            setCompanyAreaVisit('');
            setIdProofFile(null);
            setCapturedImage(null);
        } catch (err) {
            console.error("Error during check-in:", err);
            alert(`Failed to save check-in data: ${err.message}. Please try again.`);
        }
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header Section */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg p-6 mb-8">
                <div className="flex items-center">
                    <Users className="h-10 w-10 text-white mr-4" />
                    <div>
                        <h1 className="text-3xl font-bold text-white">Visitors Management</h1>
                        <p className="mt-2 text-blue-100">
                            Manage and track all visitor information and visit details
                        </p>
                    </div>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                    <div className="flex items-center">
                        <Users className="h-8 w-8 text-blue-500 mr-3" />
                        <div>
                            <p className="text-sm font-medium text-gray-600">Total Visitors</p>
                            <p className="text-2xl font-bold text-gray-900">{visitorlist.length}</p>
                        </div>
                    </div>
                </div>
                <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                    <div className="flex items-center">
                        <Search className="h-8 w-8 text-green-500 mr-3" />
                        <div>
                            <p className="text-sm font-medium text-gray-600">Search Results</p>
                            <p className="text-2xl font-bold text-gray-900">{filteredItems.length}</p>
                        </div>
                    </div>
                </div>
                <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
                    <div className="flex items-center">
                        <Calendar className="h-8 w-8 text-purple-500 mr-3" />
                        <div>
                            <p className="text-sm font-medium text-gray-600">Active Visits</p>
                            <p className="text-2xl font-bold text-gray-900">
                                {visitorlist.filter(v => new Date(v.visit_end_date) >= new Date()).length}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Enhanced Search Section */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <div className="flex items-center mb-4">
                    <Search className="h-6 w-6 text-gray-400 mr-2" />
                    <h2 className="text-lg font-semibold text-gray-900">Search Visitors</h2>
                </div>

                <div className="relative max-w-md">
                    <div className="relative flex items-center">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            onKeyDown={handleKeyPress}
                            placeholder="Search by name, organization, ref number, or invitee..."
                            className="w-full pl-10 pr-10 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                        />
                        {searchTerm && (
                            <button
                                onClick={clearSearch}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <X className="w-5 h-5" />
                            </button>
                        )}
                    </div>
                    {searchTerm && (
                        <div className="mt-2 text-sm text-gray-600">
                            Showing {filteredItems.length} of {visitorlist.length} visitors
                        </div>
                    )}
                </div>
            </div>

            {/* Popup for Check-in */}
            {showPopup && selectedVisitor && (
                <div style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 1000
                }}>
                    <div style={{
                        backgroundColor: 'white',
                        padding: '20px',
                        borderRadius: '8px',
                        width: '80%',
                        maxWidth: '1000px',
                        maxHeight: '90%',
                        overflowY: 'auto'
                    }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
                            <h2>SASMOS HET TECHNOLOGIES - Visitor Check-in</h2>
                            <button onClick={() => setShowPopup(false)} style={{ fontSize: '24px' }}>×</button>
                        </div>
                        <div style={{ display: 'flex' }}>
                            <div style={{ flex: 1 }}>
                                <table style={{ width: '100%', fontSize: '100%' }}>
                                    <tr>
                                        <td colSpan={4}>
                                            <table style={{ width: '100%', fontSize: '85%' }}>
                                                <tr>
                                                    <th colSpan={2} style={{ width: '70%', fontSize: '150%', color: 'blue' }}>
                                                        SASMOS HET TECHNOLOGIES
                                                    </th>
                                                    <th rowSpan={2} style={{ width: '30%' }}>
                                                        <img src="/bgimages/logo.png" alt="SASMOS HET" style={{ width: '98%', float: 'right' }} />
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>
                                                        VISITOR PASS # {String(Math.max(...visitorlist.map(v => parseInt(v.pass_number) || 0)) + 1).padStart(7, '0')}<br />
                                                        ISSUED ON {new Date().toLocaleString()}
                                                    </th>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colSpan={4}>
                                            <table style={{ width: '100%', fontSize: '11px', border: '1px solid black' }}>
                                                <tr>
                                                    <th style={{ textAlign: 'left', width: '20%' }}>VISITOR NAME</th>
                                                    <td style={{ width: '50%', fontSize: '14px', fontWeight: 'bold' }}>
                                                        {selectedVisitor.visitor_name.toUpperCase()}
                                                    </td>
                                                    <th rowSpan={8} style={{ width: '30%', textAlign: 'center' }}>
                                                        <canvas ref={canvasRef} width="1000" height="1000" style={{ display: 'none' }}></canvas>
                                                        <img src={capturedImage || ''} alt={selectedVisitor.visitor_name} style={{ width: '200px', height: '150px' }} />
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>ORGANISATION</th>
                                                    <td>{selectedVisitor.visitor_org.toUpperCase()}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>CONTACT #</th>
                                                    <td>{selectedVisitor.visitor_contact?.toUpperCase() || 'N/A'}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>PERSON TO MEET</th>
                                                    <td>{selectedVisitor.visitor_invitee.toUpperCase()}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>VALIDITY</th>
                                                    <td>
                                                        {new Date(selectedVisitor.visit_start_date).toLocaleDateString()} - {selectedVisitor.visit_start_time} TO
                                                        {new Date(selectedVisitor.visit_end_date).toLocaleDateString()} - {selectedVisitor.visit_end_time}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>AREA ACCESS</th>
                                                    <td>{companyVisit} - {companyAreaVisit}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>MATERIAL CARRIED</th>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            value={materialCarried}
                                                            onChange={(e) => setMaterialCarried(e.target.value)}
                                                            style={{ width: '98%', textTransform: 'uppercase' }}
                                                        />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>ID PROOF</th>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            value={idProof}
                                                            onChange={(e) => setIdProof(e.target.value)}
                                                            style={{ width: '98%', textTransform: 'uppercase' }}
                                                        />
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colSpan={4}>
                                            <table style={{ width: '100%', fontSize: '11px', border: '1px solid black' }}>
                                                <tr>
                                                    <th style={{ textAlign: 'left', width: '15%' }}>COMPANY TO VISIT</th>
                                                    <td style={{ width: '35%' }}>
                                                        <select
                                                            value={companyVisit}
                                                            onChange={(e) => setCompanyVisit(e.target.value)}
                                                            style={{ width: '80%' }}
                                                            required
                                                        >
                                                            <option value="">--</option>
                                                            {["SASMOS", "FE-SIL", "CITADEL"].map(company => (
                                                                <option key={company} value={company}>{company}</option>
                                                            ))}
                                                        </select>
                                                    </td>
                                                    <th style={{ textAlign: 'left' }}>AREA OF VISIT</th>
                                                    <td>
                                                        <select
                                                            value={companyAreaVisit}
                                                            onChange={(e) => setCompanyAreaVisit(e.target.value)}
                                                            style={{ width: '80%' }}
                                                            required
                                                        >
                                                            <option value="">--</option>
                                                            {["OFFICE AREA", "SHOPFLOOR", "EOU", "DTA", "STORES"].map(area => (
                                                                <option key={area} value={area}>{area}</option>
                                                            ))}
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: 'left' }}>ID PROOF ATTACHMENT</th>
                                                    <td>
                                                        <input
                                                            type="file"
                                                            onChange={(e) => setIdProofFile(e.target.files[0])}
                                                        />
                                                    </td>
                                                    <th style={{ textAlign: 'left' }}></th>
                                                    <td></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th colSpan={4}>
                                            <button onClick={handleCheckin} style={{ width: '100px', height: '25px', margin: '0 20px' }}>
                                                CHECKIN
                                            </button>
                                        </th>
                                    </tr>
                                </table>
                            </div>
                            <div style={{ width: '300px', marginLeft: '20px' }}>
                                <div style={{ textAlign: 'center', border: '1px solid black', width: '300px', height: '300px' }}>
                                    <video ref={videoRef} style={{ width: '300px', height: '250px', backgroundColor: '#D3D3D3' }} autoPlay></video>
                                    <br />
                                    <button onClick={captureImage} style={{ height: '30px', width: '100px' }}>
                                        CAPTURE
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Visitors Table */}
            {filteredItems.length === 0 ? (
                <div className="bg-white rounded-lg shadow-md p-12 text-center">
                    <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {searchTerm ? 'No visitors found' : 'No visitors available'}
                    </h3>
                    <p className="text-gray-500">
                        {searchTerm
                            ? `No visitors match your search for "${searchTerm}"`
                            : 'There are no visitors in the system yet.'}
                    </p>
                    {searchTerm && (
                        <button
                            onClick={clearSearch}
                            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Clear Search
                        </button>
                    )}
                </div>
            ) : (
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            <Building2 className="w-4 h-4 mr-1" />
                                            Ref Number
                                        </div>
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            STATUS
                                        </div>
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            <Users className="w-4 h-4 mr-1" />
                                            Name
                                        </div>
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Organization
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Nationality
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Gender
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Purpose
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            <MapPin className="w-4 h-4 mr-1" />
                                            Location
                                        </div>
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Invitee
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            <Calendar className="w-4 h-4 mr-1" />
                                            Start Date
                                        </div>
                                    </th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div className="flex items-center">
                                            <Calendar className="w-4 h-4 mr-1" />
                                            End Date
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {filteredItems.map((item, index) => (
                                    <tr key={index} className="hover:bg-blue-50 transition-colors duration-150">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                                {item.visitor_ref_num}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div
                                                onClick={(e) => handleLinkClick(e, item)}
                                                dangerouslySetInnerHTML={{ __html: getSubmitValue(item.visitor_ref_num) }}
                                            />
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-600">{item.visitor_name}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">{item.visitor_org}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">{item.visitor_nation}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                            item.visitor_sex === 'Male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'
                                        }`}>
                                                {item.visitor_sex}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">{item.visit_purp}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center text-sm text-gray-600">
                                                <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                                                {item.visitor_loc}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">{item.visitor_invitee}</div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">
                                                {item.visit_start_date ? new Date(item.visit_start_date).toLocaleDateString() : 'N/A'}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-600">
                                                {item.visit_end_date ? new Date(item.visit_end_date).toLocaleDateString() : 'N/A'}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}
        </div>
    );
}

export default VisitorsListForm;