"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const adminApi_1 = require("../../../services/adminApi");
const UserTable_1 = require("../../../components/Admin/UserTable");
const Pagination_1 = require("../../../components/Common/Pagination");
const useToast_1 = require("../../../hooks/useToast");
// Import Button from nextui
const react_2 = require("@nextui-org/react");
const UserManagementPage = () => {
    const [users, setUsers] = (0, react_1.useState)([]);
    const [isLoading, setIsLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    const [currentPage, setCurrentPage] = (0, react_1.useState)(1);
    const [totalPages, setTotalPages] = (0, react_1.useState)(1);
    const [totalUsers, setTotalUsers] = (0, react_1.useState)(0);
    const pageSize = 10; // Or get from configuration/state
    const { addToast } = (0, useToast_1.useToast)();
    // Add state for Create User Modal
    const { isOpen: isCreateModalOpen, onOpen: onCreateModalOpen, onClose: onCreateModalClose } = (0, react_2.useDisclosure)();
    // State for the new user form
    const [newUserEmail, setNewUserEmail] = (0, react_1.useState)("");
    const [isSubmitting, setIsSubmitting] = (0, react_1.useState)(false);
    const loadUsers = (0, react_1.useCallback)((page) => __awaiter(void 0, void 0, void 0, function* () {
        setIsLoading(true);
        setError(null);
        try {
            const result = yield (0, adminApi_1.fetchPortalUsers)({ page, pageSize });
            setUsers(result.users);
            setTotalUsers(result.totalCount);
            setTotalPages(Math.ceil(result.totalCount / pageSize));
            setCurrentPage(page);
        }
        catch (err) { // Explicitly type err
            const errorMessage = err.message || "Failed to load users. Please try again.";
            setError(errorMessage);
            addToast({ type: 'error', message: `Error loading users: ${errorMessage}` });
            console.error("Error in loadUsers:", err); // Log the full error
        }
        finally {
            setIsLoading(false);
        }
    }), [pageSize, addToast]); // Dependencies for useCallback
    (0, react_1.useEffect)(() => {
        loadUsers(currentPage);
    }, [loadUsers, currentPage]); // Trigger loadUsers when page changes or function definition changes
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            loadUsers(newPage);
        }
    };
    // Ensure this function definition exists
    const handleCreateUserSubmit = () => __awaiter(void 0, void 0, void 0, function* () {
        if (!newUserEmail) {
            addToast({ type: 'warning', message: 'Please enter an email or UPN.' });
            return;
        }
        setIsSubmitting(true);
        try {
            const userData = { email: newUserEmail };
            const createdUser = yield (0, adminApi_1.createUser)(userData);
            addToast({ type: 'success', message: `User ${createdUser.name || createdUser.email} created successfully.` });
            onCreateModalClose(); // Close modal on success
            setNewUserEmail(''); // Clear input
            loadUsers(1); // Refresh user list (go back to page 1)
        }
        catch (error) {
            addToast({ type: 'error', message: `Failed to create user: ${error.message || 'Unknown error'}` });
            console.error("Create user error:", error);
        }
        finally {
            setIsSubmitting(false);
        }
    });
    // --- RENDER --- Single return statement starts here
    return (<div className="p-4 md:p-6 lg:p-8">
      <h1 className="text-2xl font-semibold mb-4">User Management</h1>

      {/* Action Bar: Filters and Create Button */}
      <div className="flex justify-between items-center mb-4">
        <div>
          {/* Placeholder for Filters */}
          {/* <Input placeholder="Search users..." /> */}
        </div>
        {/* Add onClick handler later - NOW */}
        <react_2.Button color="primary" onPress={onCreateModalOpen}>Create User</react_2.Button>
      </div>

      {/* Loading and Error States */}
      {isLoading && (<div className="text-center py-4">Loading users...</div>)}
      {!isLoading && error && (<div className="text-center py-4 text-red-500">Error: {error}</div>)}
      {!isLoading && !error && users.length === 0 && (<div className="text-center py-4">No users found.</div>)}

      {/* User Table and Pagination */}
      {!isLoading && !error && users.length > 0 && (<>
          <UserTable_1.default users={users}/>
          <Pagination_1.default currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} itemsPerPage={pageSize} totalItems={totalUsers}/>
        </>)}

      {/* Create User Modal */}
      <react_2.Modal isOpen={isCreateModalOpen} onClose={onCreateModalClose} placement="top-center">
        <react_2.ModalContent>
          {(onClose) => (<>
              <react_2.ModalHeader className="flex flex-col gap-1">Create New User</react_2.ModalHeader>
              <react_2.ModalBody>
                {/* Form fields will go here */}
                <react_2.Input autoFocus label="Email / UPN" placeholder="Enter user's email or User Principal Name" variant="bordered" value={newUserEmail} onChange={(e) => setNewUserEmail(e.target.value)}/>
                {/* Add other fields like FirstName, LastName, Roles later */}
              </react_2.ModalBody>
              <react_2.ModalFooter>
                <react_2.Button color="danger" variant="flat" onPress={onCreateModalClose}>
                  Close
                </react_2.Button>
                <react_2.Button color="primary" onPress={handleCreateUserSubmit} isLoading={isSubmitting} disabled={!newUserEmail || isSubmitting} // Disable if no email or submitting
        >
                  {isSubmitting ? 'Creating...' : 'Create User'}
                </react_2.Button>
              </react_2.ModalFooter>
            </>)}
        </react_2.ModalContent>
      </react_2.Modal>

    </div>); // Single return statement ends here
};
exports.default = UserManagementPage;
//# sourceMappingURL=UserManagementPage.js.map