import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { findOrCreateUser, EntraUserData } from "../shared/services/userManagementService";
import { logger } from "../shared/utils/logger";
import { getUserIdFromPrincipal } from "../shared/authUtils"; // To get modifier ID later
import { PortalUser } from "../shared/interfaces"; // To map the result

// TODO: Add request body validation (e.g., using Zod)
// interface CreateUserRequestBody {
//   email: string; 
// }


async function createUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log = logger.info; // Use shared logger
    logger.info(`[CreateUser] HTTP trigger function processed a request.`);

    // TODO: Implement proper authentication check later
    // const principal = getClientPrincipal(request);
    // if (!principal) {
    //     return { status: 401, jsonBody: { error: "Unauthorized" } };
    // }
    // Check roles if needed: hasRequired<PERSON><PERSON>(principal, ['PortalAdmin'])

    // Placeholder for the ID of the user performing the creation
    const createdByUserId = 1; // TODO: Replace with actual ID from getUserIdFromPrincipal(principal);

    try {
        // 1. Parse and Validate Request Body
        // TODO: Add proper validation
        const body: any = await request.json(); 
        const userEmailOrUpn = body?.email;

        if (!userEmailOrUpn || typeof userEmailOrUpn !== 'string') {
             logger.warn("[CreateUser] Invalid request body: Missing or invalid email.", body);
             return { status: 400, jsonBody: { error: "Request body must contain a valid 'email' (or UPN) string." } };
        }
        logger.info(`[CreateUser] Attempting to create user with email/UPN: ${userEmailOrUpn}`);

        // 2. Prepare data for findOrCreateUser
        // In a real scenario, we might query Graph API here first if only email is given,
        // to get the Entra ID (object ID), full name, company, etc.
        // For now, we assume the provided email IS the UPN/Entra ID link.
        const entraUserData: EntraUserData = {
            id: userEmailOrUpn, // Using email/UPN as the EntraID placeholder
            userPrincipalName: userEmailOrUpn,
            mail: userEmailOrUpn,
            // Provide default/empty values for other required fields if not fetching from Graph
            givenName: '', // Or derive from email?
            surname: 'User', // Default
            companyName: '', // This might cause issues if company lookup fails
            department: undefined
        };

        // 3. Call the service function
        const createdDbUser = await findOrCreateUser(entraUserData, createdByUserId);

        // 4. Handle Service Response (findOrCreateUser throws on critical errors)
        if (!createdDbUser) {
             // This case might not be reachable if findOrCreateUser throws, but handle defensively
             logger.error("[CreateUser] findOrCreateUser returned null unexpectedly.");
             return { status: 500, jsonBody: { error: "Failed to create or find user." } };
        }

        // 5. Map DbUser to PortalUser for the response (Need a mapping function)
        // TODO: Create or import a suitable mapping function if mapDbUserToPortalUser isn't directly usable/exported
        const portalUserResponse: Partial<PortalUser> = { // Use partial as we might not have all fields yet
             id: createdDbUser.EntraID ?? undefined, // Handle null case explicitly
             internalId: createdDbUser.UserID,
             name: createdDbUser.Username,
             email: createdDbUser.Email,
             company: createdDbUser.CompanyName, // May be undefined if lookup failed/default used
             companyId: createdDbUser.CompanyID,
             // Roles are usually assigned separately or defaulted in findOrCreateUser
             roles: [], // Start with empty roles unless findOrCreate assigns default
             status: createdDbUser.IsActive ? 'Active' : 'Inactive',
             lastLogin: createdDbUser.LastLoginDate?.toISOString()
        };


        logger.info(`[CreateUser] Successfully created/found user: ${createdDbUser.UserID}`);
        // Return 201 Created status code
        return { status: 201, jsonBody: portalUserResponse };

    } catch (error: any) {
        logger.error("[CreateUser] Error processing request:", error);
        // Check for specific error types if needed (e.g., company not found error from findOrCreateUser)
        // if (error.message.includes("Company") && error.message.includes("not found")) {
        //     return { status: 400, jsonBody: { error: error.message } };
        // }
        return { status: 500, jsonBody: { error: `Internal server error: ${error.message || 'Unknown error'}` } };
    }
}

app.http('CreateUser', {
    methods: ['POST'],
    authLevel: 'anonymous', // Match function.json
    route: 'users',         // Match function.json
    handler: createUser
});

// Export the function for the Azure Functions runtime
export default createUser; // Or adjust export based on v4 model specifics if needed 