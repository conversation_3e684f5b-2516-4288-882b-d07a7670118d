import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';

export async function CreateSampleData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    try {
        context.log('CreateSampleData function triggered');

        // Get database connection
        const pool: ConnectionPool = await getPool();

        // Sample change requests for Company ID 4 (Avirata Defence Systems) with June deployments
        const sampleRequests = [
            {
                title: 'Database Server Upgrade - Approved for June',
                description: 'Upgrade production database server to latest version - approved for June deployment',
                status: 'Approved',
                priority: 'High',
                companyId: 4,
                deploymentDate: '2024-06-15',
                requestedCompletionDate: '2024-06-30'
            },
            {
                title: 'Security Patch Deployment',
                description: 'Deploy critical security patches to all servers',
                status: 'In Development',
                priority: 'Critical',
                companyId: 4,
                deploymentDate: '2024-06-20',
                requestedCompletionDate: '2024-06-25'
            },
            {
                title: 'Network Infrastructure Update - Completed',
                description: 'Update network switches and routers - completed in June',
                status: 'Completed',
                priority: 'Medium',
                companyId: 4,
                deploymentDate: '2024-06-10',
                requestedCompletionDate: '2024-06-15'
            },
            {
                title: 'Application Performance Optimization',
                description: 'Optimize application performance and database queries',
                status: 'Approved',
                priority: 'Medium',
                companyId: 4,
                deploymentDate: '2024-06-25',
                requestedCompletionDate: '2024-07-05'
            },
            {
                title: 'Backup System Enhancement',
                description: 'Enhance backup system with new retention policies',
                status: 'Approved',
                priority: 'Low',
                companyId: 4,
                deploymentDate: '2024-06-28',
                requestedCompletionDate: '2024-07-10'
            }
        ];

        let insertedCount = 0;
        const insertedRequests = [];

        for (const request of sampleRequests) {
            try {
                const insertQuery = `
                    INSERT INTO ChangeRequests (
                        Title, Description, Status, Priority, CompanyID, RequesterID,
                        DeploymentDate, RequestedCompletionDate, CreatedDate, ModifiedDate,
                        DeploymentDuration, DeploymentLocation, DeploymentType, 
                        RequiresSystemDowntime, CalendarColor
                    ) VALUES (
                        @Title, @Description, @Status, @Priority, @CompanyID, @RequesterID,
                        @DeploymentDate, @RequestedCompletionDate, GETDATE(), GETDATE(),
                        @DeploymentDuration, @DeploymentLocation, @DeploymentType,
                        @RequiresSystemDowntime, @CalendarColor
                    );
                    SELECT SCOPE_IDENTITY() as RequestID;
                `;

                const result = await pool.request()
                    .input('Title', request.title)
                    .input('Description', request.description)
                    .input('Status', request.status)
                    .input('Priority', request.priority)
                    .input('CompanyID', request.companyId)
                    .input('RequesterID', 1) // Using default user ID
                    .input('DeploymentDate', new Date(request.deploymentDate))
                    .input('RequestedCompletionDate', new Date(request.requestedCompletionDate))
                    .input('DeploymentDuration', 120)
                    .input('DeploymentLocation', 'Production')
                    .input('DeploymentType', 'Automated')
                    .input('RequiresSystemDowntime', request.priority === 'Critical' ? 1 : 0)
                    .input('CalendarColor', request.priority === 'Critical' ? '#FF4444' : 
                                          request.priority === 'High' ? '#FF8800' : 
                                          request.priority === 'Medium' ? '#4488FF' : '#44AA44')
                    .query(insertQuery);

                const requestId = result.recordset[0].RequestID;
                insertedRequests.push({
                    requestId,
                    title: request.title,
                    status: request.status,
                    deploymentDate: request.deploymentDate
                });

                insertedCount++;
                context.log(`Inserted: ${request.title} (ID: ${requestId})`);
            } catch (insertError) {
                const errorMessage = insertError instanceof Error ? insertError.message : 'Unknown error';
                context.log(`Error inserting ${request.title}: ${errorMessage}`);
            }
        }

        return {
            status: 200,
            jsonBody: {
                message: `Successfully created ${insertedCount} sample change requests for Company ID 4 with June deployment dates`,
                insertedCount,
                insertedRequests
            }
        };

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        context.log('Error in CreateSampleData:', errorMessage);
        return {
            status: 500,
            jsonBody: { 
                error: 'Failed to create sample data', 
                details: errorMessage 
            }
        };
    }
}

// Register the function
app.http('CreateSampleData', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'create-sample-data',
    handler: CreateSampleData
}); 