import React, { useState, useEffect } from 'react';
import { X, User, Clock, AlertCircle } from 'feather-icons-react';
import { changeManagementApi, type ChangeRequest, type Developer } from '../../services/changeManagementApi';

interface AssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  changeRequest: ChangeRequest;
  onAssignmentComplete: (updatedRequest: ChangeRequest) => void;
}

const AssignmentModal: React.FC<AssignmentModalProps> = ({
  isOpen,
  onClose,
  changeRequest,
  onAssignmentComplete
}) => {
  const [developers, setDevelopers] = useState<Developer[]>([]);
  const [selectedDeveloperId, setSelectedDeveloperId] = useState<number | null>(null);
  const [assignmentNotes, setAssignmentNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingDevelopers, setLoadingDevelopers] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadDevelopers();
      // Reset form when modal opens
      setSelectedDeveloperId(null);
      setAssignmentNotes('');
      setError(null);
    }
  }, [isOpen]);

  const loadDevelopers = async () => {
    try {
      setLoadingDevelopers(true);
      setError(null);
      const developersData = await changeManagementApi.getDevelopers();
      setDevelopers(developersData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load developers');
      console.error('Error loading developers:', err);
    } finally {
      setLoadingDevelopers(false);
    }
  };

  const handleAssign = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDeveloperId) {
      setError('Please select a developer');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const updatedRequest = await changeManagementApi.assignChangeRequest(
        changeRequest.requestId,
        selectedDeveloperId,
        assignmentNotes
      );
      
      onAssignmentComplete(updatedRequest);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign change request');
      console.error('Error assigning change request:', err);
    } finally {
      setLoading(false);
    }
  };

  const selectedDeveloper = developers.find(dev => dev.userId === selectedDeveloperId);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <User size={24} className="text-blue-600" />
              <h3 className="text-lg font-medium text-gray-900">Assign to Developer</h3>
            </div>
            <button
              onClick={onClose}
              disabled={loading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <X size={20} />
            </button>
          </div>

          {/* Change Request Info */}
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <p className="text-sm font-medium text-gray-900 mb-1">
              {changeRequest.requestNumber}: {changeRequest.title}
            </p>
            <p className="text-xs text-gray-500">
              Status: {changeRequest.status} • Priority: {changeRequest.priority}
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start space-x-2">
              <AlertCircle size={16} className="text-red-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Assignment Form */}
          <form onSubmit={handleAssign} className="space-y-4">
            {/* Developer Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Developer <span className="text-red-500">*</span>
              </label>
              
              {loadingDevelopers ? (
                <div className="flex items-center justify-center py-4">
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading developers...</span>
                </div>
              ) : (
                <select
                  value={selectedDeveloperId || ''}
                  onChange={(e) => setSelectedDeveloperId(e.target.value ? parseInt(e.target.value) : null)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value="">Choose a developer...</option>
                  {developers.map((developer) => (
                    <option key={developer.userId} value={developer.userId}>
                      {developer.fullName} 
                      {developer.currentAssignments > 0 && ` (${developer.currentAssignments} active)`}
                      {developer.departmentName && ` - ${developer.departmentName}`}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {/* Selected Developer Info */}
            {selectedDeveloper && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <User size={16} className="text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {selectedDeveloper.fullName}
                  </span>
                </div>
                <div className="text-xs text-blue-700 space-y-1">
                  <p>Email: {selectedDeveloper.email}</p>
                  {selectedDeveloper.departmentName && (
                    <p>Department: {selectedDeveloper.departmentName}</p>
                  )}
                  <div className="flex items-center space-x-1">
                    <Clock size={12} />
                    <span>Current assignments: {selectedDeveloper.currentAssignments}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Assignment Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assignment Notes (Optional)
              </label>
              <textarea
                value={assignmentNotes}
                onChange={(e) => setAssignmentNotes(e.target.value)}
                placeholder="Add any specific instructions or notes for the developer..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="flex-1 px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !selectedDeveloperId || loadingDevelopers}
                className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Assigning...' : 'Assign Developer'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AssignmentModal; 