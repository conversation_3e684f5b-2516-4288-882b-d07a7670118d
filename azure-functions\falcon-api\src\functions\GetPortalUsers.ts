import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { userManagementService } from "../shared/services/userManagementService"; // Import the service
import { getClientPrincipal, hasRequiredR<PERSON> } from "../shared/authUtils"; // Import auth utils
import { validateRequest, getPortalUsersSchema } from "../shared/validationSchemas"; // Import validation
import { logger } from "../shared/utils/logger"; // Import logger
import { PortalUser } from "../shared/interfaces"; // Import PortalUser for type safety

// Note: The userManagementService.getPortalUsersPaginated method already returns properly mapped PortalUser objects
// including the tenantId field. No additional mapping is needed here.

export async function getPortalUsers(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log = logger.info; // Use shared logger
    logger.info(`Http function processed request for url "${request.url}"`);

    // 1. Authentication & Authorization Check
    // Check if we're in development mode (no Azure App Service authentication)
    const isDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    logger.info(`GetPortalUsers: Development mode check - WEBSITE_SITE_NAME: ${process.env.WEBSITE_SITE_NAME}, isDevelopment: ${isDevelopment}`);

    if (!isDevelopment) {
        const principal = getClientPrincipal(request);
        if (!principal) {
            logger.warn("GetPortalUsers: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized" } };
        }
        // Optional: Add role check if needed, e.g., Portal Admin
        // if (!hasRequiredRole(principal, ['Portal Admin'])) {
        //     logger.warn(`GetPortalUsers: User ${principal.userDetails} lacks required role.`);
        //     return { status: 403, jsonBody: { error: "Forbidden" } };
        // }
    } else {
        logger.info("GetPortalUsers: Development mode - bypassing authentication check.");
    }

    // 2. Input Validation
    // Convert URLSearchParams to plain object for validation
    const queryParams = {
        page: request.query.get('page') || undefined,
        pageSize: request.query.get('pageSize') || undefined,
        search: request.query.get('search') || undefined,
        company: request.query.get('company') || undefined,
        role: request.query.get('role') || undefined,
        status: request.query.get('status') || undefined,
    };
    
    // Pass the data, context, and type description
    const validationErrorResponse = validateRequest(getPortalUsersSchema.shape.query, queryParams, context, "query parameters");
    if (validationErrorResponse) {
        // If validation fails, return the 400 response generated by validateRequest
        logger.warn("GetPortalUsers: Invalid request parameters."); // Log is already done in validateRequest
        return validationErrorResponse;
    }

    // If validation succeeded, parse the data again safely using the schema to get typed output
    // We know this parse will succeed because validateRequest passed.
    const parseResult = getPortalUsersSchema.shape.query.safeParse(queryParams);
    // Add a check just in case, although theoretically unreachable if validateRequest passed
    if (!parseResult.success) {
        logger.error("GetPortalUsers: Zod parse failed after successful validation check. This should not happen.", parseResult.error);
        return { status: 500, jsonBody: { error: "Internal validation error" } };
    }

    // Extract validated & typed data 
    const { page, pageSize, search, company, role, status: rawStatus } = parseResult.data;
    
    // Convert 'All' status to undefined for backend filtering logic
    const status = rawStatus === 'All' ? undefined : rawStatus;

    // Map frontend parameter names to service method parameter names
    const searchTerm = search;
    
    // For company filtering, we'll pass the company name directly to the service
    // The service method will be updated to handle company names
    const companyFilter = company && company !== 'All' ? company : undefined;
    
    // For role filtering, we'll pass the role name directly to the service
    // The service method will be updated to handle role names
    const roleFilter = role && role !== 'All' ? role : undefined;

    // Debug logging for status filter
    logger.info(`GetPortalUsers: Received parameters - page: ${page}, pageSize: ${pageSize}, searchTerm: ${searchTerm}, company: ${company}, role: ${role}, rawStatus: ${rawStatus}, processedStatus: ${status}`);
    logger.info(`GetPortalUsers: Mapped filters - companyFilter: ${companyFilter}, roleFilter: ${roleFilter}, status: ${status}`);

    try {
        // 3. Call Service Method
        const result = await userManagementService.getPortalUsersPaginated(
            { searchTerm, companyFilter, roleFilter, status },
            { page, pageSize }
        );

        // 4. Return Response
        return {
            status: 200, // Explicitly set status 200
            jsonBody: {
                users: result.users,
                totalCount: result.totalCount,
                page: page,
                pageSize: pageSize
            }
        };

    } catch (err) {
        logger.error(`Error fetching portal users: ${err}`);
        
        // In development mode, return mock data if database is unavailable
        if (isDevelopment && err instanceof Error && (err.message.includes('Database') || err.message.includes('Connection') || err.message.includes('Login failed'))) {
            logger.warn("GetPortalUsers: Database connection failed in development mode, returning mock data");
            
            const mockUsers: PortalUser[] = [
                {
                    id: "simulated-guid-12345",
                    internalId: 1,
                    name: "Chetan Pal",
                    email: "<EMAIL>",
                    company: "Avirata Defence Systems",
                    companyId: 1,
                    roles: ["Administrator", "Employee"],
                    status: "Active",
                    tenantId: "ecb4a448-4a99-443b-aaff-063150b6c9ea",
                    lastLogin: new Date().toISOString()
                },
                {
                    id: "mock-user-2",
                    internalId: 2,
                    name: "John Doe",
                    email: "<EMAIL>",
                    company: "Avirata Defence Systems",
                    companyId: 1,
                    roles: ["Employee"],
                    status: "Active",
                    tenantId: "ecb4a448-4a99-443b-aaff-063150b6c9ea",
                    lastLogin: new Date(Date.now() - 86400000).toISOString()
                },
                {
                    id: "mock-user-3",
                    internalId: 3,
                    name: "Jane Smith",
                    email: "<EMAIL>",
                    company: "SASMOS HET",
                    companyId: 2,
                    roles: ["HR Manager", "Employee"],
                    status: "Active",
                    tenantId: "ecb4a448-4a99-443b-aaff-063150b6c9ea",
                    lastLogin: new Date(Date.now() - 3600000).toISOString()
                }
            ];

            return {
                status: 200,
                jsonBody: {
                    users: mockUsers,
                    totalCount: mockUsers.length,
                    page: page,
                    pageSize: pageSize
                }
            };
        }
        
        const error = err as Error;
        return {
            status: 500,
            jsonBody: {
                error: "Failed to fetch portal users",
                details: error.message
            }
        };
    }
}

app.http('GetPortalUsers', {
    methods: ['GET'],
    route: 'portal-users', // Ensure this matches the frontend API call path (/api/portal-users via proxy)
    // Use 'function' authLevel if relying on Azure Function App's Entra ID integration
    // Keep 'anonymous' if handling auth solely within the function code (as done above)
    authLevel: 'anonymous', 
    handler: getPortalUsers
}); 