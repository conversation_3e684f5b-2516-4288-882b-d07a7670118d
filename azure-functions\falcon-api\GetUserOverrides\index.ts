import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";

export async function getUserOverrides(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const entraId = request.params.entraId;
    context.log(`Http function processed request for GetUserOverrides: ${entraId}`);

    if (!entraId) {
        return {
            status: 400,
            jsonBody: { message: "EntraID parameter is required." }
        };
    }

    try {
        // First check if the user exists in our database
        const userQuery = `
            SELECT UserID, IsActive
            FROM Users
            WHERE EntraID = @EntraID
        `;

        const userResult = await executeQuery(userQuery, { EntraID: entraId });

        if (userResult.recordset.length === 0) {
            // User not found in our database
            return {
                status: 404,
                jsonBody: {
                    message: "User not found in the portal database."
                }
            };
        }

        const userId = userResult.recordset[0].UserID;
        const isActive = userResult.recordset[0].IsActive;

        // Get user's roles
        const rolesQuery = `
            SELECT r.RoleID, r.RoleName
            FROM UserRoles ur
            JOIN Roles r ON ur.RoleID = r.RoleID
            WHERE ur.UserID = @UserID AND ur.IsActive = 1 AND r.IsActive = 1
        `;

        const rolesResult = await executeQuery(rolesQuery, { UserID: userId });

        const roles = rolesResult.recordset.map(role => role.RoleName);

        return {
            status: 200,
            jsonBody: {
                isActive: isActive === 1,
                roles: roles
            }
        };
    } catch (error) {
        context.error(`Error fetching user overrides: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: "Error fetching user overrides.",
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}

app.http('GetUserOverrides', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'users/overrides/{entraId}',
    handler: getUserOverrides
});
