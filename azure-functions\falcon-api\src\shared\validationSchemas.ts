import { HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { z } from 'zod'; // Assuming Zod is available for validation
import { logger } from './utils/logger';

// --- Helper function to format Zod errors ---
const formatZodError = (error: z.ZodError): string => {
    return error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join('; ');
};

// --- Query Parameter Schemas --- 

// Schema for SearchDirectoryUsers query parameters
export const searchQuerySchema = z.object({
    q: z.string().min(2, { message: "Search query parameter 'q' must be at least 2 characters long." }),
    limit: z.preprocess(
        (val: unknown) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val),
        z.number().int().positive({ message: "Limit parameter must be a positive integer." })
            .optional()
            .default(10)
    )
});

// Schema for GetUsers query parameters
export const listUsersQuerySchema = z.object({
    search: z.string().optional(),
    company: z.string().optional().default('All'), 
    role: z.string().optional().default('All'), 
    status: z.enum(['Active', 'Inactive', 'All']).optional().default('All'),
    page: z.preprocess(
        (val: unknown) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val),
        z.number().int().min(1, { message: "Page parameter must be 1 or greater." })
            .optional()
            .default(1)
    ),
    pageSize: z.preprocess(
        (val: unknown) => (typeof val === 'string' && val.trim() !== '' ? parseInt(val, 10) : val),
        z.number().int().min(1, { message: "PageSize parameter must be 1 or greater." }).max(100, { message: "PageSize cannot exceed 100." })
            .optional()
            .default(10)
    )
});

// Schema for route parameter containing EntraID (e.g., /portal-users/{entraId})
export const entraIdRouteParamSchema = z.object({
    entraId: z.string().min(1, { message: "EntraID route parameter cannot be empty." })
});

// Schema for route parameter containing UserID (e.g., /users/{userId}/...)
export const userIdRouteParamSchema = z.object({
    userId: z.preprocess(
        (val: unknown) => (typeof val === 'string' ? val.trim() : val),
        z.string()
         .refine((val) => /^[1-9]\d*$/.test(val), {
             message: "UserID parameter must be a positive integer string."
         })
         .transform((val) => parseInt(val, 10))
    )
});

// Schema for route parameters containing UserID and RoleID (e.g., /users/{userId}/roles/{roleId})
// Note: Not currently used, Assign/Remove use body for roleId. Keeping for potential future use.
export const userRoleRouteParamsSchema = z.object({
    userId: z.preprocess(
        (val: unknown) => (typeof val === 'string' ? val.trim() : val),
        z.string()
         .refine((val) => /^[1-9]\d*$/.test(val), {
             message: "UserID route parameter must be a positive integer string."
         })
         .transform((val) => parseInt(val, 10))
    ),
    roleId: z.preprocess(
        (val: unknown) => (typeof val === 'string' ? val.trim() : val),
        z.string()
         .refine((val) => /^[1-9]\d*$/.test(val), {
             message: "RoleID route parameter must be a positive integer string."
         })
         .transform((val) => parseInt(val, 10))
    )
});

// --- Request Body Schemas ---

// Schema for AddUser request body
export const addUserBodySchema = z.object({
    entraId: z.string().min(1, { message: "entraId in request body cannot be empty." })
});

// Schema for AssignRole request body
export const assignRoleBodySchema = z.object({
    roleId: z.number().int().positive({ message: "roleId must be a positive integer." })
});

// Schema for UpdateUser request body
export const updateUserBodySchema = z.object({
    // Allow empty array for roles
    roles: z.array(z.string({ invalid_type_error: "Role names must be strings." }), { invalid_type_error: "Roles must be an array." }),
    isActive: z.boolean({ required_error: "isActive is required.", invalid_type_error: "isActive must be a boolean." })
});

// Schema for GetRoles (No input)
export const getRolesSchema = z.object({}); // Empty schema for routes with no input

// Schema for SearchDirectoryUsers
export const searchDirectoryUsersSchema = z.object({
    query: z.object({
        searchQuery: z.string().min(1, "Search query is required"),
        limit: z.coerce.number().int().positive().optional().default(10)
    })
});

// Schema for AddUser
export const addUserSchema = z.object({
    body: z.object({
        EntraID: z.string().uuid("Invalid Entra ID format"),
    })
});

// Schema for AssignRole
export const assignRoleSchema = z.object({
    params: z.object({
        userId: z.string().min(1), // Or specific format if internal ID is used
    }),
    body: z.object({
        roleId: z.coerce.number().int().positive("Role ID must be a positive integer"),
    })
});

// Schema for RemoveRole
export const removeRoleSchema = z.object({
    params: z.object({
        userId: z.string().min(1),
        roleId: z.string().regex(/^\d+$/, "Role ID in path must be numeric") // roleId from path
    }),
});

// Schema for GetUser
export const getUserSchema = z.object({
    params: z.object({
        userId: z.string().min(1) // Can be EntraID (GUID) or Internal ID (number)
                               // Consider refining if type is known or add custom validation
    })
});

// Schema for UpdateUser (assuming only status update for now)
export const updateUserSchema = z.object({
    params: z.object({
        userId: z.string().min(1),
    }),
    body: z.object({
        status: z.enum(['Active', 'Inactive'])
        // Add other updatable fields here later, e.g., companyId
        // companyId: z.coerce.number().int().positive().optional(),
    })
});

// Schema for GetPortalUsers
export const getPortalUsersSchema = z.object({
    query: z.object({
        page: z.coerce.number().int().positive().optional().default(1),
        pageSize: z.coerce.number().int().positive().optional().default(10),
        search: z.string().optional(),
        company: z.string().optional(),
        role: z.string().optional(),
        status: z.enum(['Active', 'Inactive', 'All']).optional(),
    })
});

/**
 * Validates request data (query parameters, route parameters, or body) using a provided Zod schema.
 * 
 * @param schema - The Zod schema to use for validation.
 * @param data - The data object (e.g., request.query, request.params, parsed request body) to validate.
 * @param context - The InvocationContext for logging.
 * @param dataType - A string describing the type of data being validated (e.g., "query parameters", "request body") for logging.
 * @returns An HttpResponseInit object with status 400 if validation fails, otherwise null.
 */
export function validateRequest(schema: z.ZodSchema<any>, data: any, context: InvocationContext, dataType: string): HttpResponseInit | null {
    if (!schema) {
        context.log(`WARN: No validation schema provided for ${dataType}. Skipping validation.`);
        return null;
    }

    const result = schema.safeParse(data);

    if (!result.success) {
        const errorMessage = formatZodError(result.error);
        context.log(`Request validation failed for ${dataType}:`, errorMessage);
        // Return structured errors along with a general message
        return {
            status: 400,
            jsonBody: { 
                message: `Invalid ${dataType}.`, 
                errors: result.error.flatten().fieldErrors // Provides errors per field
            } 
        };
    }
    
    // Validation succeeded
    context.log(`Validation successful for ${dataType}.`);
    return null; 
} 