"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const feather_icons_react_1 = require("feather-icons-react"); // Using Clock and Heart for theme
const healthTips_1 = require("../data/healthTips"); // Import tips
const ComingSoonPage = ({ pageName }) => {
    const [randomTip, setRandomTip] = (0, react_1.useState)('');
    // Select a random tip on component mount
    (0, react_1.useEffect)(() => {
        const randomIndex = Math.floor(Math.random() * healthTips_1.healthTips.length);
        setRandomTip(healthTips_1.healthTips[randomIndex]);
    }, []);
    return (<div className="flex flex-col items-center justify-center text-center p-10 min-h-[calc(100vh-8rem)]"> {/* Ensure min height */} 
      {/* Modernized Look */}
      <div className="relative mb-6">
         <feather_icons_react_1.Clock size={64} className="text-indigo-500 opacity-80"/>
         {/* Optional: Add subtle animation or decoration */}
      </div>

      <h1 className="text-3xl font-bold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-purple-600">
        {pageName ? `${pageName} - Almost Ready!` : 'Almost Ready!'}
      </h1>
      <p className="text-gray-600 max-w-md mb-8">
        We're putting the finishing touches on this section. It'll be available soon!
      </p>

      {/* Health Tip Section */}
      {randomTip && (<div className="mt-6 p-4 bg-gradient-to-r from-teal-50 to-cyan-50 border-l-4 border-teal-400 rounded-md shadow-sm max-w-lg">
          <div className="flex items-center mb-2">
             <feather_icons_react_1.Heart size={18} className="text-teal-600 mr-2 flex-shrink-0"/>
             <h3 className="text-md font-semibold text-teal-800">Quick Health Tip:</h3>
          </div>
          <p className="text-sm text-teal-700 text-left">{randomTip}</p>
        </div>)}
    </div>);
};
exports.default = ComingSoonPage;
//# sourceMappingURL=ComingSoonPage.js.map