"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.executeQuery = void 0;
const sql = __importStar(require("mssql"));
const config = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_SERVER || '',
    database: process.env.DB_DATABASE || '',
    options: {
        encrypt: true,
        trustServerCertificate: process.env.NODE_ENV === 'development' // Set NODE_ENV=development for local run if needed
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};
// Store the pool promise globally
let pool = null;
let poolConnectPromise = null;
const getPool = async () => {
    if (pool && pool.connected) {
        return pool;
    }
    // If connection is in progress, wait for it
    if (poolConnectPromise) {
        return poolConnectPromise;
    }
    // Create a new connection promise
    poolConnectPromise = new Promise(async (resolve, reject) => {
        try {
            console.log(`Attempting to connect to DB: ${config.server}/${config.database} as ${config.user}`);
            pool = new sql.ConnectionPool(config);
            await pool.connect();
            console.log("DB Connection Pool Established.");
            // Clear the promise once connected
            poolConnectPromise = null;
            resolve(pool);
        }
        catch (err) {
            console.error('Database Connection Failed!', err);
            pool = null; // Reset pool on error
            poolConnectPromise = null; // Reset promise on error
            reject(err);
        }
    });
    return poolConnectPromise;
};
async function executeQuery(query, params) {
    const currentPool = await getPool(); // Ensure pool is connected
    const request = currentPool.request(); // Get a request object from the pool
    if (params) {
        for (const key in params) {
            let sqltype;
            const value = params[key];
            // Basic type inference (add more as needed)
            if (value === null || value === undefined) {
                // Default to NVarChar for null/undefined unless explicitly typed
                // Or handle based on expected column type if possible
                sqltype = sql.NVarChar;
            }
            else if (typeof value === 'string') {
                sqltype = sql.NVarChar;
            }
            else if (typeof value === 'number') {
                // Check if integer or float? For now, assume Int
                sqltype = sql.Int;
            }
            else if (typeof value === 'boolean') {
                sqltype = sql.Bit;
            }
            else if (value instanceof Date) {
                sqltype = sql.DateTime2;
            }
            else {
                sqltype = sql.NVarChar; // Default fallback
            }
            request.input(key, sqltype, value);
        }
    }
    try {
        const result = await request.query(query);
        return result;
    }
    catch (err) {
        console.error('SQL Error executing query:', query, 'Params:', params, 'Error:', err);
        throw err; // Re-throw to be handled by the calling Function
    }
}
exports.executeQuery = executeQuery;
// Optional: Close pool on process exit (useful for local dev)
process.on('exit', async () => {
    if (pool) {
        console.log("Closing DB connection pool...");
        await pool.close();
        console.log("DB connection pool closed.");
    }
});
//# sourceMappingURL=db.js.map