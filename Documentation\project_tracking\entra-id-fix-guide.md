# Entra ID Fix Implementation Guide

**Date**: January 27, 2025  
**Status**: Ready for execution
**Approach**: Dual strategy (Immediate fix + Automatic capture)

## 🎯 **Problem Identified**

**Critical Issue**: 17 out of 19 users in the database have placeholder/dummy Entra IDs instead of real Azure Object IDs:

- ❌ **Placeholder Examples**: `placeholder-1748364075554-gytpnm040`, `simulated-guid-12345`, `00000000-0000-0000-0000-000000000001`
- ✅ **Real Examples**: `52177d1e-5363-48e2-9f56-e95acbcdc727`, `fe018878-8522-49b0-9fc1-9f196305998e`
- 🚨 **Impact**: Multi-tenant authentication cannot work with placeholder values

## 🛠️ **Dual Solution Strategy**

### **Option 1: Immediate Fix (Manual Update)**
**Purpose**: Fix critical Avirata users immediately for testing
**Method**: Direct database update with real Azure Object IDs
**Timeline**: Immediate (within minutes)

**Users to Fix**:
- `<EMAIL>`
- `karthik<PERSON>.<EMAIL>` 
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Script Location**: `azure-functions/falcon-api/fix-avirata-entra-ids.js`

### **Option 2: Automatic Capture (Smart System)**
**Purpose**: Fix all users automatically when they login for the first time
**Method**: Azure authentication claims extraction + database update
**Timeline**: Ongoing (fixes users as they login)

**How It Works**:
1. User logs in through Azure Entra ID
2. `GetCurrentUser` function extracts real Object ID from authentication claims
3. System looks up user by email (fallback for placeholder data)
4. Automatically updates database with real Entra ID and Tenant ID
5. Future logins use proper composite key `(EntraID, TenantID)`

## 🔧 **Implementation Details**

### **Option 1: Immediate Fix Process**

1. **Get Real Entra IDs** from Azure Portal:
   - Azure Portal → Entra ID → Users → [User Profile] → Object ID
   - Copy the complete GUID (e.g., `6b67bf93-fada-466a-xxxx-xxxxxxxxxxxx`)

2. **Update Fix Script**:
   ```javascript
   const correctedEntraIds = {
     '<EMAIL>': 'COMPLETE_GUID_HERE',
     // ... other users
   };
   ```

3. **Execute Update**:
   ```bash
   cd azure-functions/falcon-api
   node fix-avirata-entra-ids.js
   ```

4. **Verify Results**: Script will show before/after comparison

### **Option 2: Automatic Capture (Already Implemented)**

**Location**: `azure-functions/falcon-api/src/functions/GetCurrentUser.ts`

**Key Features**:
- ✅ **Claims Extraction**: Gets real Object ID from `oid` claim
- ✅ **Tenant Detection**: Gets real Tenant ID from `tid` claim  
- ✅ **Fallback Logic**: Finds users by email if composite key fails
- ✅ **Auto-Update**: Calls `updateUserEntraIdAndTenant()` to fix placeholders
- ✅ **Company Mapping**: Determines company from email domain or tenant

**Code Flow**:
```javascript
// 1. Extract real IDs from Azure claims
const entraId = principal.claims?.find(claim => claim.typ === 'oid')?.val;
const tenantId = principal.claims?.find(claim => claim.typ === 'tid')?.val;

// 2. Try composite key lookup first
let user = await userManagementService.getPortalUserByEntraIdAndTenant(entraId, tenantId);

// 3. Fallback to email lookup for placeholder data
if (!user && email) {
    user = await userManagementService.getPortalUserByEmail(email);
    
    // 4. Auto-fix placeholder values
    if (user && user.internalId) {
        await userManagementService.updateUserEntraIdAndTenant(user.internalId, entraId, tenantId);
    }
}
```

## 📋 **Execution Plan**

### **Phase 1: Immediate Fix (Today)**
1. ⏳ **Get complete Entra IDs** for 6 Avirata users from Azure Portal
2. ⏳ **Update fix script** with real GUIDs
3. ⏳ **Execute database update** via Node.js script
4. ⏳ **Update development simulation** in GetCurrentUser.ts with real ID for chetan.pal
5. ⏳ **Test multi-tenant authentication** with fixed users

### **Phase 2: Verification (Today)**
1. ✅ **Verify automatic capture** system is working
2. ✅ **Test with SASMOS users** (should auto-fix on login)
3. ✅ **Document tenant mapping** results
4. ✅ **Update project status** documentation

### **Phase 3: Production Readiness (Next)**
1. 🔄 **Deploy updated backend** functions to Azure
2. 🔄 **Test end-to-end authentication** flows
3. 🔄 **Monitor automatic updates** through logging
4. 🔄 **Scale to remaining companies** (WestWire, LiDER, etc.)

## 🎯 **Expected Outcomes**

### **Immediate Benefits**:
- ✅ **Avirata users can authenticate** with real Azure Object IDs
- ✅ **Multi-tenant composite key lookup** works properly
- ✅ **Development testing** can proceed with real data
- ✅ **Database integrity** restored for critical users

### **Long-term Benefits**:
- ✅ **All users auto-fixed** on first login
- ✅ **No manual intervention** needed for new users
- ✅ **Scalable solution** for all SASMOS Group companies
- ✅ **Audit trail** of all Entra ID updates

## 🚨 **Critical Dependencies**

### **For Immediate Fix**:
- ⚠️ **Complete Entra IDs** needed from user (GUIDs are cut off in image)
- ⚠️ **Database access** (using existing Azure Functions credentials)
- ⚠️ **Testing environment** ready for verification

### **For Automatic Capture**:
- ✅ **Azure Entra ID authentication** configured
- ✅ **Claims extraction** working in GetCurrentUser
- ✅ **Database update methods** implemented
- ✅ **Error handling** in place

## 📊 **Success Metrics**

### **Immediate Success**:
- [ ] All 6 Avirata users have real Entra IDs
- [ ] Development authentication <NAME_EMAIL>
- [ ] Database verification shows no placeholder values for Avirata users

### **System Success**:
- [ ] SASMOS users auto-update on first login
- [ ] Composite key lookups work for all corrected users
- [ ] No authentication failures due to placeholder IDs

## 🔄 **Next Steps After Fix**

1. **Deploy Backend Updates**: Push enhanced GetCurrentUser function to Azure
2. **Test Multi-Tenant Flows**: Verify users from different companies can authenticate
3. **Get Missing Tenant IDs**: Obtain tenant IDs for WestWire and LiDER Technologies  
4. **Implement Role-Based Access**: Continue with RBAC development
5. **Email Service Updates**: Configure tenant-specific email contexts

---

**Status**: ⏳ Waiting for complete Entra IDs from user to execute Phase 1 