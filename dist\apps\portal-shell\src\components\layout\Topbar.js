"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const feather_icons_react_1 = require("feather-icons-react");
const Topbar = () => {
    // TODO: Implement search functionality
    // TODO: Implement notification panel
    // TODO: Implement user menu/profile link
    return (<header className="h-16 bg-white shadow-md flex items-center justify-between px-6 fixed top-0 right-0 left-64 z-10">
      {/* Search Bar */}
      <div className="relative">
        <feather_icons_react_1.Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"/>
        <input type="search" placeholder="Search..." className="pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"/>
      </div>

      {/* Right side icons/actions */}
      <div className="flex items-center space-x-4">
        <button className="text-gray-600 hover:text-gray-800 relative">
          <feather_icons_react_1.Bell size={20}/>
          {/* TODO: Add notification count badge */}
          {/* <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span> */}
        </button>
        <button className="text-gray-600 hover:text-gray-800">
          <feather_icons_react_1.User size={20}/>
        </button>
        {/* TODO: Replace button with User Menu Dropdown */}
      </div>
    </header>);
};
exports.default = Topbar;
//# sourceMappingURL=Topbar.js.map