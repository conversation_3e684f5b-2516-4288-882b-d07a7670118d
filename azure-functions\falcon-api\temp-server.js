const express = require('express');
const cors = require('cors');
const app = express();
const port = 7071;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Mock API endpoints
app.get('/api/current-user', (req, res) => {
    res.json({
        id: "simulated-guid-12345",
        internalId: 1,
        name: "Chetan Pal",
        email: "<EMAIL>",
        company: "Avirata Defence Systems",
        companyId: 1,
        roles: ['Employee', 'Admin'],
        status: 'Active',
        tenantId: "ecb4a448-4a99-443b-aaff-063150b6c9ea",
        lastLogin: new Date().toISOString()
    });
});

app.get('/api/change-requests', (req, res) => {
    res.json([]);
});

app.get('/api/change-management/dashboard-stats', (req, res) => {
    res.json({
        pending: 0,
        approved: 0,
        rejected: 0,
        completed: 0
    });
});

app.get('/api/zoho-desk/tickets', (req, res) => {
    res.json([]);
});

app.get('/api/roles', (req, res) => {
    res.json([
        { id: 1, name: 'Admin', description: 'Administrator' },
        { id: 2, name: 'Employee', description: 'Employee' }
    ]);
});

// Default route
app.get('/', (req, res) => {
    res.send(`
        <h1>Falcon Hub API Server</h1>
        <p>Temporary Express.js server running on port ${port}</p>
        <p>Available endpoints:</p>
        <ul>
            <li>/api/current-user</li>
            <li>/api/change-requests</li>
            <li>/api/change-management/dashboard-stats</li>
            <li>/api/zoho-desk/tickets</li>
            <li>/api/roles</li>
        </ul>
    `);
});

app.listen(port, () => {
    console.log(`🚀 Falcon Hub API server running on http://localhost:${port}`);
    console.log('📝 This is a temporary server while Azure Functions is being fixed');
}); 