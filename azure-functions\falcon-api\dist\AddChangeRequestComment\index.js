"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addChangeRequestComment = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const emailService_1 = require("../shared/services/emailService");
const sql = __importStar(require("mssql"));
async function addChangeRequestComment(request, context) {
    context.log('AddChangeRequestComment function invoked.');
    try {
        // Get the request ID from the route parameter
        const requestId = request.params.requestId;
        const body = await request.json();
        const { comment, commentType = 'General', isInternal = false, parentCommentId, userId } = body;
        if (!requestId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Request ID is required'
                    }
                }
            };
        }
        if (!comment || !comment.trim()) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Comment text is required'
                    }
                }
            };
        }
        if (!userId) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'User ID is required'
                    }
                }
            };
        }
        // Validate comment type
        const validCommentTypes = ['General', 'ApprovalNote', 'DevUpdate', 'Question', 'Answer'];
        if (!validCommentTypes.includes(commentType)) {
            return {
                status: 400,
                jsonBody: {
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: `Invalid comment type. Must be one of: ${validCommentTypes.join(', ')}`
                    }
                }
            };
        }
        // First, verify the change request exists
        const checkQuery = `
            SELECT RequestID, Status 
            FROM ChangeRequests 
            WHERE RequestID = @requestId
        `;
        const checkParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: {
                    error: {
                        code: 'NOT_FOUND',
                        message: 'Change request not found'
                    }
                }
            };
        }
        // If parentCommentId is provided, verify it exists
        if (parentCommentId) {
            const parentCheckQuery = `
                SELECT CommentID 
                FROM ChangeRequestComments 
                WHERE CommentID = @parentCommentId AND RequestID = @requestId
            `;
            const parentCheckParams = [
                { name: 'parentCommentId', type: sql.Int, value: parseInt(parentCommentId) },
                { name: 'requestId', type: sql.Int, value: parseInt(requestId) }
            ];
            const parentCheckResult = await (0, db_1.executeQuery)(parentCheckQuery, parentCheckParams);
            if (parentCheckResult.recordset.length === 0) {
                return {
                    status: 400,
                    jsonBody: {
                        error: {
                            code: 'VALIDATION_ERROR',
                            message: 'Parent comment not found'
                        }
                    }
                };
            }
        }
        // Add the comment
        const insertQuery = `
            INSERT INTO ChangeRequestComments (
                RequestID, CommentText, CommentType, IsInternal, ParentCommentID, CreatedBy, CreatedDate
            )
            OUTPUT INSERTED.CommentID
            VALUES (
                @requestId, @commentText, @commentType, @isInternal, @parentCommentId, @userId, GETDATE()
            )
        `;
        const insertParams = [
            { name: 'requestId', type: sql.Int, value: parseInt(requestId) },
            { name: 'commentText', type: sql.NVarChar, value: comment.trim() },
            { name: 'commentType', type: sql.NVarChar, value: commentType },
            { name: 'isInternal', type: sql.Bit, value: isInternal },
            { name: 'parentCommentId', type: sql.Int, value: parentCommentId ? parseInt(parentCommentId) : null },
            { name: 'userId', type: sql.Int, value: parseInt(userId) }
        ];
        const insertResult = await (0, db_1.executeQuery)(insertQuery, insertParams);
        const newCommentId = insertResult.recordset[0].CommentID;
        // Get the newly created comment with user details
        const getCommentQuery = `
            SELECT 
                c.CommentID as commentId,
                c.RequestID as requestId,
                c.CommentText as commentText,
                c.CommentType as commentType,
                c.IsInternal as isInternal,
                c.ParentCommentID as parentCommentId,
                c.CreatedBy as createdBy,
                CONCAT(u.FirstName, ' ', u.LastName) as createdByName,
                u.Email as createdByEmail,
                c.CreatedDate as createdDate,
                c.ModifiedBy as modifiedBy,
                c.ModifiedDate as modifiedDate,
                c.IsEdited as isEdited,
                cr.RequestNumber as requestNumber,
                cr.Title as title,
                cr.Description as description,
                cr.Priority as priority,
                cr.Status as status,
                cr.CreatedDate as requestCreatedDate,
                cr.RequestedCompletionDate as dueDate,
                CONCAT(requester.FirstName, ' ', requester.LastName) as requesterName,
                requester.Email as requesterEmail,
                CONCAT(assignee.FirstName, ' ', assignee.LastName) as assigneeName,
                assignee.Email as assigneeEmail,
                CONCAT(companies.CompanyName) as companyName
            FROM ChangeRequestComments c
                LEFT JOIN Users u ON c.CreatedBy = u.UserID
                LEFT JOIN ChangeRequests cr ON c.RequestID = cr.RequestID
                LEFT JOIN Users requester ON cr.RequestedBy = requester.UserID
                LEFT JOIN Users assignee ON cr.AssignedToDevID = assignee.UserID
                LEFT JOIN Companies companies ON requester.CompanyID = companies.CompanyID
            WHERE c.CommentID = @commentId
        `;
        const getCommentParams = [
            { name: 'commentId', type: sql.Int, value: newCommentId }
        ];
        const commentResult = await (0, db_1.executeQuery)(getCommentQuery, getCommentParams);
        const commentDetails = commentResult.recordset[0];
        // Send email notification for comment addition (only for non-internal comments)
        if (!isInternal) {
            try {
                const emailData = {
                    requestId: commentDetails.requestId,
                    requestNumber: commentDetails.requestNumber,
                    title: commentDetails.title,
                    description: commentDetails.description,
                    priority: commentDetails.priority,
                    status: commentDetails.status,
                    requesterName: commentDetails.requesterName,
                    requesterEmail: commentDetails.requesterEmail,
                    assigneeName: commentDetails.assigneeName,
                    assigneeEmail: commentDetails.assigneeEmail,
                    companyName: commentDetails.companyName || 'SASMOS Group',
                    comments: commentDetails.commentText,
                    actionUrl: `${process.env.FALCON_PORTAL_URL || 'https://falconhub.azurewebsites.net'}/change-management/${commentDetails.requestId}`,
                    createdDate: commentDetails.requestCreatedDate,
                    dueDate: commentDetails.dueDate
                };
                emailService_1.EmailService.getInstance().sendChangeRequestCommentAdded(emailData).catch((error) => {
                    context.error('Failed to send comment notification email:', error);
                });
                context.log(`Email notification queued for comment ${newCommentId} on change request ${requestId}`);
            }
            catch (emailError) {
                context.error('Error preparing email notification:', emailError);
                // Don't fail the comment addition if email fails
            }
        }
        context.log(`Successfully added comment ${newCommentId} to change request ${requestId}`);
        return {
            status: 201,
            jsonBody: {
                success: true,
                message: 'Comment added successfully',
                data: commentDetails
            }
        };
    }
    catch (error) {
        context.error('Error in AddChangeRequestComment:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while adding the comment',
                    details: process.env.NODE_ENV === 'development' ? (error?.message || 'Unknown error') : undefined
                }
            }
        };
    }
}
exports.addChangeRequestComment = addChangeRequestComment;
functions_1.app.http('AddChangeRequestComment', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'change-requests/{requestId}/comments',
    handler: addChangeRequestComment
});
//# sourceMappingURL=index.js.map