"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testFunc = void 0;
const functions_1 = require("@azure/functions");
async function testFunc(request, context) {
    context.log(`Http function TestFunc processed request for url "${request.url}"`);
    return {
        status: 200,
        jsonBody: {
            message: "TestFunc executed successfully!"
        }
    };
}
exports.testFunc = testFunc;
functions_1.app.http('TestFunc', {
    methods: ['GET'],
    authLevel: 'anonymous',
    route: 'test-func',
    handler: testFunc
});
//# sourceMappingURL=index.js.map