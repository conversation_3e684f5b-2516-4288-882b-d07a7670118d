/* =============================================
   REVERT Database Changes - Undo the Mess
   ============================================= */

/* Remove the fields I added that broke the working calendar */
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ChangeRequests' AND COLUMN_NAME = 'DeploymentDate')
BEGIN
    PRINT 'Removing the deployment date fields that broke the calendar...';
    
    /* Drop the index I created */
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChangeRequests_DeploymentDate')
    BEGIN
        DROP INDEX IX_ChangeRequests_DeploymentDate ON ChangeRequests;
        PRINT 'Removed deployment date index.';
    END
    
    /* Drop the constraints I added */
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentLocation')
    BEGIN
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentLocation;
        PRINT 'Removed deployment location constraint.';
    END
    
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentType')
    BEGIN
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentType;
        PRINT 'Removed deployment type constraint.';
    END
    
    /* Remove the columns I added */
    ALTER TABLE ChangeRequests 
    DROP COLUMN 
        DeploymentDate,
        DeploymentDuration,
        DeploymentLocation,
        DeploymentType,
        CalendarColor,
        RequiresSystemDowntime;

    PRINT 'Removed all deployment date fields that I incorrectly added.';
END
ELSE
BEGIN
    PRINT 'Deployment date fields do not exist - nothing to revert.';
END

PRINT 'Database reverted to working state before I broke it!'; 