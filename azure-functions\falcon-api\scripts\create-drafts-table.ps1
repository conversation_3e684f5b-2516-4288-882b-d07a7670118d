# PowerShell script to create ChangeRequestDrafts table
# This script uses SqlServer module to execute SQL commands

param(
    [string]$ServerName = "localhost",
    [string]$DatabaseName = "falcon_portal_dev",
    [string]$ConnectionString = ""
)

# SQL to create ChangeRequestDrafts table
$CreateTableSQL = @"
-- Create ChangeRequestDrafts table if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ChangeRequestDrafts')
BEGIN
    CREATE TABLE ChangeRequestDrafts (
        DraftID NVARCHAR(50) PRIMARY KEY,
        Title NVARCHAR(255) NOT NULL,
        Description NVARCHAR(MAX),
        TypeID INT NOT NULL,
        Priority NVARCHAR(20) NOT NULL,
        BusinessJustification NVARCHAR(MAX),
        ExpectedBenefit NVARCHAR(MAX),
        RequestedCompletionDate DATE,
        RichContent NVARCHAR(MAX), -- JSON string containing rich content blocks
        RequestedBy INT NOT NULL,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    -- Create indexes for better performance
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_RequestedBy 
        ON ChangeRequestDrafts(RequestedBy);
    
    CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_LastModified 
        ON ChangeRequestDrafts(LastModified DESC);
    
    PRINT 'Created table: ChangeRequestDrafts';
END
ELSE
BEGIN
    PRINT 'Table ChangeRequestDrafts already exists';
END
"@

try {
    # Check if SqlServer module is available
    if (!(Get-Module -ListAvailable -Name SqlServer)) {
        Write-Host "SqlServer module not found. Installing..." -ForegroundColor Yellow
        Install-Module -Name SqlServer -Force -AllowClobber -Scope CurrentUser
    }

    Import-Module SqlServer -Force

    # Build connection string if not provided
    if ([string]::IsNullOrEmpty($ConnectionString)) {
        $ConnectionString = "Server=$ServerName;Database=$DatabaseName;Integrated Security=true;TrustServerCertificate=true;"
    }

    Write-Host "Connecting to database: $DatabaseName on $ServerName" -ForegroundColor Green
    
    # Execute the SQL
    Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $CreateTableSQL -Verbose
    
    Write-Host "ChangeRequestDrafts table creation completed successfully!" -ForegroundColor Green
    
    # Verify table was created
    $VerifySQL = "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ChangeRequestDrafts'"
    $Result = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $VerifySQL
    
    if ($Result.TableCount -gt 0) {
        Write-Host "✅ ChangeRequestDrafts table verified successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Table verification failed!" -ForegroundColor Red
    }
}
catch {
    Write-Host "Error creating table: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to create the table manually using SQL Server Management Studio" -ForegroundColor Yellow
    
    Write-Host "`nSQL to execute manually:" -ForegroundColor Yellow
    Write-Host $CreateTableSQL -ForegroundColor Cyan
}
