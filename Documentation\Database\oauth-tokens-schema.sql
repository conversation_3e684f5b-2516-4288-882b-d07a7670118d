-- OAuth Token Persistent Storage Schema
-- Replaces global.zohoTokens in-memory storage for production

-- Main OAuth Tokens Table
CREATE TABLE OAuthTokens (
    TokenId INT IDENTITY(1,1) PRIMARY KEY,
    UserId NVARCHAR(255) NOT NULL,                    -- User identifier (dev-user, etc.)
    ServiceProvider NVARCHAR(100) NOT NULL,          -- 'zoho'
    ServiceType NVARCHAR(100) NOT NULL,              -- 'desk'
    AccessToken NVARCHAR(MAX) NOT NULL,              -- OAuth access token
    RefreshToken NVARCHAR(MAX) NULL,                 -- OAuth refresh token
    ExpiresAt DATETIME2 NOT NULL,                    -- Token expiration timestamp
    Scope NVARCHAR(500) NULL,                        -- OAuth scopes granted
    TokenType NVARCHAR(50) DEFAULT 'Bearer',         -- Token type
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1                           -- Active/revoked status
);

-- Create indexes for OAuthTokens table
CREATE INDEX IX_OAuthTokens_User_Service ON OAuthTokens (UserId, ServiceProvider, ServiceType);
CREATE INDEX IX_OAuthTokens_Expiry ON OAuthTokens (ExpiresAt);
CREATE INDEX IX_OAuthTokens_Active ON OAuthTokens (IsActive);

-- OAuth Configuration Table (for client credentials, etc.)
CREATE TABLE OAuthConfigurations (
    ConfigId INT IDENTITY(1,1) PRIMARY KEY,
    ServiceProvider NVARCHAR(100) NOT NULL,          -- 'zoho'
    ServiceType NVARCHAR(100) NOT NULL,              -- 'desk'
    ConfigKey NVARCHAR(255) NOT NULL,                -- 'client_id', 'client_secret', etc.
    ConfigValue NVARCHAR(MAX) NOT NULL,              -- Encrypted configuration value
    IsEncrypted BIT DEFAULT 1,                       -- Whether value is encrypted
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETDATE()
);

-- Create unique constraint for OAuthConfigurations
CREATE UNIQUE INDEX IX_OAuthConfig_Service_Key ON OAuthConfigurations (ServiceProvider, ServiceType, ConfigKey);

-- Token Usage Audit Log
CREATE TABLE OAuthTokenUsage (
    UsageId INT IDENTITY(1,1) PRIMARY KEY,
    TokenId INT NOT NULL,
    UsedAt DATETIME2 DEFAULT GETDATE(),
    RequestEndpoint NVARCHAR(500) NULL,              -- API endpoint accessed
    ResponseStatus INT NULL,                         -- HTTP response status
    ErrorMessage NVARCHAR(MAX) NULL,                 -- Error details if any
    
    CONSTRAINT FK_OAuthTokenUsage_TokenId FOREIGN KEY (TokenId) REFERENCES OAuthTokens(TokenId)
);

-- Insert initial configuration for ZohoDesk
-- These will be encrypted in production
INSERT INTO OAuthConfigurations (ServiceProvider, ServiceType, ConfigKey, ConfigValue, IsEncrypted) VALUES
('zoho', 'desk', 'client_id', '1000.03IZH9ZAA9U4H8OTTE0J2LNKB3U9VO', 0),
('zoho', 'desk', 'client_secret', '1acd0b7dfbbbb332a1b226a97fae998cfe4a28a3f3', 1),
('zoho', 'desk', 'redirect_uri', 'http://localhost:7071/api/auth/zoho-desk/callback', 0),
('zoho', 'desk', 'base_url', 'https://accounts.zoho.in/oauth/v2', 0),
('zoho', 'desk', 'api_base_url', 'https://desk.zoho.in/api/v1', 0);

GO

-- Stored procedure to get active token for user
CREATE PROCEDURE GetActiveOAuthToken
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
AS
BEGIN
    SELECT TOP 1 
        TokenId,
        AccessToken,
        RefreshToken,
        ExpiresAt,
        Scope,
        TokenType
    FROM OAuthTokens 
    WHERE UserId = @UserId 
        AND ServiceProvider = @ServiceProvider 
        AND ServiceType = @ServiceType
        AND IsActive = 1
        AND ExpiresAt > GETDATE()
    ORDER BY CreatedAt DESC;
END;

GO

-- Stored procedure to save/update OAuth token
CREATE PROCEDURE SaveOAuthToken
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100),
    @AccessToken NVARCHAR(MAX),
    @RefreshToken NVARCHAR(MAX) = NULL,
    @ExpiresIn INT = 3600,
    @Scope NVARCHAR(500) = NULL,
    @TokenType NVARCHAR(50) = 'Bearer'
AS
BEGIN
    DECLARE @ExpiresAt DATETIME2 = DATEADD(SECOND, @ExpiresIn, GETDATE());
    
    -- Deactivate existing tokens for this user/service
    UPDATE OAuthTokens 
    SET IsActive = 0, UpdatedAt = GETDATE()
    WHERE UserId = @UserId 
        AND ServiceProvider = @ServiceProvider 
        AND ServiceType = @ServiceType
        AND IsActive = 1;
    
    -- Insert new token
    INSERT INTO OAuthTokens (
        UserId, ServiceProvider, ServiceType, 
        AccessToken, RefreshToken, ExpiresAt, 
        Scope, TokenType
    ) VALUES (
        @UserId, @ServiceProvider, @ServiceType,
        @AccessToken, @RefreshToken, @ExpiresAt,
        @Scope, @TokenType
    );
    
    SELECT SCOPE_IDENTITY() AS TokenId;
END;

GO

-- Function to check if token needs refresh (expires in next 5 minutes)
CREATE FUNCTION NeedsTokenRefresh
(
    @UserId NVARCHAR(255),
    @ServiceProvider NVARCHAR(100),
    @ServiceType NVARCHAR(100)
)
RETURNS BIT
AS
BEGIN
    DECLARE @Result BIT = 0;
    DECLARE @ExpiresAt DATETIME2;
    
    SELECT @ExpiresAt = ExpiresAt
    FROM OAuthTokens 
    WHERE UserId = @UserId 
        AND ServiceProvider = @ServiceProvider 
        AND ServiceType = @ServiceType
        AND IsActive = 1;
    
    IF @ExpiresAt IS NOT NULL AND @ExpiresAt <= DATEADD(MINUTE, 5, GETDATE())
        SET @Result = 1;
    
    RETURN @Result;
END; 