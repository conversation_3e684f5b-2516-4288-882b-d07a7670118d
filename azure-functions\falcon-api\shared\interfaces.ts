// shared/interfaces.ts

/**
 * Database representation of a role
 */
export interface RoleDefinition {
    RoleID: number;
    RoleName: string;
    Description?: string; // This maps to RoleDescription in the database
    IsSystemRole?: boolean;
    IsActive?: boolean;
}

/**
 * Request body for creating a role - supports both naming conventions
 */
export interface CreateRoleRequestBody {
    // Backend naming convention
    RoleName?: string;
    Description?: string;

    // Frontend naming convention
    name?: string;
    description?: string;
}

/**
 * Request body for updating a role - supports both naming conventions
 */
export interface UpdateRoleRequestBody {
    // Backend naming convention
    RoleName?: string;
    Description?: string;

    // Frontend naming convention
    name?: string;
    description?: string;
}