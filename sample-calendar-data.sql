-- =============================================
-- Sample Calendar Data for Testing
-- Insert sample change requests with deployment dates
-- =============================================

-- Insert sample change requests with deployment dates for current month
INSERT INTO ChangeRequests (
    Title, Description, TypeID, Priority, Status, BusinessJustification, ExpectedBenefit,
    RequestedCompletionDate, RequestedBy, CompanyID, CreatedDate, ModifiedDate
) VALUES 
(
    'Database Performance Optimization - January Release',
    'Optimize database queries and add indexes for better performance',
    1, 'High', 'Approved', 'Improve application response time by 40%', 'Better user experience and reduced server load',
    '2025-01-30', 1, 4, GETDATE(), GETDATE()
),
(
    'Security Patch Deployment - Critical',
    'Deploy critical security patches to production environment',
    2, 'Critical', 'Ready for Deployment', 'Address critical security vulnerabilities', 'Maintain system security compliance',
    '2025-01-28', 1, 4, GETDATE(), GETDATE()
),
(
    'Mobile App Update - Version 2.1',
    'Deploy new mobile app features and bug fixes',
    1, 'Medium', 'Approved', 'User-requested features and stability improvements', 'Increased user satisfaction and retention',
    '2025-02-02', 1, 4, GETDATE(), GETDATE()
),
(
    'Network Infrastructure Upgrade',
    'Upgrade network switches and improve bandwidth',
    5, 'High', 'In Development', 'Support growing user base and data throughput', 'Improved network performance and reliability',
    '2025-02-05', 1, 4, GETDATE(), GETDATE()
),
(
    'Backup System Enhancement',
    'Implement automated backup verification and reporting',
    2, 'Low', 'Approved', 'Ensure data integrity and compliance', 'Better disaster recovery capabilities',
    '2025-02-10', 1, 4, GETDATE(), GETDATE()
);

-- Verify the data was inserted
SELECT 
    RequestID, 
    RequestNumber,
    Title, 
    Status, 
    Priority, 
    RequestedCompletionDate,
    CreatedDate
FROM ChangeRequests 
WHERE CompanyID = 4 
  AND RequestedCompletionDate >= DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1)
  AND RequestedCompletionDate < DATEADD(MONTH, 2, DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1))
ORDER BY RequestedCompletionDate;

PRINT 'Sample calendar data inserted successfully!'; 