# Zoho Desk OAuth Setup Guide

## Overview
This guide will help you set up OAuth integration with Zoho Desk to get live data instead of mock/fallback data.

## Prerequisites
1. Zoho Desk account with admin access
2. Zoho Developer Console access

## Step 1: Create Zoho OAuth Application

1. Go to [Zoho Developer Console](https://api-console.zoho.in/)
2. Click "Add Client" 
3. Choose "Server-based Applications"
4. Fill in the details:
   - **Client Name**: FalconHub IT Portal
   - **Homepage URL**: http://localhost:5174
   - **Authorized Redirect URIs**: http://localhost:7075/api/auth/zoho-desk/callback
5. Click "Create"
6. Note down the **Client ID** and **Client Secret**

## Step 2: Configure Environment Variables

Add these environment variables to your Azure Functions configuration:

```bash
ZOHO_DESK_CLIENT_ID=your_client_id_here
ZOHO_DESK_CLIENT_SECRET=your_client_secret_here
ZOHO_DESK_ORG_ID=***********
ZOHO_DESK_REDIRECT_URI=http://localhost:7075/api/auth/zoho-desk/callback
```

## Step 3: Get Authorization

1. Start your backend: `cd azure-functions/falcon-api && func start --port 7075`
2. Get the authorization URL: `curl http://localhost:7075/api/auth/zoho-desk/authorize`
3. Visit the returned `authUrl` in your browser
4. Grant permissions to your application
5. You'll be redirected to the callback URL with an authorization code

## Step 4: Test the Integration

Once authorized, test the endpoints:

```bash
# Test categories
curl http://localhost:7075/api/zoho-desk/categories

# Test departments  
curl http://localhost:7075/api/zoho-desk/departments

# Test subcategories
curl http://localhost:7075/api/zoho-desk/subcategories

# Test tickets
curl http://localhost:7075/api/zoho-desk/tickets
```

## Step 5: Create a Test Ticket

```bash
curl -X POST http://localhost:7075/api/zoho-desk/tickets \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Test Ticket from FalconHub",
    "description": "This is a test ticket created via API",
    "priority": "Medium",
    "departmentId": 1,
    "contactId": "auto-create"
  }'
```

## Troubleshooting

### Common Issues:

1. **401 Unauthorized**: You need to complete the OAuth flow first
2. **Invalid Client**: Check your Client ID and Client Secret
3. **Invalid Redirect URI**: Make sure the redirect URI matches exactly
4. **Token Expired**: Re-authorize using the authorization URL

### Debug Steps:

1. Check backend logs for detailed error messages
2. Verify your Zoho Desk organization ID
3. Ensure your Zoho account has proper permissions
4. Test with Zoho's API documentation examples

## API Endpoints

- **Authorization**: `GET /api/auth/zoho-desk/authorize`
- **Callback**: `GET /api/auth/zoho-desk/callback`
- **Token**: `POST /api/auth/zoho-desk/token`
- **Categories**: `GET /api/zoho-desk/categories`
- **Departments**: `GET /api/zoho-desk/departments`
- **Subcategories**: `GET /api/zoho-desk/subcategories`
- **Tickets**: `GET /api/zoho-desk/tickets`
- **Create Ticket**: `POST /api/zoho-desk/tickets`

## Next Steps

1. Set up proper token storage (Azure Key Vault for production)
2. Implement token refresh logic
3. Add user-specific token management
4. Configure proper error handling for production use

## Production Considerations

- Use Azure Key Vault for storing client secrets
- Implement proper user session management
- Set up token refresh automation
- Add comprehensive error handling
- Configure proper CORS settings
- Use HTTPS for all OAuth flows 