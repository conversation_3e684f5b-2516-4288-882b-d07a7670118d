"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestDraftsTable = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
async function TestDraftsTable(request, context) {
    context.log('TestDraftsTable function processed a request.');
    try {
        // Get database connection
        const pool = await (0, db_1.getPool)();
        // Check if table exists
        const checkTableQuery = `
      SELECT COUNT(*) as TableExists 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'ChangeRequestDrafts'
    `;
        const tableResult = await pool.request().query(checkTableQuery);
        const tableExists = tableResult.recordset[0].TableExists > 0;
        let draftsCount = 0;
        let tableCreated = false;
        if (!tableExists) {
            // Create the table
            const createTableQuery = `
        CREATE TABLE ChangeRequestDrafts (
          DraftID NVARCHAR(50) PRIMARY KEY,
          Title NVARCHAR(255) NOT NULL,
          Description NVARCHAR(MAX),
          TypeID INT NOT NULL,
          Priority NVARCHAR(20) NOT NULL,
          BusinessJustification NVARCHAR(MAX),
          ExpectedBenefit NVARCHAR(MAX),
          RequestedCompletionDate DATE,
          RichContent NVARCHAR(MAX), -- JSON string containing rich content blocks
          RequestedBy INT NOT NULL,
          CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
          LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
        );
        
        -- Create indexes for better performance
        CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_RequestedBy 
          ON ChangeRequestDrafts(RequestedBy);
        
        CREATE NONCLUSTERED INDEX IX_ChangeRequestDrafts_LastModified 
          ON ChangeRequestDrafts(LastModified DESC);
      `;
            await pool.request().query(createTableQuery);
            tableCreated = true;
            context.log('Created ChangeRequestDrafts table successfully');
        }
        else {
            // Count records in the table
            const countQuery = 'SELECT COUNT(*) as DraftsCount FROM ChangeRequestDrafts';
            const countResult = await pool.request().query(countQuery);
            draftsCount = countResult.recordset[0].DraftsCount;
        }
        return {
            status: 200,
            jsonBody: {
                success: true,
                tableExists: true,
                tableCreated: tableCreated,
                draftsCount: draftsCount,
                message: tableCreated ?
                    'ChangeRequestDrafts table created successfully' :
                    `ChangeRequestDrafts table exists with ${draftsCount} records`
            }
        };
    }
    catch (error) {
        context.log('Error testing/creating drafts table:', error);
        return {
            status: 500,
            jsonBody: {
                success: false,
                message: 'Error testing/creating drafts table',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}
exports.TestDraftsTable = TestDraftsTable;
functions_1.app.http('TestDraftsTable', {
    methods: ['GET'],
    authLevel: 'function',
    handler: TestDraftsTable
});
//# sourceMappingURL=index.js.map