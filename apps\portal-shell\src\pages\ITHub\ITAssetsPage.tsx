import React, { useState, useEffect } from 'react';
import { Search, Monitor, Smartphone, Printer, HardDrive, User, Package, AlertTriangle } from 'feather-icons-react';

// IT Asset interfaces
interface ITAsset {
  id: string;
  assetTag: string;
  name: string;
  type: 'Laptop' | 'Desktop' | 'Monitor' | 'Printer' | 'Mobile' | 'Tablet' | 'Server' | 'Network Equipment' | 'Peripherals';
  brand: string;
  model: string;
  serialNumber: string;
  status: 'Active' | 'Inactive' | 'Under Repair' | 'Disposed' | 'Available';
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor';
  assignedTo?: {
    name: string;
    email: string;
    company: string;
  };
  location: string;
  purchaseDate: string;
  warrantyExpiry?: string;
  cost: number;
  supplier: string;
  notes?: string;
  lastAuditDate?: string;
}

const ITAssetsPage: React.FC = () => {
  const [assets, setAssets] = useState<ITAsset[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  // const [error, setError] = useState<string | null>(null); // Unused variables

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [assignmentFilter, setAssignmentFilter] = useState<string>('all');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(15);

  // Mock data for development
  useEffect(() => {
    setIsLoading(true);

    const mockAssets: ITAsset[] = [
      {
        id: 'asset-1',
        assetTag: 'SAS-LAP-001',
        name: 'Dell Latitude 7420',
        type: 'Laptop',
        brand: 'Dell',
        model: 'Latitude 7420',
        serialNumber: '**********',
        status: 'Active',
        condition: 'Good',
        assignedTo: {
          name: 'John Doe',
          email: '<EMAIL>',
          company: 'SASMOS HET'
        },
        location: 'Mumbai Office',
        purchaseDate: '2023-06-15T00:00:00Z',
        warrantyExpiry: '2026-06-15T00:00:00Z',
        cost: 85000,
        supplier: 'Dell Technologies',
        notes: 'Standard configuration with 16GB RAM, 512GB SSD',
        lastAuditDate: '2025-01-15T00:00:00Z'
      },
      {
        id: 'asset-2',
        assetTag: 'SAS-MON-015',
        name: 'LG UltraWide Monitor',
        type: 'Monitor',
        brand: 'LG',
        model: '34WN80C-B',
        serialNumber: 'LG34WN5678',
        status: 'Active',
        condition: 'Excellent',
        assignedTo: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          company: 'Avirata Defence Systems'
        },
        location: 'Bangalore Office',
        purchaseDate: '2024-02-20T00:00:00Z',
        warrantyExpiry: '2027-02-20T00:00:00Z',
        cost: 45000,
        supplier: 'LG Electronics',
        lastAuditDate: '2025-01-10T00:00:00Z'
      },
      {
        id: 'asset-3',
        assetTag: 'SAS-PRT-008',
        name: 'HP LaserJet Pro',
        type: 'Printer',
        brand: 'HP',
        model: 'LaserJet Pro MFP M428fdw',
        serialNumber: 'HP428FDW789',
        status: 'Under Repair',
        condition: 'Fair',
        location: 'IT Store Room',
        purchaseDate: '2022-11-10T00:00:00Z',
        warrantyExpiry: '2025-11-10T00:00:00Z',
        cost: 32000,
        supplier: 'HP India',
        notes: 'Paper jam issue, sent for repair on 2025-01-25',
        lastAuditDate: '2024-12-20T00:00:00Z'
      },
      {
        id: 'asset-4',
        assetTag: 'SAS-MOB-023',
        name: 'iPhone 14 Pro',
        type: 'Mobile',
        brand: 'Apple',
        model: 'iPhone 14 Pro',
        serialNumber: 'APL14PRO456',
        status: 'Active',
        condition: 'Excellent',
        assignedTo: {
          name: 'Mike Johnson',
          email: '<EMAIL>',
          company: 'SASMOS Group'
        },
        location: 'Delhi Office',
        purchaseDate: '2024-09-15T00:00:00Z',
        warrantyExpiry: '2025-09-15T00:00:00Z',
        cost: 120000,
        supplier: 'Apple India',
        lastAuditDate: '2025-01-20T00:00:00Z'
      },
      {
        id: 'asset-5',
        assetTag: 'SAS-LAP-012',
        name: 'MacBook Pro M2',
        type: 'Laptop',
        brand: 'Apple',
        model: 'MacBook Pro 14-inch M2',
        serialNumber: 'MBP14M2789',
        status: 'Available',
        condition: 'Excellent',
        location: 'IT Store Room',
        purchaseDate: '2024-08-05T00:00:00Z',
        warrantyExpiry: '2027-08-05T00:00:00Z',
        cost: 195000,
        supplier: 'Apple India',
        notes: 'Newly purchased, ready for assignment',
        lastAuditDate: '2025-01-28T00:00:00Z'
      }
    ];

    setAssets(mockAssets);
    setIsLoading(false);
  }, []);

  // Filter assets
  const filteredAssets = assets.filter(asset => {
    const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         asset.assetTag.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         asset.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         asset.assignedTo?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || asset.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || asset.status === statusFilter;
    const matchesAssignment = assignmentFilter === 'all' ||
                             (assignmentFilter === 'assigned' && asset.assignedTo) ||
                             (assignmentFilter === 'available' && !asset.assignedTo);

    return matchesSearch && matchesType && matchesStatus && matchesAssignment;
  });

  // Pagination
  const totalPages = Math.ceil(filteredAssets.length / pageSize);
  const paginatedAssets = filteredAssets.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Helper functions
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusColor = (status: ITAsset['status']): string => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Available': return 'bg-blue-100 text-blue-800';
      case 'Under Repair': return 'bg-yellow-100 text-yellow-800';
      case 'Inactive': return 'bg-gray-100 text-gray-800';
      case 'Disposed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionColor = (condition: ITAsset['condition']): string => {
    switch (condition) {
      case 'Excellent': return 'text-green-600';
      case 'Good': return 'text-blue-600';
      case 'Fair': return 'text-yellow-600';
      case 'Poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getTypeIcon = (type: ITAsset['type']) => {
    switch (type) {
      case 'Laptop':
      case 'Desktop': return <Monitor size={16} />;
      case 'Mobile':
      case 'Tablet': return <Smartphone size={16} />;
      case 'Printer': return <Printer size={16} />;
      case 'Server':
      case 'Network Equipment': return <HardDrive size={16} />;
      default: return <Package size={16} />;
    }
  };

  const isWarrantyExpiring = (warrantyDate: string): boolean => {
    const expiry = new Date(warrantyDate);
    const now = new Date();
    const monthsUntilExpiry = (expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 30);
    return monthsUntilExpiry <= 6 && monthsUntilExpiry > 0;
  };

  const isWarrantyExpired = (warrantyDate: string): boolean => {
    return new Date(warrantyDate) < new Date();
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">IT Asset Management</h1>
        <p className="text-gray-600">Track and manage IT assets across the organization.</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Assets</p>
              <p className="text-2xl font-semibold text-gray-900">{assets.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Monitor className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.filter(a => a.status === 'Active').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <User className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Available</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.filter(a => a.status === 'Available').length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Under Repair</p>
              <p className="text-2xl font-semibold text-gray-900">
                {assets.filter(a => a.status === 'Under Repair').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-gray-500">
            {filteredAssets.length} assets found
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search assets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>

          {/* Type Filter */}
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Types</option>
            <option value="Laptop">Laptop</option>
            <option value="Desktop">Desktop</option>
            <option value="Monitor">Monitor</option>
            <option value="Printer">Printer</option>
            <option value="Mobile">Mobile</option>
            <option value="Tablet">Tablet</option>
            <option value="Server">Server</option>
            <option value="Network Equipment">Network Equipment</option>
            <option value="Peripherals">Peripherals</option>
          </select>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Available">Available</option>
            <option value="Under Repair">Under Repair</option>
            <option value="Inactive">Inactive</option>
            <option value="Disposed">Disposed</option>
          </select>

          {/* Assignment Filter */}
          <select
            value={assignmentFilter}
            onChange={(e) => setAssignmentFilter(e.target.value)}
            className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Assets</option>
            <option value="assigned">Assigned</option>
            <option value="available">Available</option>
          </select>
        </div>
      </div>

      {/* Assets List */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading assets...</p>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedAssets.length > 0 ? paginatedAssets.map((asset) => (
                  <tr key={asset.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-lg bg-gray-100 flex items-center justify-center">
                            {getTypeIcon(asset.type)}
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{asset.name}</div>
                          <div className="text-sm text-gray-500">{asset.assetTag}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{asset.type}</div>
                      <div className="text-sm text-gray-500">{asset.brand} {asset.model}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(asset.status)}`}>
                        {asset.status}
                      </span>
                      <div className={`text-xs mt-1 font-medium ${getConditionColor(asset.condition)}`}>
                        {asset.condition}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {asset.assignedTo ? (
                        <div>
                          <div className="text-sm text-gray-900">{asset.assignedTo.name}</div>
                          <div className="text-xs text-gray-500">{asset.assignedTo.company}</div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400 italic">Not assigned</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {asset.location}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {asset.warrantyExpiry ? (
                        <div>
                          <div className={`text-sm ${
                            isWarrantyExpired(asset.warrantyExpiry) ? 'text-red-600 font-medium' :
                            isWarrantyExpiring(asset.warrantyExpiry) ? 'text-yellow-600 font-medium' :
                            'text-gray-900'
                          }`}>
                            {formatDate(asset.warrantyExpiry)}
                          </div>
                          {isWarrantyExpired(asset.warrantyExpiry) && (
                            <span className="text-xs text-red-500">Expired</span>
                          )}
                          {isWarrantyExpiring(asset.warrantyExpiry) && !isWarrantyExpired(asset.warrantyExpiry) && (
                            <span className="text-xs text-yellow-600">Expiring Soon</span>
                          )}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">No warranty</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(asset.cost)}
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-8 text-center text-sm text-gray-500">
                      No assets found matching your criteria.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * pageSize, filteredAssets.length)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{filteredAssets.length}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNum === currentPage
                              ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ITAssetsPage;