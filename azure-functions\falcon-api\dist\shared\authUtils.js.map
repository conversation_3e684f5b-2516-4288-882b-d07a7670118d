{"version": 3, "file": "authUtils.js", "sourceRoot": "", "sources": ["../../src/shared/authUtils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,2CAAwC;AACxC,2CAA6B;AAC7B,6BAAoD;AACpD,4EAAuE;AAYvE;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,OAAoB;IACnD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC5D,IAAI,CAAC,MAAM,EAAE;QACT,eAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;KACf;IAED,IAAI;QACA,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAChE,MAAM,SAAS,GAAoB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACvD,mBAAmB;QACnB,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxD,eAAM,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;YACpG,OAAO,IAAI,CAAC;SAChB;QACD,OAAO,SAAS,CAAC;KACpB;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,iEAAiE,EAAE,KAAK,CAAC,CAAC;QACvF,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AApBD,gDAoBC;AAED;;;;;;;GAOG;AACH,SAAgB,eAAe,CAAC,SAAiC,EAAE,aAAuB;IACtF,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO,KAAK,CAAC,CAAC,oBAAoB;KACrC;IACD,mEAAmE;IACnE,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE;QAChE,OAAO,IAAI,CAAC;KACf;IACD,uEAAuE;IACvE,uEAAuE;IACvE,IAAI,SAAS,CAAC,MAAM,EAAE;QAClB,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC9B,CAAC,KAAK,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC;YACjD,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CACpC,EAAE;YACC,OAAO,IAAI,CAAC;SACf;KACJ;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AApBD,0CAoBC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,SAAiC;IACrD,OAAO,eAAe,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3E,CAAC;AAFD,0BAEC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,sBAAsB,CAAC,SAAiC,EAAE,OAA0B;IACtG,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;KACf;IAED,uCAAuC;IACvC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,+DAA+D,CAAC,CAAC;IAChI,MAAM,OAAO,GAAG,QAAQ,EAAE,GAAG,CAAC;IAE9B,IAAI,CAAC,OAAO,EAAE;QACV,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;QACzF,eAAM,CAAC,IAAI,CAAC,2EAA2E,EAAE,SAAS,CAAC,CAAC;QACpG,OAAO,IAAI,CAAC;KACf;IAED,IAAI;QACA,MAAM,KAAK,GAAG;;;SAGb,CAAC;QACF,MAAM,MAAM,GAAqB;YAC7B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;SAC1D,CAAC;QACF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,eAAM,CAAC,IAAI,CAAC,kDAAkD,OAAO,yBAAyB,CAAC,CAAC;YAChG,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE1C,4EAA4E;QAC5E,IAAA,2CAAmB,EAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACpC,eAAM,CAAC,KAAK,CAAC,qEAAqE,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC;YAClG,qDAAqD;QACzD,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;KACjB;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,6DAA6D,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5F,eAAM,CAAC,KAAK,CAAC,6DAA6D,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;KACf;AACL,CAAC;AA7CD,wDA6CC"}