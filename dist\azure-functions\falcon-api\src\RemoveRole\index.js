"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeRole = removeRole;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils");
const validationSchemas_1 = require("../shared/validationSchemas");
// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Portal Admin'; // Or from config/env
function removeRole(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function RemoveRole processed request for url "${request.url}"`);
        logger_1.logger.info('RemoveRole function invoked.');
        // --- Authentication & Authorization --- 
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
        }
        if (!(0, authUtils_1.hasRequiredRole)(principal, [REQUIRED_ROLE])) {
            logger_1.logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
            return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
        }
        const authenticatedUserId = yield (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
        if (!authenticatedUserId) {
            logger_1.logger.error(`RemoveRole: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
            return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
        }
        // NOTE: Using retrieved authenticatedUserId below for 'removedByUserId' field.
        // Ensure getUserIdFromPrincipal correctly retrieves the UserID from your DB 
        // based on the oid claim provided by the configured authentication provider.
        logger_1.logger.info(`RemoveRole invoked by UserID: ${authenticatedUserId}`);
        // --- End Auth --- 
        // --- Input Validation --- 
        const routeParams = {
            userId: request.params.userId,
            roleId: request.params.roleId
        }; // Extract for validation
        const validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.userRoleRouteParamsSchema, routeParams, context, "route parameters");
        if (validationError)
            return validationError;
        // Validation passed, use validated data
        const validatedRouteParams = validationSchemas_1.userRoleRouteParamsSchema.parse(routeParams); // Use parse to get typed data
        const userId = validatedRouteParams.userId; // Already a number
        const roleId = validatedRouteParams.roleId; // Already a number
        // --- End Validation --- 
        try {
            const success = yield (0, userManagementService_1.removeRoleFromUser)(userId, roleId, authenticatedUserId);
            if (success) {
                // removeRoleFromUser logs success/warning internally
                // Return 204 No Content for successful deletion/update
                return {
                    status: 204,
                };
            }
            else {
                // removeRoleFromUser handles logging the specific error
                // Return a generic server error if it failed
                return {
                    status: 500,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { message: "Failed to remove role." }
                };
            }
        }
        catch (error) {
            // This catch block is mainly for unexpected errors during the process
            logger_1.logger.error(`Unexpected error in RemoveRole function for User ${userId}, Role ${roleId} by UserID ${authenticatedUserId}:`, error);
            const errorMessage = (error instanceof Error) ? error.message : String(error);
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "An unexpected error occurred while removing the role.",
                    error: errorMessage
                }
            };
        }
    });
}
functions_1.app.http('RemoveRole', {
    methods: ['DELETE'],
    authLevel: 'anonymous', // Handled by provider + code checks
    route: 'users/{userId}/roles/{roleId}', // Match function.json
    handler: removeRole
});
//# sourceMappingURL=index.js.map