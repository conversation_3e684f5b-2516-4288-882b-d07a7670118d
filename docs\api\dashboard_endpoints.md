# Dashboard API Endpoints

This document outlines the conceptual API endpoints required for the Falcon Portal dashboard.

**Base URL:** `/api/dashboard`

---

## 1. Announcements

**Endpoint:** `GET /announcements`

**Description:** Fetches critical announcements relevant to the user.

**Query Parameters:**
*   `limit` (number, optional): Maximum number of announcements to return. Defaults to 5.
*   `companyId` (string, optional): Filter by a specific company ID. If omitted, returns announcements relevant to the user's company context (including group-wide).

**Response Body:**
```json
[
  {
    "id": "uuid", // Unique identifier for the announcement
    "title": "string", // Title of the announcement
    "description": "string", // Brief description or summary
    "scope": "Group-wide | Company Name", // Target audience (e.g., "SASMOS HET", "Group-wide")
    "publishedAt": "iso_timestamp", // When the announcement was published
    "severity": "high | medium | low", // Severity level (maps to UI styling)
    "link": "url | null" // Optional link for more details
  }
]
```

---

## 2. Pending Actions

**Endpoint:** `GET /pending-actions`

**Description:** Fetches actions assigned to the current user requiring attention.

**Query Parameters:**
*   `limit` (number, optional): Maximum number of actions to return. Defaults to 5.
*   `userId` (string, implicit): Automatically inferred from the user's authentication context.

**Response Body:**
```json
[
  {
    "id": "uuid", // Unique identifier for the action
    "type": "Approval | Review | Acknowledgment | Task", // Type of action
    "title": "string", // Concise title of the action (e.g., "Travel Request Approval")
    "source": "string", // Originator or context (e.g., "Rahul S.", "Falcon Project", "HRMS")
    "dueDate": "iso_timestamp | null", // Due date, if applicable
    "details": "string | null", // Additional short details (e.g., "Code of Conduct 2025")
    "link": "url" // Direct link to the action item within the relevant system/hub
  }
]
```

---

## 3. Quick Links

**Endpoint:** `GET /quick-links`

**Description:** Fetches the user's personalized quick links for the dashboard.

**Query Parameters:**
*   `userId` (string, implicit): Automatically inferred from the user's authentication context.

**Response Body:**
```json
[
  {
    "id": "uuid", // Unique identifier for the quick link configuration
    "title": "string", // Display title for the link
    "icon": "feather-icon-name", // Name of the Feather icon to display (e.g., "FileText")
    "targetHub": "it | admin | hr | knowledge | communication | external", // Target hub or type
    "url": "string" // The URL the quick link points to
  }
]
```

---

## 4. Recent Documents

**Endpoint:** `GET /recent-documents`

**Description:** Fetches documents recently accessed or relevant to the user.

**Query Parameters:**
*   `limit` (number, optional): Maximum number of documents to return. Defaults to 5.
*   `userId` (string, implicit): Automatically inferred from the user's authentication context.

**Response Body:**
```json
[
  {
    "id": "uuid", // Unique identifier for the document (or document reference)
    "name": "string", // Name of the document
    "lastOpenedAt": "iso_timestamp", // Timestamp when the user last opened it
    "link": "url" // Direct link to open the document
  }
]
```

---

## 5. Upcoming Events

**Endpoint:** `GET /upcoming-events`

**Description:** Fetches upcoming events relevant to the user.

**Query Parameters:**
*   `limit` (number, optional): Maximum number of events to return. Defaults to 3.
*   `companyId` (string, optional): Filter by a specific company ID. If omitted, returns events relevant to the user's company context (including group-wide).
*   `startDate` (iso_date, optional): Start date to fetch events from. Defaults to today.

**Response Body:**
```json
[
  {
    "id": "uuid", // Unique identifier for the event
    "title": "string", // Title of the event
    "startDateTime": "iso_timestamp", // Start date and time
    "endDateTime": "iso_timestamp | null", // End date and time, if applicable
    "location": "string", // Location (e.g., "Virtual", "Training Room B")
    "scope": "Group-wide | Company Name", // Target audience
    "link": "url | null" // Optional link for more details or joining the event
  }
]
``` 