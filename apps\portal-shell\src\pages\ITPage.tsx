import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  fetchITQuickActions, fetchMyAssets, fetchITPolicies 
} from '../services/itHubApi';
import { changeManagementApi, type DashboardStats } from '../services/changeManagementApi';
import { useMsal } from '@azure/msal-react';
import { loginRequest } from '../authConfig';
import { Monitor, Tool, AlertCircle, CheckCircle, GitBranch, Plus, Eye, Search } from 'feather-icons-react';
import ChangeManagementCalendar from '../components/Calendar/ChangeManagementCalendar';

const ITPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [ticketStats, setTicketStats] = useState({
    open: 0,
    inProgress: 0,
    resolvedThisWeek: 0
  });
  const [changeStats, setChangeStats] = useState<DashboardStats | null>(null);
  const [changeStatsLoading, setChangeStatsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { instance } = useMsal();

  // Function to get real ticket statistics from backend API
  const fetchRealTicketStats = async () => {
    try {
      console.log('=== DASHBOARD TICKET FETCH START ===');
      console.log('Fetching real ticket data from backend...');
      
      // Get MSAL token for authentication
      const baseHeaders: Record<string, string> = {
        'Content-Type': 'application/json'
      };
      let authHeaders = baseHeaders;

      // Try to get MSAL token if available
      try {
        const accounts = instance.getAllAccounts();
        if (accounts.length > 0) {
          const tokenRequest = {
            ...loginRequest,
            account: accounts[0],
          };
          
          const response = await instance.acquireTokenSilent(tokenRequest);
          if (response.idToken) {
            authHeaders = {
              ...baseHeaders,
              'Authorization': `Bearer ${response.idToken}`
            };
            console.log('✅ ITPage Dashboard: Added MSAL ID token to request');
          }
        }
      } catch (tokenError) {
        console.warn('⚠️ ITPage Dashboard: Failed to acquire MSAL token:', tokenError);
        // Continue without token - backend will generate unique email
      }
      
      // Primary endpoint - Zoho Desk API via our backend
      // IMPORTANT: Remove limit parameter to get ALL user tickets for accurate dashboard statistics
      // The backend filters by user email, so we'll only get the current user's tickets
      const response = await fetch('/api/zoho-desk/tickets?include=contacts,departments,assignee', {
        headers: authHeaders
      });

      console.log('API Response Status:', response.status);
      console.log('API Response OK:', response.ok);

      if (response.ok) {
        const data = await response.json();
        console.log('=== RAW BACKEND RESPONSE ===');
        console.log('Backend response:', data);
        
        // Handle different response formats
        let ticketList = [];
        if (data.data && Array.isArray(data.data)) {
          ticketList = data.data;
          console.log('✅ Using data.data array format');
        } else if (Array.isArray(data)) {
          ticketList = data;
          console.log('✅ Using direct array format');
        } else {
          console.warn('❌ Unexpected response format:', data);
          throw new Error('Invalid response format');
        }
        
        console.log('=== TICKET LIST ANALYSIS ===');
        console.log(`Total tickets found: ${ticketList.length}`);
        
        if (ticketList.length > 0) {
          console.log('Sample ticket data:', ticketList[0]);
          console.log('All ticket statuses:', ticketList.map((t: { status?: string }) => t.status));
        }
        
        // Connection successful - calculate stats from ALL user tickets
        console.log(`ZohoDesk connection successful. Found ${ticketList.length} tickets for current user.`);
        
        // Calculate statistics - check for various status formats that ZohoDesk might use
        const openCount = ticketList.filter((ticket: { status?: string }) => {
          const status = ticket.status?.toLowerCase() || '';
          return status === 'open' || status === 'new' || status === 'pending';
        }).length;
        
        const inProgressCount = ticketList.filter((ticket: { status?: string }) => {
          const status = ticket.status?.toLowerCase() || '';
          return status === 'in progress' || status === 'in_progress' || 
                 status === 'working' || status === 'assigned' || status === 'processing';
        }).length;
        
        // Calculate resolved this week
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const resolvedThisWeek = ticketList.filter((ticket: { status?: string; closedTime?: string; modifiedTime?: string }) => {
          const status = ticket.status?.toLowerCase() || '';
          const closedStatuses = ['closed', 'resolved', 'completed', 'done'];
          
          if (closedStatuses.includes(status)) {
            // Check both closedTime and modifiedTime for recent resolution
            const closedTime = ticket.closedTime || ticket.modifiedTime;
            if (closedTime) {
              const closedDate = new Date(closedTime);
            return closedDate >= oneWeekAgo;
            }
          }
          return false;
        }).length;

        console.log('=== CALCULATED STATISTICS ===');
        console.log(`📊 Dashboard Statistics Calculated:`, {
          open: openCount,
          inProgress: inProgressCount,
          resolvedThisWeek: resolvedThisWeek,
          totalTickets: ticketList.length
        });

        setTicketStats({
          open: openCount,
          inProgress: inProgressCount,
          resolvedThisWeek: resolvedThisWeek
        });

        setError(null);
        console.log('✅ Dashboard statistics updated successfully');
        console.log('=== DASHBOARD TICKET FETCH END ===');
        return true;
      } else {
        const errorText = await response.text();
        console.error('❌ Backend API response not OK:', response.status, response.statusText);
        console.error('Error response body:', errorText);
        
        // Try to parse error response
        try {
          const errorData = JSON.parse(errorText);
          console.error('Parsed error data:', errorData);
          
          if (response.status === 401) {
            setError('ZohoDesk authorization expired. Please re-authorize the connection.');
          } else {
            setError(`ZohoDesk API error: ${errorData.error || errorText}`);
          }
                 } catch {
           setError(`ZohoDesk connection failed: ${response.status} ${response.statusText}`);
         }
      }
      
      throw new Error(`Backend API request failed with status ${response.status}`);
    } catch (err) {
      console.error('❌ Failed to fetch real ticket data:', err);
      
      if (err instanceof Error) {
        if (err.message.includes('401')) {
          setError('ZohoDesk authorization required. Please set up the integration in IT Settings.');
        } else if (err.message.includes('fetch')) {
          setError('Unable to connect to ZohoDesk API. Please check your connection.');
        } else {
          setError(`ZohoDesk connection error: ${err.message}`);
        }
      } else {
        setError('Unable to connect to ZohoDesk. Please check your connection or contact support.');
      }
      
      console.log('=== DASHBOARD TICKET FETCH FAILED ===');
      return false;
    }
  };



  // Function to load Change Management dashboard stats
  const fetchChangeManagementStats = async () => {
    try {
      setChangeStatsLoading(true);
      const stats = await changeManagementApi.getDashboardStats();
      setChangeStats(stats);
    } catch (error) {
      console.error('Failed to load Change Management stats:', error);
      // Set fallback stats for display
      setChangeStats({
        totalRequests: 0,
        pendingApproval: 0,
        inDevelopment: 0,
        completed: 0,
        rejected: 0,
        byPriority: {},
        byStatus: {},
        byType: {}
      });
    } finally {
      setChangeStatsLoading(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load both ticket data and change management stats in parallel
        await Promise.all([
          (async () => {
            // Only load real data from ZohoDesk - no fallbacks or mock data
            const realDataLoaded = await fetchRealTicketStats();
            
            if (!realDataLoaded) {
              // If real data failed to load, keep stats at zero and show error
              setTicketStats({
                open: 0,
                inProgress: 0,
                resolvedThisWeek: 0
              });
              console.error('Unable to load real ticket data from ZohoDesk');
            }
          })(),
          fetchChangeManagementStats(),
          // Load additional data if needed in the future
          fetchITQuickActions(),
          fetchMyAssets(),
          fetchITPolicies()
        ]);
      } catch (error) {
        console.error("Failed to load IT Hub data:", error);
        setError('Failed to load IT Hub data.');
      }
      setLoading(false);
    };
    loadData();
  }, []);

  return (
    <div className="p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">IT Hub</h1>
        <p className="text-gray-600">Comprehensive IT support and asset management for SASMOS Group.</p>
      </div>

      {/* Zoho Desk Integration Notice */}
      {error ? (
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle size={20} className="text-yellow-600 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-yellow-800">Zoho Desk Integration Status</h3>
              <p className="text-sm text-yellow-700 mt-1">{error}</p>
            </div>
            <Link
              to="/it/setup"
              className="ml-4 px-4 py-2 bg-yellow-600 text-white text-sm rounded-md hover:bg-yellow-700 transition-colors"
            >
              Check Setup
            </Link>
          </div>
        </div>
      ) : (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle size={20} className="text-green-600 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-green-800">Zoho Desk Integration Active</h3>
              <p className="text-sm text-green-700 mt-1">
                Connected to Zoho Desk - displaying real ticket data and statistics.
              </p>
            </div>
            <Link
              to="/it/setup"
              className="ml-4 px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
            >
              Manage Integration
            </Link>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="mb-6 text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading IT Hub data...</p>
        </div>
      )}

      {/* Main Content - Split Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Support Tickets and Asset Management */}
        <div className="space-y-6">
          {/* Support Tickets */}
          <Link to="/it/tickets" className="group block">
          <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-lg">
                <Monitor size={24} className="text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Support Tickets</h3>
                <p className="text-sm text-gray-600">Create and track IT support requests</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/tickets?action=create';
                    }}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Create new ticket"
                  >
                    <Plus size={16} />
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/tickets';
                    }}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="View all tickets"
                  >
                    <Eye size={16} />
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/tickets?search=true';
                    }}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Search tickets"
                  >
                    <Search size={16} />
                  </button>
                  </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Open Tickets</span>
                <span className="font-medium text-orange-600">{ticketStats.open}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">In Progress</span>
                <span className="font-medium text-blue-600">{ticketStats.inProgress}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Resolved This Week</span>
                <span className="font-medium text-green-600">{ticketStats.resolvedThisWeek}</span>
              </div>
            </div>
          </div>
        </Link>

          {/* Asset Management */}
          <Link to="/it/assets" className="group block">
          <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <Tool size={24} className="text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Asset Management</h3>
                <p className="text-sm text-gray-600">Track hardware and software assets</p>
                  </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total Assets</span>
                <span className="font-medium text-gray-900">142</span>
             </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Available</span>
                <span className="font-medium text-green-600">28</span>
                    </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Under Maintenance</span>
                <span className="font-medium text-orange-600">5</span>
              </div>
            </div>
          </div>
        </Link>
        </div>

        {/* Right Side - Change Management */}
        <div className="space-y-6">
          {/* Change Management KPI Card */}
          <Link to="/it/change-management" className="group block">
          <div className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
              <div className="bg-indigo-100 p-3 rounded-lg">
                <GitBranch size={24} className="text-indigo-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Change Management</h3>
                <p className="text-sm text-gray-600">ITIL-based change control processes</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/change-management?action=create';
                    }}
                    className="p-2 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                    title="Create change request"
                  >
                    <Plus size={16} />
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/change-management';
                    }}
                    className="p-2 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                    title="View all requests"
                  >
                    <Eye size={16} />
                  </button>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      window.location.href = '/it/change-management?filter=pending';
                    }}
                    className="p-2 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                    title="Pending approvals"
                  >
                    <Search size={16} />
                  </button>
              </div>
            </div>
            <div className="space-y-2">
              {changeStatsLoading ? (
                <div className="flex justify-center py-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                </div>
              ) : changeStats && changeStats.totalRequests > 0 ? (
                <>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Status</span>
                    <span className="font-medium text-green-600">Active</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Requests</span>
                    <span className="font-medium text-gray-900">{changeStats.totalRequests}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Pending Approval</span>
                    <span className="font-medium text-orange-600">{changeStats.pendingApproval}</span>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Status</span>
                    <span className="font-medium text-blue-600">Ready</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Change Requests</span>
                    <span className="font-medium text-gray-900">0</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-2">
                    No requests yet - ready to use
                  </div>
                </>
              )}
            </div>
          </div>
        </Link>

          {/* Calendar Widget */}
          <ChangeManagementCalendar />
          </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link
            to="/it/tickets?action=create"
            className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center"
          >
            <div className="text-lg font-medium text-gray-900 mb-1">Create Ticket</div>
            <div className="text-sm text-gray-600">Submit new IT support request</div>
          </Link>
          
          <Link
            to="/it/assets"
            className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center"
          >
            <div className="text-lg font-medium text-gray-900 mb-1">View Assets</div>
            <div className="text-sm text-gray-600">Browse hardware inventory</div>
          </Link>
          
          <Link
            to="/it/tickets"
            className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center"
          >
            <div className="text-lg font-medium text-gray-900 mb-1">Track Request</div>
            <div className="text-sm text-gray-600">Check ticket status</div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ITPage; 