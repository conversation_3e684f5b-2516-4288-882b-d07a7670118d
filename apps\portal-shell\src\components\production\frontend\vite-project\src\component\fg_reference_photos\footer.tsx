// import React from 'react'; // Unused import
import { useState, useEffect } from "react";

function Footer() {
  const [loadTime, setLoadTime] = useState<number | null>(null);

  useEffect(() => {
    const timing = performance.timing;
    const pageLoadTime = timing.loadEventEnd - timing.navigationStart;

    // Fallback if loadEventEnd is not available yet
    if (pageLoadTime <= 0) {
      window.addEventListener("load", () => {
        const finalTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        setLoadTime(finalTime);
      });
    } else {
      setLoadTime(pageLoadTime);
    }
  }, []);
  return (
    <footer className="bg-white border-t border-gray-200 py-2 px-4 shadow-inner">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between max-w-7xl mx-auto">
        <div className="text-xs text-gray-600"><span className='text-red-500'>Refresh page by pressing ctrl + f5 often.</span></div>
        <div className="flex space-x-4 mt-2 md:mt-0 align-middle">
          <a href="https://outlook.office.com/mail" className="text-xs text-gray-600 hover:text-blue-600 transition-colors duration-200" target='_blank'> Click Here to send us your feedback</a>
        </div>

          <div className="text-xs text-gray-600 align-right">
            {loadTime !== null && (
            <span className="text-gray-400">Page Load Time: {loadTime} ms</span>
            )}
        </div>
      </div>
    </footer>
  );
}

export default Footer;
