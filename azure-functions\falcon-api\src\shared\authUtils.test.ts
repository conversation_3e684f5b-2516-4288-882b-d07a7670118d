import { HttpRequest, InvocationContext } from '@azure/functions';
import { getClientPrincipal, hasRequiredRole, getUserIdFromPrincipal, ClientPrincipal } from './authUtils';
import { executeQuery } from '../shared/db';
import { updateUserLastLogin } from '../shared/services/userManagementService';
import { logger } from '../shared/utils/logger';

// Mock dependencies
jest.mock('../shared/db');
jest.mock('../shared/services/userManagementService');
jest.mock('../shared/utils/logger');

// Mock InvocationContext for getUserIdFromPrincipal
const mockContext = { log: jest.fn() } as unknown as InvocationContext;

// Helper to create a mock HttpRequest
const createMockRequest = (principalHeaderValue: string | null): HttpRequest => {
    const headers = new Map<string, string>();
    if (principalHeaderValue !== null) {
        headers.set('x-ms-client-principal', principalHeaderValue);
    }
    return {
        headers: {
            get: (key: string) => headers.get(key) || null
        }
        // Add other HttpRequest properties if needed by the functions under test
    } as unknown as HttpRequest;
};

// Helper to encode a ClientPrincipal object into the expected header format
const encodePrincipal = (principal: ClientPrincipal): string => {
    return Buffer.from(JSON.stringify(principal)).toString('base64');
};

describe('Auth Utils', () => {

    // Reset mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    // --- Tests for getClientPrincipal ---
    describe('getClientPrincipal', () => {
        it('should return null if header is missing', () => {
            const req = createMockRequest(null);
            expect(getClientPrincipal(req)).toBeNull();
            expect(logger.warn).toHaveBeenCalledWith('getClientPrincipal: Missing x-ms-client-principal header.');
        });

        it('should return null for invalid base64 header', () => {
            const req = createMockRequest('invalid-base64');
            expect(getClientPrincipal(req)).toBeNull();
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error parsing x-ms-client-principal header:'), expect.any(Error));
        });

        it('should return null for invalid JSON structure', () => {
            const invalidJson = Buffer.from('{"invalid": "json"}').toString('base64');
            const req = createMockRequest(invalidJson);
            expect(getClientPrincipal(req)).toBeNull();
             expect(logger.warn).toHaveBeenCalledWith('getClientPrincipal: Parsed principal is missing required fields (userId, userRoles).');
        });

        it('should return the parsed principal for a valid header', () => {
            const principalData: ClientPrincipal = {
                identityProvider: 'aad',
                userId: 'test-user-id',
                userDetails: 'Test User',
                userRoles: ['authenticated', 'admin'],
                claims: [{ typ: 'oid', val: 'test-oid' }]
            };
            const encodedHeader = encodePrincipal(principalData);
            const req = createMockRequest(encodedHeader);
            expect(getClientPrincipal(req)).toEqual(principalData);
        });
    });

    // --- Tests for hasRequiredRole ---
    describe('hasRequiredRole', () => {
        const requiredAdminRole = ['Administrator'];
        const requiredUserRole = ['Employee'];

        it('should return false if principal is null', () => {
            expect(hasRequiredRole(null, requiredAdminRole)).toBe(false);
        });

        it('should return true if user has the role in userRoles', () => {
            const principal: ClientPrincipal = { identityProvider: 'aad', userId: 'test', userDetails: 'test', userRoles: ['authenticated', 'Administrator'] };
            expect(hasRequiredRole(principal, requiredAdminRole)).toBe(true);
        });

        it('should return true if user has the role in claims (type roles)', () => {
            const principal: ClientPrincipal = { identityProvider: 'aad', userId: 'test', userDetails: 'test', userRoles: ['authenticated'], claims: [{ typ: 'roles', val: 'Administrator' }] };
            expect(hasRequiredRole(principal, requiredAdminRole)).toBe(true);
        });

         it('should return true if user has the role in claims (type groups)', () => {
            const principal: ClientPrincipal = { identityProvider: 'aad', userId: 'test', userDetails: 'test', userRoles: ['authenticated'], claims: [{ typ: 'groups', val: 'Administrator' }] };
            expect(hasRequiredRole(principal, requiredAdminRole)).toBe(true);
        });

        it('should return false if user does not have the required role', () => {
            const principal: ClientPrincipal = { identityProvider: 'aad', userId: 'test', userDetails: 'test', userRoles: ['authenticated', 'Employee'] };
            expect(hasRequiredRole(principal, requiredAdminRole)).toBe(false);
        });

        it('should return true if user has one of multiple required roles', () => {
            const principal: ClientPrincipal = { identityProvider: 'aad', userId: 'test', userDetails: 'test', userRoles: ['authenticated', 'Employee'] };
            expect(hasRequiredRole(principal, ['Administrator', 'Employee'])).toBe(true);
        });
    });

    // --- Tests for getUserIdFromPrincipal ---
    describe('getUserIdFromPrincipal', () => {
        const mockPrincipal: ClientPrincipal = {
            identityProvider: 'aad',
            userId: 'test-user-id',
            userDetails: 'Test User',
            userRoles: ['authenticated', 'Employee'],
            claims: [{ typ: 'http://schemas.microsoft.com/identity/claims/objectidentifier', val: 'test-entra-id' }]
        };

        it('should return null if principal is null', async () => {
            await expect(getUserIdFromPrincipal(null, mockContext)).resolves.toBeNull();
        });

        it('should return null if oid claim is missing', async () => {
            const principalNoOid: ClientPrincipal = { ...mockPrincipal, claims: [{ typ: 'name', val: 'Test User' }] };
            await expect(getUserIdFromPrincipal(principalNoOid, mockContext)).resolves.toBeNull();
            expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('Could not find Entra ID (oid claim) in principal.'), expect.any(Object));
        });

        it('should return null if executeQuery throws an error', async () => {
            const dbError = new Error('DB Error');
            (executeQuery as jest.Mock).mockRejectedValue(dbError);
            await expect(getUserIdFromPrincipal(mockPrincipal, mockContext)).resolves.toBeNull();
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error fetching UserID'), dbError);
        });

        it('should return null if user is not found in DB', async () => {
            (executeQuery as jest.Mock).mockResolvedValue({ recordset: [] });
            await expect(getUserIdFromPrincipal(mockPrincipal, mockContext)).resolves.toBeNull();
            expect(logger.warn).toHaveBeenCalledWith(expect.stringContaining('No active user found in DB for EntraID'));
        });

        it('should return UserID and call updateUserLastLogin if user is found', async () => {
            const mockUserId = 123;
            (executeQuery as jest.Mock).mockResolvedValue({ recordset: [{ UserID: mockUserId }] });
            (updateUserLastLogin as jest.Mock).mockResolvedValue(true); // Mock last login update success

            await expect(getUserIdFromPrincipal(mockPrincipal, mockContext)).resolves.toBe(mockUserId);
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), { EntraID: 'test-entra-id' });
            // Check that updateUserLastLogin was called (even though it's async fire-and-forget)
            // We need to wait briefly for the promise chain inside the catch block to potentially execute
            await new Promise(process.nextTick); // Or jest.advanceTimersByTime if using fake timers
            expect(updateUserLastLogin).toHaveBeenCalledWith(mockUserId);
        });

        it('should return UserID even if updateUserLastLogin fails (logs error)', async () => {
            const mockUserId = 456;
            const loginError = new Error('Login update failed');
            (executeQuery as jest.Mock).mockResolvedValue({ recordset: [{ UserID: mockUserId }] });
            (updateUserLastLogin as jest.Mock).mockRejectedValue(loginError); // Mock last login update failure

            await expect(getUserIdFromPrincipal(mockPrincipal, mockContext)).resolves.toBe(mockUserId);
            expect(executeQuery).toHaveBeenCalledWith(expect.any(String), { EntraID: 'test-entra-id' });

            // Wait for the async error handler to run
            await new Promise(process.nextTick);
            expect(updateUserLastLogin).toHaveBeenCalledWith(mockUserId);
            expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Failed to update LastLoginDate'), loginError);
        });

    });
});