import { useState, useEffect } from 'react';
import './Modal.css';
import { PortalUser } from '../services/adminApi';

interface Role {
  RoleID: number;
  RoleName: string;
  Description: string;
}

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaveUser: (userId: string, userData: { 
    roles: string[]; 
    isActive: boolean; 
    name?: string; 
    company?: string; 
  }) => Promise<void>;
  user: PortalUser | null;
}

export const EditUserModal: React.FC<EditUserModalProps> = ({ isOpen, onClose, onSaveUser, user }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    selectedRoles: [] as string[],
    isActive: true
  });
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingRoles, setLoadingRoles] = useState(false);

  // Load roles when modal opens
  useEffect(() => {
    if (isOpen) {
      loadRoles();
    }
  }, [isOpen]);

  // Populate form when user changes
  useEffect(() => {
    if (user && isOpen) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        company: user.company || '',
        selectedRoles: user.roles || [],
        isActive: user.status === 'Active'
      });
    }
  }, [user, isOpen]);

  const loadRoles = async () => {
    setLoadingRoles(true);
    try {
      const response = await fetch('/api/roles');

      if (!response.ok) {
        throw new Error(`Failed to load roles. Status: ${response.status}`);
      }

      const rolesData = await response.json();
      setRoles(rolesData);
    } catch (error) {
      console.error('Error loading roles:', error);
      setError('Failed to load available roles');
    } finally {
      setLoadingRoles(false);
    }
  };

  const handleRoleChange = (roleName: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedRoles: checked
        ? [...prev.selectedRoles, roleName]
        : prev.selectedRoles.filter(r => r !== roleName)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('No user selected for editing');
      return;
    }

    if (!formData.name.trim()) {
      setError('Please enter a name');
      return;
    }

    if (!formData.company.trim()) {
      setError('Please select a company');
      return;
    }

    if (formData.selectedRoles.length === 0) {
      setError('Please select at least one role');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await onSaveUser(user.id, {
        roles: formData.selectedRoles,
        isActive: formData.isActive,
        name: formData.name,
        company: formData.company
      });
      
      onClose();
    } catch (error) {
      console.error('Error updating user:', error);
      setError(error instanceof Error ? error.message : 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Edit User: {user.name}</h2>
          <button 
            className="modal-close" 
            onClick={handleClose}
            disabled={loading}
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {error && (
            <div className="error-message" style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#fee', border: '1px solid #fcc', borderRadius: '4px', color: '#c00' }}>
              {error}
            </div>
          )}

          {/* User Information */}
          <div className="form-section">
            <h3 style={{ marginBottom: '12px', color: '#333' }}>User Information</h3>
            
            <div className="form-group">
              <label>Name *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={loading}
                style={{ 
                  width: '100%', 
                  padding: '8px', 
                  border: '1px solid #ddd', 
                  borderRadius: '4px',
                  backgroundColor: loading ? '#f5f5f5' : 'white',
                  color: loading ? '#666' : '#333'
                }}
                placeholder="Enter user's full name"
                required
              />
              <small style={{ color: '#666', fontSize: '0.9em' }}>
                Full name as it should appear in the portal
              </small>
            </div>

            <div className="form-group">
              <label>Email</label>
              <input
                type="email"
                value={formData.email}
                disabled
                style={{ 
                  width: '100%', 
                  padding: '8px', 
                  border: '1px solid #ddd', 
                  borderRadius: '4px',
                  backgroundColor: '#f5f5f5',
                  color: '#666'
                }}
              />
              <small style={{ color: '#666', fontSize: '0.9em' }}>
                Email cannot be changed (managed by identity provider)
              </small>
            </div>

            <div className="form-group">
              <label>Company *</label>
              <select
                value={formData.company}
                onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                disabled={loading}
                style={{ 
                  width: '100%', 
                  padding: '8px', 
                  border: '1px solid #ddd', 
                  borderRadius: '4px',
                  backgroundColor: loading ? '#f5f5f5' : 'white',
                  color: loading ? '#666' : '#333'
                }}
                required
              >
                <option value="">Select Company</option>
                <option value="SASMOS HET">SASMOS HET</option>
                <option value="SASMOS CMT">SASMOS CMT</option>
                <option value="SASMOS Defence">SASMOS Defence</option>
                <option value="Avirata Defence Systems">Avirata Defence Systems</option>
                <option value="SASMOS Engineering">SASMOS Engineering</option>
              </select>
              <small style={{ color: '#666', fontSize: '0.9em' }}>
                Select the user's company affiliation
              </small>
            </div>
          </div>

          {/* Portal Access Settings */}
          <div className="form-section" style={{ marginTop: '24px' }}>
            <h3 style={{ marginBottom: '12px', color: '#333' }}>Portal Access Settings</h3>
            
            <div className="form-group">
              <label>
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  disabled={loading}
                  style={{ marginRight: '8px' }}
                />
                Active User
              </label>
              <small style={{ display: 'block', color: '#666', fontSize: '0.9em', marginTop: '4px' }}>
                Unchecking this will deactivate the user's portal access
              </small>
            </div>

            <div className="form-group">
              <label>Roles *</label>
              {loadingRoles ? (
                <div style={{ padding: '8px', color: '#666' }}>Loading roles...</div>
              ) : (
                <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '12px', maxHeight: '200px', overflowY: 'auto' }}>
                  {roles.map(role => (
                    <label key={role.RoleID} style={{ display: 'block', marginBottom: '8px', cursor: 'pointer' }}>
                      <input
                        type="checkbox"
                        checked={formData.selectedRoles.includes(role.RoleName)}
                        onChange={(e) => handleRoleChange(role.RoleName, e.target.checked)}
                        disabled={loading}
                        style={{ marginRight: '8px' }}
                      />
                      <strong>{role.RoleName}</strong>
                      {role.Description && (
                        <span style={{ color: '#666', fontSize: '0.9em', marginLeft: '8px' }}>
                          - {role.Description}
                        </span>
                      )}
                    </label>
                  ))}
                </div>
              )}
              <small style={{ color: '#666', fontSize: '0.9em', marginTop: '4px' }}>
                Select the roles this user should have access to
              </small>
            </div>
          </div>

          <div className="modal-footer" style={{ marginTop: '24px' }}>
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              style={{ 
                padding: '8px 16px', 
                marginRight: '8px', 
                border: '1px solid #ddd', 
                borderRadius: '4px', 
                backgroundColor: '#f5f5f5',
                cursor: loading ? 'not-allowed' : 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || loadingRoles}
              style={{ 
                padding: '8px 16px', 
                border: 'none', 
                borderRadius: '4px', 
                backgroundColor: loading ? '#ccc' : '#28a745', 
                color: 'white',
                cursor: loading ? 'not-allowed' : 'pointer'
              }}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}; 