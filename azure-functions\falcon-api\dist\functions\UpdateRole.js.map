{"version": 3, "file": "UpdateRole.js", "sourceRoot": "", "sources": ["../../src/functions/UpdateRole.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,2CAA6B;AAUtB,KAAK,UAAU,UAAU,CAAC,OAAoB,EAAE,OAA0B;IAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;IAEzE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACf,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;SACnD,CAAC;KACL;IAED,IAAI,IAA2B,CAAC;IAChC,IAAI;QACA,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAC;KACxD;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;QAChD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;SACzD,CAAC;KACL;IAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE;QAC7G,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,4EAA4E,EAAE;SACtG,CAAC;KACL;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;QACtD,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;SACrD,CAAC;KACL;IAED,IAAI;QACA,MAAM,mBAAmB,GAAG,yEAAyE,CAAC;QACtG,MAAM,oBAAoB,GAAqB;YAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;SACnD,CAAC;QACF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;QAExF,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,aAAa,CAAC,CAAC;YAC7D,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,gBAAgB,MAAM,aAAa,EAAE;aAC7D,CAAC;SACL;QAED,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEnD,IAAI,WAAW,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;YACrF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wCAAwC,EAAE;aAClE,CAAC;SACL;QAED,IAAI,WAAW,CAAC,QAAQ,KAAK,eAAe,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,eAAe,EAAE;YACvG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE;aACnE,CAAC;SACL;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE;YAC5F,MAAM,cAAc,GAAG,oGAAoG,CAAC;YAC5H,MAAM,eAAe,GAAqB;gBACtC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;gBACrE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;aACnD,CAAC;YACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;YACxE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;gBACpC,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;gBAC/E,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE;iBACpF,CAAC;aACL;SACJ;QAED,MAAM,gBAAgB,GAAG,CAAC,CAAC;QAC3B,IAAI,KAAK,GAAG,mBAAmB,CAAC;QAChC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,YAAY,GAAqB,EAAE,CAAC;QAE1C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;YACvE,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SAC5F;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACpC,YAAY,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACxD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SAC1G;QACD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YACpC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAChF;QAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE;aAC9D,CAAC;SACL;QAED,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC9C,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACjD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAElF,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,KAAK,IAAI,0FAA0F,CAAC;QACpG,KAAK,IAAI,yBAAyB,CAAC;QACnC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAEvD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,gBAAgB,MAAM,4BAA4B,EAAE;aAC5E,CAAC;SACL;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAmB;YAChC,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,QAAQ,EAAE,aAAa,CAAC,QAAQ;YAChC,WAAW,EAAE,aAAa,CAAC,eAAe;SAC7C,CAAC;QAEF,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,WAAW;SACxB,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;QAClG,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;gBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;aAC/E;SACJ,CAAC;KACL;AACL,CAAC;AApJD,gCAoJC;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}