-- Change Request Drafts Table
-- This table stores draft change requests that users can save before submitting

CREATE TABLE ChangeRequestDrafts (
    DraftID NVARCHAR(50) PRIMARY KEY,
    Title NVARCHAR(255) NOT NULL,
    Description NTEXT,
    TypeID INT NOT NULL,
    Priority NVARCHAR(20) NOT NULL,
    BusinessJustification NTEXT,
    ExpectedBenefit NTEXT,
    RequestedCompletionDate DATE,
    RichContent NTEXT, -- JSON string containing rich content blocks
    RequestedBy INT NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    -- Foreign key constraints
    CONSTRAINT FK_ChangeRequestDrafts_RequestTypes 
        FOREIGN KEY (TypeID) REFERENCES RequestTypes(TypeID),
    CONSTRAINT FK_ChangeRequestDrafts_Users 
        FOREIGN KEY (RequestedBy) REFERENCES Users(UserID)
);

-- Indexes for better performance
CREATE INDEX IX_ChangeRequestDrafts_RequestedBy 
    ON ChangeRequestDrafts(RequestedBy);

CREATE INDEX IX_ChangeRequestDrafts_LastModified 
    ON ChangeRequestDrafts(LastModified DESC);

-- Comments for documentation
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Stores draft change requests that users can save before submitting. Supports rich content editing and auto-save functionality.',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequestDrafts';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Unique identifier for the draft (format: DRAFT-{timestamp}-{random})',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequestDrafts',
    @level2type = N'COLUMN', @level2name = N'DraftID';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'JSON string containing rich content blocks with formatting, images, and structure',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ChangeRequestDrafts',
    @level2type = N'COLUMN', @level2name = N'RichContent'; 