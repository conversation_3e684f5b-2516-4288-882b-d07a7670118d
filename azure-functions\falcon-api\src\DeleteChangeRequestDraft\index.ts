import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { ConnectionPool } from 'mssql';
import { getPool } from '../shared/db';
import { addCorsHeaders } from '../shared/cors';

export async function DeleteChangeRequestDraft(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  context.log('DeleteChangeRequestDraft function processed a request.');

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      body: ''
    });
  }

  try {
    // Get draft ID from query parameters
    const draftId = request.query.get('draftId');
    const userId = request.query.get('userId');
    
    if (!draftId || !userId) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { 
          success: false, 
          message: 'Draft ID and User ID are required' 
        }
      });
    }

    // Get database connection
    const pool: ConnectionPool = await getPool();

    // Delete the draft (only if owned by the user)
    const deleteQuery = `
      DELETE FROM ChangeRequestDrafts 
      WHERE DraftID = @draftId AND RequestedBy = @userId
    `;

    const result = await pool.request()
      .input('draftId', draftId)
      .input('userId', parseInt(userId))
      .query(deleteQuery);

    if (result.rowsAffected[0] === 0) {
      return addCorsHeaders({
        status: 404,
        jsonBody: {
          success: false,
          message: 'Draft not found or you do not have permission to delete it'
        }
      });
    }

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        message: 'Draft deleted successfully'
      }
    });

  } catch (error) {
    context.log('Error deleting draft:', error);
    
    return addCorsHeaders({
      status: 500,
      jsonBody: {
        success: false,
        message: 'Internal server error while deleting draft',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });
  }
}

app.http('DeleteChangeRequestDraft', {
  methods: ['DELETE', 'OPTIONS'],
  authLevel: 'function',
  handler: DeleteChangeRequestDraft
}); 