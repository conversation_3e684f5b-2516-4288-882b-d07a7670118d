{"version": 3, "file": "ITPage.js", "sourceRoot": "", "sources": ["../../../../../apps/portal-shell/src/pages/ITPage.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,iCAAmD;AACnD,uDAA+C;AAC/C,mDAG8B;AAC9B,uCAAuD;AACvD,oDAAoD;AAIpD,gCAAgC;AAChC,8DAA8D;AAC9D,MAAM,kBAAkB,GAAG,CAAC,EAAqH,EAAE,EAAE;QAAzH,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,OAA8F,EAAzF,KAAK,cAAjC,6BAAmC,CAAF;IAC3D,6DAA6D;IAC7D,8DAA8D;IAC9D,MAAM,aAAa,GAAI,YAAoB,CAAC,IAAuB,CAAC,IAAI,YAAY,CAAC,UAAU,CAAC;IAChG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC,EAAG,CAAC;AACxE,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAE,EAAE;IAC7C,IAAI,CAAC;QAAC,OAAO,IAAA,8BAAmB,EAAC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAAC,CAAC;IAC3E,6DAA6D;IAC7D,OAAO,EAAE,EAAE,CAAC;QAAC,OAAO,cAAc,CAAC;IAAC,CAAC;AACvC,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,EAAE;IACrC,IAAI,CAAC;QAAC,OAAO,IAAA,iBAAM,EAAC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,CAAC;IAAC,CAAC;IACxD,6DAA6D;IAC7D,OAAO,EAAE,EAAE,CAAC;QAAC,OAAO,cAAc,CAAC;IAAC,CAAC;AACvC,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAiC,EAAE,EAAE;IACvD,QAAO,MAAM,EAAE,CAAC;QACZ,KAAK,MAAM,CAAC,CAAC,OAAO,2BAA2B,CAAC;QAChD,KAAK,SAAS,CAAC,CAAC,OAAO,+BAA+B,CAAC;QACvD,KAAK,UAAU,CAAC,CAAC,OAAO,6BAA6B,CAAC;QACtD,KAAK,QAAQ,CAAC,CAAC,OAAO,2BAA2B,CAAC;QAClD,OAAO,CAAC,CAAC,OAAO,2BAA2B,CAAC;IAChD,CAAC;AACL,CAAC,CAAA;AAEH,MAAM,MAAM,GAAa,GAAG,EAAE;IAC5B,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAoB,EAAE,CAAC,CAAC;IAC9D,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAkB,EAAE,CAAC,CAAC;IAC5D,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAA,gBAAQ,EAAY,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAa,EAAE,CAAC,CAAC;IACzD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,QAAQ,GAAG,IAAA,8BAAW,GAAE,CAAC;IAE/B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAG,GAAS,EAAE;YAC1B,UAAU,CAAC,IAAI,CAAC,CAAC;YACjB,IAAI,CAAC;gBACH,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;oBACxE,IAAA,6BAAkB,EAAC,CAAC,CAAC,EAAE,wBAAwB;oBAC/C,IAAA,8BAAmB,GAAE;oBACrB,IAAA,wBAAa,GAAE;oBACf,IAAA,0BAAe,GAAE;iBAClB,CAAC,CAAC;gBACH,UAAU,CAAC,UAAU,CAAC,CAAC;gBACvB,UAAU,CAAC,UAAU,CAAC,CAAC;gBACvB,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrB,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACpD,oCAAoC;YACtC,CAAC;YACD,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC,CAAA,CAAC;QACF,QAAQ,EAAE,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,EAAE;QACvC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;QACvD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,CAAC,OAAe,EAAE,SAAiB,EAAE,EAAE;QAChE,OAAO,CAAC,GAAG,CAAC,qDAAqD,SAAS,KAAK,OAAO,6BAA6B,CAAC,CAAC;QACrH,KAAK,CAAC,iCAAiC,SAAS,+BAA+B,CAAC,CAAC;IACnF,CAAC,CAAC;IAEJ,OAAO,CACL,CAAC,GAAG,CACF;MAAA,CAAC,EAAE,CAAC,SAAS,CAAC,yBAAyB,CAAC,MAAM,EAAE,EAAE,CAElD;;MAAA,CAAC,4CAA4C,CAC7C;MAAA,CAAC,OAAO,CAAC,CAAC,CAAC,CACT,CAAC,GAAG,CAAC,SAAS,CAAC,iCAAiC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CACrF,CAAC,CAAC,CAAC,CACF,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CAEtC;;UAAA,CAAC,0CAA0C,CAC3C;UAAA,CAAC,OAAO,CACN;YAAA,CAAC,EAAE,CAAC,SAAS,CAAC,iCAAiC,CAAC,aAAa,EAAE,EAAE,CACjE;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,2GAA2G,CACxH;cAAA,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CACrB,CAAC,MAAM,CACL,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CACf,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAC9C,SAAS,CAAC,oJAAoJ,CAE9J;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,uBAAuB,CACpC;oBAAA,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,uCAAuC,EAClG;oBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CACxD;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAC3E;gBAAA,EAAE,MAAM,CAAC,CACV,CAAC,CACF;cAAA,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,2BAA2B,EAAE,CAAC,CAAC,CACpG;YAAA,EAAE,GAAG,CACP;UAAA,EAAE,OAAO,CAET;;UAAA,CAAC,gCAAgC,CACjC;UAAA,CAAC,OAAO,CAAC,SAAS,CAAC,uDAAuD,CACxE;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,wCAAwC,CACrD;cAAA,CAAC,EAAE,CAAC,SAAS,CAAC,uBAAuB,CAAC,eAAe,EAAE,EAAE,CACzD;cAAA,CAAC,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAC/H;YAAA,EAAE,GAAG,CACL;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CACxB;cAAA,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,CAAC,CAAC,CACxF;cAAA,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CACrB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,2CAA2C,CACnI;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,wCAAwC,CACrD;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,mCAAmC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,CACrE;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,oCAAoC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,CAC7G;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAC7F;kBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,cAAc,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAChG;gBAAA,EAAE,CAAC,CAAC,CACL,CAAC,CACJ;YAAA,EAAE,GAAG,CACP;UAAA,EAAE,OAAO,CAET;;UAAA,CAAC,wCAAwC,CACzC;UAAA,CAAC,OAAO,CACL;aAAA,CAAC,GAAG,CAAC,SAAS,CAAC,6CAA6C,CACzD;gBAAA,CAAC,EAAE,CAAC,SAAS,CAAC,uBAAuB,CAAC,WAAW,EAAE,EAAE,CACrD;gBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CACxI;aAAA,EAAE,GAAG,CACL;aAAA,CAAC,GAAG,CAAC,SAAS,CAAC,sGAAsG,CAClH;gBAAA,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAC1F;gBAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CACtB,CAAC,MAAM,CACL,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CACf,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAC9C,SAAS,CAAC,wIAAwI,CAElJ;oBAAA,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,kCAAkC,EAC7E;oBAAA,CAAC,GAAG,CACA;wBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CACzD;wBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,oCAAoC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CACzE;oBAAA,EAAE,GAAG,CACP;kBAAA,EAAE,MAAM,CAAC,CACV,CAAC,CACJ;cAAA,EAAE,GAAG,CACT;UAAA,EAAE,OAAO,CAET;;UAAA,CAAC,0BAA0B,CAC3B;UAAA,CAAC,OAAO,CAAC,SAAS,CAAC,uDAAuD,CACxE;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,wCAAwC,CACrD;cAAA,CAAC,EAAE,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,EAAE,EAAE,CACnD;cAAA,CAAC,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAC9H;YAAA,EAAE,GAAG,CACJ;aAAA,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CACzB;cAAA,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,CAAC,CACpF;cAAA,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CACnB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,qGAAqG,CACjI;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc,CAC3B;oBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CACzD;oBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,uBAAuB,CACpC;sBAAA,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAE,CAAA,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAClF;sBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,oCAAoC,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CACvG;oBAAA,EAAE,GAAG,CACP;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,MAAM,CACH,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAC1D,SAAS,CAAC,8LAA8L,CAExM;;kBACJ,EAAE,MAAM,CACV;gBAAA,EAAE,GAAG,CAAC,CACP,CAAC,CACJ;YAAA,EAAE,GAAG,CACP;UAAA,EAAE,OAAO,CAEX;;QAAA,EAAE,GAAG,CAAC,CACP,CACH;IAAA,EAAE,GAAG,CAAC,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,MAAM,CAAC"}