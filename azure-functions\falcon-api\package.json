{"name": "falcon-api", "version": "0.2.1", "description": "Azure Functions backend for Falcon Portal", "scripts": {"build": "tsc", "watch": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "func start --host 0.0.0.0", "start:azurite": "azurite --silent", "start:all": "concurrently \"npm run start:azurite\" \"npm run start\"", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "postbuild": "copyfiles host.json dist"}, "main": "dist/main.js", "dependencies": {"@azure/communication-email": "^1.0.0", "@azure/functions": "4.5.0", "@azure/identity": "^4.9.1", "@azure/storage-blob": "^12.27.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "isomorphic-fetch": "^3.0.0", "mssql": "^11.0.1", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^29.5.14", "@types/mssql": "^9.1.7", "@types/node": "~18.19.87", "azurite": "^3.29.0", "concurrently": "^8.2.2", "copyfiles": "^2.4.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.3.2", "typescript": "^4.9.5"}}