// API Configuration - Use Vite proxy for development
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Types for the draft management system
export interface ContentBlock {
  id: string;
  type: 'paragraph' | 'heading1' | 'heading2' | 'heading3' | 'list' | 'code' | 'image';
  content: string;
  imageUrl?: string;
  imageCaption?: string;
  imageAlt?: string;
  listItems?: string[];
}

export interface SaveDraftData {
  draftId?: string; // If updating existing draft
  title: string;
  description: string;
  typeId: number;
  priority: string;
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  requestedBy: number;
  richContent: ContentBlock[];
}

export interface ChangeRequestDraft {
  draftId: string;
  title: string;
  description: string;
  typeId: number;
  typeName: string;
  priority: string;
  businessJustification?: string;
  expectedBenefit?: string;
  requestedCompletionDate?: string;
  richContent: ContentBlock[];
  requestedBy: number;
  requestedByName: string;
  createdDate: string;
  lastModified: string;
}

export interface SaveDraftResponse {
  success: boolean;
  message: string;
  draftId: string;
  lastModified: string;
}

export interface GetDraftsResponse {
  success: boolean;
  drafts: ChangeRequestDraft[];
  count: number;
}

export interface DeleteDraftResponse {
  success: boolean;
  message: string;
}

export interface SubmitDraftResponse {
  success: boolean;
  message: string;
  requestId?: number;
  requestNumber?: string;
}

class DraftManagementApiService {
  /**
   * Save a draft change request
   */
  async saveDraft(draftData: SaveDraftData): Promise<SaveDraftResponse> {
    try {
      const url = `${API_BASE_URL}/SaveChangeRequestDraft`;
      console.log('🔍 Draft API Debug:', {
        url,
        method: 'POST',
        data: draftData,
        API_BASE_URL
      });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(draftData),
      });

      console.log('📥 Draft API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Draft API Error:', errorData);
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Draft API Success:', result);
      return result;
    } catch (error) {
      console.error('💥 Error saving draft:', error);
      throw error;
    }
  }

  /**
   * Get all drafts for a user
   */
  async getDrafts(userId: number): Promise<GetDraftsResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/GetChangeRequestDrafts?userId=${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching drafts:', error);
      throw error;
    }
  }

  /**
   * Delete a draft
   */
  async deleteDraft(draftId: string, userId: number): Promise<DeleteDraftResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/DeleteChangeRequestDraft?draftId=${draftId}&userId=${userId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting draft:', error);
      throw error;
    }
  }

  /**
   * Convert draft to change request (submit draft)
   */
  async submitDraft(draftId: string, userId: number): Promise<SubmitDraftResponse> {
    try {
      // First get the draft
      const draftsResponse = await this.getDrafts(userId);
      const draft = draftsResponse.drafts.find(d => d.draftId === draftId);
      
      if (!draft) {
        throw new Error('Draft not found');
      }

      // Convert draft to change request format
      const changeRequestData = {
        title: draft.title,
        description: draft.description,
        typeId: draft.typeId,
        priority: draft.priority,
        businessJustification: draft.businessJustification,
        expectedBenefit: draft.expectedBenefit,
        requestedCompletionDate: draft.requestedCompletionDate,
        requestedBy: draft.requestedBy,
        richContent: draft.richContent
      };

      // Submit as change request
      const response = await fetch(`${API_BASE_URL}/change-requests`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(changeRequestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Delete the draft after successful submission
      if (result.success) {
        await this.deleteDraft(draftId, userId);
      }

      return result;
    } catch (error) {
      console.error('Error submitting draft:', error);
      throw error;
    }
  }

  /**
   * Auto-save functionality with debouncing
   */
  private autoSaveTimeouts: Map<string, number> = new Map();

  autoSave(draftData: SaveDraftData, delay: number = 30000): Promise<SaveDraftResponse> {
    return new Promise((resolve, reject) => {
      const key = draftData.draftId || 'new-draft';
      
      // Clear existing timeout
      const existingTimeout = this.autoSaveTimeouts.get(key);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      // Set new timeout
      const timeoutId = window.setTimeout(async () => {
        try {
          const result = await this.saveDraft(draftData);
          this.autoSaveTimeouts.delete(key);
          resolve(result);
        } catch (error) {
          this.autoSaveTimeouts.delete(key);
          reject(error);
        }
      }, delay);

      this.autoSaveTimeouts.set(key, timeoutId);
    });
  }

  /**
   * Cancel auto-save for a specific draft
   */
  cancelAutoSave(draftId?: string): void {
    const key = draftId || 'new-draft';
    const timeoutId = this.autoSaveTimeouts.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.autoSaveTimeouts.delete(key);
    }
  }

  /**
   * Clear all auto-save timeouts
   */
  clearAllAutoSave(): void {
    this.autoSaveTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
    this.autoSaveTimeouts.clear();
  }
}

export const draftManagementApi = new DraftManagementApiService(); 