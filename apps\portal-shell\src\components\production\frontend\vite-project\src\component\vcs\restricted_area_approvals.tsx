import { useState, useEffect } from "react";

interface Visitor {
  visitor_ref_num: string;
  visitor_name: string;
  visitor_org: string;
  visitor_nation: string;
  visit_purp: string;
  visitor_invitee: string;
  visit_start_date: string;
  visit_end_date: string;
  wc_verify: string | number;
  restrict_area_entry: string | number;
  restrict_area_clearence: string | number;
}

function RestrictedAreaApprovals() {
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isOn, setIsOn] = useState(false);
  const toggle = () => setIsOn(!isOn);

  useEffect(() => {
    fetch("http://localhost:3000/api/restricted_area_approvals")
      .then((res) => res.json())
      .then(setVisitors)
      .catch((err) => console.error("Fetch error:", err));
  }, []);

  useEffect(() => {
    if (!selectedVisitor) return;

    fetch(
      `http://localhost:3000/api/visitor_id_proof_details?id=${selectedVisitor.visitor_ref_num}`
    )
      .then((res) => res.json())
      .then((data) => {
        console.log("File fetch response:", data);
        setFileName(data?.[0] || null);
      })
      .catch((err) => console.error("File fetch error:", err));
  }, [selectedVisitor]);

  const openModal = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedVisitor(null);
    setFileName(null);
    setIsOn(false);
  };

  const handleSubmit = async () => {
    if (!selectedVisitor) return;

    const currentDate = new Date().toISOString().split('T')[0];
    const validDate = new Date();
    validDate.setFullYear(validDate.getFullYear() + 1);
    const wcValidDate = validDate.toISOString().split('T')[0];

    const payload = {
      visitor_ref_num: selectedVisitor.visitor_ref_num,
      visitor_name: selectedVisitor.visitor_name,
      wc_reference_number: `WC-${selectedVisitor.visitor_ref_num}-${Date.now()}`,
      wc_date: currentDate,
      wc_verify: isOn ? "1" : "2",
      wc_user: "0401",
      wc_valid_date: wcValidDate,
      visitor_details_id: selectedVisitor.visitor_ref_num,
    };

    try {
      const response = await fetch("http://localhost:3000/api/update_visitor", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();
      if (response.ok) {
        alert("Visitor updated successfully");
        closeModal();
        const updatedVisitors = await fetch("http://localhost:3000/api/restricted_area_approvals")
          .then((res) => res.json());
        setVisitors(updatedVisitors);
      } else {
        alert(`Error: ${result.error || "Failed to update visitor"}`);
      }
    } catch (error) {
      console.error("Submit error:", error);
      alert("Failed to submit. Please try again.");
    }
  };

  const getWCVerifyStatus = (wc_verify: string | number) => {
    switch (String(wc_verify)) {
      case "0":
        return { text: "WORLD CHECK PENDING", bgColor: "orange" };
      case "1":
        return { text: "APPROVED", bgColor: "lightgreen" };
      case "2":
        return { text: "REJECTED", bgColor: "#E9967A" };
      default:
        return { text: String(wc_verify), bgColor: "transparent" };
    }
  };

  const getRestrictedAreaStatus = (
    entry: string | number,
    clearance: string | number,
    wc_verify: string | number
  ) => {
    const entryStr = String(entry);
    const clearanceStr = String(clearance);
    const wcVerifyStr = String(wc_verify);

    if (entryStr === "1") {
      if (wcVerifyStr === "0") {
        return { text: "WORLD CHECK PENDING", bgColor: "orange" };
      } else {
        switch (clearanceStr) {
          case "0":
            return { text: "PENDING", bgColor: "#BDB76B" };
          case "1":
            return { text: "APPROVED", bgColor: "lightgreen" };
          case "2":
            return { text: "REJECTED", bgColor: "#E9967A" };
          default:
            return { text: clearanceStr, bgColor: "transparent" };
        }
      }
    }
    return {
      text: entryStr === "0" ? "NO ENTRY" : entryStr,
      bgColor: "transparent",
    };
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">
        Restricted Area Approvals
      </h1>

      <div className="overflow-x-auto bg-white shadow rounded-lg">
        <table className="min-w-full text-sm text-left">
          <thead className="bg-gray-200 text-gray-600">
            <tr>
              <th className="p-3">Ref No.</th>
              <th className="p-3">Name</th>
              <th className="p-3">Org</th>
              <th className="p-3">Nation</th>
              <th className="p-3">Purpose</th>
              <th className="p-3">Invitee</th>
              <th className="p-3">Dates</th>
              <th className="p-3">WC</th>
              <th className="p-3">RESTRICTED AREA ACCESS</th>
              <th className="p-3">RESTRICTED AREA CLEARANCE</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {visitors.map((v) => {
              const wcStatus = getWCVerifyStatus(v.wc_verify);
              const restrictedStatus = getRestrictedAreaStatus(
                v.restrict_area_entry,
                v.restrict_area_clearence,
                v.wc_verify
              );

              return (
                <tr key={v.visitor_ref_num} className="hover:bg-gray-50">
                  <td
                    className="p-3 text-blue-600 cursor-pointer underline"
                    onClick={() => openModal(v)}
                  >
                    {v.visitor_ref_num}
                  </td>
                  <td className="p-3">{v.visitor_name}</td>
                  <td className="p-3">{v.visitor_org}</td>
                  <td className="p-3">{v.visitor_nation}</td>
                  <td className="p-3">{v.visit_purp}</td>
                  <td className="p-3">{v.visitor_invitee}</td>
                  <td className="p-3">
                    {v.visit_start_date} - {v.visit_end_date}
                  </td>
                  <td
                    className="p-3 font-semibold"
                    style={{ backgroundColor: wcStatus.bgColor }}
                  >
                    {wcStatus.text}
                  </td>
                  <td className="p-3">{String(v.restrict_area_entry) === "1" ? "YES" : "NO"}</td>
                  <td
                    className="p-3 font-semibold"
                    style={{ backgroundColor: restrictedStatus.bgColor }}
                  >
                    {restrictedStatus.text}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {isModalOpen && selectedVisitor && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow max-w-3xl w-full relative">
            <button
              onClick={closeModal}
              className="absolute top-3 right-4 text-gray-500 text-xl"
              aria-label="Close modal"
            >
              ✕
            </button>
            <h2 className="text-lg font-semibold mb-6 text-gray-700">
              Visitor Details
            </h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4 text-sm text-gray-800">
              <div><strong>Name:</strong> {selectedVisitor.visitor_name}</div>
              <div><strong>Organization:</strong> {selectedVisitor.visitor_org}</div>
              <div><strong>Nationality:</strong> {selectedVisitor.visitor_nation}</div>
              <div><strong>Purpose:</strong> {selectedVisitor.visit_purp}</div>
              <div><strong>Invitee:</strong> {selectedVisitor.visitor_invitee}</div>
              <div><strong>Dates:</strong> {selectedVisitor.visit_start_date} - {selectedVisitor.visit_end_date}</div>
              <div><strong>WC Verified:</strong> {getWCVerifyStatus(selectedVisitor.wc_verify).text}</div>
              <div><strong>Restricted Entry:</strong> {String(selectedVisitor.restrict_area_entry) === "1" ? "YES" : "NO"}</div>
              <div><strong>Clearance:</strong> {getRestrictedAreaStatus(
                selectedVisitor.restrict_area_entry,
                selectedVisitor.restrict_area_clearence,
                selectedVisitor.wc_verify
              ).text}</div>
              {fileName && (
                <div className="col-span-2">
                  <strong>ID Proof:</strong>{" "}
                  <a
                    href={`http://localhost:3000/uploads/${fileName}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 underline"
                  >
                    {fileName}
                  </a>
                </div>
              )}
            </div>

            <div className="mt-6">
              <strong>WORLD CHECK:</strong>{" "}
              <button
                onClick={toggle}
                style={{
                  padding: "10px 20px",
                  backgroundColor: isOn ? "green" : "gray",
                  color: "white",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                  marginLeft: "10px",
                }}
              >
                {isOn ? "ACCEPT" : "REJECT"}
              </button>
            </div>
            <div className="mt-4">
              <button
                type="button"
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                SUBMIT
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestrictedAreaApprovals;
