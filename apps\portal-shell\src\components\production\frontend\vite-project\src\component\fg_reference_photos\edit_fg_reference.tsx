"use client"

import { useState, useEffect } from "react"
import axios from "axios"
import { Edit } from 'lucide-react'
// import { useNavigate } from "react-router-dom" // Unused import

function EditFgReference() {
  const [requests, setRequests] = useState([])
  const [requestId, setRequestId] = useState("")
  const [selectedRequest, setSelectedRequest] = useState(null)
  const [selectedFiles, setSelectedFiles] = useState({})
  const [newFiles, setNewFiles] = useState([])
  const [hasAccess, setHasAccess] = useState(false)
  // const [isRedirecting, setIsRedirecting] = useState(false) // Unused state
  // const navigate = useNavigate() // Unused hook



  useEffect(() => {
    async function checkRights() {
      const empId = localStorage.getItem("emp_id")
      try {
        const res = await fetch(`http://localhost:3000/api/get_current_userrights?emp_id=${empId}`)
        if (!res.ok) throw new Error("Failed to get user rights")
        const data = await res.json()
        const rights = data.user_rights || []
        const authorized = rights.includes("EDIT") || rights.includes("MASTER USER") || rights.includes("ASSIGN RIGHTS")
        setHasAccess(authorized)
      } catch (e) {
        console.error(e)
        setHasAccess(false)
      }
    }
    checkRights()
  }, [])


  useEffect(() => {
    if (!hasAccess) return;

    const fetchRequests = async () => {
      try {
        const response = await axios.get("http://localhost:3000/api/get_fg_reference_photos")
        setRequests(response.data)
      } catch (error) {
        console.error("Error fetching requests:", error)
      }
    }

    fetchRequests()
  }, [hasAccess])

  const filteredRequests = requests.filter((request) =>
    request.photo_ref_num.toLowerCase().includes(requestId.toLowerCase()),
  )

  const handleOpenModal = (request) => {
    setSelectedRequest(request)
    const initialSelected = {}
    request.photo_attachment.forEach((file) => {
      initialSelected[file.filename] = true
    })
    setSelectedFiles(initialSelected)
    setNewFiles([])
  }

  const handleCloseModal = () => {
    setSelectedRequest(null)
    setSelectedFiles({})
    setNewFiles([])
  }

  const handleFileChange = (e) => {
    setNewFiles(Array.from(e.target.files))
  }

  const handleCheckboxChange = (filename) => {
    setSelectedFiles((prev) => ({
      ...prev,
      [filename]: !prev[filename],
    }))
  }

  const handleEditSubmit = async () => {
    const formData = new FormData()
    formData.append("photo_ref_num", selectedRequest.photo_ref_num)

    // Append new files to the FormData object
    newFiles.forEach((file) => {
      formData.append("photo_attachment", file)
    })
    formData.append("projectNo", selectedRequest.value)
    formData.append("customerName", selectedRequest.customer_name)
    formData.append("fgNo", selectedRequest.fg_no)
    formData.append("fgSerialNo", selectedRequest.fg_serial_no)
    formData.append("photoTakenOn", selectedRequest.photo_taken_on)

    try {
      await axios.post("http://localhost:3000/api/update_fg_reference", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      })
      handleCloseModal()
    } catch (error) {
      console.error("Error submitting data:", error)
    }
  }

  // If the user doesn't have access, show the unauthorized message
  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen p-6">
        <div className="bg-red-50 border border-red-400 text-red-800 rounded-xl p-8 shadow-lg text-center">
          <h1 className="text-3xl font-bold mb-4">**YOU ARE NOT AUTHORISED TO USE THIS FORM!!**</h1>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6 border-b pb-3">Edit FG Reference</h1>

      <div className="mb-8 bg-white p-4 rounded-xl shadow-lg">
        <input
          type="text"
          placeholder="Search by Photo Ref Num"
          value={requestId}
          onChange={(e) => setRequestId(e.target.value)}
          className="px-5 py-3 w-full border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
        />
      </div>

      <div className="bg-white rounded-xl shadow-xl">
        <table className="min-w-full text-sm">
          <thead className="bg-blue-600 text-white">
            <tr className="text-center uppercase font-semibold">
              {[
                "Photo Ref Num",
                "Project No",
                "Customer Name",
                "FG No",
                "FG Serial No",
                "Photo Taken On",
                "Edit",
              ].map((header, idx) => (
                <th key={idx} className="px-6 py-4 font-medium">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="text-gray-700 divide-y divide-gray-200">
            {filteredRequests.map((request) => (
              <tr key={request.photo_ref_num} className="hover:bg-gray-50 transition-colors duration-150 text-left">
                <td className="px-6 py-4">{request.photo_ref_num?.trim() || "N/A"}</td>
                <td className="px-6 py-4">{request.project_no}</td>
                <td className="px-6 py-4">{request.customer_name}</td>
                <td className="px-6 py-4">{request.fg_no}</td>
                <td className="px-6 py-4">{request.fg_serial_no}</td>
                <td className="px-6 py-4">{request.photo_taken_on}</td>
                <td className="px-6 py-4">
                  <span onClick={() => handleOpenModal(request)} className="cursor-pointer text-blue-600 hover:text-blue-800"> <Edit/> </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-11/12 max-w-4xl p-8 relative">
            <h2 className="text-xl font-bold mb-6 border-b pb-3 text-gray-800">Update Reference Photos</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[
                ["Photo Ref Num", selectedRequest.photo_ref_num],
                ["Project No", selectedRequest.project_no],
                ["Customer Name", selectedRequest.customer_name],
                ["FG No", selectedRequest.fg_no],
                ["FG Serial No", selectedRequest.fg_serial_no],
                ["Photo Taken On", selectedRequest.photo_taken_on],
              ].map(([label, value], index) => (
                <div key={index}>
                  <label className="block text-sm font-semibold text-gray-600 mb-2">{label}</label>
                  <div className="p-3 border rounded-xl bg-gray-50">{value}</div>
                </div>
              ))}
              {/* File attachments with checkboxes */}
              <div className="md:col-span-2 mt-4">
                <label className="block text-sm font-semibold text-gray-600 mb-3">Existing Photo Attachments</label>
                <ul className="space-y-3 bg-gray-50 p-4 rounded-xl border border-gray-200">
                  {selectedRequest.photo_attachment.map((file) => (
                    <li
                      key={file.filename}
                      className="flex items-center space-x-3 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <input
                        type="checkbox"
                        checked={selectedFiles[file.filename]}
                        onChange={() => handleCheckboxChange(file.filename)}
                        className="h-5 w-5 rounded text-blue-600 focus:ring-blue-500"
                      />
                      <a
                        href={file.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline flex-1 truncate"
                      >
                        {file.filename}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* File Upload */}
              <div className="md:col-span-2 mt-4">
                <label className="block text-sm font-semibold text-gray-600 mb-3">Upload New Files</label>
                <div className="border border-dashed border-gray-300 rounded-xl p-6 bg-gray-50 hover:bg-gray-100 transition-colors">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    name="photo_attachment"
                    className="block w-full text-sm text-gray-600 focus:outline-none"
                  />
                  {newFiles.length > 0 && (
                    <div className="mt-3 text-sm text-gray-600 bg-white p-3 rounded-lg border border-gray-200">
                      Selected: {newFiles.map((f) => f.name).join(", ")}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Buttons */}
            <div className="mt-8 flex justify-end space-x-4">
              <button
                onClick={handleCloseModal}
                className="px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-xl transition-colors duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleEditSubmit}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Submit Edit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EditFgReference