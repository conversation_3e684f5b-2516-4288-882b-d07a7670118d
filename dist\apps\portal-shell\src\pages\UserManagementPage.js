"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const adminApi_1 = require("../services/adminApi");
const feather_icons_react_1 = require("feather-icons-react");
// Constants
const COMPANIES = ['All', 'SASMOS HET', 'SASMOS CMT'];
const ALL_STATUSES = ['All', ...adminApi_1.PORTAL_STATUSES];
const PAGE_SIZE = 10;
// Pagination helper (same as before)
const getPaginationRange = (currentPage, totalPages, siblingCount = 1) => {
    const totalPageNumbers = siblingCount + 5;
    if (totalPageNumbers >= totalPages) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1;
    const firstPageIndex = 1;
    const lastPageIndex = totalPages;
    const DOTS = '...';
    if (!shouldShowLeftDots && shouldShowRightDots) {
        const leftItemCount = 3 + 2 * siblingCount;
        const leftRange = Array.from({ length: leftItemCount }, (_, i) => i + 1);
        return [...leftRange, DOTS, lastPageIndex];
    }
    if (shouldShowLeftDots && !shouldShowRightDots) {
        const rightItemCount = 3 + 2 * siblingCount;
        const rightRange = Array.from({ length: rightItemCount }, (_, i) => totalPages - i).reverse();
        return [firstPageIndex, DOTS, ...rightRange];
    }
    if (shouldShowLeftDots && shouldShowRightDots) {
        const middleRange = Array.from({ length: rightSiblingIndex - leftSiblingIndex + 1 }, (_, i) => leftSiblingIndex + i);
        return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
    }
    return [];
};
const UserManagementPage = () => {
    const [users, setUsers] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(true);
    const [error, setError] = (0, react_1.useState)(null);
    // Filters for the list of managed users
    const [searchTerm, setSearchTerm] = (0, react_1.useState)('');
    const [companyFilter, setCompanyFilter] = (0, react_1.useState)('All');
    const [roleFilter, setRoleFilter] = (0, react_1.useState)('All');
    const [statusFilter, setStatusFilter] = (0, react_1.useState)('All');
    // Pagination
    const [currentPage, setCurrentPage] = (0, react_1.useState)(1);
    const [totalUsers, setTotalUsers] = (0, react_1.useState)(0);
    const totalPages = Math.ceil(totalUsers / PAGE_SIZE);
    const paginationRange = getPaginationRange(currentPage, totalPages);
    // --- State for Dynamic Roles ---
    const [availableRoles, setAvailableRoles] = (0, react_1.useState)([]);
    const ALL_ROLES = ['All', ...availableRoles];
    // --- Fetch Available Roles ---
    (0, react_1.useEffect)(() => {
        const fetchRoles = () => __awaiter(void 0, void 0, void 0, function* () {
            try {
                const roles = yield (0, adminApi_1.fetchPortalRoleNames)();
                setAvailableRoles(roles);
            }
            catch (err) {
                console.error("Error fetching roles:", err);
                // Fallback to basic roles if API fails
                setAvailableRoles(['Administrator', 'Employee', 'Manager', 'Executive', 'IT Admin', 'HR Admin', 'Content Admin']);
            }
        });
        fetchRoles();
    }, []);
    // Fetch managed portal users
    (0, react_1.useEffect)(() => {
        const loadUsers = () => __awaiter(void 0, void 0, void 0, function* () {
            setLoading(true);
            setError(null);
            try {
                const response = yield (0, adminApi_1.fetchPortalUsers)(searchTerm, companyFilter, roleFilter, statusFilter, currentPage, PAGE_SIZE);
                setUsers(response.users);
                setTotalUsers(response.totalCount);
            }
            catch (err) {
                console.error("Error fetching portal users:", err);
                setError('Failed to load managed users. Please try again later.');
                // TODO: Add toast notification for error
            }
            finally {
                setLoading(false);
            }
        });
        loadUsers();
    }, [searchTerm, companyFilter, roleFilter, statusFilter, currentPage]);
    // Reset page on filter change
    (0, react_1.useEffect)(() => {
        setCurrentPage(1);
    }, [searchTerm, companyFilter, roleFilter, statusFilter]);
    // --- Handlers ---
    const handleNextPage = () => { if (currentPage < totalPages)
        setCurrentPage(currentPage + 1); };
    const handlePreviousPage = () => { if (currentPage > 1)
        setCurrentPage(currentPage - 1); };
    const handlePageChange = (pageNumber) => { if (typeof pageNumber === 'number')
        setCurrentPage(pageNumber); };
    // TODO: Implement user search functionality (to find users *not* yet managed)
    // const handleSearchForUserToAdd = () => { ... }
    return (<div className="p-4 md:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800">User Role Management</h1>
        {/* TODO: Add a button/search mechanism to find and add *new* users to manage */}
        {/* <button
            onClick={handleSearchForUserToAdd}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
            <Search size={18} className="mr-2 -ml-1" />
            Find User to Add
        </button> */}
      </div>

      {/* Filters Section (for managed users list) */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 items-end">
        <div>
          <label htmlFor="search-term" className="block text-sm font-medium text-gray-700 mb-1">Filter Managed Users</label>
          <input id="search-term" type="text" placeholder="By name or email..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"/>
        </div>
        <div>
          <label htmlFor="company-filter" className="block text-sm font-medium text-gray-700 mb-1">Company</label>
          <select id="company-filter" value={companyFilter} onChange={(e) => setCompanyFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {COMPANIES.map(company => (<option key={company} value={company}>{company}</option>))}
          </select>
        </div>
        <div>
          <label htmlFor="role-filter" className="block text-sm font-medium text-gray-700 mb-1">Role</label>
          <select id="role-filter" value={roleFilter} onChange={(e) => setRoleFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {ALL_ROLES.map(role => (<option key={role} value={role}>{role}</option>))}
          </select>
        </div>
        <div>
          <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select id="status-filter" value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
            {ALL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
          </select>
        </div>
      </div>

      {/* Managed Users Table and Pagination */} 
      <div className="bg-white shadow rounded-lg overflow-hidden"> 
          {loading && <p className="text-center text-gray-500 p-4">Loading users...</p>}
          {error && <p className="text-center text-red-500 p-4">{error}</p>} 
          
          {!loading && !error && (<>
              <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                          <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Roles</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal Status</th>
                              <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                          </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                          {users.length > 0 ? users.map((user) => (<tr key={user.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{user.name}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.company}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.roles.join(', ')}</td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>{user.status}</span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                      {/* Link to the new edit page */} 
                                      <react_router_dom_1.Link to={`/admin/manage-user/${user.id}`} className="text-indigo-600 hover:text-indigo-900">
                                          Manage
                                      </react_router_dom_1.Link>
                                  </td>
                              </tr>)) : (<tr><td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">No users found matching your criteria.</td></tr>)}
                      </tbody>
                  </table>
              </div>
              {/* Pagination Controls */} 
              {totalUsers > 0 && totalPages > 1 && (<div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    {/* Mobile Pagination */} 
                    <div className="flex-1 flex justify-between sm:hidden">
                        <button onClick={handlePreviousPage} disabled={currentPage === 1} className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">Previous</button>
                        <button onClick={handleNextPage} disabled={currentPage === totalPages} className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">Next</button>
                    </div>
                    {/* Desktop Pagination */} 
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700">Showing <span className="font-medium">{(currentPage - 1) * PAGE_SIZE + 1}</span> to <span className="font-medium">{Math.min(currentPage * PAGE_SIZE, totalUsers)}</span> of <span className="font-medium">{totalUsers}</span> results</p>
                        </div>
                        <div>
                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button onClick={handlePreviousPage} disabled={currentPage === 1} className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span className="sr-only">Previous</span><feather_icons_react_1.ChevronLeft className="h-5 w-5" aria-hidden="true"/>
                                </button>
                                {paginationRange.map((pageNumber, index) => {
                    if (pageNumber === '...') {
                        return <span key={`${pageNumber}-${index}`} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>;
                    }
                    return (<button key={pageNumber} onClick={() => handlePageChange(pageNumber)} aria-current={pageNumber === currentPage ? 'page' : undefined} className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNumber === currentPage ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`}>
                                            {pageNumber}
                                        </button>);
                })}
                                <button onClick={handleNextPage} disabled={currentPage === totalPages} className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span className="sr-only">Next</span><feather_icons_react_1.ChevronRight className="h-5 w-5" aria-hidden="true"/>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>)}
            </>)} 
      </div>
    </div>);
};
exports.default = UserManagementPage;
//# sourceMappingURL=UserManagementPage.js.map