import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { BlobServiceClient, StorageSharedKeyCredential } from '@azure/storage-blob';
import { getClientPrincipal, getUserIdFromPrincipal } from '../shared/authUtils';
import { addCorsHeaders } from '../shared/cors';

// Configuration
const STORAGE_ACCOUNT_NAME = process.env.AZURE_STORAGE_ACCOUNT_NAME || 'falconhubstorage';
const STORAGE_ACCOUNT_KEY = process.env.AZURE_STORAGE_ACCOUNT_KEY;
const CONTAINER_NAME = 'change-request-images';

export async function GetImage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  context.log('GetImage function processed a request.');

  // Handle preflight OPTIONS request
  if (request.method === 'OPTIONS') {
    return addCorsHeaders({
      status: 200,
      body: ''
    });
  }

  try {
    // Check authentication
    const principal = getClientPrincipal(request);
    const isDevelopment = !process.env.WEBSITE_SITE_NAME;
    
    if (!isDevelopment && !principal) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      });
    }

    // Extract image path from the URL path
    // The route is 'images/{*imagePath}' so we need to get everything after /api/images/
    const url = new URL(request.url);
    const pathMatch = url.pathname.match(/\/api\/images\/(.+)/);
    
    if (!pathMatch) {
      context.log('No image path found in URL:', url.pathname);
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: 'Image path is required' }
      });
    }

    const imagePath = pathMatch[1];
    context.log('Extracted image path:', imagePath);

    // Validate storage configuration
    if (!STORAGE_ACCOUNT_KEY) {
      context.log('Azure Storage Account Key not configured, returning 404');
      return addCorsHeaders({
        status: 404,
        jsonBody: { error: 'Image not found' }
      });
    }

    try {
      // Create storage shared key credential
      const sharedKeyCredential = new StorageSharedKeyCredential(STORAGE_ACCOUNT_NAME, STORAGE_ACCOUNT_KEY);

      // Create blob service client
      const blobServiceClient = new BlobServiceClient(
        `https://${STORAGE_ACCOUNT_NAME}.blob.core.windows.net`,
        sharedKeyCredential
      );

      // Get container client
      const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);

      // Get blob client
      const blobClient = containerClient.getBlobClient(imagePath);

      context.log('Checking if blob exists:', imagePath);

      // Check if blob exists
      const exists = await blobClient.exists();
      if (!exists) {
        context.log('Blob does not exist:', imagePath);
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Image not found' }
        });
      }

      context.log('Downloading blob:', imagePath);

      // Download the blob
      const downloadResponse = await blobClient.download();
      
      if (!downloadResponse.readableStreamBody) {
        context.log('No readable stream from blob download');
        return addCorsHeaders({
          status: 500,
          jsonBody: { error: 'Failed to download image' }
        });
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      for await (const chunk of downloadResponse.readableStreamBody) {
        chunks.push(Buffer.from(chunk));
      }
      const buffer = Buffer.concat(chunks);

      // Get content type from blob properties
      const contentType = downloadResponse.contentType || 'image/jpeg';

      context.log('Successfully downloaded image:', imagePath, 'Size:', buffer.length, 'Content-Type:', contentType);

      // Return the image with appropriate headers
      return addCorsHeaders({
        status: 200,
        body: buffer,
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
          'Content-Length': buffer.length.toString()
        }
      });

    } catch (error) {
      context.log('Error downloading image from blob storage:', error);
      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Failed to retrieve image' }
      });
    }

  } catch (error) {
    context.log('Error processing GetImage request:', error);
    
    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    });
  }
}

app.http('GetImage', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'images/{*imagePath}',
  handler: GetImage
}); 