"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteRole = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
async function deleteRole(request, context) {
    const roleId = Number(request.params.roleId);
    context.log(`Http function processed request for DeleteRole: ${roleId}`);
    if (isNaN(roleId)) {
        return {
            status: 400,
            jsonBody: { message: "Invalid RoleID provided." }
        };
    }
    let transactionStarted = false;
    try {
        const checkResult = await (0, db_1.executeQuery)('SELECT RoleName, IsSystemRole FROM Roles WHERE RoleID = @RoleID', { RoleID: roleId });
        if (checkResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found.` }
            };
        }
        const roleName = checkResult.recordset[0].RoleName;
        if (roleName === 'User' || roleName === 'Portal Admin') {
            return {
                status: 403,
                jsonBody: { message: `Cannot delete protected system role '${roleName}'.` }
            };
        }
        const userRoleResult = await (0, db_1.executeQuery)("SELECT RoleID FROM Roles WHERE RoleName = 'User' AND IsActive = 1");
        const defaultUserRoleId = userRoleResult.recordset.length > 0 ? userRoleResult.recordset[0].RoleID : null;
        if (!defaultUserRoleId) {
            throw new Error("Default 'User' role not found or inactive in database!");
        }
        const usersOnlyWithThisRoleQuery = `
            SELECT UserID FROM UserRoles ur1
            WHERE RoleID = @RoleID
            AND NOT EXISTS (
                SELECT 1 FROM UserRoles ur2
                WHERE ur2.UserID = ur1.UserID AND ur2.RoleID != @RoleID
            )
        `;
        const usersToUpdateResult = await (0, db_1.executeQuery)(usersOnlyWithThisRoleQuery, { RoleID: roleId });
        const userIdsToUpdate = usersToUpdateResult.recordset.map(u => u.UserID);
        await (0, db_1.executeQuery)('BEGIN TRANSACTION');
        transactionStarted = true;
        context.log(`Deleting assignments for RoleID: ${roleId}`);
        await (0, db_1.executeQuery)('DELETE FROM UserRoles WHERE RoleID = @RoleID', { RoleID: roleId });
        if (userIdsToUpdate.length > 0) {
            context.log(`Assigning default User role (ID: ${defaultUserRoleId}) to ${userIdsToUpdate.length} users.`);
            for (const userId of userIdsToUpdate) {
                const hasDefault = await (0, db_1.executeQuery)('SELECT COUNT(*) as Count FROM UserRoles WHERE UserID = @UserID AND RoleID = @RoleID', { UserID: userId, RoleID: defaultUserRoleId });
                if (hasDefault.recordset[0].Count === 0) {
                    await (0, db_1.executeQuery)('INSERT INTO UserRoles (UserID, RoleID, CreatedBy, CreatedDate) VALUES (@UserID, @RoleID, 1, GETUTCDATE())', { UserID: userId, RoleID: defaultUserRoleId });
                }
            }
        }
        context.log(`Deleting role definition for RoleID: ${roleId}`);
        const deleteResult = await (0, db_1.executeQuery)('DELETE FROM Roles WHERE RoleID = @RoleID', { RoleID: roleId });
        await (0, db_1.executeQuery)('COMMIT TRANSACTION');
        transactionStarted = false;
        if (deleteResult.rowsAffected[0] === 0) {
            context.warn(`Role with ID ${roleId} was not found during the final delete operation.`);
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found during delete.` }
            };
        }
        context.log(`Successfully deleted RoleID: ${roleId}`);
        return {
            status: 204
        };
    }
    catch (error) {
        if (transactionStarted) {
            context.error('Error during role deletion, rolling back transaction...', error);
            await (0, db_1.executeQuery)('ROLLBACK TRANSACTION');
        }
        context.error(`Error deleting role ${roleId}: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: `Error deleting role ${roleId}.`,
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}
exports.deleteRole = deleteRole;
functions_1.app.http('DeleteRole', {
    methods: ['DELETE'],
    authLevel: 'function',
    route: 'roles/{roleId:int}',
    handler: deleteRole
});
//# sourceMappingURL=index.js.map