import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { UpdateRoleRequestBody, RoleDefinition } from "../shared/interfaces";

export async function updateRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const roleId = Number(request.params.roleId);
    context.log(`Http function processed request for UpdateRole: ${roleId}`);

    let body: UpdateRoleRequestBody;
    try {
        body = await request.json() as UpdateRoleRequestBody;
    } catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }

    if (isNaN(roleId)) {
        return {
            status: 400,
            jsonBody: { message: "Invalid RoleID provided." }
        };
    }

    if (!body || (body.RoleName === undefined && body.Description === undefined)) {
        return {
            status: 400,
            jsonBody: { message: "No update data provided (RoleName or Description required)." }
        };
    }

    if (body.RoleName !== undefined && !body.RoleName.trim()) {
        return {
            status: 400,
            jsonBody: { message: "RoleName cannot be empty." }
        };
    }

    try {
        const currentRoleResult = await executeQuery('SELECT RoleID, RoleName, IsSystemRole FROM Roles WHERE RoleID = @RoleID', { RoleID: roleId });
        if (currentRoleResult.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found.` }
            };
        }

        const currentRole = currentRoleResult.recordset[0];

        if (currentRole.RoleName === 'User' && body.RoleName && body.RoleName.trim() !== 'User') {
            return {
                status: 403,
                jsonBody: { message: "Cannot rename the default 'User' role." }
            };
        }

        if (currentRole.RoleName === 'Administrator' && body.RoleName && body.RoleName.trim() !== 'Administrator') {
            return {
                status: 403,
                jsonBody: { message: "Cannot rename the 'Administrator' role." }
            };
        }

        if (body.RoleName && body.RoleName.trim().toLowerCase() !== currentRole.RoleName.toLowerCase()) {
            const nameCheckResult = await executeQuery(
                'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName) AND RoleID != @RoleID',
                { RoleName: body.RoleName.trim(), RoleID: roleId }
            );

            if (nameCheckResult.recordset[0].Count > 0) {
                return {
                    status: 409,
                    jsonBody: { message: `Role with name '${body.RoleName.trim()}' already exists.` }
                };
            }
        }

        const modifiedBy = 1;
        let setClauses: string[] = [];
        const params: { [key: string]: any } = { RoleID: roleId, ModifiedBy: modifiedBy };

        if (body.RoleName !== undefined) {
            setClauses.push('RoleName = @RoleName');
            params.RoleName = body.RoleName.trim();
        }

        if (body.Description !== undefined) {
            setClauses.push('RoleDescription = @RoleDescription');
            params.RoleDescription = body.Description.trim() || null;
        }

        setClauses.push('ModifiedBy = @ModifiedBy');
        setClauses.push('ModifiedDate = GETUTCDATE()');

        if (setClauses.length <= 2) {
            return {
                status: 400,
                jsonBody: { message: "No effective changes provided." }
            };
        }

        const query = `
            UPDATE Roles
            SET ${setClauses.join(', ')}
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription as Description, INSERTED.IsSystemRole, INSERTED.IsActive
            WHERE RoleID = @RoleID
        `;

        const result = await executeQuery(query, params);

        if (result.recordset.length === 0) {
            return {
                status: 404,
                jsonBody: { message: `Role with ID ${roleId} not found (update check).` }
            };
        }

        // Map the database result to the format expected by the frontend
        const updatedRole = {
            id: result.recordset[0].RoleID.toString(),
            name: result.recordset[0].RoleName,
            description: result.recordset[0].Description || null
        };

        context.log("Updated role:", JSON.stringify(updatedRole));

        return {
            status: 200,
            jsonBody: updatedRole
        };

    } catch (error) {
        context.error(`Error updating role ${roleId}: ${error instanceof Error ? error.message : error}`);
        return {
            status: 500,
            jsonBody: {
                message: `Error updating role ${roleId}.`,
                error: error instanceof Error ? error.message : "An unknown error occurred."
            }
        };
    }
}

app.http('UpdateRole', {
    methods: ['PUT'],
    authLevel: 'function',
    route: 'roles/{roleId:int}',
    handler: updateRole
});