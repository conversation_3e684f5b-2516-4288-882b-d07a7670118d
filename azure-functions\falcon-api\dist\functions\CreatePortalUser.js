"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisterNewPortalUser = void 0;
const functions_1 = require("@azure/functions");
const zod_1 = require("zod");
const db_1 = require("../shared/db");
const mssql_1 = __importDefault(require("mssql"));
// Updated validation schema to match frontend format
const CreatePortalUserSchema = zod_1.z.object({
    email: zod_1.z.string().email("Invalid email format."),
    roles: zod_1.z.array(zod_1.z.string()).min(1, "At least one role must be specified."),
    status: zod_1.z.enum(['Active', 'Inactive']).optional().default('Active')
});
async function RegisterNewPortalUser(request, context) {
    context.info(`INFO: [RegisterNewPortalUser] HTTP trigger function processed a request.`);
    // Authentication/Authorization Placeholder
    if (process.env.NODE_ENV === 'development') {
        context.warn(`WARN: [RegisterNewPortalUser] Bypassing authentication and authorization in development mode.`);
    }
    else {
        context.error(`ERROR: [RegisterNewPortalUser] Authentication not implemented for production.`);
        return { status: 501, body: "Authentication not implemented for production." };
    }
    let requestBody;
    // Parse and validate request body
    try {
        const rawBody = await request.json();
        requestBody = CreatePortalUserSchema.parse(rawBody);
        context.info(`INFO: [RegisterNewPortalUser] Parsed Request Body: ${JSON.stringify(requestBody)}`);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            context.error(`ERROR: [RegisterNewPortalUser] Validation Error: ${JSON.stringify(error.errors)}`);
            return {
                status: 400,
                jsonBody: {
                    message: "Invalid request body. Required fields: email, roles (array), optional: status.",
                    errors: error.errors,
                },
            };
        }
        else if (error instanceof Error) {
            context.error(`ERROR: [RegisterNewPortalUser] Request body parsing error: ${error.message}`);
            return { status: 400, jsonBody: { message: "Invalid JSON in request body.", detail: error.message } };
        }
        else {
            context.error(`ERROR: [RegisterNewPortalUser] Unknown parsing error: ${String(error)}`);
            return { status: 400, jsonBody: { message: "Failed to parse request body." } };
        }
    }
    // --- PLACEHOLDER LOGIC --- 
    // TODO: Implement lookup from Entra ID based on email to get real entraId and name.
    // TODO: Implement logic to determine the correct companyId (e.g., from email domain or default).
    const placeholderEntraId = `placeholder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`; // Generate unique placeholder
    const emailPrefix = requestBody.email.split('@')[0];
    const placeholderFirstName = emailPrefix.split('.')[0] || emailPrefix; // Take part before dot, or full prefix
    const placeholderLastName = emailPrefix.split('.')[1] || 'User'; // Take part after dot, or default to 'User'
    const placeholderUsername = requestBody.email; // Use email as username for now
    const placeholderCompanyId = 1; // Placeholder (e.g., Default Company ID)
    const placeholderCreatedById = 1; // Placeholder for CreatedBy (e.g., System User ID)
    const placeholderTenantId = 'ecb4a448-4a99-443b-aaff-063150b6c9ea'; // Placeholder tenant ID
    const isActive = requestBody.status === 'Active'; // Convert status to boolean
    context.warn(`WARN: [RegisterNewPortalUser] Using PLACEHOLDER values for EntraID (${placeholderEntraId}), Username (${placeholderUsername}), FirstName (${placeholderFirstName}), LastName (${placeholderLastName}), CompanyID (${placeholderCompanyId}), CreatedByID (${placeholderCreatedById}), TenantID (${placeholderTenantId}). Real lookup needed.`);
    // --- END PLACEHOLDER LOGIC ---
    let transaction = null;
    try {
        const pool = await (0, db_1.getPool)();
        // First, get role IDs for the provided role names
        const roleNames = requestBody.roles;
        const roleQuery = `SELECT RoleID, RoleName FROM Roles WHERE RoleName IN (${roleNames.map((_, i) => `@role${i}`).join(', ')}) AND IsActive = 1`;
        const roleRequest = pool.request();
        roleNames.forEach((roleName, i) => {
            roleRequest.input(`role${i}`, mssql_1.default.NVarChar, roleName);
        });
        const roleResult = await roleRequest.query(roleQuery);
        if (roleResult.recordset.length === 0) {
            context.error(`ERROR: [RegisterNewPortalUser] No valid roles found for: ${roleNames.join(', ')}`);
            return { status: 400, jsonBody: { message: "No valid roles found for the specified role names." } };
        }
        if (roleResult.recordset.length !== roleNames.length) {
            const foundRoles = roleResult.recordset.map(r => r.RoleName);
            const missingRoles = roleNames.filter(name => !foundRoles.includes(name));
            context.error(`ERROR: [RegisterNewPortalUser] Some roles not found: ${missingRoles.join(', ')}`);
            return { status: 400, jsonBody: { message: `Invalid role names: ${missingRoles.join(', ')}` } };
        }
        const roleIds = roleResult.recordset.map(r => r.RoleID);
        context.info(`INFO: [RegisterNewPortalUser] Found role IDs: ${roleIds.join(', ')} for roles: ${roleNames.join(', ')}`);
        // Check if user with this email already exists
        const checkResult = await pool.request()
            .input('Email', mssql_1.default.NVarChar, requestBody.email)
            .query('SELECT UserID FROM Users WHERE Email = @Email');
        if (checkResult.recordset.length > 0) {
            context.warn(`WARN: [RegisterNewPortalUser] User with email ${requestBody.email} already exists.`);
            return { status: 409, jsonBody: { message: "User with this email already exists." } };
        }
        // Start transaction
        transaction = pool.transaction();
        await transaction.begin();
        context.info(`INFO: [RegisterNewPortalUser] Transaction started.`);
        // Insert into Users table using placeholder/derived values
        const userInsertResult = await transaction.request()
            .input('EntraID', mssql_1.default.VarChar, placeholderEntraId)
            .input('Username', mssql_1.default.NVarChar, placeholderUsername) // Using email as username
            .input('FirstName', mssql_1.default.NVarChar, placeholderFirstName) // Added FirstName
            .input('LastName', mssql_1.default.NVarChar, placeholderLastName) // Added LastName
            .input('Email', mssql_1.default.NVarChar, requestBody.email)
            .input('CompanyID', mssql_1.default.Int, placeholderCompanyId)
            .input('TenantID', mssql_1.default.VarChar, placeholderTenantId) // Added TenantID
            .input('IsActive', mssql_1.default.Bit, isActive)
            .input('LastLoginDate', mssql_1.default.DateTime, null)
            .input('CreatedBy', mssql_1.default.Int, placeholderCreatedById) // Added CreatedBy
            .query(`
                INSERT INTO Users (EntraID, Username, FirstName, LastName, Email, CompanyID, TenantID, IsActive, LastLoginDate, CreatedBy)
                OUTPUT INSERTED.UserID
                VALUES (@EntraID, @Username, @FirstName, @LastName, @Email, @CompanyID, @TenantID, @IsActive, @LastLoginDate, @CreatedBy);
            `);
        if (userInsertResult.recordset.length === 0 || !userInsertResult.recordset[0].UserID) {
            throw new Error("Failed to insert user or retrieve new UserID.");
        }
        const newUserId = userInsertResult.recordset[0].UserID;
        context.info(`INFO: [RegisterNewPortalUser] User inserted with UserID: ${newUserId}`);
        // Insert into UserRoles table for each role
        for (const roleId of roleIds) {
            await transaction.request()
                .input('UserID', mssql_1.default.Int, newUserId)
                .input('RoleID', mssql_1.default.Int, roleId)
                .input('CreatedBy', mssql_1.default.Int, placeholderCreatedById)
                .query('INSERT INTO UserRoles (UserID, RoleID, CreatedBy) VALUES (@UserID, @RoleID, @CreatedBy);');
        }
        context.info(`INFO: [RegisterNewPortalUser] Inserted ${roleIds.length} role assignments.`);
        // Commit transaction
        await transaction.commit();
        context.info(`INFO: [RegisterNewPortalUser] Transaction committed successfully.`);
        // Construct the response body 
        const responseBody = {
            message: "User created successfully.",
            userId: newUserId,
            entraId: placeholderEntraId,
            email: requestBody.email,
            roles: roleNames,
            status: requestBody.status
        };
        return { status: 201, jsonBody: responseBody };
    }
    catch (error) {
        // Rollback transaction
        if (transaction) {
            try {
                await transaction.rollback();
                context.warn(`WARN: [RegisterNewPortalUser] Transaction rolled back due to error.`);
            }
            catch (rollbackError) {
                const rbErrorMsg = (rollbackError instanceof Error) ? rollbackError.message : String(rollbackError);
                context.error(`ERROR: [RegisterNewPortalUser] Error rolling back transaction: ${rbErrorMsg}`);
            }
        }
        // Log and handle errors
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        context.error(`ERROR: [RegisterNewPortalUser] Error during user creation: ${errorMessage}`);
        if (error instanceof mssql_1.default.RequestError || error instanceof mssql_1.default.TransactionError) {
            if (errorMessage.includes("FK_UserRoles_Roles") || errorMessage.includes("FK_UserRoles_Users") || errorMessage.includes("FK_Users_Companies")) {
                context.error(`ERROR: [RegisterNewPortalUser] Database constraint violation: ${errorMessage}`);
                const detailMessage = errorMessage.includes("FK_UserRoles_Roles")
                    ? "The selected Role ID does not exist."
                    : errorMessage.includes("FK_Users_Companies")
                        ? "The determined Company ID does not exist."
                        : "Database constraint violation.";
                return { status: 400, jsonBody: { message: detailMessage, detail: errorMessage } };
            }
            return { status: 500, jsonBody: { message: "Database error occurred during user creation.", detail: errorMessage } };
        }
        else {
            return {
                status: 500,
                jsonBody: { message: "An unexpected error occurred while creating the user.", detail: errorMessage }
            };
        }
    }
}
exports.RegisterNewPortalUser = RegisterNewPortalUser;
// Define the HTTP route
functions_1.app.http('RegisterNewPortalUser', {
    methods: ['POST'],
    route: 'portal-users',
    authLevel: 'anonymous',
    handler: RegisterNewPortalUser
});
//# sourceMappingURL=CreatePortalUser.js.map