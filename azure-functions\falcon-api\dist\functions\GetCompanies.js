"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCompanies = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
async function getCompanies(request, context) {
    context.log = logger_1.logger.info;
    logger_1.logger.info(`Http function processed request for url "${request.url}"`);
    // 1. Authentication & Authorization Check
    const isLocalDevelopment = !process.env.WEBSITE_SITE_NAME; // Azure App Service sets this
    if (!isLocalDevelopment) {
        // Add auth check for production
        logger_1.logger.warn("GetCompanies: Authentication check bypassed in production mode - TODO: Add proper auth.");
    }
    else {
        logger_1.logger.warn("GetCompanies: Bypassing authentication check in development mode.");
    }
    try {
        // 2. Fetch Companies from Database
        const query = `
            SELECT CompanyID, CompanyName, CompanyCode, IsActive
            FROM Companies 
            WHERE IsActive = 1
            ORDER BY CompanyName
        `;
        const result = await (0, db_1.executeQuery)(query, []);
        // 3. Return Response
        return {
            status: 200,
            jsonBody: result.recordset
        };
    }
    catch (err) {
        logger_1.logger.error(`Error fetching companies: ${err}`);
        const error = err;
        return {
            status: 500,
            jsonBody: {
                error: "Failed to fetch companies",
                details: error.message
            }
        };
    }
}
exports.getCompanies = getCompanies;
functions_1.app.http('GetCompanies', {
    methods: ['GET'],
    route: 'companies',
    authLevel: 'anonymous',
    handler: getCompanies
});
//# sourceMappingURL=GetCompanies.js.map