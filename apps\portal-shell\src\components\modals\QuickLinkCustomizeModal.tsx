import React, { useState, useEffect } from 'react';
import { X, PlusCircle, MinusCircle } from 'feather-icons-react';
import * as FeatherIcons from 'feather-icons-react';
import { QuickLink, fetchAvailableQuickLinks } from '../../services/dashboardApi'; // Import fetch function
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'; // Import dnd components

// Helper to get Feather Icon component by name (copied from DashboardSection for now)
// TODO: Move this to a shared utility file
type FeatherIconName = keyof typeof FeatherIcons;
const DynamicFeatherIcon = ({ name, ...props }: { name: string; [key: string]: unknown }) => {
  const IconComponent = (FeatherIcons as Record<string, React.ComponentType<unknown>>)[name as FeatherIconName] || FeatherIcons.AlertCircle; 
  return <IconComponent {...props} />;
};

interface QuickLinkCustomizeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentLinks: QuickLink[];
  onSave: (newLinks: QuickLink[]) => void; // Placeholder for saving
}

const QuickLinkCustomizeModal: React.FC<QuickLinkCustomizeModalProps> = ({ 
  isOpen, 
  onClose, 
  currentLinks, 
  onSave 
}) => {
  const [editedLinks, setEditedLinks] = useState<QuickLink[]>([]);
  const [availableLinks, setAvailableLinks] = useState<QuickLink[]>([]);
  const [loadingAvailable, setLoadingAvailable] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      setEditedLinks(currentLinks); 
      // Fetch available links when modal opens
      const loadAvailable = async () => {
        setLoadingAvailable(true);
        try {
          const allLinks = await fetchAvailableQuickLinks();
          setAvailableLinks(allLinks);
        } catch (error) {
          console.error("Failed to load available quick links:", error);
          // Handle error appropriately, maybe show a message
        }
        setLoadingAvailable(false);
      };
      loadAvailable();
    }
  }, [isOpen, currentLinks]); // Rerun if currentLinks changes while open (unlikely here)

  if (!isOpen) return null;

  const handleOnDragEnd = (result: DropResult) => {
    if (!result.destination) return; // Dropped outside the list

    const items = Array.from(editedLinks);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setEditedLinks(items); // Update the local state
  };

  // Handler to add a link
  const handleAddLink = (linkToAdd: QuickLink) => {
    // Add to the end, prevent duplicates
    if (!editedLinks.some(link => link.id === linkToAdd.id)) {
       setEditedLinks([...editedLinks, linkToAdd]);
    }
  };

  // Handler to remove a link
  const handleRemoveLink = (idToRemove: string) => {
     setEditedLinks(editedLinks.filter(link => link.id !== idToRemove));
  };

  const handleSave = () => {
    console.log("Saving customized links:", editedLinks);
    onSave(editedLinks); // Pass the edited state back
    // No need to onClose here, parent component handles it after save logic
  };

  // Filter available links to show only those not currently in editedLinks
  const filteredAvailableLinks = availableLinks.filter(
    available => !editedLinks.some(edited => edited.id === available.id)
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl"> {/* Increased width */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Customize Quick Links</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-6"> {/* Use grid layout */}
          {/* Column 1: Current Links (Draggable) */}
          <div>
            <h3 className="text-lg font-medium mb-2">Your Links</h3>
            <p className="text-sm text-gray-500 mb-3">Drag to reorder, click (-) to remove.</p>
            <DragDropContext onDragEnd={handleOnDragEnd}>
              <Droppable droppableId="editedLinks">
                {(provided) => (
                  <div 
                    {...provided.droppableProps} 
                    ref={provided.innerRef}
                    className="space-y-2 min-h-[100px] max-h-80 overflow-y-auto border p-3 rounded bg-gray-50"
                  >
                    {editedLinks.map((link, index) => (
                      <Draggable key={link.id} draggableId={link.id} index={index}>
                        {(provided, snapshot) => (
                          <div 
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={`flex items-center bg-white p-2 rounded border border-gray-300 ${snapshot.isDragging ? 'shadow-lg ring-2 ring-blue-300' : ''}`}
                          >
                            <DynamicFeatherIcon name={link.icon} size={18} className="text-gray-600 mr-3 flex-shrink-0" />
                            <span className="text-sm flex-grow mr-2 truncate" title={link.title}>{link.title}</span>
                            <button 
                              onClick={() => handleRemoveLink(link.id)}
                              className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-100 flex-shrink-0"
                              title="Remove Link"
                            >
                              <MinusCircle size={16} />
                            </button>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder} 
                    {editedLinks.length === 0 && (
                      <p className="text-sm text-gray-400 text-center pt-4">Drag or add links here.</p>
                    )}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>

          {/* Column 2: Available Links */}
          <div>
            <h3 className="text-lg font-medium mb-2">Available Links</h3>
            <p className="text-sm text-gray-500 mb-3">Click (+) to add a link.</p>
            <div className="space-y-2 max-h-80 overflow-y-auto border p-3 rounded bg-gray-50">
              {loadingAvailable && <p className="text-sm text-gray-500">Loading...</p>}
              {!loadingAvailable && filteredAvailableLinks.map((link) => (
                <div 
                  key={link.id} 
                  className="flex items-center bg-white p-2 rounded border border-gray-200"
                >
                  <DynamicFeatherIcon name={link.icon} size={18} className="text-gray-500 mr-3 flex-shrink-0" />
                  <span className="text-sm flex-grow mr-2 truncate" title={link.title}>{link.title}</span>
                   <button 
                      onClick={() => handleAddLink(link)}
                      className="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-100 flex-shrink-0"
                      title="Add Link"
                    >
                      <PlusCircle size={16} />
                    </button>
                </div>
              ))}
              {!loadingAvailable && filteredAvailableLinks.length === 0 && (
                 <p className="text-sm text-gray-400 text-center py-4">All available links added.</p>
              )}
               {/* TODO: Add error message display if loading fails */}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button 
            onClick={onClose} 
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
          >
            Cancel
          </button>
          <button 
            onClick={handleSave} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickLinkCustomizeModal; 