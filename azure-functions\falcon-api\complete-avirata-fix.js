const sql = require('mssql');

async function completeAvirataFix() {
    const config = {
        server: 'fp-sql-falcon-dev-cin-001.database.windows.net',
        database: 'fp-sqldb-falcon-dev-cin-001',
        user: 'falconhub_admin',
        password: 'Str!k3ab0ltThor',
        options: {
            encrypt: true,
            trustServerCertificate: false
        }
    };

    try {
        const pool = await sql.connect(config);
        console.log('Connected to database successfully.');
        
        // Check current state of chetan.pal specifically
        console.log('\nBEFORE UPDATE - Chetan Pal current state:');
        const beforeResult = await pool.request()
            .input('email', sql.VarChar, '<EMAIL>')
            .query(`
                SELECT 
                    u.UserID,
                    u.Email, 
                    u.EntraID,
                    u.TenantID,
                    c.CompanyName
                FROM Users u
                JOIN Companies c ON u.CompanyID = c.CompanyID
                WHERE u.Email = @email
            `);
        
        if (beforeResult.recordset.length > 0) {
            const user = beforeResult.recordset[0];
            console.log(`Email: ${user.Email}`);
            console.log(`Current EntraID: ${user.EntraID}`);
            console.log(`TenantID: ${user.TenantID}`);
            console.log(`Company: ${user.CompanyName}`);
            
            // Update chetan.pal with the correct EntraID
            const correctEntraId = '6b67bf93-fada-466a-aabb-0edaa0f8afcd';
            
            console.log(`\nUpdating chetan.pal EntraID from "${user.EntraID}" to "${correctEntraId}"`);
            
            const updateResult = await pool.request()
                .input('newEntraId', sql.VarChar, correctEntraId)
                .input('email', sql.VarChar, '<EMAIL>')
                .query(`
                    UPDATE Users 
                    SET EntraID = @newEntraId,
                        ModifiedDate = GETDATE(),
                        ModifiedBy = 1
                    WHERE Email = @email
                `);
            
            console.log(`Update completed. Rows affected: ${updateResult.rowsAffected[0]}`);
            
            // Verify the update
            console.log('\nAFTER UPDATE - Verification:');
            const afterResult = await pool.request()
                .input('email', sql.VarChar, '<EMAIL>')
                .query(`
                    SELECT 
                        u.UserID,
                        u.Email, 
                        u.EntraID,
                        u.TenantID,
                        c.CompanyName
                    FROM Users u
                    JOIN Companies c ON u.CompanyID = c.CompanyID
                    WHERE u.Email = @email
                `);
            
            if (afterResult.recordset.length > 0) {
                const updatedUser = afterResult.recordset[0];
                console.log(`Email: ${updatedUser.Email}`);
                console.log(`Updated EntraID: ${updatedUser.EntraID}`);
                console.log(`TenantID: ${updatedUser.TenantID}`);
                console.log(`Company: ${updatedUser.CompanyName}`);
                
                if (updatedUser.EntraID === correctEntraId) {
                    console.log('\n✅ SUCCESS: chetan.pal EntraID update completed successfully!');
                } else {
                    console.log('\n❌ ERROR: chetan.pal EntraID was not updated correctly!');
                }
            }
        } else {
            console.log('❌ ERROR: chetan.pal user not found in database');
        }
        
        await pool.close();
    } catch (error) {
        console.error('Database error:', error.message);
        console.error('Stack:', error.stack);
    }
}

completeAvirataFix(); 