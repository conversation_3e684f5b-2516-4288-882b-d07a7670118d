"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeletePortalUser = void 0;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const authUtils_1 = require("../shared/authUtils");
const logger_1 = require("../shared/utils/logger");
async function DeletePortalUser(request, context) {
    context.log = logger_1.logger.info; // Use logger for context.log
    logger_1.logger.info(`Http function processed request for url "${request.url}" to delete user.`);
    // Authentication bypass for development mode
    if (process.env.NODE_ENV === 'development') {
        logger_1.logger.warn("DeletePortalUser: Bypassing authentication in development mode.");
    }
    else {
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            logger_1.logger.warn("DeletePortalUser: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }
        // Ensure the user is an administrator
        if (!(0, authUtils_1.isAdmin)(principal)) {
            logger_1.logger.warn(`DeletePortalUser: Forbidden. User ${principal.userDetails} (ID: ${principal.userId}) is not an admin.`);
            return { status: 403, jsonBody: { error: "Forbidden. Administrator role required." } };
        }
    }
    const entraId = request.params.entraId;
    if (!entraId) {
        logger_1.logger.warn("DeletePortalUser: Entra ID missing from request parameters.");
        return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
    }
    try {
        // We need the internal UserID of the authenticated admin to log who made the change.
        // For local development, if admin doesn't exist in DB, use default system user ID
        let adminInternalUserId;
        if (process.env.NODE_ENV === 'development') {
            // In development mode, use default system user ID
            adminInternalUserId = 1;
            logger_1.logger.warn("DeletePortalUser: Using default system user ID (1) for audit in development mode.");
        }
        else {
            const principal = (0, authUtils_1.getClientPrincipal)(request);
            if (!principal) {
                logger_1.logger.warn("DeletePortalUser: Principal should exist at this point.");
                return { status: 500, jsonBody: { error: "Internal error: Principal missing." } };
            }
            adminInternalUserId = await userManagementService_1.userManagementService.getUserIdByEntraId(principal.userId) || 1;
            if (adminInternalUserId === 1) {
                logger_1.logger.warn(`DeletePortalUser: Admin user ${principal.userDetails} (ID: ${principal.userId}) not found in database. Using default system user ID for audit.`);
            }
        }
        const success = await userManagementService_1.userManagementService.deactivatePortalUserByEntraId(entraId, adminInternalUserId);
        if (success) {
            logger_1.logger.info(`DeletePortalUser: Successfully deactivated user with Entra ID ${entraId}.`);
            return { status: 204 }; // No content, successful deletion (soft delete)
        }
        else {
            logger_1.logger.warn(`DeletePortalUser: User with Entra ID ${entraId} not found or already inactive.`);
            return { status: 404, jsonBody: { error: "User not found or already inactive." } };
        }
    }
    catch (err) {
        logger_1.logger.error(`Error in DeletePortalUser for Entra ID ${entraId}:`, err);
        const error = err;
        return { status: 500, jsonBody: { error: error.message || "Failed to delete user due to an internal error." } };
    }
}
exports.DeletePortalUser = DeletePortalUser;
functions_1.app.http('DeletePortalUser', {
    methods: ['DELETE'],
    authLevel: 'anonymous',
    route: 'portal-users/{entraId}',
    handler: DeletePortalUser // Corrected handler name to match the exported function
});
//# sourceMappingURL=DeletePortalUser.js.map