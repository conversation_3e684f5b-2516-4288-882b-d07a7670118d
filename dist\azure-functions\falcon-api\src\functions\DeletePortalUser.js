"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeletePortalUser = DeletePortalUser;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const authUtils_1 = require("../shared/authUtils");
const logger_1 = require("../shared/utils/logger");
function DeletePortalUser(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log = logger_1.logger.info; // Use logger for context.log
        logger_1.logger.info(`Http function processed request for url "${request.url}" to delete user.`);
        const principal = (0, authUtils_1.getClientPrincipal)(request);
        if (!principal) {
            logger_1.logger.warn("DeletePortalUser: Unauthenticated access attempt.");
            return { status: 401, jsonBody: { error: "Unauthorized. Client principal missing." } };
        }
        // Ensure the user is an administrator
        if (!(0, authUtils_1.isAdmin)(principal)) {
            logger_1.logger.warn(`DeletePortalUser: Forbidden. User ${principal.userDetails} (ID: ${principal.userId}) is not an admin.`);
            return { status: 403, jsonBody: { error: "Forbidden. Administrator role required." } };
        }
        const entraId = request.params.entraId;
        if (!entraId) {
            logger_1.logger.warn("DeletePortalUser: Entra ID missing from request parameters.");
            return { status: 400, jsonBody: { error: "Entra ID must be provided in the path." } };
        }
        try {
            // We need the internal UserID of the authenticated admin to log who made the change.
            // For local development, if admin doesn't exist in DB, use default system user ID
            let adminInternalUserId = yield userManagementService_1.userManagementService.getUserIdByEntraId(principal.userId);
            if (!adminInternalUserId) {
                // For local development/testing, use a default system user ID (1)
                // In production, this should be properly handled by creating the admin user first
                logger_1.logger.warn(`DeletePortalUser: Admin user ${principal.userDetails} (ID: ${principal.userId}) not found in database. Using default system user ID for audit.`);
                adminInternalUserId = 1; // Default system user ID for audit purposes
            }
            const success = yield userManagementService_1.userManagementService.deactivatePortalUserByEntraId(entraId, adminInternalUserId);
            if (success) {
                logger_1.logger.info(`DeletePortalUser: Successfully deactivated user with Entra ID ${entraId}.`);
                return { status: 204 }; // No content, successful deletion (soft delete)
            }
            else {
                logger_1.logger.warn(`DeletePortalUser: User with Entra ID ${entraId} not found or already inactive.`);
                return { status: 404, jsonBody: { error: "User not found or already inactive." } };
            }
        }
        catch (err) {
            logger_1.logger.error(`Error in DeletePortalUser for Entra ID ${entraId}:`, err);
            const error = err;
            return { status: 500, jsonBody: { error: error.message || "Failed to delete user due to an internal error." } };
        }
    });
}
functions_1.app.http('DeletePortalUser', {
    methods: ['DELETE'],
    authLevel: 'anonymous', // Keep as anonymous for now, relying on in-code isAdmin check
    route: 'portal-users/{entraId}',
    handler: DeletePortalUser // Corrected handler name to match the exported function
});
//# sourceMappingURL=DeletePortalUser.js.map