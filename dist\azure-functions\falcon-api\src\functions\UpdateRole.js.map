{"version": 3, "file": "UpdateRole.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/UpdateRole.ts"], "names": [], "mappings": ";;;;;;;;;;;AAcA,gCAoJC;AAlKD,gDAAyF;AACzF,qCAA4C;AAE5C,mDAAgD;AAChD,6BAA6B;AAU7B,SAAsB,UAAU,CAAC,OAAoB,EAAE,OAA0B;;QAC7E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,mDAAmD,MAAM,EAAE,CAAC,CAAC;QAEzE,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAChB,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;aACnD,CAAC;QACN,CAAC;QAED,IAAI,IAA2B,CAAC;QAChC,IAAI,CAAC;YACD,IAAI,IAAG,MAAM,OAAO,CAAC,IAAI,EAA2B,CAAA,CAAC;QACzD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YAChD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;aACzD,CAAC;QACN,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE,CAAC;YAC9G,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,4EAA4E,EAAE;aACtG,CAAC;QACN,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;YACvD,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE;aACrD,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,MAAM,mBAAmB,GAAG,yEAAyE,CAAC;YACtG,MAAM,oBAAoB,GAAqB;gBAC3C,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;aACnD,CAAC;YACF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAY,EAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;YAExF,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3C,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,aAAa,CAAC,CAAC;gBAC7D,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,gBAAgB,MAAM,aAAa,EAAE;iBAC7D,CAAC;YACN,CAAC;YAED,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAEnD,IAAI,WAAW,CAAC,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;gBACtF,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wCAAwC,EAAE;iBAClE,CAAC;YACN,CAAC;YAED,IAAI,WAAW,CAAC,QAAQ,KAAK,cAAc,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,cAAc,EAAE,CAAC;gBACtG,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,wCAAwC,EAAE;iBAClE,CAAC;YACN,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC7F,MAAM,cAAc,GAAG,oGAAoG,CAAC;gBAC5H,MAAM,eAAe,GAAqB;oBACtC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;oBACrE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;iBACnD,CAAC;gBACF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;gBACxE,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;oBACrC,eAAM,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;oBAC/E,OAAO;wBACH,MAAM,EAAE,GAAG;wBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,mBAAmB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE;qBACpF,CAAC;gBACN,CAAC;YACL,CAAC;YAED,MAAM,gBAAgB,GAAG,CAAC,CAAC;YAC3B,IAAI,KAAK,GAAG,mBAAmB,CAAC;YAChC,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,YAAY,GAAqB,EAAE,CAAC;YAE1C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;gBACxE,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC7F,CAAC;YACD,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;gBACrC,YAAY,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACxD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC3G,CAAC;YACD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACrC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,KAAK,EAAE,sCAAsC,EAAE;iBAC9D,CAAC;YACN,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAC9C,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACjD,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAElF,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,KAAK,IAAI,0FAA0F,CAAC;YACpG,KAAK,IAAI,yBAAyB,CAAC;YACnC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAEvD,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,QAAQ,EAAE,EAAE,OAAO,EAAE,gBAAgB,MAAM,4BAA4B,EAAE;iBAC5E,CAAC;YACN,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,WAAW,GAAmB;gBAChC,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,WAAW,EAAE,aAAa,CAAC,eAAe;aAC7C,CAAC;YAEF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,WAAW;aACxB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAClG,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,uBAAuB,MAAM,GAAG;oBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;iBAC/E;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,YAAY,EAAE;IACnB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,oBAAoB;IAC3B,OAAO,EAAE,UAAU;CACtB,CAAC,CAAC"}