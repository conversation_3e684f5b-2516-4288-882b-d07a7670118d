# =============================================
# Simple Database Update Script
# =============================================

$ServerInstance = "fp-sql-falcon-dev-cin-001.database.windows.net"
$Database = "fp-sqldb-falcon-dev-cin-001"
$Username = "falconAdmin"

# Prompt for password securely
$SecurePassword = Read-Host -AsSecureString -Prompt "Enter database password for $Username"

Write-Host "Connecting to Azure SQL Database..." -ForegroundColor Yellow
Write-Host "Server: $ServerInstance" -ForegroundColor Gray
Write-Host "Database: $Database" -ForegroundColor Gray
Write-Host "Username: $Username" -ForegroundColor Gray

try {
    # Import SqlServer module if not already loaded
    if (!(Get-Module -Name SqlServer -ListAvailable)) {
        Write-Host "Installing SqlServer module..." -ForegroundColor Yellow
        Install-Module -Name SqlServer -Force -AllowClobber
    }

    # Get the SQL file path
    $SqlFilePath = Join-Path (Split-Path $PSScriptRoot -Parent) -ChildPath "add-deployment-date-fields.sql"
    
    if (Test-Path $SqlFilePath) {
        Write-Host "Running SQL script: $SqlFilePath" -ForegroundColor Yellow
        
        # Execute the SQL file
        Invoke-Sqlcmd -ServerInstance $ServerInstance -Database $Database -Username $Username -Password $SecurePassword -InputFile $SqlFilePath -Verbose
        
        Write-Host "✅ Database schema update completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ SQL file not found: $SqlFilePath" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error updating database schema: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host $_.Exception.StackTrace -ForegroundColor Red
}

Write-Host "Script execution completed." -ForegroundColor Cyan 