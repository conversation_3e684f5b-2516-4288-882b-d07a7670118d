"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaveChangeRequestDraft = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const cors_1 = require("../shared/cors");
async function SaveChangeRequestDraft(request, context) {
    context.log('SaveChangeRequestDraft function processed a request.');
    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            body: ''
        });
    }
    try {
        // Parse request body
        const requestBody = await request.text();
        if (!requestBody) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'Request body is required'
                }
            });
        }
        const draftData = JSON.parse(requestBody);
        // Validate required fields for draft
        if (!draftData.title?.trim()) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                jsonBody: {
                    success: false,
                    message: 'Title is required for draft'
                }
            });
        }
        // Get database connection
        const pool = await (0, db_1.getPool)();
        let draftId;
        const now = new Date();
        if (draftData.draftId) {
            // Update existing draft
            const updateDraftQuery = `
        UPDATE ChangeRequestDrafts 
        SET 
          Title = @title,
          Description = @description,
          TypeID = @typeId,
          Priority = @priority,
          BusinessJustification = @businessJustification,
          ExpectedBenefit = @expectedBenefit,
          RequestedCompletionDate = @requestedCompletionDate,
          RichContent = @richContent,
          LastModified = @lastModified
        WHERE DraftID = @draftId AND RequestedBy = @requestedBy
      `;
            await pool.request()
                .input('draftId', draftData.draftId)
                .input('title', draftData.title)
                .input('description', draftData.description)
                .input('typeId', draftData.typeId)
                .input('priority', draftData.priority)
                .input('businessJustification', draftData.businessJustification || null)
                .input('expectedBenefit', draftData.expectedBenefit || null)
                .input('requestedCompletionDate', draftData.requestedCompletionDate || null)
                .input('richContent', JSON.stringify(draftData.richContent))
                .input('requestedBy', draftData.requestedBy)
                .input('lastModified', now)
                .query(updateDraftQuery);
            draftId = draftData.draftId;
        }
        else {
            // Create new draft
            draftId = generateDraftId();
            const insertDraftQuery = `
        INSERT INTO ChangeRequestDrafts (
          DraftID, Title, Description, TypeID, Priority, 
          BusinessJustification, ExpectedBenefit, RequestedCompletionDate,
          RichContent, RequestedBy, CreatedDate, LastModified
        ) VALUES (
          @draftId, @title, @description, @typeId, @priority,
          @businessJustification, @expectedBenefit, @requestedCompletionDate,
          @richContent, @requestedBy, @createdDate, @lastModified
        )
      `;
            await pool.request()
                .input('draftId', draftId)
                .input('title', draftData.title)
                .input('description', draftData.description)
                .input('typeId', draftData.typeId)
                .input('priority', draftData.priority)
                .input('businessJustification', draftData.businessJustification || null)
                .input('expectedBenefit', draftData.expectedBenefit || null)
                .input('requestedCompletionDate', draftData.requestedCompletionDate || null)
                .input('richContent', JSON.stringify(draftData.richContent))
                .input('requestedBy', draftData.requestedBy)
                .input('createdDate', now)
                .input('lastModified', now)
                .query(insertDraftQuery);
        }
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            jsonBody: {
                success: true,
                message: 'Draft saved successfully',
                draftId: draftId,
                lastModified: now.toISOString()
            }
        });
    }
    catch (error) {
        context.log('Error saving draft:', error);
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            jsonBody: {
                success: false,
                message: 'Internal server error while saving draft',
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        });
    }
}
exports.SaveChangeRequestDraft = SaveChangeRequestDraft;
function generateDraftId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substr(2, 5);
    return `DRAFT-${timestamp}-${randomStr}`.toUpperCase();
}
functions_1.app.http('SaveChangeRequestDraft', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    handler: SaveChangeRequestDraft
});
//# sourceMappingURL=index.js.map