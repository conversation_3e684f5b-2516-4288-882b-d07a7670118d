{"version": 3, "file": "Sidebar.js", "sourceRoot": "", "sources": ["../../../../../apps/portal-shell/src/components/Sidebar.tsx"], "names": [], "mappings": ";;AAAA,iCAA0B;AAC1B,6DAE6B,CAAC,yBAAyB;AAEvD,oCAAoC;AACpC,MAAM,QAAQ,GAAG;IACf,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,0BAAI,EAAE;IACnD,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,0BAAI,EAAE;IACvD,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,6BAAO,EAAE;IAC5C,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,2BAAK,EAAE;IAC1C,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,+BAAS,EAAE;IACpD,EAAE,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,mBAAmB,EAAE,IAAI,EAAE,mCAAa,EAAE;CACzE,CAAC;AAEF,yBAAyB;AACzB,MAAM,cAAc,GAAG;IACrB,YAAY;IACZ,OAAO;IACP,oBAAoB;IACpB,yBAAyB;IACzB,oBAAoB;IACpB,sBAAsB;IACtB,QAAQ;CACT,CAAC;AASF,MAAM,OAAO,GAA2B,CAAC,EACvC,aAAa,EACb,UAAU,EACV,UAAU,EACV,QAAQ,EACT,EAAE,EAAE;IACH,+BAA+B;IAC/B,MAAM,cAAc,GAAG,CAAC,SAAiB,EAAE,EAAE;QAC3C,UAAU,CAAC,SAAS,CAAC,CAAC;IACxB,CAAC,CAAC;IAEF,6BAA6B;IAC7B,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC;IAEF,OAAO,CACL,CAAC,GAAG,CACF,EAAE,CAAC,SAAS,CACZ,SAAS,CAAC,CAAC,0BAA0B,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,0DAA0D,CAAC,CAE5H;MAAA,CAAC,sBAAsB,CACvB;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,gEAAgE,CAC7E;QAAA,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,uDAAuD,CAAC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAAC,aAAa,EAAE,GAAG,CACrI;QAAA,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,gCAAgC,CAC3F;UAAA,CAAC,0BAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACjB;QAAA,EAAE,MAAM,CACV;MAAA,EAAE,GAAG,CAEL;;MAAA,CAAC,sBAAsB,CACvB;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAC/C;QAAA,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,mDAAmD,CACvF;UAAA,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAC7B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CACzD,CAAC,CACJ;QAAA,EAAE,MAAM,CACV;MAAA,EAAE,GAAG,CAEL;;MAAA,CAAC,gBAAgB,CACjB;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,6BAA6B,CAC1C;QAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CACpB,CAAC,MAAM,CACL,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CACb,YAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CACtB,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CACvC,SAAS,CAAC,CAAC,2DAA2D,aAAa,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAC9J,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAElB;YAAA,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EACpB;YAAA,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,0BAA0B,CAAC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAC1F;UAAA,EAAE,MAAM,CAAC,CACV,CAAC,CACJ;MAAA,EAAE,GAAG,CAEL;;MAAA,CAAC,0BAA0B,CAC3B;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,8BAA8B,CAC3C;QAAA,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAChC;UAAA,CAAC,GAAG,CAAC,SAAS,CAAC,sFAAsF,CACnG;YAAA,CAAC,0BAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,YAAY,EACxC;UAAA,EAAE,GAAG,CACL;UAAA,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,UAAU,IAAI,QAAQ,EAAE,CAAC,CAC3D;YAAA,CAAC,oDAAoD,CACrD;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,8BAA8B,CAAC,WAAW,EAAE,GAAG,CAC9D;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,gCAAgC,CAAC,eAAe,EAAE,GAAG,CACtE;UAAA,EAAE,GAAG,CACP;QAAA,EAAE,GAAG,CACP;MAAA,EAAE,GAAG,CACP;IAAA,EAAE,GAAG,CAAC,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,OAAO,CAAC"}