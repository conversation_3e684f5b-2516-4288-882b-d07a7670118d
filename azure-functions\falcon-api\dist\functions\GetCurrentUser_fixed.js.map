{"version": 3, "file": "GetCurrentUser_fixed.js", "sourceRoot": "", "sources": ["../../src/functions/GetCurrentUser_fixed.ts"], "names": [], "mappings": ";;AAAA,gDAAyF;AACzF,oFAAiF;AACjF,mDAAgD;AAChD,mDAAyD;AAGzD,8DAA8D;AAC9D,SAAS,oBAAoB,CAAC,KAAa;IACvC,IAAI,CAAC,KAAK;QAAE,OAAO,MAAM,CAAC;IAE1B,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,uDAAuD;IACvD,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,OAAO,SAAS;aACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;aACvE,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;IAED,uDAAuD;IACvD,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AAChF,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,GAAgB,EAAE,OAA0B;IACtE,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAEhD,2EAA2E;IAC3E,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,8BAA8B;IACpF,eAAM,CAAC,IAAI,CAAC,+DAA+D,OAAO,CAAC,GAAG,CAAC,iBAAiB,oBAAoB,aAAa,EAAE,CAAC,CAAC;IAE7I,IAAI,aAAa,EAAE;QACf,8DAA8D;QAC9D,0EAA0E;QAC1E,eAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;QAE7F,6CAA6C;QAC7C,MAAM,OAAO,GAAG,sBAAsB,CAAC,CAAC,4CAA4C;QACpF,MAAM,KAAK,GAAG,8BAA8B,CAAC;QAC7C,MAAM,IAAI,GAAG,YAAY,CAAC;QAC1B,MAAM,QAAQ,GAAG,sCAAsC,CAAC;QAExD,eAAM,CAAC,IAAI,CAAC,qDAAqD,OAAO,YAAY,KAAK,aAAa,QAAQ,EAAE,CAAC,CAAC;QAElH,iEAAiE;QACjE,IAAI,YAAY,GAAsB,MAAM,6CAAqB,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAElG,qFAAqF;QACrF,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE;YACxB,eAAM,CAAC,IAAI,CAAC,+EAA+E,KAAK,EAAE,CAAC,CAAC;YACpG,YAAY,GAAG,MAAM,6CAAqB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEvE,wFAAwF;YACxF,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,EAAE;gBACzC,eAAM,CAAC,IAAI,CAAC,4EAA4E,YAAY,CAAC,EAAE,OAAO,OAAO,EAAE,CAAC,CAAC;gBACzH,MAAM,6CAAqB,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAChF,8CAA8C;gBAC9C,YAAY,CAAC,EAAE,GAAG,OAAO,CAAC;aAC7B;SACJ;QAED,IAAI,YAAY,EAAE;YACd,yGAAyG;YACzG,MAAM,WAAW,GAAe;gBAC5B,GAAG,YAAY;gBACf,IAAI,EAAE,IAAI,IAAI,YAAY,CAAC,IAAI;gBAC/B,KAAK,EAAE,KAAK,IAAI,YAAY,CAAC,KAAK;gBAClC,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,qDAAqD,YAAY,CAAC,KAAK,gBAAgB,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACnI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;SACjD;aAAM;YACH,wEAAwE;YACxE,eAAM,CAAC,IAAI,CAAC,sCAAsC,OAAO,KAAK,KAAK,qEAAqE,CAAC,CAAC;YAE1I,gDAAgD;YAChD,MAAM,OAAO,GAAG,2BAA2B,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEhD,gDAAgD;YAChD,MAAM,WAAW,GAAe;gBAC5B,EAAE,EAAE,OAAO;gBACX,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,KAAK,IAAI,OAAO;gBACvB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,SAAS;gBACpB,KAAK,EAAE,CAAC,UAAU,CAAC;gBACnB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,0DAA0D,KAAK,eAAe,OAAO,EAAE,CAAC,CAAC;YACrG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;SACjD;KACJ;IAED,oDAAoD;IACpD,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,GAAG,CAAC,CAAC;IAC1C,IAAI,CAAC,SAAS,EAAE;QACZ,eAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QAC5E,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,CAAC;KACzF;IAED,IAAI;QACA,gDAAgD;QAChD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAClD,KAAK,CAAC,GAAG,KAAK,+DAA+D;YAC7E,KAAK,CAAC,GAAG,KAAK,KAAK,CACtB,EAAE,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC;QAE3B,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAChD,KAAK,CAAC,GAAG,KAAK,oEAAoE;YAClF,KAAK,CAAC,GAAG,KAAK,OAAO;YACrB,KAAK,CAAC,GAAG,KAAK,oBAAoB,CACrC,EAAE,GAAG,IAAI,SAAS,CAAC,WAAW,CAAC;QAEhC,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAC/C,KAAK,CAAC,GAAG,KAAK,4DAA4D;YAC1E,KAAK,CAAC,GAAG,KAAK,MAAM,CACvB,EAAE,GAAG,IAAI,oBAAoB,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC;QAEjD,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CACnD,KAAK,CAAC,GAAG,KAAK,uDAAuD;YACrE,KAAK,CAAC,GAAG,KAAK,KAAK,CACtB,EAAE,GAAG,CAAC;QAEP,eAAM,CAAC,IAAI,CAAC,8CAA8C,OAAO,YAAY,KAAK,aAAa,QAAQ,EAAE,CAAC,CAAC;QAE3G,iEAAiE;QACjE,IAAI,YAAY,GAAsB,MAAM,6CAAqB,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAElG,qFAAqF;QACrF,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE;YACxB,eAAM,CAAC,IAAI,CAAC,iEAAiE,KAAK,EAAE,CAAC,CAAC;YACtF,YAAY,GAAG,MAAM,6CAAqB,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEvE,wFAAwF;YACxF,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,EAAE;gBACzC,eAAM,CAAC,IAAI,CAAC,8DAA8D,YAAY,CAAC,EAAE,OAAO,OAAO,EAAE,CAAC,CAAC;gBAC3G,MAAM,6CAAqB,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBAChF,8CAA8C;gBAC9C,YAAY,CAAC,EAAE,GAAG,OAAO,CAAC;aAC7B;SACJ;QAED,IAAI,YAAY,EAAE;YACd,yGAAyG;YACzG,MAAM,WAAW,GAAe;gBAC5B,GAAG,YAAY;gBACf,IAAI,EAAE,IAAI,IAAI,YAAY,CAAC,IAAI;gBAC/B,KAAK,EAAE,KAAK,IAAI,YAAY,CAAC,KAAK;gBAClC,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,uCAAuC,YAAY,CAAC,KAAK,gBAAgB,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrH,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;SACjD;aAAM;YACH,wEAAwE;YACxE,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,KAAK,KAAK,qEAAqE,CAAC,CAAC;YAE5H,gDAAgD;YAChD,MAAM,OAAO,GAAG,2BAA2B,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEhD,gDAAgD;YAChD,MAAM,WAAW,GAAe;gBAC5B,EAAE,EAAE,OAAO;gBACX,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,KAAK,IAAI,OAAO;gBACvB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,SAAS;gBACpB,KAAK,EAAE,CAAC,UAAU,CAAC;gBACnB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,4CAA4C,KAAK,eAAe,OAAO,EAAE,CAAC,CAAC;YACvF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;SACjD;KACJ;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,yCAAyC,SAAS,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAClF,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,0CAA0C,EAAE,EAAE,CAAC;KAC3F;AACL,CAAC;AAED,sEAAsE;AACtE,SAAS,2BAA2B,CAAC,KAAa,EAAE,QAAiB;IACjE,IAAI,CAAC,KAAK;QAAE,OAAO,iBAAiB,CAAC;IAErC,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;IAEvD,iCAAiC;IACjC,QAAQ,WAAW,EAAE;QACjB,KAAK,mBAAmB,CAAC;QACzB,KAAK,uCAAuC;YACxC,OAAO,yBAAyB,CAAC;QACrC,KAAK,YAAY,CAAC;QAClB,KAAK,wBAAwB;YACzB,OAAO,YAAY,CAAC;QACxB,KAAK,iBAAiB;YAClB,OAAO,cAAc,CAAC;QAC1B,KAAK,YAAY;YACb,OAAO,QAAQ,CAAC;QACpB,KAAK,aAAa;YACd,OAAO,SAAS,CAAC;QACrB,KAAK,YAAY;YACb,OAAO,QAAQ,CAAC;QACpB,KAAK,0BAA0B;YAC3B,OAAO,sBAAsB,CAAC;QAClC;YACI,uCAAuC;YACvC,IAAI,QAAQ,EAAE;gBACV,QAAQ,QAAQ,EAAE;oBACd,KAAK,sCAAsC;wBACvC,OAAO,yBAAyB,CAAC;oBACrC,KAAK,sCAAsC,CAAC;oBAC5C,KAAK,sCAAsC;wBACvC,OAAO,cAAc,CAAC;oBAC1B;wBACI,OAAO,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;wBAC/C,OAAO,iBAAiB,CAAC;iBAChC;aACJ;YACD,OAAO,CAAC,IAAI,CAAC,yBAAyB,WAAW,EAAE,CAAC,CAAC;YACrD,OAAO,iBAAiB,CAAC;KAChC;AACL,CAAC;AAED,sDAAsD;AACtD,SAAS,oBAAoB,CAAC,WAAmB;IAC7C,QAAQ,WAAW,EAAE;QACjB,KAAK,yBAAyB;YAC1B,OAAO,CAAC,CAAC;QACb,KAAK,YAAY;YACb,OAAO,CAAC,CAAC;QACb,KAAK,cAAc;YACf,OAAO,CAAC,CAAC;QACb,KAAK,QAAQ;YACT,OAAO,CAAC,CAAC;QACb,KAAK,SAAS;YACV,OAAO,CAAC,CAAC;QACb,KAAK,QAAQ;YACT,OAAO,CAAC,CAAC;QACb,KAAK,sBAAsB;YACvB,OAAO,CAAC,CAAC;QACb;YACI,OAAO,CAAC,CAAC,CAAC,gCAAgC;KACjD;AACL,CAAC;AAED,eAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE;IACvB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,cAAc;IACrB,OAAO,EAAE,cAAc;CAC1B,CAAC,CAAC"}