// Knowledge Hub API Service
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:7071/api';

// Interfaces matching the backend responses
export interface Document {
  id: string;
  title: string;
  description?: string;
  fileName: string;
  fileExtension: string;
  fileSizeKB: number;
  category: {
    id: string;
    name: string;
  };
  company: string;
  isPublished: boolean;
  version: string;
  downloadCount: number;
  viewCount: number;
  createdBy: {
    name: string;
    email: string;
  };
  createdDate: string;
  lastModified?: string;
  tags: string[];
}

export interface DocumentCategory {
  id: string;
  name: string;
  parentId?: string;
  documentCount: number;
}

export interface KnowledgeArticle {
  id: string;
  title: string;
  summary: string;
  content: string;
  category: {
    id: string;
    name: string;
  };
  company: string;
  isPublished: boolean;
  viewCount: number;
  rating: number;
  createdBy: {
    name: string;
    email: string;
  };
  createdDate: string;
  lastModified?: string;
  tags: string[];
}

export interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface DocumentsResponse {
  documents: Document[];
  pagination: PaginationInfo;
}

export interface ArticlesResponse {
  articles: KnowledgeArticle[];
  pagination: PaginationInfo;
}

export interface UploadDocumentRequest {
  title: string;
  description?: string;
  categoryId?: string;
  tags?: string[];
  isPublished?: boolean;
}

export interface UploadDocumentResponse {
  message: string;
  document: Document;
}

// API Functions
export const fetchDocuments = async (params: {
  search?: string;
  categoryId?: string;
  companyId?: string;
  page?: number;
  limit?: number;
}): Promise<DocumentsResponse> => {
  const queryParams = new URLSearchParams();
  
  if (params.search) queryParams.append('search', params.search);
  if (params.categoryId) queryParams.append('categoryId', params.categoryId);
  if (params.companyId) queryParams.append('companyId', params.companyId);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());

  const response = await fetch(`${API_BASE_URL}/documents?${queryParams}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch documents: ${response.statusText}`);
  }

  return response.json();
};

export const fetchDocumentCategories = async (): Promise<DocumentCategory[]> => {
  const response = await fetch(`${API_BASE_URL}/documents/categories`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch categories: ${response.statusText}`);
  }

  return response.json();
};

export const fetchKnowledgeArticles = async (params: {
  search?: string;
  categoryId?: string;
  companyId?: string;
  page?: number;
  limit?: number;
}): Promise<ArticlesResponse> => {
  const queryParams = new URLSearchParams();
  
  if (params.search) queryParams.append('search', params.search);
  if (params.categoryId) queryParams.append('categoryId', params.categoryId);
  if (params.companyId) queryParams.append('companyId', params.companyId);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());

  const response = await fetch(`${API_BASE_URL}/knowledge/articles?${queryParams}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch articles: ${response.statusText}`);
  }

  return response.json();
};

// New upload function
export const uploadDocument = async (file: File, metadata: UploadDocumentRequest): Promise<UploadDocumentResponse> => {
  // Convert file to base64
  const fileContent = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Remove data URL prefix (data:type;base64,)
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

  const requestData = {
    file: {
      name: file.name,
      content: fileContent,
      size: file.size,
      type: file.type
    },
    metadata
  };

  const response = await fetch(`${API_BASE_URL}/documents/upload`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(requestData)
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: response.statusText }));
    throw new Error(errorData.error || `Failed to upload document: ${response.statusText}`);
  }

  return response.json();
};

// Download document function
export const downloadDocument = async (documentId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/documents/${documentId}/download`, {
    method: 'GET',
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: response.statusText }));
    throw new Error(errorData.error || `Failed to download document: ${response.statusText}`);
  }

  // Handle different response types
  if (response.status === 302) {
    // Production mode - redirect to blob storage
    const location = response.headers.get('Location');
    if (location) {
      window.open(location, '_blank');
    }
  } else {
    // Development mode or other response
    const result = await response.json();
    console.log('Download result:', result);
    // For development, could show a modal or notification
    if (result.note) {
      alert(result.note);
    }
  }
};

// Helper functions for formatting
export const formatFileSize = (sizeKB: number): string => {
  if (sizeKB < 1024) {
    return `${sizeKB} KB`;
  } else if (sizeKB < 1024 * 1024) {
    return `${(sizeKB / 1024).toFixed(1)} MB`;
  } else {
    return `${(sizeKB / (1024 * 1024)).toFixed(1)} GB`;
  }
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Mock data fallback for development
export const getMockDocuments = (): Document[] => [
  {
    id: 'doc-1',
    title: 'Employee Handbook 2025',
    description: 'Comprehensive guide for all employees covering policies, procedures, and company culture.',
    fileName: 'Employee_Handbook_2025.pdf',
    fileExtension: 'pdf',
    fileSizeKB: 2048,
    category: { id: 'policies', name: 'Company Policies' },
    company: 'Group-wide',
    isPublished: true,
    version: '2.1',
    downloadCount: 245,
    viewCount: 892,
    createdBy: { name: 'HR Admin', email: '<EMAIL>' },
    createdDate: '2025-01-15T10:30:00Z',
    lastModified: '2025-01-20T14:20:00Z',
    tags: ['policy', 'handbook', 'hr', 'new-employee']
  },
  {
    id: 'doc-2',
    title: 'Travel Expense Policy',
    description: 'Guidelines for submitting and approving travel expenses.',
    fileName: 'Travel_Expense_Policy.pdf',
    fileExtension: 'pdf',
    fileSizeKB: 512,
    category: { id: 'policies', name: 'Company Policies' },
    company: 'Group-wide',
    isPublished: true,
    version: '1.3',
    downloadCount: 156,
    viewCount: 423,
    createdBy: { name: 'Finance Team', email: '<EMAIL>' },
    createdDate: '2024-12-10T09:15:00Z',
    lastModified: '2025-01-05T11:45:00Z',
    tags: ['policy', 'travel', 'expenses', 'finance']
  }
];

export const getMockCategories = (): DocumentCategory[] => [
  { id: 'policies', name: 'Company Policies', documentCount: 12 },
  { id: 'procedures', name: 'Standard Procedures', documentCount: 8 },
  { id: 'training', name: 'Training Materials', documentCount: 15 },
  { id: 'compliance', name: 'Compliance Documents', documentCount: 6 },
  { id: 'it-guides', name: 'IT Guides', documentCount: 10 }
];

export const getMockArticles = (): KnowledgeArticle[] => [
  {
    id: 'art-1',
    title: 'How to Submit a Leave Request',
    summary: 'Step-by-step guide for submitting leave requests through the HR portal.',
    content: 'Detailed instructions for leave request submission...',
    category: { id: 'hr-guides', name: 'HR Guides' },
    company: 'Group-wide',
    isPublished: true,
    viewCount: 342,
    rating: 4.5,
    createdBy: { name: 'HR Team', email: '<EMAIL>' },
    createdDate: '2025-01-10T14:30:00Z',
    tags: ['hr', 'leave', 'guide', 'process']
  }
]; 