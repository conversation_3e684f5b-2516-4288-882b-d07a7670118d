{"version": 3, "file": "SyncUserFromEntra.js", "sourceRoot": "", "sources": ["../../src/functions/SyncUserFromEntra.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAkG;AAClG,kEAA+D;AAC/D,2CAA6B;AAQ7B,4CAA4C;AAC5C,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,+BAA+B;AAE/D,KAAK,UAAU,iBAAiB,CAAC,OAAoB,EAAE,OAA0B;IACpF,OAAO,CAAC,GAAG,CAAC,8DAA8D,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;IAC1F,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IAEnD,oCAAoC;IACpC,IAAI,mBAA2B,CAAC;IAEhC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE;QAC3E,eAAM,CAAC,IAAI,CAAC,6EAA6E,CAAC,CAAC;QAC3F,mBAAmB,GAAG,CAAC,CAAC,CAAC,sCAAsC;KAClE;SAAM;QACH,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE;YACZ,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YAClE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,CAAC;SAC1F;QAED,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE;YAC9C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,oDAAoD,aAAa,IAAI,CAAC,CAAC;YACzI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,yDAAyD,EAAE,EAAE,CAAC;SAC1G;QAED,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,UAAU,EAAE;YACb,eAAM,CAAC,KAAK,CAAC,oFAAoF,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;YAChJ,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,6EAA6E,EAAE,EAAE,CAAC;SAC9H;QACD,mBAAmB,GAAG,UAAU,CAAC;KACpC;IAED,eAAM,CAAC,IAAI,CAAC,wCAAwC,mBAAmB,EAAE,CAAC,CAAC;IAE3E,qCAAqC;IACrC,IAAI,UAAU,GAAoB,EAAE,CAAC;IAErC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;QAC3B,IAAI;YACA,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,EAAqB,CAAC;SACxD;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;YACxE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,CAAC;SAChF;KACJ;IAED,8DAA8D;IAC9D,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7D,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC;IAE9C,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE;QACvB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,wDAAwD,EAAE,EAAE,CAAC;KACzG;IAED,IAAI;QACA,IAAI,QAAQ,EAAE;YACV,iCAAiC;YACjC,OAAO,MAAM,YAAY,CAAC,mBAAmB,CAAC,CAAC;SAClD;aAAM;YACH,qBAAqB;YACrB,OAAO,MAAM,cAAc,CAAC,OAAQ,EAAE,mBAAmB,CAAC,CAAC;SAC9D;KAEJ;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC9E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,uDAAuD;gBAC9D,OAAO,EAAE,YAAY;aACxB;SACJ,CAAC;KACL;AACL,CAAC;AAxED,8CAwEC;AAED,qBAAqB;AACrB,KAAK,UAAU,cAAc,CAAC,OAAe,EAAE,gBAAwB;IACnE,eAAM,CAAC,IAAI,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;IAEzD,0CAA0C;IAC1C,MAAM,cAAc,GAAG;;;;KAItB,CAAC;IACF,MAAM,eAAe,GAAqB;QACtC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;KAC1D,CAAC;IAEF,MAAM,UAAU,GAAG,MAAM,IAAA,iBAAY,EAAC,cAAc,EAAE,eAAe,CAAC,CAAC;IAEvE,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5D,eAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,yBAAyB,CAAC,CAAC;QACpE,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,sBAAsB,OAAO,mEAAmE;aAC1G;SACJ,CAAC;KACL;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACvC,eAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,MAAM,mBAAmB,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IAErH,kCAAkC;IAClC,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACpE,IAAI,CAAC,SAAS,EAAE;QACZ,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,KAAK,EAAE,sBAAsB,OAAO,mCAAmC;aAC1E;SACJ,CAAC;KACL;IAED,kCAAkC;IAClC,MAAM,WAAW,GAAG;;;;;;;;;KASnB,CAAC;IAEF,MAAM,YAAY,GAAqB;QACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;QACvD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE;QACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE;QACnE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE;QAC7D,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE;QAC7E,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;KACjE,CAAC;IAEF,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IAE9C,eAAM,CAAC,IAAI,CAAC,4CAA4C,MAAM,CAAC,MAAM,gBAAgB,CAAC,CAAC;IAEvF,OAAO;QACH,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE;YACN,OAAO,EAAE,8CAA8C;YACvD,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,aAAa,EAAE;gBACX,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,aAAa,EAAE,SAAS,CAAC,aAAa;aACzC;SACJ;KACJ,CAAC;AACN,CAAC;AAED,oCAAoC;AACpC,KAAK,UAAU,YAAY,CAAC,gBAAwB;IAChD,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;IAE/D,kCAAkC;IAClC,MAAM,gBAAgB,GAAG;;;;KAIxB,CAAC;IAEF,MAAM,WAAW,GAAG,MAAM,IAAA,iBAAY,EAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAE7D,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9D,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,wCAAwC;gBACjD,WAAW,EAAE,CAAC;aACjB;SACJ,CAAC;KACL;IAED,MAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;IAChD,eAAM,CAAC,IAAI,CAAC,SAAS,UAAU,gBAAgB,CAAC,CAAC;IAEjD,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,oBAAoB;IACpB,KAAK,MAAM,MAAM,IAAI,WAAW,CAAC,SAAS,EAAE;QACxC,IAAI;YACA,eAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,MAAM,eAAe,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;YAE3E,+BAA+B;YAC/B,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,EAAE;gBACZ,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,OAAO,uCAAuC,CAAC,CAAC;gBAC3E,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,CAAC,OAAO,kCAAkC,CAAC,CAAC;gBACtE,UAAU,EAAE,CAAC;gBACb,SAAS;aACZ;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAAG;;;;;;;;;aASnB,CAAC;YAEF,MAAM,YAAY,GAAqB;gBACnC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE;gBACvD,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,SAAS,EAAE;gBACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE;gBACnE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE;gBAC7D,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,aAAa,EAAE;gBAC7E,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,EAAE;aACjE,CAAC;YAEF,MAAM,IAAA,iBAAY,EAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAC9C,WAAW,EAAE,CAAC;YAEd,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;SAE9D;QAAC,OAAO,KAAK,EAAE;YACZ,eAAM,CAAC,KAAK,CAAC,sBAAsB,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,IAAI,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAChH,UAAU,EAAE,CAAC;SAChB;KACJ;IAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,aAAa,UAAU,YAAY,UAAU,EAAE,CAAC,CAAC;IAExG,OAAO;QACH,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE;YACN,OAAO,EAAE,wBAAwB,WAAW,6BAA6B;YACzE,UAAU,EAAE,UAAU;YACtB,WAAW,EAAE,WAAW;YACxB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;SACjD;KACJ,CAAC;AACN,CAAC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE;IAC1B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IACxB,KAAK,EAAE,iCAAiC;IACxC,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,iBAAiB;CAC7B,CAAC,CAAC"}