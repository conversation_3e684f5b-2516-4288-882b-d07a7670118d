"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRole = void 0;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
const sql = __importStar(require("mssql"));
async function createRole(request, context) {
    context.log(`Http function processed request for url "${request.url}"`);
    let body;
    try {
        body = await request.json();
    }
    catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }
    if (!body || !body.name || !body.name.trim()) {
        return {
            status: 400,
            jsonBody: { message: "Role 'name' is required." }
        };
    }
    try {
        const roleName = body.name.trim();
        const description = body.description?.trim() || null;
        const createdBy = 1; // Placeholder
        // 1. Check if role already exists (case-insensitive)
        const checkQuery = 'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName)';
        // Refactored parameters
        const checkParams = [
            { name: 'RoleName', type: sql.NVarChar, value: roleName }
        ];
        const checkResult = await (0, db_1.executeQuery)(checkQuery, checkParams);
        if (checkResult.recordset[0].Count > 0) {
            logger_1.logger.warn(`Role creation attempt failed: Role '${roleName}' already exists.`);
            return {
                status: 409,
                jsonBody: { message: `Role with name '${roleName}' already exists.` }
            };
        }
        // 3. Insert new role
        const query = `
            INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate, ModifiedDate) 
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription, INSERTED.IsActive 
            VALUES (@RoleNameParam, @DescriptionParam, 0, 1, @CreatedByParam, GETUTCDATE(), GETUTCDATE())
        `;
        // Refactored parameters
        const insertParams = [
            { name: 'RoleNameParam', type: sql.NVarChar, value: roleName },
            { name: 'DescriptionParam', type: sql.NVarChar, value: description },
            { name: 'CreatedByParam', type: sql.Int, value: createdBy }
        ];
        const result = await (0, db_1.executeQuery)(query, insertParams);
        if (!result.recordset || result.recordset.length === 0) {
            logger_1.logger.error("Role creation failed: No record returned after insert.");
            return {
                status: 500,
                jsonBody: {
                    message: "Error creating role.",
                    error: "No record returned after insert."
                }
            };
        }
        const outputRow = result.recordset[0];
        const newRole = {
            RoleID: outputRow.RoleID,
            RoleName: outputRow.RoleName,
            Description: outputRow.RoleDescription
        };
        return {
            status: 201,
            jsonBody: newRole
        };
    }
    catch (error) {
        context.error(`Error creating role: ${error instanceof Error ? error.message : String(error)}`);
        return {
            status: 500,
            jsonBody: {
                message: "Error creating role.",
                error: error instanceof Error ? error.message : String(error)
            }
        };
    }
}
exports.createRole = createRole;
functions_1.app.http('CreateRole', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'roles/create',
    handler: createRole
});
//# sourceMappingURL=CreateRole.js.map