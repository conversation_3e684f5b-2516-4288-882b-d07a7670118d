import { IPublicClientApplication, AccountInfo } from "@azure/msal-browser";
import { loginRequest } from "../authConfig";

// Define core types needed for the new approach
export interface PortalUser {
  id: string; // User ID (e.g., Entra ID Object ID)
  internalId?: number; // Internal database ID
  name: string;
  email: string;
  company: string; // e.g., "SASMOS HET", "SASMOS CMT"
  companyId?: number; // Company ID from database
  roles: string[]; // Portal-specific roles
  status: 'Active' | 'Inactive'; // Portal-specific status
  lastLogin?: string; // Tracked by portal or Entra ID?
}

// NEW: Define Role type
export interface RoleDefinition {
  id: string;
  name: string;
  description?: string;
}

// Backend role interface (from database)
interface BackendRole {
  RoleID: number;
  RoleName: string;
  Description: string;
}

export const PORTAL_STATUSES: PortalUser['status'][] = ['Active', 'Inactive'];

// Helper to get current MSAL context
let currentMsalContext: { instance: IPublicClientApplication; accounts: AccountInfo[] } | null = null;

// Function to set the MSAL context (to be called from components)
export const setMsalContext = (instance: IPublicClientApplication, accounts: AccountInfo[]) => {
  currentMsalContext = { instance, accounts };
};

// Helper function to make API calls with authentication
const callApiWithAuth = async (endpoint: string, options: RequestInit = {}): Promise<unknown> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  // If we have an authenticated account, get the access token
  if (currentMsalContext && currentMsalContext.accounts.length > 0) {
    const account = currentMsalContext.accounts[0];
    try {
      const tokenRequest = {
        ...loginRequest,
        account: account,
      };
      
      const response = await currentMsalContext.instance.acquireTokenSilent(tokenRequest);
      // Use idToken for authentication with our own API (not accessToken!)
      if (response.idToken) {
        headers['Authorization'] = `Bearer ${response.idToken}`;
      } else {
        console.warn('No ID token received from MSAL');
      }
    } catch (tokenError) {
      console.warn('Failed to acquire token silently:', tokenError);
      // Continue without token - backend will handle appropriately
    }
  }

  const response = await fetch(`/api${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }

  // Handle 204 No Content responses (like successful DELETE operations)
  if (response.status === 204 || response.headers.get('content-length') === '0') {
    return null;
  }

  return response.json();
};

// Legacy function for backward compatibility
const callApi = callApiWithAuth;

// Convert backend user data to frontend format
const transformBackendUser = (backendUser: Record<string, unknown>): PortalUser => {
  console.log('🔄 Transforming backend user:', backendUser);
  
  // Handle roles - can be string (comma-separated) or array
  let roles: string[] = [];
  if (typeof backendUser.roles === 'string') {
    roles = backendUser.roles.split(',').map(role => role.trim()).filter(role => role.length > 0);
  } else if (Array.isArray(backendUser.roles)) {
    roles = backendUser.roles;
  }
  
  const transformedUser: PortalUser = {
    id: backendUser.id as string || backendUser.UserID as string,
    internalId: backendUser.internalId as number || backendUser.UserID as number,
    name: backendUser.name as string || backendUser.Username as string,
    email: backendUser.email as string || backendUser.Email as string,
    company: backendUser.company as string || backendUser.CompanyName as string,
    companyId: backendUser.companyId as number || backendUser.CompanyID as number,
    roles,
    status: backendUser.status as PortalUser['status'] || (backendUser.IsActive ? 'Active' : 'Inactive'),
    lastLogin: backendUser.lastLogin as string
  };
  
  console.log('✅ Transformed user:', transformedUser.name, 'ID:', transformedUser.id, 'Email:', transformedUser.email);
  return transformedUser;
};

// Convert backend role data to frontend format
const transformBackendRole = (backendRole: BackendRole): RoleDefinition => {
  return {
    id: backendRole.RoleID.toString(),
    name: backendRole.RoleName,
    description: backendRole.Description,
  };
};

// --- API Functions (Revised Approach) ---

// Fetch ALL users matching filters, merging managed data
export interface PaginatedPortalUsersResponse {
    users: PortalUser[];
    totalCount: number;
}

export const fetchPortalUsers = async (
    searchTerm?: string, 
    companyFilter?: string, 
    roleFilter?: string, 
    statusFilter?: PortalUser['status'] | 'All', 
    page: number = 1, 
    pageSize: number = 10
): Promise<PaginatedPortalUsersResponse> => {
    try {
        // Build query parameters
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (companyFilter && companyFilter !== 'All') params.append('company', companyFilter);
        if (roleFilter && roleFilter !== 'All') params.append('role', roleFilter);
        if (statusFilter && statusFilter !== 'All') params.append('status', statusFilter);
        params.append('page', page.toString());
        params.append('pageSize', pageSize.toString());

        const queryString = params.toString();
        const endpoint = `/portal-users${queryString ? `?${queryString}` : ''}`;
        
        console.log('Fetching users from:', endpoint);
        console.log('Status filter being sent:', statusFilter);
        console.log('All parameters:', { searchTerm, companyFilter, roleFilter, statusFilter, page, pageSize });
        
        const response = await callApi(endpoint) as { users: Record<string, unknown>[], totalCount: number };
        
        console.log('Backend response:', response);
        
        // Transform backend data to frontend format
        const transformedUsers = response.users.map(transformBackendUser);
        
        return {
            users: transformedUsers,
            totalCount: response.totalCount || transformedUsers.length
        };
    } catch (error) {
        console.error('Error fetching portal users:', error);
        throw error;
    }
};

// Fetch a specific user (check managed first, then directory for defaults)
export const fetchPortalUser = async (userId: string): Promise<PortalUser | null> => {
    try {
        console.log(`Fetching user by ID: ${userId}`);
        const response = await callApi(`/portal-users/${userId}`) as Record<string, unknown>;
        
        if (response) {
            console.log('Found user:', response);
            return transformBackendUser(response);
        } else {
            console.warn(`User with ID ${userId} not found`);
            return null;
        }
        
    } catch (error) {
        console.error(`Error fetching portal user ${userId}:`, error);
        // Do not re-throw, just return null as the user might not be in the portal DB yet
        return null;
    }
};

// Update roles/status - Creates entry if user isn't already managed
export const updatePortalUser = async (userId: string, data: Pick<PortalUser, 'roles' | 'status'>): Promise<PortalUser | null> => {
    try {
        console.log('Updating user:', userId, data);
        
        const backendData = {
            roles: data.roles,
            isActive: data.status === 'Active'
        };

        const response = await callApi(`/portal-users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(backendData),
        }) as Record<string, unknown>;

        if (response) {
             return transformBackendUser(response);
        }
       
        return null;
        
    } catch (error) {
        console.error('Error updating portal user:', error);
        
        // Provide helpful error message
        if (error instanceof Error && error.message.includes('404')) {
            throw new Error('User update service is temporarily unavailable. The UpdatePortalUser endpoint is not loaded in the Azure Functions runtime. Please restart the backend or check the function imports.');
        }
        
        throw error;
    }
};

// --- Role Definition Management Functions (Now using real API) ---

export const fetchRoleDefinitions = async (): Promise<RoleDefinition[]> => {
    try {
        console.log('Fetching roles from database...');
        const response = await callApi('/roles') as BackendRole[];
        console.log('Roles response:', response);
        
        // Transform backend roles to frontend format
        const transformedRoles = response.map(transformBackendRole);
        return transformedRoles;
    } catch (error) {
        console.error('Error fetching role definitions:', error);
        throw error;
    }
};

export const createRoleDefinition = async (roleData: Omit<RoleDefinition, 'id'>): Promise<RoleDefinition> => {
    try {
        if (!roleData || !roleData.name || !roleData.name.trim()) {
            throw new Error("Role name cannot be empty.");
        }
        
        const response = await callApi('/roles/create', {
            method: 'POST',
            body: JSON.stringify({
                name: roleData.name,
                description: roleData.description || ''
            })
        }) as BackendRole;
        
        return transformBackendRole(response);
    } catch (error) {
        console.error('Error creating role definition:', error);
        throw error;
    }
};

export const updateRoleDefinition = async (roleId: string, roleData: Partial<Omit<RoleDefinition, 'id'>>): Promise<RoleDefinition | null> => {
    try {
        if (roleData.name && roleData.name.trim() === '') {
            throw new Error("Role name cannot be empty.");
        }
        
        const updateData: Record<string, unknown> = {};
        if (roleData.name) updateData.RoleName = roleData.name;
        if (roleData.description !== undefined) updateData.RoleDescription = roleData.description;
        
        const response = await callApi(`/roles/${roleId}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        }) as BackendRole;
        
        return transformBackendRole(response);
    } catch (error) {
        console.error('Error updating role definition:', error);
        throw error;
    }
};

export const deleteRoleDefinition = async (roleId: string): Promise<boolean> => {
    try {
        await callApi(`/roles/${roleId}`, {
            method: 'DELETE'
        });
        
        return true;
    } catch (error) {
        console.error('Error deleting role definition:', error);
        throw error;
    }
};

// Function to get available portal role *names* (used by User Edit Page)
export const fetchPortalRoleNames = async (): Promise<string[]> => {
    try {
        const response = await callApi('/roles') as { RoleID: number; RoleName: string; Description: string }[];
        return response.map(role => role.RoleName);
    } catch (error) {
        console.error('Error fetching portal role names:', error);
        throw error;
    }
};

// Create a new user
export const createPortalUser = async (userData: { email: string; roles: string[]; status: 'Active' | 'Inactive' }): Promise<PortalUser | null> => {
    try {
        console.log('Creating user:', userData);
        
        // Transform frontend data to backend format
        const createData = {
            email: userData.email,
            roles: userData.roles,
            status: userData.status
        };
        
        const response = await callApi('/portal-users', {
            method: 'POST',
            body: JSON.stringify(createData)
        }) as { message: string; userId: number; entraId?: string };
        
        console.log('Create response:', response);
        
        // If creation was successful, return a basic user object indicating success
        if (response.message && response.message.includes("successfully")) {
            // Return a basic user object with the provided data
            // Don't try to fetch the user with placeholder ID - just indicate success
            const createdUser: PortalUser = {
                id: response.userId?.toString() || Date.now().toString(), // Use userId if available, otherwise timestamp
                internalId: response.userId,
                name: userData.email.split('@')[0], // Extract name from email
                email: userData.email,
                company: 'Unknown', // Will be updated when user list refreshes
                roles: userData.roles,
                status: userData.status,
                lastLogin: undefined
            };
            return createdUser;
        }
        
        throw new Error(response.message || 'User creation failed');
    } catch (error: unknown) {
        console.error('Error creating portal user:', error);
        
        // Handle specific error cases for better user experience
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (errorMessage.includes('409')) {
            throw new Error(`A user with the email address "${userData.email}" already exists in the system. Please use a different email address or check if the user is already registered.`);
        } else if (errorMessage.includes('400')) {
            throw new Error('Invalid user data provided. Please check the email format and selected roles.');
        } else if (errorMessage.includes('500')) {
            throw new Error('Server error occurred while creating the user. Please try again later or contact support.');
        } else {
            // For other errors, try to extract meaningful message
            throw new Error(errorMessage || 'Failed to create user. Please try again.');
        }
    }
};

// Delete a user from the portal (removes portal access, doesn't affect Entra ID)
export const deletePortalUser = async (userId: string): Promise<boolean> => {
    try {
        console.log('Deleting portal access for user:', userId);
        
        await callApi(`/portal-users/${userId}`, {
            method: 'DELETE'
        });
        
        console.log('✅ User deletion successful for ID:', userId);
        
        return true;
    } catch (error) {
        console.error('Error deleting portal user:', error);
        
        // Handle specific error cases for better user experience
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (errorMessage.includes('404')) {
            throw new Error('User deletion service is temporarily unavailable. The DeletePortalUser endpoint is not loaded in the Azure Functions runtime.');
        } else if (errorMessage.includes('403')) {
            throw new Error('You do not have permission to remove users from the portal.');
        } else if (errorMessage.includes('500')) {
            throw new Error('Server error occurred while removing the user. Please try again later.');
        } else {
            throw new Error(errorMessage || 'Failed to remove user from portal. Please try again.');
        }
    }
};

// TODO: Add function updateUserRoles(userId, roles) -> This might be part of updateUser or separate

// TODO: Add function fetchUser(userId)
// TODO: Add function updateUser(userId, userData)
// TODO: Add function createUser(userData)
// TODO: Add function fetchRoles() -> return MOCK_ROLES
// TODO: Add function updateUserRoles(userId, roles)

// Add interface for Company
export interface Company {
    CompanyID: number;
    CompanyName: string;
    CompanyCode: string;
    IsActive: boolean;
}

// Add function to fetch companies
export const fetchCompanies = async (): Promise<Company[]> => {
    try {
        const response = await callApi('/companies') as Company[];
        return response;
    } catch (error) {
        console.error('Error fetching companies:', error);
        throw error;
    }
};

// Add function to fetch company names
export const fetchCompanyNames = async (): Promise<string[]> => {
    try {
        const response = await callApi('/companies') as Company[];
        return ['All', ...response.map(company => company.CompanyName)];
    } catch (error) {
        console.error('Error fetching company names:', error);
        // Fallback to hardcoded values if API fails
        return ['All', 'Avirata Defence Systems', 'SASMOS HET'];
    }
}; 