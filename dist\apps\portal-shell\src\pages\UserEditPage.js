"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const react_router_dom_1 = require("react-router-dom");
const adminApi_1 = require("../services/adminApi");
const feather_icons_react_1 = require("feather-icons-react");
const react_hot_toast_1 = require("react-hot-toast");
const UserEditPage = () => {
    const { userId } = (0, react_router_dom_1.useParams)();
    const navigate = (0, react_router_dom_1.useNavigate)();
    // State for user data, form data (roles/status), loading/saving, errors, roles
    const [user, setUser] = (0, react_1.useState)(null);
    const [formData, setFormData] = (0, react_1.useState)({ roles: [], status: 'Inactive' });
    const [availableRoles, setAvailableRoles] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(true);
    const [isSaving, setIsSaving] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    // Fetch user details and available roles
    (0, react_1.useEffect)(() => {
        const loadData = () => __awaiter(void 0, void 0, void 0, function* () {
            if (!userId) {
                setError('User ID is missing.');
                react_hot_toast_1.default.error('User ID is missing from the URL.');
                setLoading(false);
                return;
            }
            setLoading(true);
            setError(null);
            try {
                // Fetch both user details and available roles concurrently
                const [fetchedUser, fetchedRoles] = yield Promise.all([
                    (0, adminApi_1.fetchPortalUser)(userId),
                    (0, adminApi_1.fetchPortalRoleNames)()
                ]);
                if (fetchedUser) {
                    setUser(fetchedUser);
                    // Initialize form only with editable fields (roles, status)
                    setFormData({ roles: fetchedUser.roles, status: fetchedUser.status });
                }
                else {
                    const notFoundMsg = 'User not found in portal management.';
                    setError(notFoundMsg);
                    react_hot_toast_1.default.error(notFoundMsg);
                    // Consider navigating back if user not found
                    // setTimeout(() => navigate('/admin/users'), 2000);
                }
                setAvailableRoles(fetchedRoles);
            }
            catch (err) {
                console.error("Error loading user data or roles:", err);
                const loadErrorMsg = 'Failed to load user details or roles.';
                setError(loadErrorMsg);
                react_hot_toast_1.default.error(loadErrorMsg);
            }
            finally {
                setLoading(false);
            }
        });
        loadData();
    }, [userId, navigate]);
    // Handle status change
    const handleStatusChange = (e) => {
        setFormData(prev => (Object.assign(Object.assign({}, prev), { status: e.target.value })));
    };
    // Handle role changes (multi-select)
    const handleRoleChange = (e) => {
        const selectedRoles = Array.from(e.target.selectedOptions, option => option.value);
        setFormData(prev => (Object.assign(Object.assign({}, prev), { roles: selectedRoles.length > 0 ? selectedRoles : ['User'] })));
    };
    // Handle form submission
    const handleSave = (e) => __awaiter(void 0, void 0, void 0, function* () {
        e.preventDefault();
        if (!userId)
            return;
        setIsSaving(true);
        const toastId = react_hot_toast_1.default.loading('Saving changes...');
        try {
            const updatedUser = yield (0, adminApi_1.updatePortalUser)(userId, formData);
            if (updatedUser) {
                setUser(updatedUser); // Update local display
                setFormData({ roles: updatedUser.roles, status: updatedUser.status }); // Resync form
                react_hot_toast_1.default.success('User roles/status updated successfully!', { id: toastId });
            }
            else {
                react_hot_toast_1.default.error('Failed to save: User not found.', { id: toastId });
                setError('Failed to save user. User might not exist anymore.');
            }
        }
        catch (err) {
            console.error("Error saving user:", err);
            let message = 'An error occurred while saving.';
            if (err instanceof Error)
                message = err.message;
            react_hot_toast_1.default.error(message, { id: toastId });
        }
        finally {
            setIsSaving(false);
        }
    });
    return (<div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center mb-4">
        <button onClick={() => navigate('/admin/user-management')} // Navigate back to the main list
     className="mr-3 p-1 rounded-full hover:bg-gray-200 transition-colors" aria-label="Back to user management list">
          <feather_icons_react_1.ArrowLeft size={20} className="text-gray-600"/>
        </button>
        <h1 className="text-2xl font-semibold text-gray-800">Manage User Roles & Status</h1>
      </div>

      {loading && <p className="text-center text-gray-500">Loading user details...</p>}
      {error && !loading && <p className="text-center text-red-600 bg-red-100 p-3 rounded-md border border-red-300 mb-4">Error: {error}</p>}

      {!loading && user && (<form onSubmit={handleSave} className="bg-white shadow rounded-lg p-6 space-y-6">
          {/* Display User Info (Readonly) */}
          <div className="mb-6 pb-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-2">{user.name}</h2>
            <p className="text-sm text-gray-500">{user.email}</p>
            <p className="text-sm text-gray-500">Company: {user.company}</p>
          </div>

          {/* Status Select */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Portal Status</label>
            <select id="status" name="status" value={formData.status} onChange={handleStatusChange} required className="w-full md:w-1/2 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white">
              {adminApi_1.PORTAL_STATUSES.map(status => (<option key={status} value={status}>{status}</option>))}
            </select>
             <p className="text-xs text-gray-500 mt-1">Controls if the user is active or inactive within the portal.</p>
          </div>

          {/* Roles Multi-Select */}
          <div>
            <label htmlFor="roles" className="block text-sm font-medium text-gray-700 mb-1">Assign Portal Roles</label>
            <select id="roles" name="roles" multiple value={formData.roles} onChange={handleRoleChange} required // Must assign at least one role
         className="w-full md:w-1/2 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 h-40 bg-white">
              {availableRoles.map(role => (<option key={role} value={role}>{role}</option>))}
            </select>
            <p className="text-xs text-gray-500 mt-1">Hold Ctrl (or Cmd on Mac) to select multiple roles. Determines user permissions within the portal.</p>
          </div>

          {/* Save Button */} 
          <div className="flex justify-start pt-4 border-t border-gray-200">
            <button type="submit" disabled={isSaving || loading} className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${isSaving ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'}`}>
              <feather_icons_react_1.Save size={16} className={`mr-2 ${isSaving ? 'animate-spin' : ''}`}/>
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>)}

      {!loading && !user && !error && (<p className="text-center text-gray-500">User could not be found or loaded.</p>)}
    </div>);
};
exports.default = UserEditPage;
//# sourceMappingURL=UserEditPage.js.map