-- Check if DeploymentDate exists and remove all the fields I added
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ChangeRequests' AND COLUMN_NAME = 'DeploymentDate')
BEGIN
    -- Drop index if exists
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ChangeRequests_DeploymentDate')
        DROP INDEX IX_ChangeRequests_DeploymentDate ON ChangeRequests;
    
    -- Drop constraints if they exist
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentLocation')
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentLocation;
    
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_ChangeRequests_DeploymentType')
        ALTER TABLE ChangeRequests DROP CONSTRAINT CK_ChangeRequests_DeploymentType;
    
    -- Remove the columns I incorrectly added
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentDate;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentDuration;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentLocation;
    ALTER TABLE ChangeRequests DROP COLUMN DeploymentType;
    ALTER TABLE ChangeRequests DROP COLUMN CalendarColor;
    ALTER TABLE ChangeRequests DROP COLUMN RequiresSystemDowntime;
    
    PRINT 'Reverted database to working state';
END 