"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRole = createRole;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
function createRole(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function processed request for url "${request.url}"`);
        let body;
        try {
            body = yield request.json();
            context.log("Received request body:", JSON.stringify(body));
        }
        catch (e) {
            context.error("ERROR parsing request body:", e);
            return {
                status: 400,
                jsonBody: { message: "Invalid JSON in request body." }
            };
        }
        // Handle both frontend (name/description) and backend (RoleName/Description) property naming
        const roleName = body.RoleName || body.name;
        const description = body.Description || body.description;
        if (!roleName || !roleName.trim()) {
            return {
                status: 400,
                jsonBody: { message: "Role name is required." }
            };
        }
        try {
            // Use the variables we defined above
            const roleNameTrimmed = roleName.trim();
            const descriptionTrimmed = (description === null || description === void 0 ? void 0 : description.trim()) || null;
            const createdBy = 1; // Placeholder
            const checkResult = yield (0, db_1.executeQuery)('SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName)', { RoleName: roleNameTrimmed });
            if (checkResult.recordset[0].Count > 0) {
                return {
                    status: 409,
                    jsonBody: { message: `Role with name '${roleNameTrimmed}' already exists.` }
                };
            }
            // The column in the database is RoleDescription, not Description
            const query = `
            INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate)
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription as Description, INSERTED.IsSystemRole, INSERTED.IsActive
            VALUES (@RoleName, @RoleDescription, 0, 1, @CreatedBy, GETUTCDATE())
        `;
            const params = { RoleName: roleNameTrimmed, RoleDescription: descriptionTrimmed, CreatedBy: createdBy };
            const result = yield (0, db_1.executeQuery)(query, params);
            // Map the database result to the format expected by the frontend
            const newRole = {
                id: result.recordset[0].RoleID.toString(),
                name: result.recordset[0].RoleName,
                description: result.recordset[0].Description
            };
            context.log("Created new role:", JSON.stringify(newRole));
            return {
                status: 201,
                jsonBody: newRole
            };
        }
        catch (error) {
            context.error(`Error creating role: ${error instanceof Error ? error.message : error}`);
            return {
                status: 500,
                jsonBody: {
                    message: "Error creating role.",
                    error: error instanceof Error ? error.message : "An unknown error occurred."
                }
            };
        }
    });
}
functions_1.app.http('CreateRole', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'roles',
    handler: createRole
});
//# sourceMappingURL=index.js.map