"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignRole = void 0;
const functions_1 = require("@azure/functions");
const userManagementService_1 = require("../shared/services/userManagementService");
const logger_1 = require("../shared/utils/logger");
const authUtils_1 = require("../shared/authUtils"); // Import auth utils
const validationSchemas_1 = require("../shared/validationSchemas");
// Define required role(s)
// IMPORTANT: Verify this role name matches the actual role/group configured in Entra ID for portal administrators.
const REQUIRED_ROLE = 'Administrator'; // Match the actual role name in database
async function assignRole(request, context) {
    context.log(`Http function AssignRole processed request for url "${request.url}"`);
    logger_1.logger.info('AssignRole function invoked.');
    // --- Authentication & Authorization --- 
    const principal = (0, authUtils_1.getClientPrincipal)(request);
    if (!principal) {
        return { status: 401, jsonBody: { message: "Unauthorized. Client principal missing." } };
    }
    // TODO: Refine role check based on actual Entra ID roles/groups configuration
    if (!(0, authUtils_1.hasRequiredRole)(principal, [REQUIRED_ROLE])) {
        logger_1.logger.warn(`User ${principal.userDetails} (ID: ${principal.userId}) attempted action without required role '${REQUIRED_ROLE}'.`);
        return { status: 403, jsonBody: { message: "Forbidden. User does not have the required permissions." } };
    }
    const authenticatedUserId = await (0, authUtils_1.getUserIdFromPrincipal)(principal, context);
    if (!authenticatedUserId) {
        // This indicates the authenticated Entra ID user doesn't exist or isn't active in our Users table
        logger_1.logger.error(`AssignRole: Could not find active local user for authenticated principal: ${principal.userId} / ${principal.userDetails}`);
        return { status: 403, jsonBody: { message: "Forbidden. Authenticated user not found or inactive in the portal database." } };
    }
    // NOTE: Using retrieved authenticatedUserId below for 'assignedByUserId' field.
    // Ensure getUserIdFromPrincipal correctly retrieves the UserID from your DB 
    // based on the oid claim provided by the configured authentication provider.
    logger_1.logger.info(`AssignRole invoked by UserID: ${authenticatedUserId}`);
    // --- End Auth --- 
    // --- Input Validation --- 
    // Validate Route Parameters
    const routeParams = { userId: request.params.userId }; // Extract for validation
    let validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.userIdRouteParamSchema, routeParams, context, "route parameters");
    if (validationError)
        return validationError;
    // Validation passed, use validated data
    const validatedRouteParams = validationSchemas_1.userIdRouteParamSchema.parse(routeParams); // Use parse to get typed data
    const userId = validatedRouteParams.userId; // Already a number
    // Validate Request Body
    let parsedBody;
    try {
        parsedBody = await request.json();
    }
    catch (error) {
        logger_1.logger.error('AssignRole: Invalid JSON in request body.', error);
        return { status: 400, jsonBody: { message: "Invalid JSON in request body." } };
    }
    validationError = (0, validationSchemas_1.validateRequest)(validationSchemas_1.assignRoleBodySchema, parsedBody, context, "request body");
    if (validationError)
        return validationError;
    // Validation passed, use validated data
    const validatedBody = validationSchemas_1.assignRoleBodySchema.parse(parsedBody);
    const { roleId } = validatedBody; // Already a number
    // --- End Validation --- 
    try {
        // Use the retrieved authenticated user ID for auditing
        const success = await (0, userManagementService_1.assignRoleToUser)(userId, roleId, authenticatedUserId);
        if (success) {
            logger_1.logger.info(`Successfully assigned/reactivated role ${roleId} for user ${userId} by UserID ${authenticatedUserId}.`);
            return {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: "Role assigned successfully." }
            };
        }
        else {
            // assignRoleToUser handles logging the specific error
            // Return a generic server error if it failed
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: "Failed to assign role." }
            };
        }
    }
    catch (error) {
        // This catch block is mainly for unexpected errors during the process
        logger_1.logger.error(`Unexpected error in AssignRole function for User ${userId}, Role ${roleId} by UserID ${authenticatedUserId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "An unexpected error occurred while assigning the role.",
                error: errorMessage
            }
        };
    }
}
exports.assignRole = assignRole;
functions_1.app.http('AssignRole', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'users/{userId}/roles',
    handler: assignRole
});
//# sourceMappingURL=index.js.map