import { RoleDefinition } from "../../shared/interfaces"; // Assuming RoleDefinition is used elsewhere
import { PortalUser } from "../../shared/interfaces"; // Add PortalUser import
// Import callApi if it's from a separate utility file
// import { callApi } from '../utils/apiUtils'; // Example

// Add other interfaces as needed...

// Interface for the data sent to create a user
export interface CreateUserData {
  email: string; // Or UPN
  // Add other optional fields later if needed (firstName, lastName, initialRoles?)
}

// --- API Call Functions --- 

// --- User Management --- ADDED ---

/**
 * Creates a new portal user.
 * Sends the user's email/UPN to the backend, which handles finding/creating 
 * the user in the local DB and assigning default roles.
 * @param userData - Object containing the user's email/UPN.
 * @returns The newly created PortalUser object.
 */
export const createUser = async (userData: CreateUserData): Promise<PortalUser> => {
  console.log("API: createUser called", userData);
  try {
    // Assuming callApi handles JSON stringification and Content-Type
    const newUser = await callApi<PortalUser>('/api/users', {
      method: 'POST',
      body: JSON.stringify(userData), // Ensure body is stringified
      headers: {
        'Content-Type': 'application/json' // Explicitly set Content-Type
      }
    });
    console.log("Successfully created user:", newUser);
    return newUser;
  } catch (error: any) {
    console.error("Error in createUser calling backend:", error);
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Failed to create user: ${error.message || 'Unknown error'}`);
  }
};

// --- Role Management --- 