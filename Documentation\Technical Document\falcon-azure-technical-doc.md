# Falcon Portal - Azure Implementation Technical Document

**Document Version:** 1.0  
**Date:** April 23, 2025  
**Author:** <PERSON>, Anthropic

## 1. Executive Summary

This document outlines the technical architecture and implementation plan for deploying the Falcon Portal on Microsoft Azure. The Falcon Portal will serve as a unified corporate hub for SASMOS Group of companies, providing personalized experiences based on company affiliation, location, department, and role.

The implementation will follow a phased approach with initial deployment of core functionality, followed by gradual rollout of additional features. The solution will leverage React for frontend, Node.js for backend services, Microsoft SQL Server for data storage, and Azure services for infrastructure, security, and integration.

## 2. System Architecture

### 2.1 High-Level Architecture

The Falcon Portal will be implemented as an integrated web application with the following components:

![Falcon Portal Architecture](https://api.placeholder.com/960/540)

1. **Presentation Layer**
   - React-based web application
   - Responsive design for desktop and mobile devices
   - Micro-frontend architecture for integrating applications

2. **API Layer**
   - Node.js REST API services
   - Azure API Management for external service integration
   - Azure Functions for specific serverless operations

3. **Data Layer**
   - Azure SQL Database (primary data store)
   - Azure Blob Storage (document storage)
   - Azure Redis Cache (session and performance caching)

4. **Integration Layer**
   - Azure Logic Apps for workflow automation
   - Azure Service Bus for messaging between components
   - Direct API integrations with PeopleStrong and other systems

5. **Identity and Security Layer**
   - Microsoft Entra ID (formerly Azure AD) for authentication and SSO
   - Role-based access control (RBAC)
   - Azure Key Vault for secure storage of secrets and certificates

6. **Infrastructure Layer**
   - Azure App Service for hosting web applications and APIs
   - Azure CDN for content delivery
   - Azure Virtual Network for network isolation
   - Azure Monitor for logging and monitoring

### 2.2 Detailed Architecture Components

#### 2.2.1 Frontend Architecture

The Falcon Portal frontend will be developed using React with the following structure:

- **Core Shell Application**: Main portal framework, navigation, and shell components
- **Micro-frontends**: Independent modules for each Hub (Knowledge Hub, IT Hub, HR Hub, etc.)
- **Shared UI Components**: Reusable UI components based on a design system
- **Authentication Integration**: Integration with Entra ID for SSO

#### 2.2.2 Backend Architecture

The backend services will be implemented using Node.js:

- **API Gateway**: Azure API Management service to manage all APIs
- **Microservices**: Domain-specific services for each functional area
- **Authentication Middleware**: Integration with Entra ID
- **Data Access Layer**: SQL and document storage access

#### 2.2.3 Data Architecture

Data will be stored in multiple Azure services based on the type of data:

- **Azure SQL Database**: Relational data (user profiles, configurations, etc.)
- **Azure Blob Storage**: Document storage for Knowledge Hub
- **Azure Redis Cache**: Session management and caching
- **Azure Search Service**: Full-text search across documents and content

## 3. Technical Stack

### 3.1 Frontend Stack

- **Framework**: React 18+
- **State Management**: Redux or Context API
- **UI Component Library**: Material-UI or Microsoft Fluent UI
- **Styling**: Tailwind CSS
- **Build Tool**: Webpack or Vite
- **Testing**: Jest and React Testing Library
- **Integration Libraries**:
  - Axios for API calls
  - React-iframe for embedded external applications

### 3.2 Backend Stack

- **Runtime**: Node.js 18+ LTS
- **API Framework**: Express.js
- **Authentication**: Passport.js with Entra ID integration
- **ORM**: Sequelize or TypeORM
- **API Documentation**: Swagger/OpenAPI
- **API Proxy**: For FreshService integration
- **Testing**: Mocha/Chai or Jest

### 3.3 Data Stack

- **Primary Database**: Azure SQL Database
- **Document Storage**: Azure Blob Storage
- **Search Engine**: Azure Cognitive Search
- **Caching**: Azure Redis Cache

### 3.4 DevOps Stack

- **Source Control**: Azure DevOps Repos or GitHub
- **CI/CD**: Azure DevOps Pipelines
- **Infrastructure as Code**: Azure Resource Manager (ARM) templates or Terraform
- **Monitoring**: Azure Monitor and Application Insights
- **Logging**: Azure Log Analytics

## 4. System Components and Implementation Details

### 4.1 Authentication and User Management

The authentication system will leverage Microsoft Entra ID to provide:

- Single Sign-On (SSO) across the portal and integrated applications
- Multi-factor authentication (MFA) for sensitive operations
- Role-based access control integrated with corporate directory
- User profile management with attributes for personalization

**Implementation Details:**
- Azure AD B2C for employee authentication
- JWT tokens for API authentication
- Role and permission mapping stored in SQL Database
- User profile synchronization with corporate directory

### 4.2 Portal Shell and Navigation

The portal shell will provide the consistent UI framework and navigation for all hub modules:

- Responsive top navigation bar and sidebar
- Company selector
- Personalized dashboard
- Notifications center
- User profile and settings

**Implementation Details:**
- React-based shell application
- Context-based state management for current user, company, and selections
- Lazy-loaded hub modules to improve initial load time
- Configuration-driven navigation to support dynamic changes

### 4.3 Knowledge Hub

The Knowledge Hub will provide document management and knowledge base capabilities:

- Document upload, storage, and retrieval
- Document categorization and tagging
- Knowledge article creation and management
- Full-text search across all content

**Implementation Details:**
- Azure Blob Storage for document storage with metadata in SQL
- Azure Cognitive Search for full-text search capabilities
- Document preview using PDF.js or similar libraries
- Version control implementation at the application level

### 4.4 IT Hub

The IT Hub will provide IT service desk and software access capabilities through integration with FreshService (formerly FreshDesk):

- Integration with FreshService for ticket submission and tracking
- Software catalog and access requests
- IT announcements and notifications
- IT knowledge base

**Implementation Details:**
- FreshService integration using their REST API
- Single Sign-On with FreshService via SAML or OAuth
- Embedded ticket creation and viewing within the portal
- Status synchronization between portal and FreshService
- Notification system using Azure SignalR Service for ticket updates
- Email notifications handled by FreshService

### 4.5 HR Hub

The HR Hub will provide HR policy and self-service capabilities:

- HR policy repository
- PeopleStrong integration
- HR request workflows
- Self-service forms

**Implementation Details:**
- PeopleStrong integration via API or deep linking
- Workflow management using Azure Logic Apps
- Form builder implementation for HR request forms
- Policy document storage and versioning

### 4.6 Admin Hub

The Admin Hub will provide administrative services:

- Travel management
- Hospitality services
- Transportation services
- Facility management
- Procurement services

**Implementation Details:**
- Workflow implementation using Azure Logic Apps
- Booking systems using SQL for data storage
- Approval workflows with email notifications
- Reporting capabilities with Power BI embedding

### 4.7 Communication Hub

The Communication Hub will provide announcement and event capabilities:

- Multi-level announcement targeting
- Events calendar with registration
- Customer interaction showcase

**Implementation Details:**
- Targeting rules stored in SQL Database
- Event registration and tracking
- Rich text content storage
- Notification delivery via multiple channels

### 4.8 Personalization Engine

The personalization engine will filter and prioritize content based on user attributes:

- Company affiliation filtering
- Location-aware content
- Role-based content prioritization
- User preference management

**Implementation Details:**
- Rule-based filtering engine
- User preferences stored in SQL Database
- Content tagging and metadata system
- Machine learning-based recommendations (future phase)

## 5. Azure Resources and Configuration

### 5.1 Azure Resource Group Structure

All Azure resources will be deployed to the Central India region and organized into the following resource groups:

- **falcon-prod-rg**: Production environment resources
- **falcon-dev-rg**: Development environment resources
- **falcon-shared-rg**: Shared resources (e.g., Azure DevOps, Key Vault)
- **falcon-integration-rg**: Resources specifically for integrations like FreshService

### 5.2 Core Azure Services

#### 5.2.1 Azure App Service

- **App Service Plan**: Standard tier (S1) for production
- **Web Apps**:
  - falcon-portal-web (Frontend)
  - falcon-api (Backend API)
  - falcon-admin (Admin Portal)

#### 5.2.2 Azure SQL Database

- **Service Tier**: Standard S1 with geo-redundancy
- **Databases**:
  - falcon-db (Main application database)
  - falcon-logging-db (Logging and auditing)

#### 5.2.3 Azure Blob Storage

- **Storage Account**: Standard LRS for document storage
- **Containers**:
  - documents (Knowledge Hub documents)
  - templates (System templates)
  - media (Images and media files)

#### 5.2.4 Azure Cognitive Search

- **Service Tier**: Basic
- **Index Configurations**:
  - documents-index (Knowledge Hub documents)
  - kb-articles-index (Knowledge Base articles)
  - policies-index (HR policies)

#### 5.2.5 Azure Entra ID

- **App Registrations**:
  - Falcon Portal Web Application
  - Falcon API
- **Enterprise Applications**: Integration with PeopleStrong

#### 5.2.6 Azure Key Vault

- **Secret Management**: API keys, connection strings
- **Certificate Management**: SSL certificates

#### 5.2.7 Azure CDN

- **Profile**: Standard Microsoft tier
- **Endpoints**: Static assets and media

#### 5.2.8 Azure API Management

- **Tier**: Developer
- **APIs**: Backend API, External service integrations

### 5.3 Networking Configuration

- **Virtual Network**: falcon-vnet (10.0.0.0/16)
- **Subnets**:
  - app-subnet (10.0.1.0/24)
  - data-subnet (10.0.2.0/24)
- **Network Security Groups**: Appropriate rules for each subnet

### 5.4 Monitoring and Logging

- **Application Insights**: Configured for all web applications
- **Log Analytics Workspace**: Centralized logging solution
- **Azure Monitor Alerts**: Performance and availability alerts

## 6. Security Implementation

### 6.1 Authentication and Authorization

- Microsoft Entra ID for authentication
- Role-based access control (RBAC) for authorization
- JWT tokens for API authentication
- Multi-factor authentication for sensitive operations

### 6.2 Data Security

- Encryption at rest for all data stores
- TLS 1.2+ for all data in transit
- Azure SQL Transparent Data Encryption (TDE)
- Azure Storage encryption with Microsoft-managed keys

### 6.3 Network Security

- Azure Private Endpoints for database access
- Azure Web Application Firewall (WAF) protection
- Network security groups with appropriate rules
- HTTPS enforcement and HTTP to HTTPS redirection

### 6.4 Application Security

- OWASP security best practices
- Input validation and output encoding
- Protection against common web vulnerabilities
- Regular security scanning and penetration testing

### 6.5 Compliance

- Data residency maintained in Central India
- Audit logging for all sensitive operations
- Role-based access to sensitive data

## 7. Integration Strategy

### 7.1 FreshService Integration

- **Integration Type**: REST API integration and SSO
- **Authentication**: API key for backend integration, SAML for SSO
- **Integration Points**:
  - Ticket creation and submission
  - Ticket status tracking
  - Knowledge base articles
  - User synchronization
- **Implementation Approaches**:
  1. **API Integration**:
     - Backend proxy service to make authenticated FreshService API calls
     - Custom React components for ticket creation and listing
     - Real-time ticket status updates 
  2. **SSO Integration**:
     - Configure SAML SSO in FreshService
     - Set up Entra ID as identity provider
     - Implement seamless navigation between portal and FreshService
  3. **Hybrid Approach** (recommended):
     - Custom ticket creation UI in the portal using APIs
     - iFrame embedding for ticket lists and detailed views
     - Single sign-on for seamless authentication
- **Azure Components**:
  - Azure API Management for FreshService API integration
  - Azure Key Vault for storing API credentials
  - Azure Functions for serverless API proxy (optional)
  - Entra ID for SSO authentication

### 7.2 PeopleStrong Integration

- **Integration Type**: API integration and deep linking
- **Authentication**: OAuth 2.0 or API key
- **Data Flow**: One-way pull from PeopleStrong to Falcon

### 7.3 Email Integration

- **Service**: Azure Communication Services or SendGrid
- **Templates**: HTML email templates for notifications
- **Triggers**: Event-based email notifications

### 7.4 External Application Integration

- **Integration Approaches**:
  - Deep linking with SSO
  - iFrame embedding where appropriate
  - API integration for data exchange
  - Micro-frontend integration for native-like experience

### 7.5 Future Integrations

- **Microsoft Teams**: Integration for notifications and approvals
- **Mobile App**: Native mobile application integration
- **Power BI**: Advanced reporting and analytics

## 8. Phased Implementation Plan

### 8.1 Phase 1 (Week 1)

**Core Portal Framework and Initial Modules**

- Portal shell and navigation
- Authentication and user management
- Dashboard
- IT Hub (FreshService integration for ticket management)
- Knowledge Hub (document repository)
- Links to existing applications

**Azure Resources Required**:
- Azure App Service
- Azure SQL Database
- Azure Blob Storage
- Azure Entra ID
- Azure CDN
- Azure API Management (for FreshService API integration)

**Implementation Steps**:
1. Set up Azure resource groups and core services
2. Implement authentication with Entra ID
3. Develop portal shell and navigation
4. Implement FreshService integration for IT tickets
5. Implement document repository functionality
6. Configure links to existing applications
7. Deploy to development environment
8. User acceptance testing
9. Production deployment

### 8.2 Phase 2 (Week 2-3)

**Enhanced Functionality and Additional Modules**

- Knowledge Hub (enhanced with search and knowledge base)
- IT Hub (software catalog and request workflows, deeper FreshService integration)
- HR Hub (basic functionality)
- Admin Hub (travel and hospitality services)

**Azure Resources Required**:
- Azure Cognitive Search
- Azure Logic Apps
- Azure API Management (enhanced configuration)

**Implementation Steps**:
1. Implement full-text search for Knowledge Hub
2. Develop Knowledge Base article management
3. Enhance FreshService integration with ticket dashboard and analytics
4. Build software catalog and access request workflows
5. Implement basic HR Hub functionality
6. Develop travel and hospitality services
7. Deploy to development environment
8. User acceptance testing
9. Production deployment

### 8.3 Phase 3 (Week 4+)

**Complete Features and Advanced Functionality**

- Communication Hub
- Admin Hub (remaining services)
- Personalization enhancements
- Advanced reporting
- Mobile responsiveness enhancements

**Azure Resources Required**:
- Azure SignalR Service
- Power BI Embedded (if required)

**Implementation Steps**:
1. Implement Communication Hub
2. Complete Admin Hub functionality
3. Enhance personalization engine
4. Develop advanced reporting capabilities
5. Optimize for mobile devices
6. Deploy to development environment
7. User acceptance testing
8. Production deployment

## 9. Infrastructure as Code (IaC)

For repeatable and consistent deployments, the following ARM template structure is recommended:

```json
{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "environment": {
      "type": "string",
      "allowedValues": [
        "dev",
        "test",
        "prod"
      ],
      "defaultValue": "dev"
    }
  },
  "variables": {
    "appName": "[concat('falcon-', parameters('environment'))]",
    "location": "centralindia"
  },
  "resources": [
    // App Service resources
    // SQL Database resources
    // Storage Account resources
    // Other Azure resources
  ],
  "outputs": {
    // Resource endpoints and connection information
  }
}
```

## 10. CI/CD Pipeline

### 10.1 Development Workflow

1. Developers commit code to feature branches
2. Pull requests trigger automated builds and tests
3. Approved pull requests are merged to development branch
4. Automated deployment to development environment
5. Manual promotion to test and production

### 10.2 Azure DevOps Pipeline Configuration

```yaml
trigger:
  branches:
    include:
      - development
      - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: falcon-variables
  - name: buildConfiguration
    value: 'Release'

stages:
  - stage: Build
    jobs:
      - job: BuildJob
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '18.x'
          - script: npm ci
            displayName: 'npm install'
          - script: npm run build
            displayName: 'npm build'
          - script: npm test
            displayName: 'npm test'
          - task: PublishBuildArtifacts@1
            inputs:
              pathtoPublish: 'build'
              artifactName: 'falcon-portal'

  - stage: DeployDev
    dependsOn: Build
    condition: succeeded()
    jobs:
      - deployment: DeployDev
        environment: development
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    WebAppName: '$(webAppName)-dev'
                    packageForLinux: '$(Pipeline.Workspace)/falcon-portal'

  - stage: DeployProd
    dependsOn: DeployDev
    condition: succeeded()
    jobs:
      - deployment: DeployProd
        environment: production
        strategy:
          runOnce:
            deploy:
              steps:
                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: '$(azureSubscription)'
                    appType: 'webApp'
                    WebAppName: '$(webAppName)-prod'
                    packageForLinux: '$(Pipeline.Workspace)/falcon-portal'
```

## 11. Monitoring and Operations

### 11.1 Monitoring Strategy

- **Azure Application Insights**: Application performance monitoring
- **Azure Monitor**: Infrastructure monitoring
- **Log Analytics**: Log collection and analysis
- **Azure Security Center**: Security monitoring

### 11.2 Alerts Configuration

- **Performance Alerts**:
  - High CPU/memory usage
  - Slow response times
  - Failed requests
- **Availability Alerts**:
  - Web tests for key functionality
  - API availability
- **Security Alerts**:
  - Failed authentication attempts
  - Unusual access patterns

### 11.3 Backup Strategy

- **SQL Database**: Point-in-time backups
- **Storage Accounts**: Geo-redundant storage
- **Configuration**: Azure DevOps or GitHub repository

### 11.4 Disaster Recovery

- **Recovery Plan**: Documented procedures for various failure scenarios
- **Recovery Point Objective (RPO)**: 1 hour
- **Recovery Time Objective (RTO)**: 4 hours

## 12. Cost Optimization Strategies

To maintain cost-effectiveness while meeting requirements:

### 12.1 Right-sizing Resources

- Start with smaller instance sizes and scale as needed
- Utilize auto-scaling for App Service plans
- Consider serverless options (Azure Functions) for sporadic workloads

### 12.2 Reserved Instances

- For consistent workloads, consider Azure Reserved Instances
- SQL Database reserved capacity for cost savings

### 12.3 Free Tier Utilization

- Azure DevOps (free for small teams)
- Application Insights (free tier)
- Azure Cognitive Search (free tier for development)

### 12.4 Estimated Monthly Costs

| Service | Tier | Estimated Cost (USD) |
|---------|------|----------------------|
| App Service | Standard S1 | $70 |
| SQL Database | Standard S1 | $30 |
| Storage Account | Standard LRS | $5-10 |
| Azure Cognitive Search | Basic | $75 |
| API Management | Developer | $50 |
| Other Services | Various | $20-30 |
| **Total** | | **$250-265** |

## 13. Next Steps and Recommendations

### 13.1 Immediate Next Steps

1. Finalize Azure subscription and resource access
2. Set up development environment and CI/CD pipeline
3. Implement core authentication and portal shell
4. Begin development of Phase 1 components

### 13.2 Technical Considerations

- Consider adopting a micro-frontend architecture for scalability
- Implement proper caching strategy for improved performance
- Design with future mobile app integration in mind
- Use feature flags for gradual rollout of functionality

### 13.3 Future Enhancements

- Mobile application development
- Chatbot integration for self-service
- Advanced analytics and reporting
- AI-powered personalization and recommendations

## 14. Conclusion

This technical document provides a comprehensive plan for implementing the Falcon Portal on Azure. The architecture leverages Azure's managed services to provide a scalable, secure, and cost-effective solution for SASMOS Group's requirements.

The phased implementation approach allows for quick delivery of core functionality while maintaining flexibility for future enhancements. By focusing on a React frontend, Node.js backend, and Azure SQL Database, the solution aligns with the specified technology preferences while taking advantage of Azure's integration capabilities.

## Appendix A: Azure Resource Naming Conventions

| Resource Type | Naming Pattern | Example |
|---------------|----------------|---------|
| Resource Group | falcon-{env}-rg | falcon-prod-rg |
| App Service | falcon-{component}-{env} | falcon-portal-prod |
| SQL Server | falconsql{env} | falconsqlprod |
| SQL Database | falcon-db-{env} | falcon-db-prod |
| Storage Account | falconstorage{env} | falconstorageprod |
| Key Vault | falcon-kv-{env} | falcon-kv-prod |
| CDN Profile | falcon-cdn-{env} | falcon-cdn-prod |

## Appendix B: Database Schema Overview

The primary database will include the following key tables:

1. **Users**
   - UserID
   - EntraID
   - Email
   - CompanyID
   - LocationID
   - DepartmentID
   - RoleID

2. **Companies**
   - CompanyID
   - CompanyName
   - LogoURL

3. **Locations**
   - LocationID
   - LocationName
   - CompanyID
   - Address

4. **Departments**
   - DepartmentID
   - DepartmentName
   - CompanyID

5. **Documents**
   - DocumentID
   - Title
   - Description
   - BlobURL
   - CategoryID
   - CompanyID
   - DepartmentID
   - CreatedBy
   - CreatedDate
   - ModifiedBy
   - ModifiedDate
   - Version

6. **ITTickets**
   - TicketID
   - Subject
   - Description
   - Status
   - Priority
   - RequestedBy
   - AssignedTo
   - CreatedDate
   - LastUpdatedDate
   - CompanyID
   - CategoryID

7. **Announcements**
   - AnnouncementID
   - Title
   - Content
   - PublishDate
   - ExpiryDate
   - Priority
   - CompanyID
   - LocationID
   - DepartmentID
   - CreatedBy
   - CreatedDate

8. **Events**
   - EventID
   - Title
   - Description
   - StartDateTime
   - EndDateTime
   - Location
   - CompanyID
   - DepartmentID
   - CreatedBy
   - CreatedDate

## Appendix C: Security Checklist

- [ ] Implement Microsoft Entra ID integration
- [ ] Configure role-based access control
- [ ] Enforce HTTPS with TLS 1.2+
- [ ] Set up Azure Key Vault for secrets management
- [ ] Enable Azure SQL TDE
- [ ] Configure network security groups
- [ ] Set up logging and monitoring
- [ ] Implement proper input validation
- [ ] Perform security testing
- [ ] Establish backup and disaster recovery procedures
