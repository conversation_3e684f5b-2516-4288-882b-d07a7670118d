{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/AddUser/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAWA,0BAmGC;AA9GD,gDAAyF;AACzF,kEAA+D;AAC/D,oFAA2F;AAC3F,mDAAgD;AAChD,mDAAkG;AAClG,mEAAiF;AAEjF,0BAA0B;AAC1B,mHAAmH;AACnH,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,qBAAqB;AAE3D,SAAsB,OAAO,CAAC,OAAoB,EAAE,OAA0B;;QAC1E,OAAO,CAAC,GAAG,CAAC,oDAAoD,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAChF,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEzC,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yCAAyC,EAAE,EAAE,CAAC;QAC7F,CAAC;QACD,IAAI,CAAC,IAAA,2BAAe,EAAC,SAAS,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,WAAW,SAAS,SAAS,CAAC,MAAM,6CAA6C,aAAa,IAAI,CAAC,CAAC;YAClI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,yDAAyD,EAAE,EAAE,CAAC;QAC9G,CAAC;QACD,MAAM,mBAAmB,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACvB,eAAM,CAAC,KAAK,CAAC,0EAA0E,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;YACtI,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,6EAA6E,EAAE,EAAE,CAAC;QACjI,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,8BAA8B,mBAAmB,EAAE,CAAC,CAAC;QACjE,oBAAoB;QAEpB,4BAA4B;QAC5B,IAAI,UAAe,CAAC;QACpB,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,EAAE,CAAC;QACnF,CAAC;QAED,MAAM,eAAe,GAAG,IAAA,mCAAe,EAAC,qCAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAChG,IAAI,eAAe;YAAE,OAAO,eAAe,CAAC;QAE5C,wCAAwC;QACxC,MAAM,aAAa,GAAG,qCAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,8BAA8B;QACzF,MAAM,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC;QAClC,0BAA0B;QAE1B,IAAI,CAAC;YACD,qCAAqC;YACrC,eAAM,CAAC,IAAI,CAAC,2DAA2D,OAAO,EAAE,CAAC,CAAC;YAClF,MAAM,SAAS,GAAG,MAAM,2BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE1D,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,eAAM,CAAC,IAAI,CAAC,+BAA+B,OAAO,0BAA0B,CAAC,CAAC;gBAC9E,OAAO;oBACH,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,OAAO,EAAE,sBAAsB,OAAO,0BAA0B,EAAE;iBACjF,CAAC;YACN,CAAC;YAED,iEAAiE;YACjE,MAAM,aAAa,GAAkB;gBACjC,EAAE,EAAE,SAAS,CAAC,EAAE;gBAChB,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;gBAC9C,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,UAAU,EAAE,SAAS,CAAC,UAAU;aACnC,CAAC;YAEF,mDAAmD;YACnD,eAAM,CAAC,IAAI,CAAC,mDAAmD,OAAO,eAAe,mBAAmB,EAAE,CAAC,CAAC;YAC5G,yDAAyD;YACzD,MAAM,MAAM,GAAG,MAAM,IAAA,wCAAgB,EAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YAE1E,IAAI,CAAC,MAAM,EAAE,CAAC;gBACT,eAAM,CAAC,KAAK,CAAC,iDAAiD,OAAO,GAAG,CAAC,CAAC;gBAC1E,0EAA0E;gBAC1E,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YAC5E,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,kDAAkD,MAAM,CAAC,MAAM,iBAAiB,OAAO,EAAE,CAAC,CAAC;YAEvG,+BAA+B;YAC/B,OAAO;gBACH,MAAM,EAAE,GAAG,EAAE,mEAAmE;gBAChF,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,MAAM;aACnB,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,0CAA0C,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,YAAY,GAAG,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,MAAM,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,oCAAoC,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,sCAAsC,CAAC,CAAC;gBACrK,CAAC,CAAC,GAAG,CAAC,0CAA0C;gBAChD,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc;YAE1F,OAAO;gBACH,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACN,OAAO,EAAE,oBAAoB;oBAC7B,KAAK,EAAE,YAAY;iBACtB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,SAAS,EAAE;IAChB,OAAO,EAAE,CAAC,MAAM,CAAC;IACjB,SAAS,EAAE,WAAW,EAAE,oCAAoC;IAC5D,KAAK,EAAE,OAAO,EAAE,sBAAsB;IACtC,OAAO,EAAE,OAAO;CACnB,CAAC,CAAC"}