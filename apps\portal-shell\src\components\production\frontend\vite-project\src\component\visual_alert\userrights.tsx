"use client"

import { useState, useEffect } from "react"
// import * as FeatherIcons from 'feather-icons-react'; // Unused import

import { Users, Shield, Save, AlertTriangle } from "lucide-react"

export default function UserRights() {
  const [employees, setEmployees] = useState([])
  const [selectedEmployee, setSelectedEmployee] = useState("")
  const [userRights, setUserRights] = useState({
    assignRights: false,
    create: false,
    masterUser: false,
    update: false,
  })
  const [existingRights, setExistingRights] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [_searchTerm, _setSearchTerm] = useState("") // searchTerm unused - prefixed with _
  const [loggedInUser, setLoggedInUser] = useState({
    login_userid: "",
    login_username: "",
    user_rights: [],
  })
  const [hasAccess, setHasAccess] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const getUser = () => {
      try {
        const userData = sessionStorage.getItem("userData")
        if (userData) {
          const parsed = JSON.parse(userData)
          setLoggedInUser({
            login_userid: parsed.employee_id,
            login_username: parsed.employee_name,
            user_rights: parsed.user_rights || [],
          })

          // Check user rights using the new API
          checkUserAccess(parsed.employee_id)
        }
      } catch (e) {
        console.error(e)
        setHasAccess(false)
      } finally {
        setIsLoading(false)
      }
    }

    const checkUserAccess = async (employeeId) => {
      try {
        const response = await fetch(`http://localhost:3000/api/get_current_userrights?emp_id=${employeeId}`)
        if (response.ok) {
          const data = await response.json()
          const userRights = data.user_rights || []
          const canAccess = userRights.includes("ASSIGN RIGHTS") || userRights.includes("MASTER USER")
          setHasAccess(canAccess)
        } else {
          setHasAccess(false)
        }
      } catch (error) {
        console.error("Error checking user access:", error)
        setHasAccess(false)
      }
    }

    getUser()
  }, [])

  useEffect(() => {
    if (hasAccess) {
      fetchEmployees()
      fetchExistingRights()
    }
  }, [hasAccess])

  const fetchEmployees = async () => {
    try {
      const response = await fetch("http://localhost:3000/api/employees")
      if (!response.ok) throw new Error("Failed to fetch employees")
      const data = await response.json()
      setEmployees(data)
    } catch (error) {
      console.error("Error fetching employees:", error)
      alert("Failed to fetch employees")
    }
  }

  const fetchExistingRights = async () => {
    try {
      const response = await fetch("http://localhost:3000/api/user_rights")
      if (!response.ok) throw new Error("Failed to fetch user rights")
      const data = await response.json()
      setExistingRights(data)
    } catch (error) {
      console.error("Error fetching user rights:", error)
    }
  }

  const handleEmployeeSelect = (employeeValue) => {
    setSelectedEmployee(employeeValue)

    // Find existing rights for this employee
    const employeeId = employeeValue.split(" - ")[1]
    const existing = existingRights.find((right) => right.employee_id === employeeId)

    if (existing) {
      setUserRights({
        assignRights: existing.assign_rights === 1,
        create: existing.create_rights === 1,
        masterUser: existing.master_user === 1,
        update: existing.update_rights === 1,
      })
    } else {
      setUserRights({
        assignRights: false,
        create: false,
        masterUser: false,
        update: false,
      })
    }
  }

  const handleRightChange = (rightName, value) => {
    setUserRights((prev) => ({
      ...prev,
      [rightName]: value,
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!selectedEmployee) {
      alert("Please select a user")
      return
    }

    setIsSubmitting(true)

    try {
      const employeeParts = selectedEmployee.split(" - ")
      const employeeName = employeeParts[0]
      const employeeId = employeeParts[1]

      // Convert checkbox states to array of rights
      const user_rights = []
      if (userRights.assignRights) user_rights.push("Assign Rights")
      if (userRights.create) user_rights.push("create")
      if (userRights.masterUser) user_rights.push("Master User")
      if (userRights.update) user_rights.push("update")

      const response = await fetch("http://localhost:3000/api/assign_user_rights", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          employee_id: employeeId,
          employee_name: employeeName,
          user_rights: user_rights,
          logged_emp_id: loggedInUser.login_userid,
          logged_emp_name: loggedInUser.login_username,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to update user rights")
      }

      const result = await response.json()
      alert(result.message || "User rights updated successfully!")

      // Refresh existing rights
      await fetchExistingRights()

      // Reset form
      setSelectedEmployee("")
      setUserRights({
        assignRights: false,
        create: false,
        masterUser: false,
        update: false,
      })
    } catch (error) {
      console.error("Error updating user rights:", error)
      alert("Failed to update user rights. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  /* // Unused variable - commented out
  const _filteredEmployees = employees.filter(
    (employee) =>
      employee.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.employee_id.toLowerCase().includes(searchTerm.toLowerCase()),
  )
  */

  // const getRightsDisplay = (rights) => { // Unused function
  //   const rightsList = []
  //   if (rights.user_rights) {
  //     if (rights.user_rights.includes("assign_rights")) rightsList.push("Assign Rights")
  //     if (rights.user_rights.includes("create")) rightsList.push("Create")
  //     if (rights.user_rights.includes("master")) rightsList.push("Master User")
  //     if (rights.user_rights.includes("update")) rightsList.push("Update")
  //   }
  //   return rightsList.length > 0 ? rightsList.join(", ") : "No Rights"
  // }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-xl shadow-xl p-8">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You are not authorized to access the User Rights page.</p>
            <p className="text-sm text-gray-500">Required permissions: Assign Rights or Master User</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-xl shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                USER RIGHTS
              </h1>
              <p className="text-gray-600 text-lg">Manage user permissions and access control</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* User Rights Form */}
          <div className="bg-white rounded-xl shadow-xl border-0 overflow-hidden">
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-indigo-50">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Users className="w-5 h-5" />
                Assign User Rights
              </h2>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* User Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">SELECT USER</label>
                <select
                  value={selectedEmployee}
                  onChange={(e) => handleEmployeeSelect(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                  required
                >
                  <option value="">Select an employee...</option>
                  {employees.map((employee, index) => (
                    <option
                      key={`${employee.employee_id}-${index}`}
                      value={`${employee.employee_name} - ${employee.employee_id}`}
                    >
                      {employee.employee_name} - {employee.employee_id}
                    </option>
                  ))}
                </select>
              </div>

              {/* Rights Checkboxes */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="assignRights"
                    checked={userRights.assignRights}
                    onChange={(e) => handleRightChange("assignRights", e.target.checked)}
                    className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                  />
                  <label htmlFor="assignRights" className="text-sm font-medium text-gray-700">
                    ASSIGN RIGHTS
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="create"
                    checked={userRights.create}
                    onChange={(e) => handleRightChange("create", e.target.checked)}
                    className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                  />
                  <label htmlFor="create" className="text-sm font-medium text-gray-700">
                    CREATE
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="masterUser"
                    checked={userRights.masterUser}
                    onChange={(e) => handleRightChange("masterUser", e.target.checked)}
                    className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                  />
                  <label htmlFor="masterUser" className="text-sm font-medium text-gray-700">
                    MASTER USER
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="update"
                    checked={userRights.update}
                    onChange={(e) => handleRightChange("update", e.target.checked)}
                    className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                  />
                  <label htmlFor="update" className="text-sm font-medium text-gray-700">
                    UPDATE
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting || !selectedEmployee}
                  className={`px-8 py-3 rounded-lg font-semibold flex items-center gap-2 transition-all ${
                    isSubmitting || !selectedEmployee
                      ? "bg-gray-400 cursor-not-allowed opacity-60"
                      : "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white shadow-lg"
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      SUBMIT
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Current User Rights List */}

        </div>
      </div>
    </div>
  )
}
