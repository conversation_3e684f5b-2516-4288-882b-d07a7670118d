"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
// Correct import path for the function being tested
const DeleteRole_1 = require("./DeleteRole"); // Assuming function file is DeleteRole.ts
// Correct import path for the shared db module (relative to src/functions)
const db_1 = require("../shared/db");
const mssql_1 = require("mssql");
// Mock the database module
jest.mock('../shared/db');
// --- Mocks (same as before) ---
const mockRequest = {
    input: jest.fn().mockReturnThis(),
    query: jest.fn(),
    execute: jest.fn(),
};
const mockTransaction = {
    begin: jest.fn(),
    commit: jest.fn(),
    rollback: jest.fn(),
    request: jest.fn().mockReturnValue(mockRequest),
};
const mockPool = {
    transaction: jest.fn().mockReturnValue(mockTransaction),
    request: jest.fn().mockReturnValue(mockRequest),
};
beforeEach(() => {
    jest.clearAllMocks();
    db_1.getPool.mockResolvedValue(mockPool);
});
describe('DeleteRole Function', () => {
    const mockContext = {
        log: jest.fn(),
        warn: jest.fn(),
        error: jest.fn()
    };
    const createMockRequest = (roleId) => {
        return {
            params: { roleId: roleId.toString() }
        };
    };
    it('should return 400 for invalid roleId', () => __awaiter(void 0, void 0, void 0, function* () {
        const request = createMockRequest('invalid');
        const response = yield (0, DeleteRole_1.deleteRole)(request, mockContext);
        expect(response.status).toBe(400);
        expect(response.jsonBody).toEqual({ message: 'Invalid RoleID provided.' });
        expect(mockPool.transaction).not.toHaveBeenCalled();
    }));
    it('should return 404 if role not found', () => __awaiter(void 0, void 0, void 0, function* () {
        const request = createMockRequest(999);
        mockRequest.query.mockResolvedValueOnce({ recordset: [] });
        const response = yield (0, DeleteRole_1.deleteRole)(request, mockContext);
        expect(response.status).toBe(404);
        expect(response.jsonBody).toEqual({ message: 'Role with ID 999 not found.' });
        expect(mockPool.transaction).toHaveBeenCalled();
        expect(mockTransaction.request).toHaveBeenCalled();
        expect(mockRequest.input).toHaveBeenCalledWith('roleId', mssql_1.default.Int, 999);
        expect(mockRequest.query).toHaveBeenCalledWith(expect.stringContaining('SELECT RoleName FROM Roles')); // Updated query check
        expect(mockTransaction.rollback).toHaveBeenCalled();
    }));
    it('should return 400 if trying to delete protected base role', () => __awaiter(void 0, void 0, void 0, function* () {
        const request = createMockRequest(1);
        mockRequest.query.mockResolvedValueOnce({
            recordset: [{ RoleName: 'Administrator' }]
        });
        const response = yield (0, DeleteRole_1.deleteRole)(request, mockContext);
        expect(response.status).toBe(400); // Check for 400
        expect(response.jsonBody).toEqual({ message: "Cannot delete protected base role: Administrator" }); // Updated message check
        expect(mockPool.transaction).toHaveBeenCalled();
        expect(mockTransaction.rollback).toHaveBeenCalled();
    }));
    it('should successfully delete a role and return 204', () => __awaiter(void 0, void 0, void 0, function* () {
        const request = createMockRequest(5);
        const defaultRoleId = 2;
        const userIdsToUpdate = [101, 102];
        // Mock responses sequence based on DeleteRole.ts logic
        mockRequest.query // 1. Role check (non-protected)
            .mockResolvedValueOnce({ recordset: [{ RoleName: 'Test Role' }] });
        mockRequest.query // 2. Get default role ID ('Employee')
            .mockResolvedValueOnce({ recordset: [{ RoleID: defaultRoleId }] });
        mockRequest.query // 3. Find users needing role update
            .mockResolvedValueOnce({ recordset: userIdsToUpdate.map(id => ({ UserID: id })) });
        mockRequest.query // 4. Delete existing assignments for role 5
            .mockResolvedValueOnce({ rowsAffected: [3] }); // Mock response for DELETE query
        // 5. Reassign loop (User 101 needs reassignment, User 102 doesn't)
        mockRequest.query // 5a. Reassign User 101 (INSERT)
            .mockResolvedValueOnce({ rowsAffected: [1] }); // Mock response for INSERT query
        // Note: The actual function doesn't check before inserting, simplified mock
        mockRequest.query // 6. Delete the role definition itself
            .mockResolvedValueOnce({ rowsAffected: [1] }); // Mock response for DELETE query
        const response = yield (0, DeleteRole_1.deleteRole)(request, mockContext);
        expect(response.status).toBe(204);
        expect(mockPool.transaction).toHaveBeenCalled();
        expect(mockTransaction.begin).toHaveBeenCalled();
        // Verify queries
        expect(mockRequest.query).toHaveBeenCalledTimes(1 + 1 + 1 + 1 + userIdsToUpdate.length + 1); // RoleCheck, DefaultRole, FindUsers, DeleteAssign, Insert(xUsers), DeleteRole
        // Verify inputs and specific queries if needed
        expect(mockTransaction.commit).toHaveBeenCalled();
        expect(mockTransaction.rollback).not.toHaveBeenCalled();
    }));
    it('should handle errors during transaction and rollback', () => __awaiter(void 0, void 0, void 0, function* () {
        const request = createMockRequest(5);
        const dbError = new Error('Database boom');
        mockRequest.query // 1. Role check (non-protected)
            .mockResolvedValueOnce({ recordset: [{ RoleName: 'Test Role' }] });
        mockRequest.query // 2. Get default role ID ('Employee')
            .mockResolvedValueOnce({ recordset: [{ RoleID: 2 }] });
        mockRequest.query // 3. Find users needing role update
            .mockResolvedValueOnce({ recordset: [{ UserID: 101 }] });
        mockRequest.query // 4. Simulate error during delete assignments
            .mockRejectedValueOnce(dbError);
        const response = yield (0, DeleteRole_1.deleteRole)(request, mockContext);
        expect(response.status).toBe(500);
        expect(response.jsonBody).toEqual({
            message: 'Error during role deletion transaction.', // Match error message from function
            error: dbError.message
        });
        expect(mockPool.transaction).toHaveBeenCalled();
        expect(mockTransaction.begin).toHaveBeenCalled();
        expect(mockRequest.query).toHaveBeenCalledTimes(1 + 1 + 1 + 1); // Up to the point of failure
        expect(mockTransaction.commit).not.toHaveBeenCalled();
        expect(mockTransaction.rollback).toHaveBeenCalled();
        expect(mockContext.error).toHaveBeenCalledWith(expect.stringContaining('Error during role deletion transaction'), dbError);
    }));
});
//# sourceMappingURL=DeleteRole.test.js.map