import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { executeQuery } from '../shared/db';
import { logger } from '../shared/utils/logger';
import sql from 'mssql';

// Define authorized admin users - only these users should have Administrator role
const AUTHORIZED_ADMIN_USERS = [
    '<EMAIL>',
    '<EMAIL>',
    // Add other authorized admin users here
];

/**
 * Security function to remove Administrator role from unauthorized users
 * This should be run to clean up any incorrect admin assignments
 */
export async function fixUnauthorizedAdminAccess(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    try {
        logger.info('Starting unauthorized admin access cleanup...');
        
        // 1. Get all users with Administrator role
        const adminUsersQuery = `
            SELECT 
                u.UserID,
                u.Email,
                u.FirstName,
                u.LastName,
                ur.<PERSON>r<PERSON>ole<PERSON>,
                ur.<PERSON>,
                ur.CreatedBy
            FROM Users u
            JOIN UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            JOIN Roles r ON ur.RoleID = r.RoleID
            WHERE r.RoleName = 'Administrator'
            ORDER BY u.Email
        `;
        
        const adminResult = await executeQuery(adminUsersQuery, []);
        
        if (!adminResult.recordset || adminResult.recordset.length === 0) {
            logger.info('No users with Administrator role found');
            return {
                status: 200,
                jsonBody: {
                    message: 'No users with Administrator role found',
                    removedCount: 0
                }
            };
        }
        
        logger.info(`Found ${adminResult.recordset.length} users with Administrator role`);
        
        // 2. Identify unauthorized admin users
        const unauthorizedAdmins = adminResult.recordset.filter(user => 
            !AUTHORIZED_ADMIN_USERS.includes(user.Email.toLowerCase())
        );
        
        if (unauthorizedAdmins.length === 0) {
            logger.info('No unauthorized admin users found');
            return {
                status: 200,
                jsonBody: {
                    message: 'No unauthorized admin users found',
                    authorizedAdmins: adminResult.recordset.map(u => u.Email),
                    removedCount: 0
                }
            };
        }
        
        logger.info(`Found ${unauthorizedAdmins.length} unauthorized admin users to clean up`);
        
        // 3. Remove Administrator role from unauthorized users
        const removedUsers = [];
        
        for (const user of unauthorizedAdmins) {
            try {
                const deactivateQuery = `
                    UPDATE UserRoles 
                    SET IsActive = 0, 
                        ModifiedBy = 1, 
                        ModifiedDate = GETUTCDATE()
                    WHERE UserRoleID = @UserRoleID
                `;
                
                const params = [
                    { name: 'UserRoleID', type: sql.Int, value: user.UserRoleID }
                ];
                
                await executeQuery(deactivateQuery, params);
                
                removedUsers.push({
                    email: user.Email,
                    name: `${user.FirstName} ${user.LastName}`,
                    originallyAssignedBy: user.CreatedBy,
                    originallyAssignedDate: user.CreatedDate
                });
                
                logger.info(`Removed Administrator role from ${user.Email}`);
                
            } catch (error) {
                logger.error(`Failed to remove Administrator role from ${user.Email}:`, error);
            }
        }
        
        // 4. Return summary
        return {
            status: 200,
            jsonBody: {
                message: `Successfully removed Administrator role from ${removedUsers.length} unauthorized users`,
                removedUsers: removedUsers,
                authorizedAdmins: adminResult.recordset
                    .filter(u => AUTHORIZED_ADMIN_USERS.includes(u.Email.toLowerCase()))
                    .map(u => u.Email),
                removedCount: removedUsers.length
            }
        };
        
    } catch (error) {
        logger.error('Error in fixUnauthorizedAdminAccess:', error);
        return {
            status: 500,
            jsonBody: {
                error: 'Failed to fix unauthorized admin access',
                message: error instanceof Error ? error.message : 'Unknown error'
            }
        };
    }
}

// Register the function
app.http('FixUnauthorizedAdminAccess', {
    methods: ['POST'],
    authLevel: 'anonymous',
    handler: fixUnauthorizedAdminAccess
});

export default fixUnauthorizedAdminAccess; 