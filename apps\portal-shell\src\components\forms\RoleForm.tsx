import React, { useState, useEffect } from 'react';
import { RoleDefinition } from '../../services/adminApi';

interface RoleFormProps {
    // Optional initial data for editing
    initialData?: RoleDefinition | null;
    onSubmit: (formData: Omit<RoleDefinition, 'id'>) => Promise<void>; 
    onCancel: () => void;
    isSaving: boolean; // To disable button during submission
    submitError?: string | null; // To display submission errors
    // Accept render prop for action buttons
    renderFooter: (isValid: boolean) => React.ReactNode; 
}

const RoleForm: React.FC<RoleFormProps> = ({ 
    initialData, 
    onSubmit, 
    submitError, 
    renderFooter // Destructure render prop
}) => {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [isValid, setIsValid] = useState(false); // Track basic validity
    const [formError, setFormError] = useState<string | null>(null);

    // Update validity when name changes
    useEffect(() => {
        setIsValid(name.trim().length > 0);
    }, [name]);

    useEffect(() => {
        if (initialData) {
            setName(initialData.name || '');
            setDescription(initialData.description || '');
            setIsValid(initialData.name?.trim().length > 0); // Initial validity
        } else {
            // Reset for add form
            setName('');
            setDescription('');
            setIsValid(false); // Invalid initially for add
        }
        setFormError(null); // Clear errors when initial data changes (or form opens)
    }, [initialData]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setFormError(null);

        if (!isValid) { // Use state validity
            setFormError('Role name cannot be empty.');
            return;
        }

        // Prevent editing name of 'User' role if needed (though button should be disabled)
        if (initialData?.name === 'User' && name !== 'User') {
             setFormError("Cannot rename the default 'User' role.");
             return;
        }

        const formData = { name: name.trim(), description: description.trim() };
        await onSubmit(formData);
    };

    return (
        <form onSubmit={handleSubmit} id="role-form">
            <div className="mb-4">
                <label htmlFor="roleName" className="block text-sm font-medium text-gray-700 mb-1">
                    Role Name <span className="text-red-500">*</span>
                </label>
                <input
                    type="text"
                    id="roleName"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="e.g., Content Approver"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
                    required
                    disabled={initialData?.name === 'User'} // Disable editing name for 'User' role
                />
            </div>
            <div className="mb-4">
                <label htmlFor="roleDescription" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                </label>
                <textarea
                    id="roleDescription"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                    placeholder="Briefly describe the role's purpose..."
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
            </div>

            {/* Display validation errors */}
            {formError && <p className="text-sm text-red-600 mb-3">{formError}</p>}
            {/* Display submission errors from parent */}
             {submitError && <p className="text-sm text-red-600 mb-3">Error: {submitError}</p>}

            {/* Render the footer buttons passed from parent */}
            <div className="flex justify-end pt-4 border-t border-gray-200 space-x-2">
                {renderFooter(isValid)} 
            </div>
        </form>
    );
};

export default RoleForm; 