import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { logger } from "../shared/utils/logger";
import * as sql from 'mssql'; // Add mssql import
import { QueryParameter, executeQuery } from "../shared/db"; // Add QueryParameter import and use correct executeQuery import
import { validateRequest, entraIdRouteParamSchema } from "../shared/validationSchemas";
import { getClientPrincipal } from "../shared/authUtils"; // Only need getClientPrincipal

// Interface matching frontend adminApi.ts PortalUser
interface PortalUser {
    id: number; // UserID
    entraId: string; // EntraID
    name: string; // Combined First + Last Name
    email: string;
    company: string; // CompanyName
    companyId: number; // CompanyID
    department?: string; // DepartmentName
    roles: string[]; // Role Names
    status: 'Active' | 'Inactive'; // Based on IsActive
    // lastLogin?: string; // TODO: Decide if needed/source
}

export async function getUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function GetUser processed request for url "${request.url}"`);
    logger.info('GetUser function invoked.');

    // --- Authentication Check --- 
    const principal = getClientPrincipal(request);
    if (!principal) {
        // Allow if provider allows anonymous and header is missing, 
        // but return 401 if header is present but invalid or not populated by provider
        // For simplicity, let's require authentication for this specific user endpoint
        if (request.headers.get('x-ms-client-principal')) {
             logger.warn('GetUser: Invalid or missing client principal header despite presence.');
             return { status: 401, jsonBody: { message: "Unauthorized. Invalid client principal." } };
        } else {
             // If header truly missing, depends on provider config. Assume protected.
             logger.warn('GetUser: Client principal header missing.');
            return { status: 401, jsonBody: { message: "Unauthorized." } };
        }
    }
    // No specific role check needed, any authenticated user can view user details?
    // --- End Auth --- 

    // --- Input Validation --- 
    const routeParams = { entraId: request.params.entraId }; // Extract for validation
    // Use the actual entraIdRouteParamSchema
    const validationError = validateRequest(entraIdRouteParamSchema, routeParams, context, "route parameters");
    if (validationError) return validationError;
    
    // Validation passed, use validated data
    const validatedRouteParams = entraIdRouteParamSchema.parse(routeParams); // Use parse to get typed data
    const entraId = validatedRouteParams.entraId; // Already a string

    // Manual validation REMOVED
    // --- End Validation --- 

    try {
        // Query to get user details, company name, department name, and aggregated role names
        const query = `
            SELECT 
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName,
                d.DepartmentName,
                STRING_AGG(r.RoleName, ',') WITHIN GROUP (ORDER BY r.RoleName) AS RoleNames
            FROM 
                Users u
            JOIN 
                Companies c ON u.CompanyID = c.CompanyID
            LEFT JOIN
                Departments d ON u.DepartmentID = d.DepartmentID AND d.IsActive = 1 -- Optional join for Department
            LEFT JOIN 
                UserRoles ur ON u.UserID = ur.UserID AND ur.IsActive = 1
            LEFT JOIN 
                Roles r ON ur.RoleID = r.RoleID AND r.IsActive = 1
            WHERE 
                u.EntraID = @EntraID
            GROUP BY
                u.UserID, u.EntraID, u.FirstName, u.LastName, u.Email, u.IsActive, u.DepartmentID,
                c.CompanyID, c.CompanyName, d.DepartmentName
        `;

        logger.debug("Executing GetUser query for Entra ID:", entraId);
        const userParams: QueryParameter[] = [
            { name: 'EntraID', type: sql.NVarChar, value: entraId }
        ];
        const result = await executeQuery(query, userParams);

        if (result.recordset.length === 0) {
            logger.warn(`GetUser: User not found for Entra ID: ${entraId}`);
            return {
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: `User with EntraID ${entraId} not found.` }
            };
        }

        const dbUser = result.recordset[0];

        // Map the DB result to the PortalUser interface
        const portalUser: PortalUser = {
            id: dbUser.UserID,
            entraId: dbUser.EntraID,
            name: `${dbUser.FirstName} ${dbUser.LastName}`,
            email: dbUser.Email,
            company: dbUser.CompanyName,
            companyId: dbUser.CompanyID,
            department: dbUser.DepartmentName || undefined,
            roles: dbUser.RoleNames ? dbUser.RoleNames.split(',') : [],
            status: dbUser.IsActive ? 'Active' : 'Inactive'
        };

        logger.info(`Successfully retrieved user details for EntraID ${entraId} (UserID: ${portalUser.id})`);

        return {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: portalUser
        };

    } catch (error) {
        logger.error(`Error in GetUser function for EntraID ${entraId}:`, error);
        const errorMessage = (error instanceof Error) ? error.message : String(error);
        return {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Error retrieving user details.",
                error: errorMessage
            }
        };
    }
}

app.http('GetUser', {
    methods: ['GET'],
    authLevel: 'anonymous', // Handled by provider + code checks
    route: 'portal-users/{entraId}', // Match function.json
    handler: getUser
}); 