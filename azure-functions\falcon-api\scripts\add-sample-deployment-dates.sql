-- =============================================
-- Add Sample Deployment Dates to Change Requests
-- Purpose: Populate calendar with sample deployment data
-- =============================================

-- Update some existing change requests with deployment dates
UPDATE ChangeRequests 
SET 
    DeploymentDate = DATEADD(DAY, 
        CASE 
            WHEN RequestID % 7 = 0 THEN 1
            WHEN RequestID % 7 = 1 THEN 3
            WHEN RequestID % 7 = 2 THEN 7
            WHEN RequestID % 7 = 3 THEN 10
            WHEN RequestID % 7 = 4 THEN 14
            WHEN RequestID % 7 = 5 THEN 21
            ELSE 30
        END, 
        GETDATE()
    ),
    DeploymentDuration = 
        CASE Priority
            WHEN 'Critical' THEN 30
            WHEN 'High' THEN 60
            WHEN 'Medium' THEN 120
            WHEN 'Low' THEN 240
            ELSE 60
        END,
    DeploymentLocation = 
        CASE RequestID % 3
            WHEN 0 THEN 'Production'
            WHEN 1 THEN 'Staging'
            ELSE 'Development'
        END,
    DeploymentType = 
        CASE Priority
            WHEN 'Critical' THEN 'Emergency'
            WHEN 'High' THEN 'Manual'
            ELSE 'Automatic'
        END,
    RequiresSystemDowntime = 
        CASE 
            WHEN Priority IN ('Critical', 'High') THEN 1
            ELSE 0
        END,
    CalendarColor = 
        CASE Priority
            WHEN 'Critical' THEN '#FF4444'
            WHEN 'High' THEN '#FF8800'
            WHEN 'Medium' THEN '#4488FF'
            WHEN 'Low' THEN '#44AA44'
            ELSE '#666666'
        END
WHERE Status IN ('Approved', 'Under Review', 'In Development')
    AND DeploymentDate IS NULL;

-- Add some specific deployment dates for better calendar visualization
UPDATE ChangeRequests 
SET DeploymentDate = DATEADD(HOUR, 14, CAST(GETDATE() AS DATE)) -- Today at 2 PM
WHERE RequestNumber LIKE 'CR-00001';

UPDATE ChangeRequests 
SET DeploymentDate = DATEADD(HOUR, 10, DATEADD(DAY, 2, CAST(GETDATE() AS DATE))) -- Day after tomorrow at 10 AM
WHERE RequestNumber LIKE 'CR-00002';

UPDATE ChangeRequests 
SET DeploymentDate = DATEADD(HOUR, 16, DATEADD(DAY, 5, CAST(GETDATE() AS DATE))) -- Next week at 4 PM
WHERE RequestNumber LIKE 'CR-00003';

-- Update some requests to create calendar conflicts for testing
UPDATE ChangeRequests 
SET 
    DeploymentDate = DATEADD(HOUR, 14, DATEADD(DAY, 1, CAST(GETDATE() AS DATE))), -- Tomorrow at 2 PM
    DeploymentLocation = 'Production',
    RequiresSystemDowntime = 1
WHERE RequestNumber IN ('CR-00004', 'CR-00005'); -- These will conflict

PRINT 'Sample deployment dates added to change requests for calendar testing'; 