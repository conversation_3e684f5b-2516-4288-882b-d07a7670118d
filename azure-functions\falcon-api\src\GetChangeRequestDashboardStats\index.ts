import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery, QueryParameter } from "../shared/db";
import * as sql from 'mssql';

export async function getChangeRequestDashboardStats(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log('GetChangeRequestDashboardStats function invoked.');
    
    try {
        // Get total counts for all change requests
        const totalQuery = `
            SELECT COUNT(*) as totalRequests
            FROM ChangeRequests 
        `;

        // Get status breakdown
        const statusQuery = `
            SELECT 
                Status,
                COUNT(*) as count
            FROM ChangeRequests 
            GROUP BY Status
        `;

        // Get priority breakdown
        const priorityQuery = `
            SELECT 
                Priority,
                COUNT(*) as count
            FROM ChangeRequests 
            GROUP BY Priority
        `;

        // Get type breakdown
        const typeQuery = `
            SELECT 
                crt.TypeName,
                COUNT(*) as count
            FROM ChangeRequests cr
                LEFT JOIN ChangeRequestTypes crt ON cr.TypeID = crt.TypeID
            GROUP BY crt.TypeName
        `;

        // Execute all queries
        const [totalResult, statusResult, priorityResult, typeResult] = await Promise.all([
            executeQuery(totalQuery),
            executeQuery(statusQuery),
            executeQuery(priorityQuery),
            executeQuery(typeQuery)
        ]);

        // Process results
        const totalRequests = totalResult.recordset[0]?.totalRequests || 0;

        // Convert status results to counts
        const statusCounts = statusResult.recordset.reduce((acc: any, row: any) => {
            acc[row.Status] = row.count;
            return acc;
        }, {});

        const pendingApproval = (statusCounts['Submitted'] || 0) + (statusCounts['Under Review'] || 0);
        const inDevelopment = statusCounts['In Development'] || 0;
        const completed = statusCounts['Completed'] || 0;
        const rejected = statusCounts['Rejected'] || 0;

        // Convert priority results to object
        const priorityCounts = priorityResult.recordset.reduce((acc: any, row: any) => {
            acc[row.Priority] = row.count;
            return acc;
        }, {});

        // Convert type results to object
        const typeCounts = typeResult.recordset.reduce((acc: any, row: any) => {
            acc[row.TypeName || 'Unknown'] = row.count;
            return acc;
        }, {});

        const dashboardStats = {
            totalRequests,
            pendingApproval,
            inDevelopment, 
            completed,
            rejected,
            byPriority: priorityCounts,
            byStatus: statusCounts,
            byType: typeCounts
        };

        context.log(`Dashboard stats generated: ${totalRequests} total requests`);

        return {
            status: 200,
            jsonBody: dashboardStats
        };

    } catch (error) {
        context.error('Error in GetChangeRequestDashboardStats:', error);
        return {
            status: 500,
            jsonBody: {
                error: {
                    code: 'INTERNAL_SERVER_ERROR',
                    message: 'An error occurred while fetching dashboard statistics'
                }
            }
        };
    }
}

app.http('GetChangeRequestDashboardStats', {
    methods: ['GET'],
    route: 'change-management/dashboard-stats',
    authLevel: 'anonymous',
    handler: getChangeRequestDashboardStats
}); 