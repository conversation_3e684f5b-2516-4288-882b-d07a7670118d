import { useState, useEffect } from 'react';
import axios from 'axios';

function SasmosToolInterface() {
  const [formData, setFormData] = useState({
    category: '',
    milPartNo: '',
    manufacturingPartNo: '',
    bu: '',
    ipt: '',
    selectType: '',
    quantity: '',
    company: 'SASMOS'
  });

  const [toolData, setToolData] = useState([]);
  const [filteredMilParts, setFilteredMilParts] = useState([]);
  const [filteredMfgParts, setFilteredMfgParts] = useState([]);
  const [loading, setLoading] = useState(true);

  const companyBuArray = {
    SASMOS: ['MEAPAC', 'USEC', 'INDIA', 'CENTRAL-QA', 'MAINTENANCE', 'CENTRAL', 'SEZ-MEAPAC', 'SEZ-EPS', 'WFP-MAINTENANCE', 'SEZ-MAINTENANCE', 'SEZ-LM', 'SEZ-BOEING', 'SEZ-MBDA FRANCE AND UK', 'SEZ-ADS', 'SEZ-SEEKER', 'SEZ-ADMIN'],
    'FE-SIL': ['CENTRAL-QA', 'MAINTENANCE', 'P8', 'PILATUS', 'G280', 'AIRBUS', 'BEDEK', 'F/A-18', 'SEZ-F/A-18', 'SEZ-SAAB'],
    SCFO: ['SCFO', 'MAINTENANCE', 'SEZ-SCFO']
  };

  const companyBuIptArray = {
    SASMOS: {
      INDIA: ['DRDO', 'TATAPOWER', 'L&T', 'ISRO', 'ADE', 'CABS', 'DRDL', 'CVRDE', 'CHAKRADH', 'ARA', 'BEML', 'ISRO - BANGALORE', 'TASL', 'HONEYWELL', 'BEL', 'BHARAT FORGE', 'COLLINS AEROSPACE', 'RCI', 'SIEMENS ENERGY'],
      CENTRAL: ['MAINTENANCE', 'SQA', 'CALIBRATION', 'QC', 'QA', 'TESTING', 'STORES', 'HR', 'ADMIN', 'FINANCE', 'PURCHASE', 'PLANNING', 'ENGINEERING', 'SALES', 'BD', 'QE', 'IT'],
      MEAPAC: ['IAI-MABAT', 'RAFAEL', 'ELBIT', 'IAI-ELTA', 'IAI-MALAT', 'REDLER', 'RAY-Q'],
      'SEZ-MEAPAC': ['IAI-MABAT', 'RAFAEL', 'ELBIT', 'IAI-ELTA', 'IAI-MALAT', 'REDLER', 'RAY-Q', 'NETLINE'],
      'SEZ-EPS': ['SEZ-EPS'],
      'WFP-MAINTENANCE': ['WFP-MAINTENANCE'],
      'SEZ-MAINTENANCE': ['SEZ-MAINTENANCE'],
      'SEZ-LM': ['LM'],
      'SEZ-BOEING': ['BOEING'],
      'SEZ-MBDA FRANCE AND UK': ['MBDA FRANCE AND UK'],
      'SEZ-ADS': ['SEZ-ADS'],
      'SEZ-SEEKER': ['SEEKER'],
      'SEZ-ADMIN': ['SEZ-ADMIN']
    },
    'FE-SIL': {
      CENTRAL: ['MAINTENANCE', 'SQA', 'CALIBRATION', 'QC', 'QA', 'TESTING', 'STORES', 'HR', 'ADMIN', 'FINANCE', 'PURCHASE', 'PLANNING', 'ENGINEERING', 'SALES', 'BD', 'QE'],
      P8: ['P8'],
      PILATUS: ['PILATUS'],
      G280: ['G280'],
      AIRBUS: ['AIRBUS'],
      BEDEK: ['BEDEK'],
      'F/A-18': ['F/A-18'],
      'SEZ-F/A-18': ['SEZ-F/A-18'],
      'SEZ-SAAB': ['SEZ-SAAB']
    },
    SCFO: {
      SCFO: ['SCFO'],
      MAINTENANCE: ['MAINTENANCE'],
      'SEZ-SCFO': ['SEZ-SCFO']
    }
  };

  const category = [
    { name: 'LOCATOR' },
    { name: 'HYDRAULIC DIE SET' },
    { name: 'RETENTION TOOL SET' },
    { name: 'HOLDING FIXTURE' },
    { name: 'METAL BANDING TOOL' },
    { name: 'HX4 DIE SET' },
    { name: 'INSERTION/EXTRACTION TOOL' },
    { name: 'COAX CABLE CUTTER' },
    { name: 'TORQUE ADAPTER' },
    { name: 'PNEUMATIC CRIMP DIE SET' }
  ];

  const tool_type = [
    { name: 'ADD TOOL' },
    { name: 'SCRAP TOOL' },
    { name: 'LOST TOOL' }
  ];

  useEffect(() => {
    axios.get('http://localhost:3000/api/non_calibrated_mfg_details')
      .then(response => {
        setToolData(response.data);
        setLoading(false);
      })
      .catch(error => {
        console.error("Error fetching tool data:", error);
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (formData.category) {
      const matchingMILs = toolData
        .filter(item => item.tool_category.toUpperCase() === formData.category)
        .map(item => item.non_calibrated_mfg_part_number.toUpperCase());
      setFilteredMilParts([...new Set(matchingMILs)]);
      setFormData(prev => ({ ...prev, milPartNo: '', manufacturingPartNo: '' }));
      setFilteredMfgParts([]);
    }
  }, [formData.category, toolData]);

  useEffect(() => {
    if (formData.milPartNo) {
      const matchingMfgParts = toolData
        .filter(item =>
          item.tool_category.toUpperCase() === formData.category &&
          item.non_calibrated_mfg_part_number.toUpperCase() === formData.milPartNo
        )
        .map(item => item.non_calibrated_mfg_sub_part_number.toUpperCase());
      setFilteredMfgParts([...new Set(matchingMfgParts)]);
      setFormData(prev => ({ ...prev, manufacturingPartNo: '' }));
    }
  }, [formData.milPartNo]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      ...(field === 'company' ? { bu: '', ipt: '' } : {}),
      ...(field === 'bu' ? { ipt: '' } : {})
    }));
  };

  const handleSubmit = async () => {
    try {
      const payload = {
        select_tool: formData.category,
        manufacture_no: formData.milPartNo,
        subtool_no: formData.manufacturingPartNo,
        select_bu: formData.bu,
        select_ipt: formData.ipt,
        create_type: formData.selectType,
        quantity: formData.quantity
      };

      const response = await axios.post("http://localhost:3000/api/submit-tool", payload);
      if (response.data.message === 'Data inserted successfully') {
        alert('Form submitted successfully!');
        window.location.reload()
        setFormData({
          category: '',
          milPartNo: '',
          manufacturingPartNo: '',
          bu: '',
          ipt: '',
          selectType: '',
          quantity: '',
          company: ''
        });

      } else {
        alert('Submission failed. Please try again.');

      }
    } catch (error) {
      console.error('Submit error:', error);
      alert('An error occurred while submitting. Please check the console.');
    }
  };

  const companyOptions = Object.keys(companyBuArray);
  const buOptions = formData.company ? companyBuArray[formData.company] || [] : [];
  const iptOptions = formData.bu ? companyBuIptArray[formData.company]?.[formData.bu] || [] : [];

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-6">
      <div className="w-full max-w-3xl bg-white rounded-xl shadow-xl p-8">
        <h2 className="text-2xl font-bold text-center text-blue-700 mb-6">NON-CALIBRATED TOOL FORM</h2>

        {loading ? (
          <div className="text-center text-gray-600">Loading...</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <SelectField label="Company" value={formData.company} onChange={e => handleInputChange('company', e.target.value)} options={companyOptions} />
            <SelectField label="Category" value={formData.category} onChange={e => handleInputChange('category', e.target.value)} options={category.map(c => c.name)} />
            <SelectField label="MIL Part No" value={formData.milPartNo} onChange={e => handleInputChange('milPartNo', e.target.value)} options={filteredMilParts} />
            <SelectField label="Mfg Part No" value={formData.manufacturingPartNo} onChange={e => handleInputChange('manufacturingPartNo', e.target.value)} options={filteredMfgParts} />
            <SelectField label="Business Unit" value={formData.bu} onChange={e => handleInputChange('bu', e.target.value)} options={buOptions} />
            <SelectField label="IPT" value={formData.ipt} onChange={e => handleInputChange('ipt', e.target.value)} options={iptOptions} />
            <SelectField label="Tool Type" value={formData.selectType} onChange={e => handleInputChange('selectType', e.target.value)} options={tool_type.map(t => t.name)} />
            <div>
              <label className="block mb-2 text-sm font-semibold text-gray-700">Quantity</label>
              <input type="number" value={formData.quantity} onChange={e => handleInputChange('quantity', e.target.value)} className="w-full px-4 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-400 focus:outline-none" />
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <button onClick={handleSubmit} className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition">
            Submit Tool
          </button>
        </div>
      </div>
    </div>
  );
}

function SelectField({ label, value, onChange, options }) {
  return (
    <div>
      <label className="block mb-2 text-sm font-semibold text-gray-700">{label}</label>
      <select value={value} onChange={onChange} className="w-full px-4 py-2 border rounded-lg shadow-sm bg-white focus:ring-2 focus:ring-blue-400 focus:outline-none">
        <option value="">Select {label}</option>
        {options.map((item, idx) => (
          <option key={idx} value={item}>{item}</option>
        ))}
      </select>
    </div>
  );
}

export default SasmosToolInterface;
