{"version": 3, "file": "GetDocumentCategories.js", "sourceRoot": "", "sources": ["../../src/functions/GetDocumentCategories.ts"], "names": [], "mappings": ";;;AAAA,gDAAyF;AACzF,qCAA4D;AAC5D,mDAAgD;AAChD,mDAAiF;AAU1E,KAAK,UAAU,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;IACxF,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,IAAI;QACA,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAA,8BAAkB,EAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAErD,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,EAAE;YAC9B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,CAAC;SAC/D;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,kCAAsB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE;YAC3B,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,CAAC;SACjE;QAED,MAAM,KAAK,GAAG;;;;;;;;;;;;;SAab,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7C,MAAM,UAAU,GAAuB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YACvE,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC7B,IAAI,EAAE,GAAG,CAAC,YAAY;YACtB,QAAQ,EAAE,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE;YAC1C,aAAa,EAAE,GAAG,CAAC,aAAa,IAAI,CAAC;SACxC,CAAC,CAAC,CAAC;QAEJ,eAAM,CAAC,IAAI,CAAC,oCAAoC,UAAU,CAAC,MAAM,wBAAwB,MAAM,IAAI,UAAU,EAAE,CAAC,CAAC;QAEjH,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,UAAU;SACvB,CAAC;KAEL;IAAC,OAAO,KAAK,EAAE;QACZ,eAAM,CAAC,KAAK,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;QAC3E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,OAAO;YACH,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE;gBACN,OAAO,EAAE,uCAAuC;gBAChD,KAAK,EAAE,YAAY;aACtB;SACJ,CAAC;KACL;AACL,CAAC;AA3DD,sDA2DC;AAED,wBAAwB;AACxB,eAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE;IAC9B,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,sBAAsB;IAC7B,OAAO,EAAE,qBAAqB;CACjC,CAAC,CAAC"}