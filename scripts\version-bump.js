#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get the version type from command line arguments
const versionType = process.argv[2] || 'patch';

if (!['patch', 'minor', 'major'].includes(versionType)) {
  console.error('Usage: node version-bump.js [patch|minor|major]');
  console.error('Default: patch');
  process.exit(1);
}

try {
  // Read current package.json
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const currentVersion = packageJson.version;
  
  console.log(`Current version: ${currentVersion}`);
  
  // Bump version using npm version command
  console.log(`Bumping ${versionType} version...`);
  execSync(`npm version ${versionType} --no-git-tag-version`, { stdio: 'inherit' });
  
  // Read the new version
  const updatedPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const newVersion = updatedPackageJson.version;
  
  console.log(`New version: ${newVersion}`);
  
  // Stage the package.json change
  execSync('git add package.json', { stdio: 'inherit' });
  
  console.log(`\nVersion bumped from ${currentVersion} to ${newVersion}`);
  console.log('package.json has been staged for commit.');
  console.log('\nNext steps:');
  console.log('1. Make your changes');
  console.log('2. Stage your changes with: git add .');
  console.log(`3. Commit with: git commit -m "v${newVersion}: [Your commit message]"`);
  console.log('4. Push with: git push origin main');
  
} catch (error) {
  console.error('Error bumping version:', error.message);
  process.exit(1);
} 