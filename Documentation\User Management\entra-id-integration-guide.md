# Microsoft Entra ID Integration Guide for Falcon Portal

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Implementation Steps](#implementation-steps)
   - [3.1 Authentication Setup](#31-authentication-setup)
   - [3.2 User Directory Integration](#32-user-directory-integration)
   - [3.3 Role Management Implementation](#33-role-management-implementation)
   - [3.4 User Interface Components](#34-user-interface-components)
4. [API Reference](#api-reference)
5. [Database Schema](#database-schema)
6. [Security Considerations](#security-considerations)
7. [Advanced Features](#advanced-features)
8. [Troubleshooting](#troubleshooting)
9. [References](#references)

## 1. Introduction

This guide outlines the implementation approach for integrating Microsoft Entra ID (formerly Azure AD) with the Falcon Portal to manage user access and permissions. The integration enables:

- Single Sign-On (SSO) through Microsoft Entra ID
- User directory synchronization
- Role-based access control (RBAC)
- Administrative interface for permission management

The solution allows administrators to search for users in the company's Entra ID directory and assign specific roles to selected users, while maintaining general user access for all authenticated users.

## 2. Architecture Overview

![Architecture Diagram](https://api.placeholder.com/800/400)

The integration architecture consists of the following components:

1. **Authentication Layer**: Handles SSO through Microsoft Authentication Library (MSAL)
2. **User Directory Service**: Interacts with Microsoft Graph API to search and retrieve user information
3. **Role Management Layer**: Manages role assignments in the local database
4. **Administrative Interface**: Provides UI for user search and role management

The system uses a hybrid approach where:
- Authentication is delegated to Entra ID
- User metadata is synchronized from Entra ID
- Role assignments are stored and managed locally

## 3. Implementation Steps

### 3.1 Authentication Setup

#### Configuration Settings

Create a configuration file for Entra ID integration:

```typescript
// src/config/auth.ts
export default {
  entraId: {
    tenantId: process.env.ENTRA_TENANT_ID,
    clientId: process.env.ENTRA_CLIENT_ID, 
    clientSecret: process.env.ENTRA_CLIENT_SECRET,
    audience: process.env.ENTRA_AUDIENCE,
    issuer: `https://login.microsoftonline.com/${process.env.ENTRA_TENANT_ID}/v2.0`
  },
  roles: {
    admin: 'Administrator',
    contentManager: 'ContentManager',
    approver: 'Approver',
    general: 'GeneralUser'
  }
}
```

#### Authentication Middleware

Implement JWT authentication with Passport.js:

```typescript
// src/middlewares/auth.ts
import passport from 'passport';
import { BearerStrategy } from 'passport-azure-ad';
import config from '../config/auth';

const options = {
  identityMetadata: `https://login.microsoftonline.com/${config.tenantId}/v2.0/.well-known/openid-configuration`,
  clientID: config.clientId,
  audience: config.audience,
  issuer: config.issuer,
  validateIssuer: true,
  passReqToCallback: false,
  loggingLevel: 'info'
};

const bearerStrategy = new BearerStrategy(options, (token: any, done: any) => {
  // Extract user information from token
  const user = {
    id: token.oid,
    email: token.preferred_username,
    name: token.name,
    roles: token.roles || [],
    companyId: token.extension_CompanyId
  };
  
  return done(null, user, token);
});

passport.use(bearerStrategy);

export const authenticateJwt = passport.authenticate('oauth-bearer', { session: false });
```

#### Role-Based Authorization

Implement middleware for role-based access control:

```typescript
// src/middlewares/roleCheck.ts
export const requireRole = (allowedRoles: string[]) => {
  return (req: any, res: any, next: any) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required'
        }
      });
    }
    
    const userRoles = user.roles || [];
    const hasPermission = allowedRoles.some(role => userRoles.includes(role));
    
    if (!hasPermission) {
      return res.status(403).json({
        error: {
          code: 'FORBIDDEN',
          message: 'You do not have permission to access this resource'
        }
      });
    }
    
    next();
  };
};
```

### 3.2 User Directory Integration

#### Microsoft Graph API Service

Create a service to interact with Microsoft Graph API:

```typescript
// src/services/graphService.ts
import axios from 'axios';
import qs from 'querystring';
import { logger } from '../utils/logger';
import config from '../config/auth';

export class GraphService {
  private accessToken: string | null = null;
  private tokenExpires: Date | null = null;

  // Get access token for Graph API
  private async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpires && this.tokenExpires > new Date()) {
      return this.accessToken;
    }

    try {
      // Get new token
      const tokenEndpoint = `https://login.microsoftonline.com/${config.entraId.tenantId}/oauth2/v2.0/token`;
      
      const body = {
        client_id: config.entraId.clientId,
        scope: 'https://graph.microsoft.com/.default',
        client_secret: config.entraId.clientSecret,
        grant_type: 'client_credentials'
      };

      const response = await axios.post(tokenEndpoint, qs.stringify(body), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      this.accessToken = response.data.access_token;
      
      // Calculate token expiration (subtract 5 minutes for safety margin)
      const expiresIn = response.data.expires_in;
      this.tokenExpires = new Date(Date.now() + (expiresIn - 300) * 1000);
      
      return this.accessToken;
    } catch (error) {
      logger.error('Error getting Graph API access token', error);
      throw new Error('Failed to authenticate with Microsoft Graph API');
    }
  }

  // Search for users in Entra ID
  public async searchUsers(searchQuery: string, limit: number = 10): Promise<any[]> {
    try {
      const token = await this.getAccessToken();
      
      // Filter users based on display name, given name, surname, or email
      const filter = `startswith(displayName,'${searchQuery}') or startswith(givenName,'${searchQuery}') ` +
                    `or startswith(surname,'${searchQuery}') or startswith(mail,'${searchQuery}')`;
      
      // Select only needed fields
      const select = 'id,displayName,givenName,surname,mail,userPrincipalName,department,companyName';
      
      const response = await axios.get(`https://graph.microsoft.com/v1.0/users`, {
        params: {
          $filter: filter,
          $top: limit,
          $select: select
        },
        headers: {
          'Authorization': `Bearer ${token}`,
          'ConsistencyLevel': 'eventual'
        }
      });

      return response.data.value;
    } catch (error) {
      logger.error('Error searching users in Graph API', error);
      throw new Error('Failed to search users in Microsoft Entra ID');
    }
  }

  // Get a single user by ID
  public async getUserById(userId: string): Promise<any> {
    try {
      const token = await this.getAccessToken();
      
      const select = 'id,displayName,givenName,surname,mail,userPrincipalName,department,companyName';
      
      const response = await axios.get(`https://graph.microsoft.com/v1.0/users/${userId}`, {
        params: {
          $select: select
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return response.data;
    } catch (error) {
      logger.error(`Error getting user with ID ${userId} from Graph API`, error);
      throw new Error('Failed to get user details from Microsoft Entra ID');
    }
  }
}

// Export singleton instance
export const graphService = new GraphService();
```

#### User Management Service

Implement service for user synchronization and role management:

```typescript
// src/services/userManagementService.ts
import { User, UserRole, Role } from '../models';
import { graphService } from './graphService';
import { logger } from '../utils/logger';
import { Op } from 'sequelize';
import sequelize from '../config/database';

// Find or create a user based on Entra ID information
export const findOrCreateUser = async (entraUser: any, createdBy: number): Promise<User> => {
  try {
    // Check if user already exists
    let user = await User.findOne({
      where: { EntraID: entraUser.id }
    });
    
    if (!user) {
      // Create new user
      user = await User.create({
        EntraID: entraUser.id,
        Username: entraUser.userPrincipalName,
        Email: entraUser.mail || entraUser.userPrincipalName,
        FirstName: entraUser.givenName || '',
        LastName: entraUser.surname || '',
        CompanyID: 1, // Default company ID, should be mapped based on company name
        DepartmentID: null, // Would need mapping from entraUser.department
        ProfileImageURL: null,
        IsActive: true,
        CreatedBy: createdBy,
        CreatedDate: new Date()
      });
      
      // Assign default role (General User)
      const generalUserRole = await Role.findOne({
        where: { RoleName: 'Employee' }
      });
      
      if (generalUserRole) {
        await UserRole.create({
          UserID: user.UserID,
          RoleID: generalUserRole.RoleID,
          IsActive: true,
          CreatedBy: createdBy,
          CreatedDate: new Date()
        });
      }
    }
    
    return user;
  } catch (error) {
    logger.error('Error in findOrCreateUser', error);
    throw error;
  }
};

// Assign role to user
export const assignRoleToUser = async (userId: number, roleId: number, assignedBy: number): Promise<boolean> => {
  const transaction = await sequelize.transaction();
  
  try {
    // Check if assignment already exists
    const existingRole = await UserRole.findOne({
      where: {
        UserID: userId,
        RoleID: roleId
      }
    });
    
    if (existingRole) {
      // If inactive, reactivate
      if (!existingRole.IsActive) {
        await existingRole.update({
          IsActive: true,
          ModifiedBy: assignedBy,
          ModifiedDate: new Date()
        }, { transaction });
      }
      // Otherwise it's already assigned
    } else {
      // Create new assignment
      await UserRole.create({
        UserID: userId,
        RoleID: roleId,
        IsActive: true,
        CreatedBy: assignedBy,
        CreatedDate: new Date()
      }, { transaction });
    }
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error assigning role ${roleId} to user ${userId}`, error);
    throw error;
  }
};

// Remove role from user
export const removeRoleFromUser = async (userId: number, roleId: number, removedBy: number): Promise<boolean> => {
  try {
    const userRole = await UserRole.findOne({
      where: {
        UserID: userId,
        RoleID: roleId,
        IsActive: true
      }
    });
    
    if (!userRole) {
      return false; // Role not assigned
    }
    
    // Soft delete by marking as inactive
    await userRole.update({
      IsActive: false,
      ModifiedBy: removedBy,
      ModifiedDate: new Date()
    });
    
    return true;
  } catch (error) {
    logger.error(`Error removing role ${roleId} from user ${userId}`, error);
    throw error;
  }
};
```

### 3.3 Role Management Implementation

#### Database Models

User and Role models:

```typescript
// src/models/User.ts
import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';

class User extends Model {
  public UserID!: number;
  public EntraID!: string;
  public Username!: string;
  public Email!: string;
  public FirstName!: string;
  public LastName!: string;
  public CompanyID!: number;
  public LocationID!: number | null;
  public DepartmentID!: number | null;
  public ProfileImageURL!: string | null;
  public IsActive!: boolean;
  public CreatedBy!: number;
  public CreatedDate!: Date;
  public ModifiedBy!: number | null;
  public ModifiedDate!: Date | null;
}

User.init(
  {
    UserID: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    EntraID: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true,
    },
    Username: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    Email: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    FirstName: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    LastName: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    CompanyID: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    LocationID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    DepartmentID: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ProfileImageURL: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    CreatedBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    CreatedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    ModifiedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ModifiedDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'Users',
    timestamps: false,
  }
);

export default User;
```

```typescript
// src/models/Role.ts
import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';

class Role extends Model {
  public RoleID!: number;
  public RoleName!: string;
  public RoleDescription!: string | null;
  public IsSystemRole!: boolean;
  public IsActive!: boolean;
  public CreatedBy!: number;
  public CreatedDate!: Date;
  public ModifiedBy!: number | null;
  public ModifiedDate!: Date | null;
}

Role.init(
  {
    RoleID: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    RoleName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
    },
    RoleDescription: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    IsSystemRole: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    CreatedBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    CreatedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    ModifiedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ModifiedDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'Roles',
    timestamps: false,
  }
);

export default Role;
```

```typescript
// src/models/UserRole.ts
import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';

class UserRole extends Model {
  public UserRoleID!: number;
  public UserID!: number;
  public RoleID!: number;
  public IsActive!: boolean;
  public CreatedBy!: number;
  public CreatedDate!: Date;
  public ModifiedBy!: number | null;
  public ModifiedDate!: Date | null;
}

UserRole.init(
  {
    UserRoleID: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    UserID: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    RoleID: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    CreatedBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    CreatedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    ModifiedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ModifiedDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'UserRoles',
    timestamps: false,
  }
);

export default UserRole;
```

#### API Controllers

Implement controllers for user management:

```typescript
// src/controllers/userManagementController.ts
import { Request, Response } from 'express';
import { 
  searchEntraUsers, 
  findOrCreateUser, 
  getUsersWithRoles, 
  assignRoleToUser, 
  removeRoleFromUser 
} from '../services/userManagementService';
import { graphService } from '../services/graphService';
import { paginationParams } from '../utils/pagination';
import { logger } from '../utils/logger';
import { Role } from '../models';

// Search users in Entra ID
export const searchUsers = async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string || '';
    const limit = Number(req.query.limit) || 10;
    
    if (!query || query.length < 2) {
      return res.status(400).json({
        error: {
          code: 'INVALID_QUERY',
          message: 'Search query must be at least 2 characters'
        }
      });
    }
    
    const users = await searchEntraUsers(query, limit);
    
    return res.status(200).json(users);
  } catch (error) {
    logger.error('Error in searchUsers controller', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while searching users'
      }
    });
  }
};

// Add user from Entra ID
export const addUser = async (req: Request, res: Response) => {
  try {
    const entraId = req.body.entraId;
    
    if (!entraId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_PARAMETER',
          message: 'Entra ID is required'
        }
      });
    }
    
    // Get user details from Graph API
    const entraUser = await graphService.getUserById(entraId);
    
    // Create or update user in our database
    const user = await findOrCreateUser(entraUser, req.user.id);
    
    return res.status(200).json(user);
  } catch (error) {
    logger.error('Error in addUser controller', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while adding the user'
      }
    });
  }
};

// Assign role to user
export const assignRole = async (req: Request, res: Response) => {
  try {
    const userId = Number(req.params.userId);
    const roleId = Number(req.body.roleId);
    
    if (!userId || !roleId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_PARAMETER',
          message: 'User ID and Role ID are required'
        }
      });
    }
    
    const result = await assignRoleToUser(userId, roleId, req.user.id);
    
    return res.status(200).json({
      success: result,
      message: 'Role assigned successfully'
    });
  } catch (error) {
    logger.error('Error in assignRole controller', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while assigning the role'
      }
    });
  }
};

// Remove role from user
export const removeRole = async (req: Request, res: Response) => {
  try {
    const userId = Number(req.params.userId);
    const roleId = Number(req.params.roleId);
    
    if (!userId || !roleId) {
      return res.status(400).json({
        error: {
          code: 'MISSING_PARAMETER',
          message: 'User ID and Role ID are required'
        }
      });
    }
    
    const result = await removeRoleFromUser(userId, roleId, req.user.id);
    
    return res.status(200).json({
      success: result,
      message: result ? 'Role removed successfully' : 'Role was not assigned to user'
    });
  } catch (error) {
    logger.error('Error in removeRole controller', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while removing the role'
      }
    });
  }
};
```

#### API Routes

Define routes for user management:

```typescript
// src/routes/userManagementRoutes.ts
import { Router } from 'express';
import { 
  searchUsers, 
  addUser, 
  getUsers, 
  getRoles, 
  assignRole, 
  removeRole 
} from '../controllers/userManagementController';
import { authenticateJwt } from '../middlewares/auth';
import { requireRole } from '../middlewares/roleCheck';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateJwt);

// Only administrators can manage users
router.use(requireRole(['Administrator']));

// GET /v1/user-management/search - Search users in Entra ID
router.get('/search', searchUsers);

// POST /v1/user-management/users - Add user from Entra ID
router.post('/users', addUser);

// GET /v1/user-management/users - Get users with their roles
router.get('/users', getUsers);

// GET /v1/user-management/roles - Get available roles
router.get('/roles', getRoles);

// POST /v1/user-management/users/:userId/roles - Assign role to user
router.post('/users/:userId/roles', assignRole);

// DELETE /v1/user-management/users/:userId/roles/:roleId - Remove role from user
router.delete('/users/:userId/roles/:roleId', removeRole);

export default router;
```

### 3.4 User Interface Components

#### User Management React Component

Create a React component for the user management interface:

```jsx
// UserManagement.jsx
import React, { useState, useEffect } from 'react';
import { 
  Box, Button, TextField, Typography, Table, TableBody, 
  TableCell, TableContainer, TableHead, TableRow, Paper,
  Dialog, DialogTitle, DialogContent, DialogActions,
  Chip, FormControl, InputLabel, Select, MenuItem,
  Snackbar, Alert, CircularProgress, Grid
} from '@mui/material';
import { Search as SearchIcon, PersonAdd as PersonAddIcon } from '@mui/icons-material';

const UserManagement = () => {
  // State for user list
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [roles, setRoles] = useState([]);
  const [selectedRole, setSelectedRole] = useState('');
  
  // State for dialogs
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [assignRoleDialogOpen, setAssignRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  
  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load users and roles on component mount
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);
  
  // Fetch users with their roles
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/user-management/users');
      const data = await response.json();
      setUsers(data.items);
    } catch (error) {
      console.error('Error fetching users:', error);
      showSnackbar('Failed to load users', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch available roles
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/v1/user-management/roles');
      const data = await response.json();
      setRoles(data);
    } catch (error) {
      console.error('Error fetching roles:', error);
      showSnackbar('Failed to load roles', 'error');
    }
  };
  
  // Search users in Entra ID
  const searchUsers = async () => {
    if (searchQuery.length < 2) {
      showSnackbar('Search query must be at least 2 characters', 'warning');
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/user-management/search?q=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();
      setSearchResults(data);
      setAddUserDialogOpen(true);
    } catch (error) {
      console.error('Error searching users:', error);
      showSnackbar('Failed to search users', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Add user from Entra ID
  const addUser = async (entraId) => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/user-management/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ entraId })
      });
      
      if (response.ok) {
        showSnackbar('User added successfully', 'success');
        setAddUserDialogOpen(false);
        fetchUsers();
      } else {
        const error = await response.json();
        throw new Error(error.error.message || 'Failed to add user');
      }
    } catch (error) {
      console.error('Error adding user:', error);
      showSnackbar(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Assign role to user
  const assignRole = async () => {
    if (!selectedUser || !selectedRole) {
      showSnackbar('Please select a user and a role', 'warning');
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/user-management/users/${selectedUser.UserID}/roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roleId: selectedRole })
      });
      
      if (response.ok) {
        showSnackbar('Role assigned successfully', 'success');
        setAssignRoleDialogOpen(false);
        fetchUsers();
      } else {
        const error = await response.json();
        throw new Error(error.error.message || 'Failed to assign role');
      }
    } catch (error) {
      console.error('Error assigning role:', error);
      showSnackbar(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Remove role from user
  const removeRole = async (userId, roleId) => {
    if (!window.confirm('Are you sure you want to remove this role?')) {
      return;
    }
    
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/user-management/users/${userId}/roles/${roleId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        showSnackbar('Role removed successfully', 'success');
        fetchUsers();
      } else {
        const error = await response.json();
        throw new Error(error.error.message || 'Failed to remove role');
      }
    } catch (error) {
      console.error('Error removing role:', error);
      showSnackbar(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };
  
  // Show snackbar notification
  const showSnackbar = (message, severity = 'info') => {
    setSnackbar({ open: true, message, severity });
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };
  
  // Open assign role dialog
  const openAssignRoleDialog = (user) => {
    setSelectedUser(user);
    setSelectedRole('');
    setAssignRoleDialogOpen(true);
  };
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        User Management
      </Typography>
      
      {/* Search and Add User */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={9}>
          <TextField
            fullWidth
            label="Search Users in Microsoft Entra ID"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            variant="outlined"
            placeholder="Search by name or email"
          />
        </Grid>
        <Grid item xs={3}>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            onClick={searchUsers}
            startIcon={<SearchIcon />}
            disabled={loading}
            sx={{ height: '100%' }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
      
      {/* User Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Department</TableCell>
              <TableCell>Roles</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.UserID}>
                  <TableCell>{`${user.FirstName} ${user.LastName}`}</TableCell>
                  <TableCell>{user.Email}</TableCell>
                  <TableCell>{user.Company?.CompanyName || '-'}</TableCell>
                  <TableCell>{user.Department?.DepartmentName || '-'}</TableCell>
                  <TableCell>
                    {user.UserRoles?.map((userRole) => (
                      <Chip
                        key={userRole.UserRoleID}
                        label={userRole.Role.RoleName}
                        color="primary"
                        variant="outlined"
                        onDelete={() => removeRole(user.UserID, userRole.RoleID)}
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => openAssignRoleDialog(user)}
                    >
                      Assign Role
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Add User Dialog */}
      <Dialog open={addUserDialogOpen} onClose={() => setAddUserDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add User from Microsoft Entra ID</DialogTitle>
        <DialogContent>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Department</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {searchResults.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  searchResults.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>{user.displayName}</TableCell>
                      <TableCell>{user.mail || user.userPrincipalName}</TableCell>
                      <TableCell>{user.department || '-'}</TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="contained"
                          color="primary"
                          onClick={() => addUser(user.id)}
                          disabled={loading}
                        >
                          Add User
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddUserDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>
      
      {/* Assign Role Dialog */}
      <Dialog open={assignRoleDialogOpen} onClose={() => setAssignRoleDialogOpen(false)}>
        <DialogTitle>Assign Role to User</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box sx={{ mt: 2, minWidth: 300 }}>
              <Typography variant="body1" gutterBottom>
                User: {selectedUser.FirstName} {selectedUser.LastName}
              </Typography>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Role</InputLabel>
                <Select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  label="Role"
                >
                  <MenuItem value="">
                    <em>Select a role</em>
                  </MenuItem>
                  {roles.map((role) => (
                    <MenuItem key={role.RoleID} value={role.RoleID}>
                      {role.RoleName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignRoleDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={assignRole} 
            variant="contained" 
            color="primary"
            disabled={!selectedRole || loading}
          >
            Assign
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};