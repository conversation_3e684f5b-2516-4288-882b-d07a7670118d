"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRoles = getRoles;
const functions_1 = require("@azure/functions");
const db_1 = require("../shared/db");
const logger_1 = require("../shared/utils/logger");
function getRoles(request, context) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`Http function GetRoles processed request for url "${request.url}"`);
        logger_1.logger.info('GetRoles function invoked.');
        // TODO: Add authentication check here if needed based on function.json authLevel
        try {
            const query = `
            SELECT 
                RoleID, 
                RoleName, 
                RoleDescription 
            FROM Roles 
            WHERE IsActive = 1 
            ORDER BY RoleName;
        `;
            const result = yield (0, db_1.executeQuery)(query);
            // Map DB result to frontend RoleDefinition interface
            const roles = result.recordset.map(dbRole => ({
                id: dbRole.RoleID,
                name: dbRole.RoleName,
                description: dbRole.RoleDescription || null // Ensure null if empty/null from DB
            }));
            logger_1.logger.info(`Successfully retrieved ${roles.length} active roles.`);
            return {
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: roles // Use jsonBody in V4 model for automatic serialization
            };
        }
        catch (error) {
            logger_1.logger.error('Error in GetRoles function:', error);
            // Type assertion for error handling
            const errorMessage = (error instanceof Error) ? error.message : String(error);
            return {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    message: "Error retrieving roles.",
                    error: errorMessage
                }
            };
        }
    });
}
// Register the function using the V4 model
functions_1.app.http('GetRoles', {
    methods: ['GET'],
    authLevel: 'anonymous', // Match function.json
    route: 'roles', // Match function.json
    handler: getRoles
});
//# sourceMappingURL=index.js.map