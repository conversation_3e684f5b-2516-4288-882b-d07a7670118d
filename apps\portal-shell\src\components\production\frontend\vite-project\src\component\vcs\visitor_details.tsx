import { useEffect, useState } from 'react';

interface Visitor {
  visitor_name: string;
  visitor_org: string;
  visitor_nation: string;
  visitor_loc: string;
  visitor_contact: string;
  visitor_mail: string;
}

interface ApprovalDetails {
  visit_date: string;
  purpose: string;
  invitee: string;
  visitor_pc: string;
}

const VisitorReport: React.FC = () => {
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [approvalDetails, setApprovalDetails] = useState<ApprovalDetails | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetch(`http://localhost:3000/api/visitors`)
      .then(res => res.json())
      .then(data => {
        setVisitors(data.data);
      })
      .catch(err => {
        console.error('Error fetching visitors', err);
      });
  }, []);
  const handleVisitorClick = (visitor: any) => {
    setSelectedVisitor(visitor);

    fetch(`http://localhost:3000/api/visitor_data_approvals?visitor_name=${encodeURIComponent(visitor.visitor_name)}`)
      .then(res => res.json())
      .then(data => {
        setApprovalDetails(data);
        setShowModal(true);
      })
      .catch(err => {
        console.error("Error fetching approval details", err);
        setApprovalDetails(null);
        setShowModal(true); // still show modal with basic info
      });
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedVisitor(null);
    setApprovalDetails(null);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-7xl mx-auto bg-white rounded-2xl shadow-lg p-6">
        <h2 className="text-3xl font-semibold text-blue-700 mb-6 border-b pb-2">
          Visitor Control MIS Report
        </h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-blue-100">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">SL #</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Name</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Organisation</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Nationality</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Location</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Contact #</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-blue-800">Mail ID</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {visitors.map((visitor, index) => (
                <tr key={index} className="hover:bg-gray-50 cursor-pointer">
                  <td className="px-4 py-3 text-sm text-gray-700">{index + 1}</td>
                  <td
                    className="px-4 py-3 text-sm text-blue-600 underline"
                    onClick={() => handleVisitorClick(visitor)}
                  >
                    {visitor.visitor_name}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-700">{visitor.visitor_org}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{visitor.visitor_nation}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{visitor.visitor_loc}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{visitor.visitor_contact}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{visitor.visitor_mail}</td>
                </tr>
              ))}
              {visitors.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-4 py-6 text-center text-gray-500">
                    No visitors found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Popup Modal */}
      {showModal && selectedVisitor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white w-[90%] max-w-5xl p-4 rounded-xl shadow-xl relative flex flex-wrap gap-4">
            <button
              className="absolute top-2 right-2 text-red-600 font-bold text-xl"
              onClick={closeModal}
            >
              ✕
            </button>

            {/* Visitor Details */}
            <div className="w-full sm:w-[40%] border border-gray-300 p-4">
              <h3 className="bg-blue-600 text-white text-center py-2 mb-3 font-bold">
                VISITOR DETAILS
              </h3>
              <p><strong>VISITOR NAME:</strong> {selectedVisitor.visitor_name}</p>
              <p><strong>ORGANISATION:</strong> {selectedVisitor.visitor_org}</p>
              <p><strong>NATIONALITY:</strong> {selectedVisitor.visitor_nation}</p>
              <p><strong>LOCATION:</strong> {selectedVisitor.visitor_loc}</p>
              <p><strong>PHONE #:</strong> {selectedVisitor.visitor_contact}</p>
              <p><strong>MAIL ID:</strong> {selectedVisitor.visitor_mail}</p>
            </div>

            {/* Visit Details */}
            <div className="w-full sm:w-[35%] border border-gray-300 p-4">
              <table className="w-full border-collapse border text-sm">
                <thead>
                  <tr className="bg-blue-100 text-left">
                    <th className="border px-2 py-1">VISIT DATE</th>
                    <th className="border px-2 py-1">PURPOSE</th>
                    <th className="border px-2 py-1">INVITEE</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="bg-green-100">
                    <td className="border px-2 py-1">{approvalDetails?.visit_date || 'N/A'}</td>
                    <td className="border px-2 py-1">{approvalDetails?.purpose || 'N/A'}</td>
                    <td className="border px-2 py-1">{approvalDetails?.invitee || 'N/A'}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Visitor Photo */}
            <div className="w-full sm:w-[20%] flex justify-center items-center border border-gray-300 p-2">
            {approvalDetails?.visitor_pc ? (
  <a
    href={`http://localhost:3000/VISITOR_PICS/${approvalDetails.visitor_pc}`}
    target="_blank"
    rel="noopener noreferrer"
  >
    <img
      src={`http://localhost:3000/VISITOR_PICS/${approvalDetails.visitor_pc}`}
      alt={selectedVisitor.visitor_name || "Visitor"}
      className="w-full h-auto max-h-64 object-contain rounded-md"
    />
  </a>
) : (
  <p>No Photo</p>
)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisitorReport;
