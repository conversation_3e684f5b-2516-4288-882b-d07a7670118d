{"version": 3, "file": "GetRoles.js", "sourceRoot": "", "sources": ["../../../../../azure-functions/falcon-api/src/functions/GetRoles.ts"], "names": [], "mappings": ";;;;;;;;;;;AAIA,4BAkCC;AAtCD,gDAAyF;AACzF,qCAA4C;AAG5C,SAAsB,QAAQ,CAAC,OAAoB,EAAE,OAA0B;;QAC3E,OAAO,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QAExE,IAAI,CAAC;YACD,uCAAuC;YACvC,MAAM,MAAM,GAAG,MAAM,IAAA,iBAAY,EAAC,oGAAoG,CAAC,CAAC;YAExI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YAEjF,uFAAuF;YACvF,MAAM,KAAK,GAAqB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC5D,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,WAAW,EAAE,MAAM,CAAC,eAAe,CAAC,sCAAsC;gBAC1E,oEAAoE;gBACpE,qCAAqC;gBACrC,4BAA4B;aAC/B,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE,KAAK;aAClB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;YAEzF,OAAO;gBACH,MAAM,EAAE,GAAG;gBACX,QAAQ,EAAE;oBACN,OAAO,EAAE,uBAAuB;oBAChC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;iBAC/E;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CAAA;AAED,eAAG,CAAC,IAAI,CAAC,UAAU,EAAE;IACjB,OAAO,EAAE,CAAC,KAAK,CAAC;IAChB,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,QAAQ;CACpB,CAAC,CAAC"}