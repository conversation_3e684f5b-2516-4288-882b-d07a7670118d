import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { executeQuery } from "../shared/db";
import { RoleDefinition } from "../shared/interfaces";
import { logger } from "../shared/utils/logger";
import * as sql from 'mssql';
import { QueryParameter } from "../shared/db";

// Define request body type locally
interface CreateRoleRequestBody {
    name: string;
    description?: string;
}

export async function createRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}"`);

    let body: CreateRoleRequestBody;
    try {
        body = await request.json() as CreateRoleRequestBody;
    } catch (e) {
        context.error("ERROR parsing request body:", e);
        return {
            status: 400,
            jsonBody: { message: "Invalid JSON in request body." }
        };
    }

    if (!body || !body.name || !body.name.trim()) {
        return {
            status: 400,
            jsonBody: { message: "Role 'name' is required." }
        };
    }

    try {
        const roleName = body.name.trim();
        const description = body.description?.trim() || null;
        const createdBy = 1; // Placeholder

        // 1. Check if role already exists (case-insensitive)
        const checkQuery = 'SELECT COUNT(*) as Count FROM Roles WHERE LOWER(RoleName) = LOWER(@RoleName)';
        // Refactored parameters
        const checkParams: QueryParameter[] = [
            { name: 'RoleName', type: sql.NVarChar, value: roleName }
        ];
        const checkResult = await executeQuery(checkQuery, checkParams);

        if (checkResult.recordset[0].Count > 0) {
            logger.warn(`Role creation attempt failed: Role '${roleName}' already exists.`);
            return {
                status: 409,
                jsonBody: { message: `Role with name '${roleName}' already exists.` }
            };
        }

        // 3. Insert new role
        const query = `
            INSERT INTO Roles (RoleName, RoleDescription, IsSystemRole, IsActive, CreatedBy, CreatedDate, ModifiedDate) 
            OUTPUT INSERTED.RoleID, INSERTED.RoleName, INSERTED.RoleDescription, INSERTED.IsActive 
            VALUES (@RoleNameParam, @DescriptionParam, 0, 1, @CreatedByParam, GETUTCDATE(), GETUTCDATE())
        `;
        // Refactored parameters
        const insertParams: QueryParameter[] = [
            { name: 'RoleNameParam', type: sql.NVarChar, value: roleName },
            { name: 'DescriptionParam', type: sql.NVarChar, value: description },
            { name: 'CreatedByParam', type: sql.Int, value: createdBy }
        ];
        const result = await executeQuery(query, insertParams);

        if (!result.recordset || result.recordset.length === 0) {
            logger.error("Role creation failed: No record returned after insert.");
            return {
                status: 500,
                jsonBody: {
                    message: "Error creating role.",
                    error: "No record returned after insert."
                }
            };
        }
        
        const outputRow = result.recordset[0];
        const newRole: RoleDefinition = {
            RoleID: outputRow.RoleID,
            RoleName: outputRow.RoleName,
            Description: outputRow.RoleDescription
        };

        return {
            status: 201,
            jsonBody: newRole
        };

    } catch (error) {
        context.error(`Error creating role: ${error instanceof Error ? error.message : String(error)}`);
        return {
            status: 500,
            jsonBody: {
                message: "Error creating role.",
                error: error instanceof Error ? error.message : String(error)
            }
        };
    }
}

app.http('CreateRole', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'roles/create',
    handler: createRole
});
