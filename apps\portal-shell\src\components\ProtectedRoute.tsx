import React from 'react';
import { Navigate } from 'react-router-dom';
import { useCurrentUser, hasRole, hasAnyRole } from '../services/userContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredRoles?: string[];
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const AccessDeniedPage: React.FC<{ requiredRole?: string; requiredRoles?: string[] }> = ({ 
  requiredRole, 
  requiredRoles 
}) => {
  const { user } = useCurrentUser();
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
          <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
        
        <p className="text-gray-600 mb-4">
          You don't have permission to access this section of the portal.
        </p>
        
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-700 mb-2">
            <strong>Required Role:</strong> {requiredRole || requiredRoles?.join(' or ')}
          </p>
          <p className="text-sm text-gray-700">
            <strong>Your Roles:</strong> {user?.roles.join(', ') || 'None'}
          </p>
        </div>
        
        <div className="space-y-3">
          <button
            onClick={() => window.history.back()}
            className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors"
          >
            Go Back
          </button>
          
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
        
        <p className="text-xs text-gray-500 mt-6">
          If you believe you should have access to this section, please contact your administrator.
        </p>
      </div>
    </div>
  );
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredRoles,
  fallbackPath = '/dashboard',
  showAccessDenied = true
}) => {
  const { user, loading } = useCurrentUser();

  // Show loading spinner while user data is being fetched
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is not authenticated, redirect to login (handled by MSAL)
  if (!user || !user.isAuthenticated) {
    return <Navigate to={fallbackPath} replace />;
  }

  // Check if user has required role(s)
  let hasAccess = false;

  if (requiredRole) {
    hasAccess = hasRole(user, requiredRole);
  } else if (requiredRoles && requiredRoles.length > 0) {
    hasAccess = hasAnyRole(user, requiredRoles);
  } else {
    // No specific role required, allow access for authenticated users
    hasAccess = true;
  }

  if (!hasAccess) {
    if (showAccessDenied) {
      return <AccessDeniedPage requiredRole={requiredRole} requiredRoles={requiredRoles} />;
    } else {
      return <Navigate to={fallbackPath} replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute; 