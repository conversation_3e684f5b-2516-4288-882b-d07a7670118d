import React, { useState, useEffect, useCallback } from 'react';
import * as FeatherIcons from 'feather-icons-react';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import {
  fetchAnnouncements,
  fetchPendingActions,
  fetchQuickLinks,
  fetchRecentDocuments,
  fetchUpcomingEvents,
  // Types
  Announcement,
  PendingAction,
  QuickLink,
  RecentDocument,
  UpcomingEvent,
  // Mock update function (add later)
  updateQuickLinks
} from '../services/dashboardApi'; // Correct path back
import { formatDistanceToNow, format } from 'date-fns'; // For date formatting
import QuickLinkCustomizeModal from './modals/QuickLinkCustomizeModal'; // Import the modal
import toast from 'react-hot-toast'; // Import toast

// Define a type for FeatherIcons keys
type FeatherIconName = keyof typeof FeatherIcons;

// Helper to get Feather Icon component by name
const DynamicFeatherIcon = ({ name, ...props }: { name: string; [key: string]: unknown }) => {
  // Use the FeatherIconName type, but still need assertion for dynamic access
  const IconComponent = (FeatherIcons as Record<string, React.ComponentType<unknown>>)[name as FeatherIconName] || FeatherIcons.AlertCircle; 
  return <IconComponent {...props} />;
};

// Define state structure for each section
interface SectionState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
}

const DashboardSection: React.FC = () => {
  // State for each dashboard section
  const [announcements, setAnnouncements] = useState<SectionState<Announcement>>({ data: [], loading: true, error: null });
  const [pendingActions, setPendingActions] = useState<SectionState<PendingAction>>({ data: [], loading: true, error: null });
  const [quickLinks, setQuickLinks] = useState<SectionState<QuickLink>>({ data: [], loading: true, error: null });
  const [recentDocs, setRecentDocs] = useState<SectionState<RecentDocument>>({ data: [], loading: true, error: null });
  const [upcomingEvents, setUpcomingEvents] = useState<SectionState<UpcomingEvent>>({ data: [], loading: true, error: null });
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false); // State for modal visibility

  const navigate = useNavigate(); // Initialize useNavigate hook

  // --- Mock User Context (Replace with actual context later) ---
  const MOCK_USER_COMPANY_ID = 'SASMOS HET'; 

  // Fetch data on component mount
  useEffect(() => {
    const loadData = async () => {
      // Fetch Announcements
      try {
        // Pass mock company ID
        const data = await fetchAnnouncements(5, MOCK_USER_COMPANY_ID);
        setAnnouncements({ data, loading: false, error: null });
      } catch (error) {
        console.error('Failed to load announcements:', error);
        setAnnouncements({ data: [], loading: false, error: 'Failed to load announcements.' });
      }

      // Fetch Pending Actions
      try {
        const data = await fetchPendingActions();
        setPendingActions({ data, loading: false, error: null });
      } catch (error) {
        console.error('Failed to load pending actions:', error);
        setPendingActions({ data: [], loading: false, error: 'Failed to load pending actions.' });
      }

      // Fetch Quick Links
      try {
        const data = await fetchQuickLinks();
        setQuickLinks({ data, loading: false, error: null });
      } catch (error) {
        console.error('Failed to load quick links:', error);
        setQuickLinks({ data: [], loading: false, error: 'Failed to load quick links.' });
      }

      // Fetch Recent Documents
      try {
        const data = await fetchRecentDocuments();
        setRecentDocs({ data, loading: false, error: null });
      } catch (error) {
        console.error('Failed to load recent documents:', error);
        setRecentDocs({ data: [], loading: false, error: 'Failed to load recent documents.' });
      }

      // Fetch Upcoming Events
      try {
         // Pass mock company ID
        const data = await fetchUpcomingEvents(3, MOCK_USER_COMPANY_ID);
        setUpcomingEvents({ data, loading: false, error: null });
      } catch (error) {
        console.error('Failed to load upcoming events:', error);
        setUpcomingEvents({ data: [], loading: false, error: 'Failed to load upcoming events.' });
      }
    };

    loadData();
  }, [MOCK_USER_COMPANY_ID]);

  // --- Helper Functions for Rendering ---
  const getSeverityBorder = (severity: 'high' | 'medium' | 'low') => {
    switch (severity) {
      case 'high': return 'border-red-500';
      case 'medium': return 'border-yellow-500';
      case 'low': return 'border-gray-400'; // Use gray for low
      default: return 'border-gray-500';
    }
  };

  const getActionIcon = (type: PendingAction['type']) => {
    switch (type) {
      case 'Approval': return { component: FeatherIcons.CheckSquare, color: 'text-orange-500' };
      case 'Review': return { component: FeatherIcons.Edit, color: 'text-blue-500' };
      case 'Acknowledgment': return { component: FeatherIcons.ThumbsUp, color: 'text-green-500' };
      case 'Task': return { component: FeatherIcons.Clipboard, color: 'text-purple-500' };
      default: return { component: FeatherIcons.AlertCircle, color: 'text-gray-500' };
    }
  };

  const formatRelativeTime = (isoDate: string) => {
    try {
      return formatDistanceToNow(new Date(isoDate), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting relative time:', error);
      return 'Invalid date';
    }
  };

    const formatEventDateTime = (startIso: string, endIso?: string | null) => {
    try {
      const startDate = new Date(startIso);
      const endDate = endIso ? new Date(endIso) : null;

      const datePart = format(startDate, 'MMM d, yyyy');
      const startTimePart = format(startDate, 'h:mm a');
      
      if (endDate && format(startDate, 'yyyy-MM-dd') === format(endDate, 'yyyy-MM-dd')) {
        // Same day event
        const endTimePart = format(endDate, 'h:mm a');
        return `${datePart} • ${startTimePart} - ${endTimePart}`;
      } else {
        // Single time or multi-day (just show start time for now)
        return `${datePart} • ${startTimePart}`;
      }
    } catch (error) {
      console.error('Error formatting event date time:', error);
      return 'Invalid date/time';
    }
  };

  // --- Navigation Handlers (Updated) ---
  const handleViewAll = (section: string) => {
    // Map section name to a route path (adjust paths as needed)
    let path = '/dashboard'; // Default
    switch (section.toLowerCase()) {
      case 'announcements': path = '/communication/announcements'; break;
      case 'pending actions': path = '/actions'; break; // Or a dedicated actions page
      case 'recent documents': path = '/knowledge/documents'; break;
      case 'upcoming events': path = '/communication/events'; break;
    }
    console.log(`Navigating to View All: ${section} at ${path}`);
    navigate(path);
  };

  const handleCustomize = (section: string) => {
    if (section === 'Quick Links') {
      setIsCustomizeModalOpen(true); // Open the modal
    } else {
      console.log(`Customize action not implemented for: ${section}`);
    }
  };

  // Placeholder for handling save from modal
  const handleSaveQuickLinks = useCallback(async (newLinks: QuickLink[]) => {
    const toastId = toast.loading('Saving Quick Links...');
    try {
      await updateQuickLinks(newLinks);
      setQuickLinks(prevState => ({ ...prevState, data: newLinks, loading: false, error: null }));
      setIsCustomizeModalOpen(false);
      toast.success('Quick Links updated successfully!', { id: toastId });
    } catch (error) {
      console.error('Error saving quick links:', error);
      toast.error('Failed to save Quick Links. Please try again.', { id: toastId });
    }
  }, []);

  // --- Render Section Content --- 
  const renderSection = <T,>(
    state: SectionState<T>,
    renderItem: (item: T) => React.ReactNode,
    emptyMessage: string = "Nothing to display."
  ) => {
    if (state.loading) {
      return <div className="text-center text-gray-500 py-4">Loading...</div>;
    }
    if (state.error) {
      return <div className="text-center text-red-600 py-4">Error: {state.error}</div>;
    }
    if (state.data.length === 0) {
      return <div className="text-center text-gray-500 py-4">{emptyMessage}</div>;
    }
    return <div className="space-y-3">{state.data.map(renderItem)}</div>;
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Welcome to Falcon Hub</h1>
        <p className="text-gray-600 mb-8">Your centralized portal for all company resources and updates</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Critical Announcements */}
        <div className="bg-white p-4 rounded shadow col-span-1 md:col-span-3">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Critical Announcements</h2>
            <span 
              className="text-sm text-blue-600 cursor-pointer hover:underline"
              onClick={() => handleViewAll('Announcements')}
            >
              View All
            </span> 
          </div>
          {renderSection<Announcement>(
            announcements,
            (ann) => (
              <div key={ann.id} className={`border-l-4 ${getSeverityBorder(ann.severity)} pl-3 py-2`}>
                <div className="font-medium">{ann.title}</div>
                <div className="text-sm text-gray-600">{ann.description}</div>
                <div className="text-xs text-gray-500 mt-1 flex items-center flex-wrap">
                  {ann.scope !== 'Group-wide' && (
                      <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20 mr-2">
                          {ann.scope}
                      </span>
                  )}
                  <span className="mr-2">{formatRelativeTime(ann.publishedAt)}</span>
                  {ann.link && <a href={ann.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Details</a>}
                </div>
              </div>
            ),
            "No critical announcements right now."
          )}
        </div>
        
        {/* Pending Actions */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Pending Actions</h2>
            <span 
              className="text-sm text-blue-600 cursor-pointer hover:underline"
              onClick={() => handleViewAll('Pending Actions')}
            >
              View All
            </span>
          </div>
          {renderSection<PendingAction>(
            pendingActions,
            (action) => {
              const { component: Icon, color } = getActionIcon(action.type);
              const handleActionClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
                  e.preventDefault(); // Prevent default link behavior
                  console.log(`Navigating to action: ${action.title} at ${action.link}`);
                  navigate(action.link); // Use navigate for internal links
              };
              return (
                <a 
                  href={action.link} 
                  key={action.id} 
                  onClick={handleActionClick} 
                  className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                >
                  <Icon size={16} className={`${color} mr-2 flex-shrink-0`} />
                  <div>
                    <div className="text-sm font-medium">{action.title}</div>
                    <div className="text-xs text-gray-500">
                      {action.source && `${action.source} • `}
                      {action.details && `${action.details} • `}
                      {action.dueDate ? `Due ${formatRelativeTime(action.dueDate)}` : 'No due date'}
                    </div>
                  </div>
                </a>
              );
            },
            "No pending actions."
          )}
        </div>
        
        {/* Quick Links */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Quick Links</h2>
            <span 
              className="text-sm text-blue-600 cursor-pointer hover:underline"
              onClick={() => handleCustomize('Quick Links')}
            >
              Customize
            </span>
          </div>
          {quickLinks.loading && <div className="text-center text-gray-500 py-4">Loading...</div>}
          {quickLinks.error && <div className="text-center text-red-600 py-4">Error: {quickLinks.error}</div>}
          {!quickLinks.loading && !quickLinks.error && (
            <div className="grid grid-cols-2 gap-2">
              {quickLinks.data.length === 0 && <div className="col-span-2 text-center text-gray-500 py-4">No quick links configured.</div>}
              {quickLinks.data.map(link => {
                  const isExternal = link.targetHub === 'external';
                  const LinkComponent = isExternal ? 'a' : 'span'; // Use span for internal links handled by onClick
                  const handleQuickLinkClick = (e: React.MouseEvent) => {
                      if (!isExternal) {
                          e.preventDefault();
                          console.log(`Navigating to quick link: ${link.title} at ${link.url}`);
                          navigate(link.url);
                      }
                      // External links will behave like normal <a> tags
                  };

                  return (
                      <LinkComponent 
                        href={isExternal ? link.url : undefined} 
                        key={link.id}
                        onClick={handleQuickLinkClick}
                        className="flex flex-col items-center justify-center bg-gray-50 p-3 rounded hover:bg-gray-100 cursor-pointer text-center"
                        target={isExternal ? "_blank" : undefined}
                        rel={isExternal ? "noopener noreferrer" : undefined}
                      >
                        <DynamicFeatherIcon name={link.icon} size={24} className="text-blue-500 mb-1" />
                        <span className="text-xs">{link.title}</span>
                      </LinkComponent>
                  );
              })}
            </div>
          )}
        </div>
        
        {/* Recent Documents */}
        <div className="bg-white p-4 rounded shadow">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Recent Documents</h2>
             <span 
               className="text-sm text-blue-600 cursor-pointer hover:underline"
               onClick={() => handleViewAll('Recent Documents')}
             >
               View All
            </span>
          </div>
          {renderSection<RecentDocument>(
            recentDocs,
            (doc) => {
                const handleDocClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
                    e.preventDefault();
                    console.log(`Navigating to document: ${doc.name} at ${doc.link}`);
                    navigate(doc.link);
                };
                return (
                  <a 
                    href={doc.link} 
                    key={doc.id} 
                    onClick={handleDocClick}
                    className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                  >
                    <FeatherIcons.FileText size={16} className="text-gray-500 mr-2 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium truncate" title={doc.name}>{doc.name}</div>
                      <div className="text-xs text-gray-500">Opened {formatRelativeTime(doc.lastOpenedAt)}</div>
                    </div>
                  </a>
                );
            },
            "No recent documents found."
          )}
        </div>

        {/* Upcoming Events Section */}
        <div className="bg-white p-4 rounded shadow col-span-1 md:col-span-3">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-lg font-semibold">Upcoming Events</h2>
            <span 
              className="text-sm text-blue-600 cursor-pointer hover:underline"
              onClick={() => handleViewAll('Upcoming Events')}
            >
              View All
            </span>
          </div>
          {upcomingEvents.loading && <div className="text-center text-gray-500 py-4">Loading...</div>}
          {upcomingEvents.error && <div className="text-center text-red-600 py-4">Error: {upcomingEvents.error}</div>}
          {!upcomingEvents.loading && !upcomingEvents.error && (
             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {upcomingEvents.data.length === 0 && <div className="col-span-1 md:col-span-3 text-center text-gray-500 py-4">No upcoming events.</div>}
              {upcomingEvents.data.map(event => {
                  const handleEventClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
                      if(event.link) {
                          e.preventDefault();
                          console.log(`Navigating to event: ${event.title} at ${event.link}`);
                          navigate(event.link);
                      }
                      // If no link, do nothing on click
                  };
                  return (
                    <a 
                      href={event.link || '#'} 
                      key={event.id} 
                      onClick={handleEventClick}
                      className={`border rounded p-3 hover:shadow block ${event.link ? 'cursor-pointer' : 'cursor-default'}`}
                    >
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm text-gray-600">{formatEventDateTime(event.startDateTime, event.endDateTime)}</div>
                      <div className="text-xs text-gray-500 mt-1 flex items-center">
                        <span className="mr-2">{event.location}</span>•
                        {event.scope !== 'Group-wide' ? (
                            <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-600/20 ml-2">
                              {event.scope}
                            </span>
                         ) : (
                            <span className="inline-flex items-center rounded-md bg-gray-50 px-2 py-0.5 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10 ml-2">
                              Group-wide
                            </span>
                         )}
                      </div>
                    </a>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Modal Render */}
      <QuickLinkCustomizeModal 
        isOpen={isCustomizeModalOpen}
        onClose={() => setIsCustomizeModalOpen(false)}
        currentLinks={quickLinks.data} // Pass current links to modal
        onSave={handleSaveQuickLinks} // Pass save handler
      />
    </div>
  );
};

export default DashboardSection; 